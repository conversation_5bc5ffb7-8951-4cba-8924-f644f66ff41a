import os
import time
import json
import pickle
import threading
import datetime
import random
import mmap
import struct
import logging
import psutil
import traceback
from threading import Lock
from logging.handlers import TimedRotatingFileHandler
from xtquant.xttype import StockAccount
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
import tempfile
import sys
import subprocess

# 添加在导入后
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
class SharedMemoryPathManager:
    """统一管理共享内存路径的类，确保服务端和策略端路径一致"""
    
    @staticmethod
    def get_shared_memory_path(create_dir=True):
        """
        生成共享内存文件路径，各处使用的统一入口
        
        Args:
            create_dir: 是否创建目录
            
        Returns:
            str: 共享内存文件的绝对路径
        """
        # 1. 优先使用环境变量指定的固定路径
        if "QMT_SHARED_MEM_DIR" in os.environ:
            base_dir = os.environ["QMT_SHARED_MEM_DIR"]
        else:
            # 2. 不存在环境变量时，使用项目根目录 - 最关键的统一策略
            # 检测"Strategy"目录，确保在项目根目录结构中正确定位
            try:
                # 从当前文件开始向上查找项目根目录
                current_dir = os.path.dirname(os.path.abspath(__file__))
                strategy_dir = None
                
                # 向上最多查找3层目录
                for _ in range(3):
                    if os.path.basename(current_dir) == "Strategy" or os.path.basename(current_dir).lower() == "strategy":
                        strategy_dir = current_dir
                        break
                    parent = os.path.dirname(current_dir)
                    if parent == current_dir:  # 已到达根目录
                        break
                    current_dir = parent
                
                if strategy_dir:
                    # 在Strategy目录下创建共享目录
                    base_dir = os.path.join(strategy_dir, "qmt_trading_shm")
                else:
                    # 找不到Strategy目录时的备用方案
                    log_message = "无法确定Strategy目录，使用临时目录"
                    if 'logging' in globals():
                        logging.warning(log_message)
                    elif 'LogWarn' in globals():
                        LogWarn(log_message)
                    base_dir = os.path.join(tempfile.gettempdir(), "qmt_trading")
            except Exception as e:
                # 异常情况使用临时目录
                log_message = f"确定目录时出现异常：{str(e)}，使用临时目录"
                if 'logging' in globals():
                    logging.error(log_message)
                elif 'LogError' in globals():
                    LogError(log_message)
                base_dir = os.path.join(tempfile.gettempdir(), "qmt_trading")
        
        # 3. 处理多实例后缀
        suffix = ""
        if "QMT_SHARED_MEM_SUFFIX" in os.environ:
            suffix = f"_{os.environ['QMT_SHARED_MEM_SUFFIX']}"
        
        # 4. 确保目录存在
        if create_dir:
            try:
                os.makedirs(base_dir, exist_ok=True)
            except OSError as e:
                log_message = f"创建共享内存目录失败: {base_dir}, 错误: {str(e)}"
                if 'logging' in globals():
                    logging.error(log_message)
                elif 'LogError' in globals():
                    LogError(log_message)
                # 回退到临时目录
                base_dir = os.path.join(tempfile.gettempdir(), "qmt_trading")
                os.makedirs(base_dir, exist_ok=True)
        
        # 5. 构建最终路径
        file_path = os.path.abspath(os.path.join(base_dir, f"QMT_TRADING_GATEWAY{suffix}.dat"))
        
        # 6. 记录日志
        log_message = f"使用共享内存文件路径: {file_path}"
        if 'logging' in globals():
            logging.info(log_message)
        elif 'LogInfo' in globals():
            LogInfo(log_message)
            
        return file_path
class FileSystemUtils:
    """文件系统工具类"""
    
    @staticmethod
    def get_shared_memory_path():
        """获取共享内存文件路径"""
        # 直接调用统一的路径管理器
        return SharedMemoryPathManager.get_shared_memory_path()

# 共享内存区域常量定义
MEM_SIZE = 1024 * 1024  # 1MB共享内存区
HEADER_SIZE = 64        # 头部信息大小
COMMAND_SIZE = 256 * 1024  # 命令区域大小
RESPONSE_SIZE = 256 * 1024  # 响应区域大小
DATA_SIZE = 512 * 1024  # 数据区域大小

# 共享内存区域名称
SHMEM_NAME = "QMT_TRADING_GATEWAY"
SHMEM_FILE = FileSystemUtils.get_shared_memory_path()

# 状态和命令常量
CMD_NONE = 0
CMD_CONNECT = 1
CMD_PLACE_ORDER = 2
CMD_CANCEL_ORDER = 3
CMD_QUERY = 4
CMD_SHUTDOWN = 99

RESP_NONE = 0
RESP_SUCCESS = 1
RESP_FAILURE = 2
RESP_DATA = 3

# 数据区域索引
DATA_ACCOUNT = 0
DATA_POSITIONS = 1
DATA_ORDERS = 2
DATA_TRADES = 3
DATA_STATUS = 4

class SharedMemoryReader:
    """读取共享内存数据的简化类"""
    
    def __init__(self):
        self.lock = threading.RLock()  # 添加锁以同步访问
        self.shmem_file = FileSystemUtils.get_shared_memory_path()
        self.is_initialized = False
        self.file = None  # 初始化文件句柄为None
        self.mm = None    # 初始化内存映射对象为None
        
        try:
            # 检查共享内存文件是否存在
            if not os.path.exists(self.shmem_file):
                logging.warning(f"共享内存文件不存在: {self.shmem_file}")
                return
            
            # 打开共享内存
            self.file = open(self.shmem_file, 'r+b')
            self.mm = mmap.mmap(self.file.fileno(), MEM_SIZE)
            self.is_initialized = True
        except Exception as e:
            logging.error(f"初始化共享内存读取器失败: {e}")
            self.close()  # 确保异常时关闭已打开的资源
        
        # 注册析构函数
        import atexit
        atexit.register(self.close)
    
    def __enter__(self):
        """支持上下文管理器，使用with语句"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出时自动关闭文件"""
        self.close()
        return False  # 不屏蔽异常
    
    def __del__(self):
        """析构函数，关闭共享内存"""
        self.close()

    def close(self):
        """安全关闭所有资源"""
        if hasattr(self, 'lock'):  # 确保锁存在
            with self.lock:  # 使用锁确保线程安全
                # 安全地关闭mmap对象
                if hasattr(self, 'mm') and self.mm:
                    try:
                        self.mm.close()
                    except Exception as e:
                        logging.error(f"关闭内存映射对象失败: {str(e)}")
                    finally:
                        self.mm = None
                
                # 安全地关闭文件
                if hasattr(self, 'file') and self.file:
                    try:
                        self.file.close()
                    except Exception as e:
                        logging.error(f"关闭文件句柄失败: {str(e)}")
                    finally:
                        self.file = None
                
                self.is_initialized = False
        else:  # 没有锁的情况
            # 安全地关闭mmap对象
            if hasattr(self, 'mm') and self.mm:
                try:
                    self.mm.close()
                except Exception:
                    pass
                finally:
                    self.mm = None
            
            # 安全地关闭文件
            if hasattr(self, 'file') and self.file:
                try:
                    self.file.close()
                except Exception:
                    pass
                finally:
                    self.file = None
            
            self.is_initialized = False

    def set_data(self, idx, data):
        """设置数据区域"""
        with self.lock:
            if not self.is_initialized:
                return False
            
            try:
                offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + idx * DATA_SIZE // 5
                self.mm.seek(offset)
                serialized = pickle.dumps(data)
                self.mm.write(serialized[:DATA_SIZE // 5])
                return True
            except Exception as e:
                logging.error(f"设置共享内存数据失败: {e}")
                return False
    
    def get_data(self, idx):
        """获取数据区域"""
        if not self.is_initialized:
            return None
            
        try:
            offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + idx * DATA_SIZE // 5
            self.mm.seek(offset)
            data = self.mm.read(DATA_SIZE // 5)
            try:
                return pickle.loads(data)
            except:
                return None
        except Exception as e:
            logging.error(f"读取共享内存数据失败: {e}")
            return None
    
    def is_gateway_running(self):
        """检查网关是否运行中"""
        if not self.is_initialized:
            return False
            
        try:
            self.mm.seek(16)
            heartbeat = struct.unpack('I', self.mm.read(4))[0]
            current_time = int(time.time())
            # 心跳超过10秒视为网关不在运行
            return current_time - heartbeat < 10
        except:
            return False

    def close(self):
        if hasattr(self, 'mm') and self.mm:
            self.mm.close()
            self.mm = None
        if hasattr(self, 'file') and self.file:
            self.file.close()
            self.file = None
class EnhancedSharedMemory:
    """增强版共享内存管理类"""
    
    def __init__(self, create=False, auto_recover=True):
        self.auto_recover = auto_recover
        self.lock = threading.RLock()  # 使用可重入锁
        self.is_initialized = False
        self.clients = set()  # 记录连接的客户端
        self.file = None  # 初始化文件句柄为None
        self.mm = None    # 初始化内存映射对象为None
        
        # 首先调用 FileSystemUtils 获取正确的共享内存主文件路径
        self.shmem_file_primary = FileSystemUtils.get_shared_memory_path()
        # 基于主文件路径生成备份文件路径
        self.shmem_file_backup = self.shmem_file_primary + ".bak"
        
        # 确保目录存在 (get_shared_memory_path 内部已处理，但再次确认无害)
        try:
            os.makedirs(os.path.dirname(self.shmem_file_primary), exist_ok=True)
        except Exception as e:
            logging.error(f"无法创建或访问共享内存目录: {os.path.dirname(self.shmem_file_primary)}, Error: {e}")
            # 如果目录创建失败，可能无法继续，可以考虑抛出异常或返回
            return # 或者 raise OSError(...) 
        
        # 尝试打开或创建共享内存文件 (现在会使用正确的路径)
        success = self._initialize_memory_file(create)
        if not success and self.auto_recover:
            self._recover_from_backup()
        
        # 注册析构函数，确保在对象被销毁时关闭文件句柄
        import atexit
        atexit.register(self.close)
    
    def __enter__(self):
        """支持上下文管理器，使用with语句"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出时自动关闭文件"""
        self.close()
        return False  # 不屏蔽异常
    
    def __del__(self):
        """析构函数，确保对象被垃圾回收时关闭文件"""
        self.close()

    def _initialize_memory_file(self, create):
        """初始化内存文件，带占用进程处理机制"""
        try:
            # 关闭之前可能打开的文件句柄
            self.close()
            
            if create:
                # 检查文件是否存在，如果存在可能被占用
                if os.path.exists(self.shmem_file_primary):
                    logging.info(f"发现已存在的共享内存文件: {self.shmem_file_primary}")
                    # 终止占用进程
                    self._terminate_processes_using_file(self.shmem_file_primary)
                    # 删除可能损坏的文件
                    try:
                        os.remove(self.shmem_file_primary)
                        logging.info("已删除可能损坏的文件")
                    except Exception as e:
                        logging.error(f"无法删除文件: {str(e)}")
                        return False
                
                # 创建新文件
                if not self._create_memory_file():
                    return False
                    
                # 初始化内存结构
                self._initialize_memory_structure()
                
                # 创建备份
                import shutil
                try:
                    shutil.copy2(self.shmem_file_primary, self.shmem_file_backup)
                    logging.info("成功创建备份文件")
                except Exception as e:
                    logging.warning(f"创建备份失败: {str(e)}")
            
            # 打开内存映射
            try:
                self.file = open(self.shmem_file_primary, 'r+b')
                self.mm = mmap.mmap(self.file.fileno(), MEM_SIZE)
                self.is_initialized = True
                return True
            except Exception as e:
                logging.error(f"打开共享内存文件失败: {str(e)}")
                self.close()
                return False
            
        except Exception as e:
            logging.error(f"初始化共享内存失败: {str(e)}")
            self.close()
            return False
        
    def _initialize_memory_structure(self):
        """初始化共享内存的数据结构"""
        with open(self.shmem_file_primary, 'r+b') as f:
            mm = mmap.mmap(f.fileno(), MEM_SIZE)
            mm.seek(0)
            mm.write(struct.pack('I', 0))  # 命令标志
            mm.write(struct.pack('I', 0))  # 响应标志
            mm.write(struct.pack('Q', int(time.time())))  # 创建时间戳
            mm.write(struct.pack('I', int(time.time())))  # 心跳标志
            mm.write(struct.pack('I', 0))  # 客户端计数器
            mm.write(struct.pack('I', 0))  # 服务状态标志
            mm.write(struct.pack('I', 0))  # 预留1
            mm.write(struct.pack('I', 0))  # 预留2
            mm.close()
            
    def _recover_from_backup(self):
        """从备份文件恢复"""
        try:
            if os.path.exists(self.shmem_file_backup):
                import shutil
                logging.info("尝试从备份文件恢复共享内存")
                
                # 关闭可能打开的文件
                self.close()
                
                # 复制备份文件
                shutil.copy2(self.shmem_file_backup, self.shmem_file_primary)
                
                # 重新打开文件和内存映射
                try:
                    self.file = open(self.shmem_file_primary, 'r+b')
                    self.mm = mmap.mmap(self.file.fileno(), MEM_SIZE)
                    self.is_initialized = True
                    logging.info("成功从备份恢复共享内存文件")
                except Exception as e:
                    logging.error(f"恢复后打开共享内存文件失败: {str(e)}")
                    self.close()  # 确保关闭可能已经打开的文件
        except Exception as e:
            logging.error(f"从备份恢复失败: {str(e)}")
            self.close()  # 确保异常时也关闭文件
    
    def register_client(self, client_id):
        """注册客户端"""
        with self.lock:
            if not self.is_initialized:
                return False
            
            self.clients.add(client_id)
            # 更新客户端计数
            self.mm.seek(20)
            client_count = struct.unpack('I', self.mm.read(4))[0] + 1
            self.mm.seek(20)
            self.mm.write(struct.pack('I', client_count))
            return True
    
    def unregister_client(self, client_id):
        """注销客户端"""
        with self.lock:
            if not self.is_initialized or client_id not in self.clients:
                return False
            
            self.clients.remove(client_id)
            # 更新客户端计数
            self.mm.seek(20)
            client_count = max(0, struct.unpack('I', self.mm.read(4))[0] - 1)
            self.mm.seek(20)
            self.mm.write(struct.pack('I', client_count))
            return True
    
    def set_command(self, cmd, data=None):
        """设置命令"""
        with self.lock:
            # 写入命令区域
            self.mm.seek(0)
            self.mm.write(struct.pack('I', cmd))  # 命令标志
            
            # 写入命令数据
            if data:
                self.mm.seek(HEADER_SIZE)
                serialized = pickle.dumps(data)
                self.mm.write(serialized[:COMMAND_SIZE])
            
    
    def get_command(self):
        """获取命令"""
        with self.lock:
            self.mm.seek(0)
            cmd = struct.unpack('I', self.mm.read(4))[0]
            
            if cmd != CMD_NONE:
                # 读取命令数据
                self.mm.seek(HEADER_SIZE)
                data = self.mm.read(COMMAND_SIZE)
                try:
                    # 尝试反序列化直到遇到EOF
                    data = pickle.loads(data)
                except:
                    data = None
                
                return cmd, data
            
            return CMD_NONE, None
    
    def clear_command(self):
        """清除命令"""
        with self.lock:
            self.mm.seek(0)
            self.mm.write(struct.pack('I', CMD_NONE))
    
    def set_response(self, resp, data=None):
        """设置响应"""
        with self.lock:
            # 写入响应区域
            self.mm.seek(4)
            self.mm.write(struct.pack('I', resp))  # 响应标志
            
            # 写入响应数据
            if data:
                self.mm.seek(HEADER_SIZE + COMMAND_SIZE)
                serialized = pickle.dumps(data)
                self.mm.write(serialized[:RESPONSE_SIZE])
    
    def get_data(self, idx):
        """获取数据区域"""
        with self.lock:
            offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + idx * DATA_SIZE // 5
            self.mm.seek(offset)
            data = self.mm.read(DATA_SIZE // 5)
            try:
                return pickle.loads(data)
            except:
                return None
    
    def is_gateway_running(self):
        """检查网关是否运行中"""
        with self.lock:
            self.mm.seek(16)
            heartbeat = struct.unpack('I', self.mm.read(4))[0]
            current_time = int(time.time())
            # 心跳超过10秒视为网关不在运行
            return current_time - heartbeat < 10

    def update_heartbeat(self, current_time):
        """更新心跳"""
        with self.lock:
            if not self.is_initialized:
                return False
            
            self.mm.seek(16)
            self.mm.write(struct.pack('I', current_time))
            return True
    
    def get_client_count(self):
        """获取当前连接的客户端数量"""
        with self.lock:
            if not self.is_initialized:
                return 0
            
            self.mm.seek(20)
            return struct.unpack('I', self.mm.read(4))[0]
    
    def set_data(self, idx, data):
        """设置数据区域"""
        with self.lock:
            if not self.is_initialized:
                return False
                
            offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + idx * DATA_SIZE // 5
            self.mm.seek(offset)
            serialized = pickle.dumps(data)
            self.mm.write(serialized[:DATA_SIZE // 5])
            return True

    def close(self):
        """安全关闭所有资源，确保正确释放内存映射和文件句柄"""
        try:
            # 使用上下文管理器确保锁被正确释放
            with self.lock:
                # 安全地关闭mmap对象
                if hasattr(self, 'mm') and self.mm:
                    try:
                        self.mm.close()
                    except Exception as e:
                        logging.error(f"关闭内存映射对象失败: {str(e)}")
                    finally:
                        self.mm = None
                
                # 安全地关闭文件
                if hasattr(self, 'file') and self.file:
                    try:
                        self.file.close()
                    except Exception as e:
                        logging.error(f"关闭文件句柄失败: {str(e)}")
                    finally:
                        self.file = None
                
                self.is_initialized = False
                
                # 确保客户端计数置零
                self.clients.clear()
        except Exception as e:
            logging.error(f"关闭共享内存资源时发生异常: {str(e)}")
            # 即使出现异常也确保资源被释放
            self.mm = None
            self.file = None
            self.is_initialized = False

    def _terminate_processes_using_file(self, filepath):
        """识别并终止占用指定文件的进程"""
        try:
            import psutil
            terminated = False
            
            # 查找 Python 进程，检查是否有进程打开了该文件
            for proc in psutil.process_iter(['pid', 'name', 'open_files']):
                try:
                    # 过滤出 Python 进程
                    if 'python' in proc.info['name'].lower():
                        # 获取进程打开的文件列表
                        open_files = proc.open_files()
                        for file in open_files:
                            if filepath.lower() in file.path.lower():
                                logging.warning(f"发现进程占用共享内存文件: PID={proc.pid}, 进程名={proc.name()}")
                                
                                # 检查进程是否是当前进程
                                if proc.pid != os.getpid():
                                    # 不是当前进程，则终止它
                                    logging.warning(f"正在终止占用共享内存文件的进程: PID={proc.pid}")
                                    proc.terminate()
                                    try:
                                        # 等待进程终止
                                        proc.wait(timeout=3)
                                        terminated = True
                                        logging.info(f"成功终止占用文件的进程 PID={proc.pid}")
                                    except psutil.TimeoutExpired:
                                        # 如果等待超时，强制杀死进程
                                        logging.warning(f"进程未响应，强制终止: PID={proc.pid}")
                                        proc.kill()
                                        terminated = True
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            # 如果终止了进程，等待系统释放文件
            if terminated:
                logging.info("等待系统释放文件...")
                time.sleep(2)
            
        except Exception as e:
            logging.error(f"终止占用文件进程时出错: {str(e)}")

    def _create_memory_file(self):
        """安全地创建内存映射文件，并确保正确设置大小"""
        try:
            # 创建临时文件
            temp_file = self.shmem_file_primary + ".temp"
            
            # 先准备一个正确大小的临时文件
            with open(temp_file, 'wb') as f:
                # 创建正确大小的文件
                f.seek(MEM_SIZE - 1)
                f.write(b'\x00')
                f.flush()
                os.fsync(f.fileno())  # 确保写入磁盘
            
            # 然后安全地移动到目标位置，替换可能存在的文件
            try:
                if os.path.exists(self.shmem_file_primary):
                    os.replace(temp_file, self.shmem_file_primary)
                else:
                    os.rename(temp_file, self.shmem_file_primary)
                logging.info(f"成功创建共享内存文件: {self.shmem_file_primary}")
                return True
            except Exception as e:
                logging.error(f"移动临时文件失败: {str(e)}")
                return False
        except Exception as e:
            logging.error(f"创建临时文件失败: {str(e)}")
            return False

    def _recover_from_corruption(self):
        """
        从共享内存损坏中恢复，重新初始化核心数据
        """
        try:
            self.logger.warning("检测到共享内存数据损坏，尝试重新初始化...")
            
            # 关闭可能存在的旧资源
            self.close()
            
            # 终止占用文件的进程
            self._terminate_processes_using_file(self.shmem_file_primary)
            
            # 删除损坏的文件
            try:
                if os.path.exists(self.shmem_file_primary):
                    os.remove(self.shmem_file_primary)
                    self.logger.info(f"已删除损坏的共享内存文件: {self.shmem_file_primary}")
            except Exception as e:
                self.logger.error(f"删除损坏文件失败: {str(e)}")
                return False
            
            # 创建新文件
            if not self._create_memory_file():
                return False
            
            # 重新初始化内存结构
            self._initialize_memory_structure()
            
            # 打开内存映射
            try:
                self.file = open(self.shmem_file_primary, 'r+b')
                self.mm = mmap.mmap(self.file.fileno(), MEM_SIZE)
                self.is_initialized = True
                
                # 重要：初始化核心数据区域
                current_time = int(time.time())
                
                # 1. 初始化状态数据
                status_data = {
                    "is_healthy": False,  # 初始设为False，直到完成所有初始化
                    "start_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "last_heartbeat": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "connected": False,
                    "recovery_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "recovery_reason": "共享内存数据损坏"
                }
                self.set_data(DATA_STATUS, status_data)
                
                # 2. 初始化账户数据（空数据）
                account_data = {
                    "cash": 0.0,
                    "frozen_cash": 0.0,
                    "market_value": 0.0,
                    "total_asset": 0.0,
                    "fetch_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "is_initialized": False  # 标记为未完成初始化
                }
                self.set_data(DATA_ACCOUNT, account_data)
                
                # 3. 初始化订单、持仓和交易数据
                self.set_data(DATA_ORDERS, {})
                self.set_data(DATA_POSITIONS, {})
                self.set_data(DATA_TRADES, {})
                
                # 4. 更新心跳
                self.update_heartbeat(current_time)
                
                self.logger.info("共享内存已重新初始化并设置基础数据")
                return True
            except Exception as e:
                self.logger.error(f"重新初始化共享内存失败: {str(e)}")
                self.close()
                return False
        except Exception as e:
            self.logger.error(f"从损坏中恢复失败: {str(e)}")
            self.close()
            return False

# 交易回调对象
class GatewayCallback(XtQuantTraderCallback):
    def __init__(self, gateway):
        self.gateway = gateway
    
    def on_disconnected(self):
        self.gateway.logger.warning("[网关] 连接断开")
        self.gateway.connected = False
        self.gateway.status["connected"] = False
        self.gateway.status["last_disconnect"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    def on_order_stock_async_response(self, response):
        self.logger.info(f"[网关] 异步下单响应: {response.order_id}, 备注: {response.order_remark}")
    
    def on_stock_order(self, order):
        self.logger.info(f"[网关] 委托回报: {order.order_id}, 状态: {order.order_status}")
        with self.gateway.lock:
            self.gateway.orders[order.order_id] = {
                "order_id": order.order_id,
                "stock_code": order.stock_code,
                "order_status": order.order_status,
                "order_time": order.order_time,
                "traded_volume": order.traded_volume,
                "traded_price": order.traded_price,
                "order_volume": order.order_volume,
                "price": order.price,
                "order_remark": order.order_remark
            }
    
    def on_stock_trade(self, trade):
        self.logger.info(f"[网关] 成交回报: {trade.order_id}, 数量: {trade.traded_volume}")
        with self.gateway.lock:
            # 保存成交记录
            trade_key = f"{trade.order_id}_{trade.traded_id}"
            self.gateway.trades[trade_key] = {
                "order_id": trade.order_id,
                "stock_code": trade.stock_code,
                "traded_id": trade.traded_id,
                "traded_time": trade.traded_time,
                "traded_volume": trade.traded_volume,
                "traded_price": trade.traded_price
            }
            
            # 更新订单信息
            if trade.order_id in self.gateway.orders:
                self.gateway.orders[trade.order_id]["traded_volume"] = trade.traded_volume
                self.gateway.orders[trade.order_id]["traded_price"] = trade.traded_price
    
    def on_order_error(self, error):
        self.logger.error(f"[网关] 委托错误: {error.error_msg}")
        self.gateway.status["last_error"] = {
            "time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "message": error.error_msg,
            "order_id": error.order_id
        }

class TradingGatewayService:
    """交易网关服务类"""
    def __init__(self):
        self.logger = self._init_logger()
        # 创建共享内存        # 加载配置文件
        self.config = self._load_config()
        # 初始化日志系统
        self.shmem = EnhancedSharedMemory(create=True)
        self.callback = GatewayCallback(self)
        # 交易API相关
        self.client_path = None
        self.account_id = None
        self.trader = None
        self.account = None
        self.connected = False
        self.session_id = None
        
        # 数据缓存
        self.account_data = {}
        self.positions = {}
        self.orders = {}
        self.trades = {}
        self.status = {"connected": False, "start_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
        
        # 同步锁
        self.lock = Lock()
        
        # 服务线程
        self.running = False
        self.service_thread = None
        
        # 注册清理
        import atexit
        atexit.register(self._cleanup_resources)

    def _cleanup_resources(self):
        """清理所有资源"""
        # 关闭交易线程
        if hasattr(self, 'trader') and self.trader:
            try:
                self.trader.stop()
            except Exception as e:
                self.logger.error(f"停止交易接口异常: {str(e)}")
            finally:
                self.trader = None
        
        # 关闭共享内存
        if hasattr(self, 'shmem') and self.shmem:
            try:
                self.shmem.close()
            except Exception as e:
                self.logger.error(f"关闭共享内存异常: {str(e)}")
            finally:
                self.shmem = None
        
        self.logger.info("资源已清理")

    def _load_config(self):
        """从同目录加载配置文件"""
        config_path = os.path.join(os.path.dirname(__file__), "gateway_config.json")
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {str(e)}")
            raise SystemExit("无法启动服务：缺少配置文件")

    def _init_logger(self):
        """初始化日志记录器"""
        logger = logging.getLogger("TradingGateway")
        logger.setLevel(logging.INFO)
        
        # 创建每天轮转的日志文件
        handler = TimedRotatingFileHandler(
            filename="gateway_service.log",
            when="midnight",
            backupCount=7,
            encoding='utf-8'
        )
        formatter = logging.Formatter(
            '[%(asctime)s] [%(levelname)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        return logger
    
    def start(self):
        """启动服务，增强数据初始化"""
        if self.running:
            self.logger.warning("服务已在运行中")
            return False
        
        self.running = True
        current_time = int(time.time())
        
        # 初始状态设置为未健康，直到完成所有初始化
        self.status = {
            "is_healthy": False,
            "last_heartbeat_time": current_time,
            "start_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "connected": False,
            "initialization_phase": "starting"
        }
        
        self.shmem.set_data(DATA_STATUS, self.status)
        # 立即更新心跳
        self.shmem.update_heartbeat(current_time)

        # 启动服务线程
        self.service_thread = threading.Thread(target=self._service_loop, daemon=True)
        self.service_thread.start()
        
        # 立即尝试连接交易接口
        client_path = self.config.get("client_path")
        account_id = self.config.get("account_id")
        
        if client_path and account_id:
            self.logger.info(f"启动后自动连接交易接口: 路径={client_path}, 账号={account_id}")
            connection_success = self._connect(client_path, account_id)
            
            if connection_success:
                self.logger.info("交易接口连接成功，服务启动完成")
                # 状态更新已在_connect方法内完成
                return True
            else:
                self.logger.warning("交易接口连接失败，服务以离线模式启动")
                # 更新状态为离线模式
                self.status["initialization_phase"] = "offline_mode"
                self.status["connection_error"] = "启动时连接失败"
                self.shmem.set_data(DATA_STATUS, self.status)
        else:
            self.logger.warning("配置中缺少客户端路径或账号ID，服务以离线模式启动")
            self.status["initialization_phase"] = "offline_mode"
            self.status["configuration_error"] = "缺少客户端路径或账号ID"
            self.shmem.set_data(DATA_STATUS, self.status)
        
        self.logger.info("交易网关服务已启动")
        return True
    
    def _service_loop(self):
        """增强的服务循环"""
        self.logger.info("服务循环已启动")
        last_heartbeat = time.time()
        last_health_check = time.time()  # 分离健康检查计时器
        reconnect_attempts = 0
        client_path = self.config["client_path"]
        account_id = self.config["account_id"]
        
        # 记录启动信息
        self.logger.info(f"服务启动时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"客户端路径: {client_path}")
        self.logger.info(f"账号ID: {account_id}")

        # 导入traceback模块用于错误跟踪
        import traceback
        
        # 启动时立即更新心跳和执行健康检查
        try:


            current_time = time.time()
            self.shmem.update_heartbeat(int(current_time))

            tracking_dict = self.shmem.get_data(DATA_STATUS)
            logging.info(f"1共享内存已重新初始化并设置基础数据: {tracking_dict},心跳状态:{self.shmem.is_gateway_running()}")

            # 初始化健康状态为True，除非健康检查失败
            self.status["is_healthy"] = True
            self.status["last_heartbeat"] = datetime.datetime.fromtimestamp(current_time).strftime("%Y-%m-%d %H:%M:%S")
            self.status["last_heartbeat_timestamp"] = current_time
            self.shmem.set_data(DATA_STATUS, self.status)

            tracking_dict = self.shmem.get_data(DATA_STATUS)
            logging.info(f"2共享内存已重新初始化并设置基础数据: {tracking_dict},心跳状态:{self.shmem.is_gateway_running()}")

            # 执行首次健康检查
            is_healthy = self._health_check()
            self.logger.info(f"初始健康检查结果: {'通过' if is_healthy else '失败'}")
        except Exception as e:
            self.logger.error(f"初始化心跳和健康状态失败: {str(e)}")
            self.logger.error(traceback.format_exc())

        while self.running:
            try:
                current_time = time.time()

                # 心跳更新（每3秒更新一次，频率更高）
                if current_time - last_heartbeat >= 3.0:
                    try:
                        # 减少日志输出，仅保留关键日志
                        # 使用单次锁定完成所有操作，减少锁竞争
                        with self.lock:
                            # 直接更新心跳时间戳
                            self.shmem.update_heartbeat(int(current_time))
                            
                            # 更新本地状态和共享内存状态（一次性完成）
                            heartbeat_time_str = datetime.datetime.fromtimestamp(current_time).strftime("%Y-%m-%d %H:%M:%S")
                            self.status.update({
                                "running": True,
                                "connected": self.connected,
                                "last_heartbeat": heartbeat_time_str,
                                "last_heartbeat_timestamp": current_time
                                # 不覆盖其他字段，特别是is_healthy状态
                            })
                            
                            # 一次性设置数据
                            self.shmem.set_data(DATA_STATUS, self.status)
                        
                        last_heartbeat = current_time
                        # 降低日志级别，不要在每次心跳都记录INFO日志
                        self.logger.debug(f"心跳更新: {int(current_time)}")
                    except Exception as e:
                        self.logger.error(f"心跳更新失败: {str(e)}")
                        self.logger.error(traceback.format_exc())

                # 健康检查（每分钟）
                if current_time - last_health_check > 60:
                    is_healthy = self._health_check()
                    last_health_check = current_time
                    
                    # 确保健康状态也被更新到状态中
                    self.status["is_healthy"] = is_healthy
                    self.shmem.set_data(DATA_STATUS, self.status)
                
                # 自动重连逻辑
                if not self.connected and reconnect_attempts < self.config.get("max_reconnect", 3):
                    self.logger.warning("检测到连接断开，尝试自动重连...")
                    if self._auto_reconnect(client_path, account_id):
                        reconnect_attempts = 0
                    else:
                        reconnect_attempts += 1
                        time.sleep(5)

                # 处理命令
                try:
                    cmd, data = self.shmem.get_command()
                    if cmd > 0:
                        self._handle_command(cmd, data)
                except Exception as e:
                    self.logger.error(f"[网关] 处理命令异常: {e}")
                
                time.sleep(0.1)  # 减少睡眠时间，提高响应速度

            except Exception as e:
                self.logger.error(f"服务循环异常: {str(e)}")
                self.logger.error(traceback.format_exc())
        
        self.logger.info("[网关] 服务循环已退出")
    
    def _handle_command(self, cmd, data):
        """处理命令"""
        if cmd == CMD_CONNECT:
            # 连接命令
            result = self._connect(data.get("client_path"), data.get("account_id"))
            self.shmem.set_response(RESP_SUCCESS if result else RESP_FAILURE, 
                                   {"success": result, "message": "连接成功" if result else "连接失败"})
        
        elif cmd == CMD_PLACE_ORDER:
            # 下单命令
            order_id = self._place_order(
                data.get("symbol"),
                data.get("direction"),
                data.get("quantity"),
                data.get("price_type"),
                data.get("price"),
                data.get("strategy_name", "auto_trading"),
                data.get("remark", "")
            )
            self.shmem.set_response(RESP_SUCCESS if order_id else RESP_FAILURE, 
                                   {"success": bool(order_id), "order_id": order_id})
        
        elif cmd == CMD_CANCEL_ORDER:
            # 撤单命令
            result = self._cancel_order(data.get("order_id"))
            self.shmem.set_response(RESP_SUCCESS if result else RESP_FAILURE, 
                                   {"success": result})
        
        elif cmd == CMD_QUERY:
            # 查询命令
            query_type = data.get("query_type")
            result = None
            
            if query_type == "account":
                result = self._query_account()
            elif query_type == "positions":
                result = self._query_positions()
            elif query_type == "orders":
                result = self._query_orders()
            elif query_type == "trades":
                result = self._query_trades()
            elif query_type == "order":
                result = self._query_order(data.get("order_id"))
            
            self.shmem.set_response(RESP_DATA, {"data": result})
        
        elif cmd == CMD_SHUTDOWN:
            # 关闭命令
            self._shutdown()
            self.shmem.set_response(RESP_SUCCESS, {"message": "服务已关闭"})
    
    def _connect(self, client_path, account_id):
        """连接交易接口"""
        try:            
            if not client_path or not account_id:
                self.logger.error("[网关] 客户端路径或账户ID为空")
                return False
            
            self.client_path = client_path
            self.account_id = account_id
            self.session_id = int(time.time())
                        
            # 创建交易对象
            self.trader = XtQuantTrader(self.client_path, self.session_id)
            self.account = StockAccount(self.account_id)
            
            # 注册回调
            self.trader.register_callback(self.callback)
            
            # 启动交易线程
            self.trader.start()
            
            # 建立连接
            connect_result = self.trader.connect()
            if connect_result == 0:
                self.connected = True
                self.status["connected"] = True
                self.status["last_connect"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.logger.info("[网关] 交易服务器连接成功")
                
                # 订阅交易推送
                subscribe_result = self.trader.subscribe(self.account)
                if subscribe_result == 0:
                    self.logger.info("[网关] 交易主推订阅成功")
                else:
                    self.logger.error(f"[网关] 交易主推订阅失败，错误码: {subscribe_result}")
                
                # 初始查询数据
                self._update_data()
                
                return True
            else:
                self.logger.error(f"[网关] 交易服务器连接失败，错误码: {connect_result}")
                return False
                
        except Exception as e:
            self.logger.error(f"[网关] 连接异常: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False
    
    def _place_order(self, symbol, direction, quantity, price_type, price, strategy_name="auto_trading", remark=""):
        """下单"""
        if not self.trader or not self.account or not self.connected:
            self.logger.error("[网关] 交易对象或账号未初始化，或连接未建立")
            return None
        
        try:
            order_id = self.trader.order_stock(
                self.account, 
                symbol, 
                direction, 
                quantity, 
                price_type, 
                price, 
                strategy_name, 
                remark
            )
            
            if order_id:
                self.logger.info(f"[网关] 下单成功，订单ID: {order_id}")
                return order_id
            else:
                self.logger.info("[网关] 下单失败")
                return None
                
        except Exception as e:
            self.logger.error(f"[网关] 下单异常: {str(e)}")
            return None
    
    def _cancel_order(self, order_id):
        """撤单"""
        if not self.trader or not self.account or not self.connected:
            self.logger.error("[网关] 交易对象或账号未初始化，或连接未建立")
            return False
        
        try:
            result = self.trader.cancel_order_stock(self.account, order_id)
            if result == 0:
                self.logger.info(f"[网关] 撤单成功，订单ID: {order_id}")
                return True
            else:
                self.logger.error(f"[网关] 撤单失败，订单ID: {order_id}, 错误码: {result}")
                return False
                
        except Exception as e:
            self.logger.error(f"[网关] 撤单异常: {str(e)}")
            return False
    
    def _query_account(self):
        """查询账户资产"""
        if not self.trader or not self.account or not self.connected:
            return None
        
        try:
            asset = self.trader.query_stock_asset(self.account)
            if asset:
                self.account_data = {
                    "cash": asset.cash,
                    "frozen_cash": asset.frozen_cash,
                    "market_value": asset.market_value,
                    "total_asset": asset.total_asset,
                    "fetch_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                return self.account_data
            return None
        except Exception as e:
            self.logger.error(f"[网关] 查询账户异常: {str(e)}")
            return None
    
    def _query_positions(self):
        """查询持仓"""
        if not self.trader or not self.account or not self.connected:
            return []
        
        try:
            positions = self.trader.query_stock_positions(self.account)
            self.positions = {}
            
            for pos in positions:
                self.positions[pos.stock_code] = {
                    "stock_code": pos.stock_code,
                    "volume": pos.volume,
                    "can_use_volume": pos.can_use_volume,
                    "avg_price": pos.avg_price,
                    "market_value": pos.market_value,
                    "cost_price": pos.cost_price if hasattr(pos, 'cost_price') else 0
                }
            
            return self.positions
        except Exception as e:
            self.logger.error(f"[网关] 查询持仓异常: {str(e)}")
            return {}
    
    def _query_orders(self):
        """查询当日委托"""
        if not self.trader or not self.account or not self.connected:
            return []
        
        try:
            orders = self.trader.query_stock_orders(self.account)
            
            # 更新订单字典
            with self.lock:
                for order in orders:
                    self.orders[order.order_id] = {
                        "order_id": order.order_id,
                        "stock_code": order.stock_code,
                        "order_status": order.order_status,
                        "order_time": order.order_time,
                        "traded_volume": order.traded_volume,
                        "traded_price": order.traded_price,
                        "order_volume": order.order_volume,
                        "price": order.price,
                        "order_remark": order.order_remark
                    }
            
            return self.orders
        except Exception as e:
            self.logger.error(f"[网关] 查询委托异常: {str(e)}")
            return {}
    
    def _query_trades(self):
        """查询当日成交"""
        if not self.trader or not self.account or not self.connected:
            return []
        
        try:
            trades = self.trader.query_stock_trades(self.account)
            
            # 更新成交字典
            with self.lock:
                for trade in trades:
                    trade_key = f"{trade.order_id}_{trade.traded_id}"
                    self.trades[trade_key] = {
                        "order_id": trade.order_id,
                        "stock_code": trade.stock_code,
                        "traded_id": trade.traded_id,
                        "traded_time": trade.traded_time,
                        "traded_volume": trade.traded_volume,
                        "traded_price": trade.traded_price
                    }
            
            return self.trades
        except Exception as e:
            self.logger.error(f"[网关] 查询成交异常: {str(e)}")
            return {}
    
    def _query_order(self, order_id):
        """查询单个订单"""
        if not self.trader or not self.account or not self.connected:
            return None
        
        try:
            order = self.trader.query_stock_order(self.account, order_id)
            if order:
                return {
                    "order_id": order.order_id,
                    "stock_code": order.stock_code,
                    "order_status": order.order_status,
                    "order_time": order.order_time,
                    "traded_volume": order.traded_volume,
                    "traded_price": order.traded_price,
                    "order_volume": order.order_volume,
                    "price": order.price,
                    "order_remark": order.order_remark
                }
            return None
        except Exception as e:
            self.logger.error(f"[网关] 查询订单异常: {str(e)}")
            return None
    
    def _update_data(self):
        """更新所有数据"""
        try:
            # 查询并更新账户数据
            self._query_account()
            self.shmem.set_data(DATA_ACCOUNT, self.account_data)
            
            # 查询并更新持仓数据
            self._query_positions()
            self.shmem.set_data(DATA_POSITIONS, self.positions)
            
            # 查询并更新订单数据
            self._query_orders()
            self.shmem.set_data(DATA_ORDERS, self.orders)
            
            # 查询并更新成交数据
            self._query_trades()
            self.shmem.set_data(DATA_TRADES, self.trades)
            
            # 更新状态
            self.shmem.set_data(DATA_STATUS, self.status)
            
        except Exception as e:
            self.logger.error(f"[网关] 更新数据异常: {str(e)}")
    
    def _shutdown(self):
        """关闭服务"""
        self.running = False  # 先停止服务循环
        self.connected = False  # 停止监控线程
        # 清理所有资源
        self._cleanup_resources()
        self.logger.info("[网关] 服务已关闭")
    
    def run_forever(self):
        """阻塞运行"""
        self.start()
        
        try:
            while self.running and self.service_thread.is_alive():
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("[网关] 收到中断信号，关闭服务...")
            self._shutdown()
        except Exception as e:
            self.logger.error(f"服务运行异常: {str(e)}")
            self.logger.error(traceback.format_exc())
            self._shutdown()

    def _auto_reconnect(self,client_path, account_id):
        """自动重连核心逻辑"""
        try:
            # 先停止监控线程
            self.connected = False  # 新增：先停止监控线程循环
            
            # 停止旧连接
            if self.trader:
                try:
                    self.trader.stop()
                except Exception as e:
                    self.logger.error(f"停止交易接口异常: {str(e)}")
                finally:
                    self.trader = None  # 确保置空
            
            # 重建交易对象前等待1秒
            time.sleep(1)
            
            # 创建新会话ID
            self.session_id = int(time.time()) + random.randint(0, 1000)
            
            # 重建交易对象
            self.trader = XtQuantTrader(client_path, self.session_id)  # 使用参数传入的client_path
            self.account = StockAccount(account_id)  # 使用参数传入的account_id
            self.trader.register_callback(self.callback)
            
            # 启动交易线程
            self.trader.start()
            
            # 执行连接
            result = self.trader.connect()
            if result == 0:
                self.connected = True  # 先设置连接状态
                self._after_successful_connection(self.account)
                return True
            return False
        except Exception as e:
            self.logger.error(f"自动重连失败: {str(e)}")
            return False

    def _after_successful_connection(self, account):
        """连接成功后的处理"""
        # 订阅账户
        if account:
            subscribe_result = self.trader.subscribe(account)
            if subscribe_result != 0:
                self.logger.error(f"订阅失败，错误码: {subscribe_result}")
                return False
        
        # 立即获取并更新所有数据，确保共享内存中的数据有效
        try:
            # 更新账户数据
            asset = self.trader.query_stock_asset(account)
            if asset:
                self.account_data = {
                    "cash": asset.cash,
                    "frozen_cash": asset.frozen_cash,
                    "market_value": asset.market_value,
                    "total_asset": asset.total_asset,
                    "fetch_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "is_initialized": True  # 标记为已完成初始化
                }
                self.shmem.set_data(DATA_ACCOUNT, self.account_data)
                self.logger.info(f"成功更新账户数据: 总资产={asset.total_asset}")
            
            # 更新持仓数据
            positions = self.trader.query_stock_positions(account)
            self.positions = {}
            for pos in positions:
                self.positions[pos.stock_code] = {
                    "stock_code": pos.stock_code,
                    "volume": pos.volume,
                    "can_use_volume": pos.can_use_volume,
                    "avg_price": pos.avg_price,
                    "market_value": pos.market_value,
                    "cost_price": pos.cost_price if hasattr(pos, 'cost_price') else 0
                }
            self.shmem.set_data(DATA_POSITIONS, self.positions)
            self.logger.info(f"成功更新持仓数据: {len(self.positions)}个品种")
            
            # 更新订单和成交数据
            self._query_orders()
            self.shmem.set_data(DATA_ORDERS, self.orders)
            self._query_trades()
            self.shmem.set_data(DATA_TRADES, self.trades)
            
            # 更新状态
            self.status["connected"] = True
            self.status["last_connect"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.status["is_healthy"] = True  # 所有数据已加载，标记为健康
            self.shmem.set_data(DATA_STATUS, self.status)
            
            self.logger.info("连接成功后已更新所有数据")
            return True
        except Exception as e:
            self.logger.error(f"更新数据失败: {str(e)}")
            return False

    def _start_monitor_thread(self):
        """启动监控线程"""
        def monitor_task():
            # 增加运行状态检查
            while self.running and self.connected:  # 增加self.running判断
                try:
                    # 检查客户端进程
                    if not self._check_qmt_process():
                        self.logger.error("检测到QMT客户端进程异常退出")
                        self.connected = False
                        break
                    
                    # # 检查网络连接（增加返回值处理）
                    # network_ok = self._check_network()
                    # if not network_ok:
                    #     self.logger.warning("网络连接异常，尝试重新连接...")
                    #     self.connected = False
                    #     break
                    
                    time.sleep(30)
                except Exception as e:
                    self.logger.error(f"监控线程异常: {str(e)}")
                    break
        
        threading.Thread(target=monitor_task, daemon=True).start()

    def _check_qmt_process(self):
        """检查QMT客户端进程"""
        try:
            for proc in psutil.process_iter(['name']):
                if proc.info['name'] in ('miniquote.exe', 'XtMiniQmt.exe'):
                    return True
            return False
        except Exception as e:
            self.logger.error(f"进程检查失败: {str(e)}")
            return False

    def _check_network(self):
        """网络连接检查"""
        import socket
        try:
            # 测试连接QMT服务器（示例地址，需替换实际地址）
            sock = socket.create_connection(("127.0.0.1", 80), timeout=3)
            sock.close()
            return True
        except Exception as e:
            self.logger.warning(f"网络连接检查失败: {str(e)}")
            return False

    def _health_check(self):
        """
        改进的健康检查方法，增加数据有效性验证
        """
        self.logger.debug("执行健康检查...")
        status_details = {"检查时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
        all_checks_passed = True

        # 1. 检查共享内存文件存在
        if not os.path.exists(self.shmem.shmem_file_primary):
            all_checks_passed = False
            status_details["共享内存文件"] = "不存在"
            self.logger.error("健康检查失败: 共享内存文件不存在")
        else:
            # 检查文件大小是否正确
            file_size = os.path.getsize(self.shmem.shmem_file_primary)
            if file_size != MEM_SIZE:
                all_checks_passed = False
                status_details["共享内存文件大小"] = f"{file_size}字节(预期{MEM_SIZE}字节)"
                self.logger.error(f"健康检查失败: 共享内存文件大小不正确 {file_size} != {MEM_SIZE}")
            else:
                status_details["共享内存文件"] = "正常"
                
        # 2. 检查共享内存初始化
        if not self.shmem or not self.shmem.is_initialized:
            all_checks_passed = False
            status_details["共享内存"] = "未初始化"
            self.logger.warning("健康检查失败: 共享内存未初始化")
        else:
            status_details["共享内存"] = "已初始化"
        
        # 3. 检查数据有效性
        if self.shmem and self.shmem.is_initialized:
            # 3.1 心跳状态检查（确保心跳更新正常）
            try:
                current_time = int(time.time())
                self.shmem.mm.seek(16)
                heartbeat = struct.unpack('I', self.shmem.mm.read(4))[0]

                # 检查心跳有效性（不为0且不超过未来时间，且在合理范围内）
                if heartbeat == 0 or heartbeat > current_time + 60:  # 允许60秒的时钟差异
                    all_checks_passed = False
                    status_details["心跳时间戳"] = f"无效 ({heartbeat})"
                    self.logger.warning(f"健康检查失败: 心跳时间戳无效 {heartbeat}")

                    # 尝试恢复心跳
                    recovery_success = self._recover_from_heartbeat_failure()
                    status_details["心跳恢复尝试"] = "成功" if recovery_success else "失败"
                elif current_time - heartbeat > 30:  # 如果心跳超过30秒未更新
                    all_checks_passed = False
                    status_details["心跳状态"] = f"延迟 ({current_time - heartbeat}秒)"
                    self.logger.warning(f"健康检查警告: 心跳更新延迟 {current_time - heartbeat}秒")

                    # 尝试强制更新心跳
                    try:
                        self.shmem.update_heartbeat(current_time)
                        status_details["心跳强制更新"] = "成功"
                    except Exception as e:
                        status_details["心跳强制更新"] = f"失败 ({str(e)})"
                else:
                    status_details["心跳状态"] = "正常"
            except Exception as e:
                all_checks_passed = False
                status_details["心跳检查"] = f"异常 ({str(e)})"
                self.logger.warning(f"心跳检查异常: {str(e)}")

            # 3.2 检查核心数据区域是否包含有效数据
            try:
                # 检查账户数据
                account_data = self.shmem.get_data(DATA_ACCOUNT)
                if not account_data or not isinstance(account_data, dict):
                    all_checks_passed = False
                    status_details["账户数据"] = "无效或不存在"
                    self.logger.warning("健康检查失败: 账户数据无效或不存在")
                else:
                    status_details["账户数据"] = "有效"
                    
                # 检查状态数据
                status_data = self.shmem.get_data(DATA_STATUS)
                if not status_data or not isinstance(status_data, dict):
                    all_checks_passed = False
                    status_details["状态数据"] = "无效或不存在"
                    self.logger.warning("健康检查失败: 状态数据无效或不存在")
                else:
                    status_details["状态数据"] = "有效"
            except Exception as e:
                all_checks_passed = False
                status_details["数据区检查"] = f"异常 ({str(e)})"
                self.logger.warning(f"数据区检查异常: {str(e)}")
        
        # 4. 检查交易线程
        if hasattr(self, 'trader') and self.trader:
            status_details["交易线程"] = "正常"
        else:
            all_checks_passed = False
            status_details["交易线程"] = "异常"
            self.logger.warning("健康检查失败: 交易线程异常")
        
        # 5. 检查客户端路径
        client_path = self.config.get("client_path", "")
        if client_path and os.path.exists(client_path):
            status_details["客户端路径"] = "有效"
        else:
            all_checks_passed = False
            status_details["客户端路径"] = "无效"
            self.logger.warning(f"健康检查失败: 客户端路径无效 ({client_path})")
        
        # 6. 检查与交易系统的连接状态
        if self.connected:
            status_details["交易连接"] = "已连接"
        else:
            all_checks_passed = False
            status_details["交易连接"] = "未连接"
            self.logger.warning("健康检查失败: 交易系统未连接")
        
        # 更新健康状态到共享内存和本地状态
        try:
            current_status = self.shmem.get_data(DATA_STATUS) or {}
            current_status["is_healthy"] = all_checks_passed
            current_status["health_check_details"] = status_details
            current_status["last_health_check"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            self.shmem.set_data(DATA_STATUS, current_status)
            self.status = current_status
            
            if all_checks_passed:
                self.logger.info("健康检查通过")
            else:
                self.logger.warning(f"健康检查未通过: {status_details}")
        except Exception as e:
            self.logger.error(f"更新健康状态到共享内存失败: {str(e)}")
            all_checks_passed = False
        
        return all_checks_passed

    def is_gateway_running(self):
        """判断嵌入式网关服务是否在运行"""
        return self.shmem.is_gateway_running()

    def _recover_from_heartbeat_failure(self):
        """从心跳失败中恢复"""
        self.logger.warning("检测到心跳失败，尝试恢复...")
        
        try:
            # 重置心跳时间戳
            current_time = int(time.time())
            self.shmem.update_heartbeat(current_time)
            
            # 重新检查共享内存健康状态
            if not self.shmem.is_initialized:
                self.logger.error("共享内存未初始化，尝试重新初始化...")
                # 关闭当前共享内存并重新创建
                self.shmem.close()
                self.shmem = EnhancedSharedMemory(create=True)
                
                # 重新初始化状态数据
                self.status["is_healthy"] = False
                self.status["recovery_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.status["recovery_reason"] = "心跳失败恢复"
                self.shmem.set_data(DATA_STATUS, self.status)
                self.shmem.update_heartbeat(current_time)
                
            return True
        except Exception as e:
            self.logger.error(f"从心跳失败恢复失败: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False

class WatchdogMonitor:
    """服务看门狗监控类"""
    
    def __init__(self, service_control, check_interval=10):
        self.service_control = service_control
        self.check_interval = check_interval
        self.running = False
        self.thread = None
        self.last_recovery_time = 0
        self.recovery_attempts = 0
        self.max_recovery_attempts = 5
        
        # 注册析构函数
        import atexit
        atexit.register(self.stop)
    
    def __del__(self):
        """析构函数，确保资源释放"""
        self.stop()

    def start(self):
        """启动监控"""
        if self.thread and self.thread.is_alive():
            return
            
        self.running = True
        self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.thread.start()
        
    def stop(self):
        """停止监控"""
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=3)
            
    def _monitor_loop(self):
        """监控循环"""
        while self.running:
            try:
                self._check_service_health()
            except Exception as e:
                logging.error(f"监控服务健康状态时发生错误: {str(e)}")
            
            time.sleep(self.check_interval)
            
    def _check_service_health(self):
        """检查服务健康状态"""
        # 检查共享内存是否可访问
        mem_ok = self.service_control.mem_reader.is_initialized
        
        # 检查服务是否在运行
        service_running = (self.service_control.service_process and 
                           self.service_control.service_process.poll() is None)
        
        # 检查心跳是否正常
        heartbeat_ok = self.service_control.mem_reader.is_gateway_running()
        
        # 获取服务状态
        status_data = self.service_control.mem_reader.get_data(DATA_STATUS) or {}
        is_healthy = status_data.get("is_healthy", False)
        
        # 综合判断服务健康状态
        service_healthy = mem_ok and service_running and heartbeat_ok and is_healthy
        
        if not service_healthy:
            # 记录不健康的原因
            reasons = []
            if not mem_ok:
                reasons.append("共享内存不可访问")
            if not service_running:
                reasons.append("服务进程不在运行")
            if not heartbeat_ok:
                reasons.append("服务心跳异常")
            if not is_healthy:
                reasons.append("服务报告不健康状态")
                
            logging.warning(f"服务不健康，原因: {', '.join(reasons)}")
            
            # 尝试恢复服务
            self._try_recover_service(reasons)
    
    def _try_recover_service(self, reasons):
        """尝试恢复服务"""
        current_time = time.time()
        
        # 如果短时间内尝试恢复次数过多，延长等待时间
        if current_time - self.last_recovery_time < 60:  # 1分钟内
            self.recovery_attempts += 1
        else:
            self.recovery_attempts = 1
            
        # 记录本次恢复尝试时间
        self.last_recovery_time = current_time
        
        # 如果尝试次数超过上限，暂停恢复
        if self.recovery_attempts > self.max_recovery_attempts:
            logging.error(f"服务恢复尝试次数过多({self.recovery_attempts})，暂停自动恢复，请人工干预")
            return
            
        # 根据不同的原因采取不同的恢复策略
        if "共享内存不可访问" in reasons:
            # 尝试重新初始化共享内存
            logging.info("尝试重新初始化共享内存")
            try:
                self.service_control.mem_reader = SharedMemoryReader()
            except:
                pass
        
        if "服务进程不在运行" in reasons or "服务心跳异常" in reasons:
            # 尝试重启服务
            logging.info("尝试重启服务")
            self.service_control.restart(force=True)


# 启动网关服务的脚本示例
def start_gateway_service():
    """启动交易网关服务"""
    try:
        # 检查配置文件是否存在
        config_path = os.path.join(os.path.dirname(__file__), "gateway_config.json")
        if not os.path.exists(config_path):
            logging.info(f"配置文件不存在: {config_path}")
            logging.info("正在创建默认配置文件...")
            
            # 创建默认配置
            default_config = {
                "client_path": "D:\\qmt\\userdata_mini",  # 默认路径，需要用户修改
                "account_id": "您的资金账号",  # 默认账号，需要用户修改
                "max_reconnect": 3,
                "heartbeat_interval": 3,
                "health_check_interval": 60
            }
            
            # 写入默认配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=4, ensure_ascii=False)
            
            logging.info(f"默认配置文件已创建: {config_path}")
            logging.info("请编辑配置文件，填入正确的客户端路径和资金账号后再启动服务")
            return
        
        # 检查是否已有服务在运行
        with SharedMemoryReader() as reader:  # 使用上下文管理器确保资源释放
            if reader.is_initialized and reader.is_gateway_running():
                logging.info("检测到交易网关服务已在运行")
                return
        
        # 初始化服务实例
        gateway = TradingGatewayService()
        
        # 检查共享内存初始化状态
        if not gateway.shmem.is_initialized:
            logging.info("共享内存初始化失败，服务无法启动")
            gateway._cleanup_resources()  # 确保释放资源
            return
    
        # 启动服务
        gateway.start()
        
        # 检查是否已连接，若未连接则尝试连接
        try:
            if not gateway.connected:
                client_path = gateway.config.get("client_path")
                account_id = gateway.config.get("account_id")
                if client_path and account_id:
                    gateway.logger.info("尝试自动连接交易账户...")
                    if gateway._connect(client_path, account_id):
                        gateway.logger.info("交易账户连接成功")
                    else:
                        gateway.logger.error("交易账户连接失败")
                else:
                    gateway.logger.error("配置文件中缺少客户端路径或账户ID")
            
            gateway.logger.info("交易网关服务已启动，按Ctrl+C终止...")
            gateway.run_forever()
        except KeyboardInterrupt:
            gateway.logger.info("收到键盘中断，服务正在关闭...")
            gateway._shutdown()
        except Exception as e:
            gateway.logger.error(f"服务运行异常: {str(e)}")
            gateway.logger.error(traceback.format_exc())
            gateway._shutdown()
    except Exception as e:
        logging.info(f"启动服务时发生错误: {str(e)}")
        if 'traceback' in sys.modules:
            try:
                traceback.format_exc()
            except:
                logging.info(f"详细错误信息: {traceback.format_exc()}")
        else:
            logging.info("无法显示详细错误信息: traceback模块未导入")

if __name__ == "__main__":
    start_gateway_service()