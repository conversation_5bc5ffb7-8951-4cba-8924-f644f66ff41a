import time
import talib as ta
import numpy as np 
import numba as nb
from collections import deque

g_params['信号合约'] = 'CFFEX|Z|IC|MAIN'     # 信号计算的合约品种
g_params['基础时间'] = 'M'   # k线基础时间
g_params['基础快周期'] =  1  # 基础快周期
g_params['基础慢周期'] =  3  # 基础慢周期
g_params['布林通道周期'] =  26  # 布林通道周期

g_params['近带通道宽度比例'] = 0.2# 均价线近带通道宽度比例
g_params['中带通道宽度比例'] = 0.6# 均价线中带通道宽度比例
g_params['远带通道宽度比例'] = 0.8# 均价线远带通道宽度比例

g_params['开盘切换盘中计数'] = 5# 开盘切换盘中计数  
g_params['开盘行情斜率阈值'] = 60# 开盘行情斜率阈值
g_params['开盘行情加速度阈值'] = 3# 开盘行情加速度阈值
g_params['开盘行情利润回撤平仓阈值'] = 0# 开盘行情利润回撤平仓阈值

g_params['盘中行情斜率阈值'] = 3# 盘中行情斜率阈值
g_params['盘中行情加速度阈值'] = 3# 盘中行情加速度阈值
g_params['盘中行情利润回撤平仓阈值'] = 0# 盘中行情利润回撤平仓阈值

g_params['V反形态取样K线数'] = 3# V反形态取样K线数
g_params['V反形态左侧斜率阈值'] = 3# V反形态左侧斜率阈值
g_params['V反形态左侧加速度阈值'] = 3# V反形态左侧加速度阈值
g_params['V反形态右侧斜率阈值'] = 3# V反形态右侧斜率阈值
g_params['V反形态右侧加速度阈值'] = 3# V反形态右侧加速度阈值

g_params['交易方向开关']  = 0  #大于0开多，小于0开空，等于0双向开仓
g_params['下单手数']  = 1  #Lots
g_params['超价跳数']  = 2  #ovprice_tick
g_params['收盘倒计时分钟数设置'] = 1#距离收盘时间范围不开仓，并清仓    
g_params['订阅数据长度']  =  20000  #订阅数据长度

# 日内成交量加权平均价类
class DailyVWAP:
    def __init__(self, channel_width_ratios=None):
        # 初始化状态变量
        self.bars_pos = 0           # 日内累计K线计数
        self.status_date = 0        # 当前日期
        self.vol = 0.0              # 日内累计成交量
        self.money = 0.0            # 日内累计成交额
        self.high = 0.0             # 日内最高价
        self.low = 0.0              # 日内最低价
        self.vwap = 0.0             # 日内成交量加权平均价
        self.vwap_queue = deque([0.0], maxlen=30)  # 均价队列，用于计算角度
        
        # 通道宽度比例
        self.channel_width_ratios = channel_width_ratios or {
            'near': 0.2,   # 近带通道宽度比例
            'middle': 0.6, # 中带通道宽度比例
            'far': 0.8     # 远带通道宽度比例
        }
    
    def update(self, date, volume, price, high=None, low=None):
        """更新日内VWAP
        
        Args:
            date: 当前日期
            volume: 当前K线成交量
            price: 当前K线均价
            high: 当前K线最高价，可选
            low: 当前K线最低价，可选
        
        Returns:
            bool: 是否为新的一天
        """
        is_new_day = False
        
        if date == self.status_date:
            # 同一天，累加数据
            self.bars_pos += 1
            self.vol += volume
            self.money += volume * price
            
            # 更新日内最高最低价
            if high is not None and low is not None:
                self.high = max(self.high, high)
                self.low = min(self.low, low)
        else:
            # 新的一天，重置数据
            self.status_date = date
            self.bars_pos = 1
            self.vol = volume
            self.money = volume * price
            
            if high is not None and low is not None:
                self.high = high
                self.low = low
                
            is_new_day = True
            
        # 计算VWAP
        self.vwap = self.money / self.vol if self.vol > 0 else price
        self.vwap_queue.append(self.vwap)
        
        return is_new_day
    
    def get_channels(self, price):
        """计算VWAP通道
        
        Args:
            price: 用于计算通道宽度的价格
            
        Returns:
            dict: 包含各通道上下限
        """
        channels = {}
        
        for name, ratio in self.channel_width_ratios.items():
            width = ratio / 100 * price
            channels[f'{name}_upper'] = self.vwap + width
            channels[f'{name}_lower'] = self.vwap - width
            
        return channels
    
    def calculate_slope_angle(self, interval=1, samples=None):
        """计算VWAP的斜率和角度
        
        Args:
            interval: 时间间隔
            samples: 使用的样本数，默认使用所有可用数据
            
        Returns:
            tuple: (斜率, 角度)的元组，分别对应不同的样本长度
        """
        results = []
        
        # 如果未指定samples，使用2-4的样本
        if samples is None:
            samples = [2, 3, 4]
            
        for n in samples:
            if len(self.vwap_queue) >= n:
                # 转换为numpy数组进行计算
                price_array = np.array(list(self.vwap_queue)[-n:])
                x = np.arange(len(price_array)) * interval
                slope, _ = np.polyfit(x, price_array, 1)
                angle = np.degrees(np.arctan(slope))
                results.append((slope, angle))
            else:
                results.append((0, 0))
                
        return results

# 通用工具函数
@nb.njit(cache=True) 
def REF(x,m):#前向回溯函数
    l=len(x)
    if l>m:
        if m>0:
            return np.hstack((np.full(m,x[0]),x[:-m])) 
        else:return x    
    else:return np.full(l,x[0])  

def calculate_slope_angle(price, interval=1):
    x = np.arange(len(price)) * interval  # 计算时间间隔
    slope, _ = np.polyfit(x, price, 1)  # 线性拟合计算斜率
    angle = np.degrees(np.arctan(slope))  # 计算斜率对应的角度
    return slope, angle

def HHV(x,m):
    if m>1:
        return ta.MAX(x,m)
    else:return x

def LLV(x,m):
    if m>1:
        return ta.MIN(x,m)
    else:return x

def MA(x,m):
    if m>1:
        return ta.MA(x,m)
    else:return x

def SUM(x,m):
    if m>1:
        return ta.SUM(x,m)
    else:return x

def STD(x,m):
    if m>1:
        return ta.STDDEV(x,m)
    else:return x

scode,K_btime,K_FastCycle,K_SlowCycle,K_BOLLCycle='',0,0,0,0
近带通道宽度比例,中带通道宽度比例,远带通道宽度比例=0.0,0.0,0.0
开盘切换盘中计数,开盘行情斜率阈值,开盘行情加速度阈值,开盘行情利润回撤平仓阈值=0,0,0,0
盘中行情斜率阈值,盘中行情加速度阈值,盘中行情利润回撤平仓阈值=0,0,0
V反形态取样K线数,V反形态左侧斜率阈值,V反形态左侧加速度阈值,V反形态右侧斜率阈值,V反形态右侧加速度阈值=0,0,0,0,0
TradeSW,Lots,ovprice_tick,CloseTime,sublength=0,0,0,0,0
def initialize(context): 
    global g_params,scode,K_btime,K_FastCycle,K_SlowCycle,K_BOLLCycle
    global 近带通道宽度比例,中带通道宽度比例,远带通道宽度比例
    global 开盘切换盘中计数,开盘行情斜率阈值,开盘行情加速度阈值,开盘行情利润回撤平仓阈值
    global 盘中行情斜率阈值,盘中行情加速度阈值,盘中行情利润回撤平仓阈值
    global V反形态取样K线数,V反形态左侧斜率阈值,V反形态左侧加速度阈值,V反形态右侧斜率阈值,V反形态右侧加速度阈值
    global TradeSW,Lots,ovprice_tick,CloseTime,sublength
    global fast_vwap, slow_vwap

    scode   = g_params['信号合约']    # 
    K_btime = g_params['基础时间'] # 
    K_FastCycle = g_params['基础快周期'] # 
    K_SlowCycle = g_params['基础慢周期'] #
    K_BOLLCycle = g_params['布林通道周期']

    近带通道宽度比例 = g_params['近带通道宽度比例']
    中带通道宽度比例 = g_params['中带通道宽度比例']
    远带通道宽度比例 = g_params['远带通道宽度比例']
    
    # 设置VWAP计算器的通道宽度比例
    channel_ratios = {
        'near': 近带通道宽度比例,
        'middle': 中带通道宽度比例,
        'far': 远带通道宽度比例
    }
    fast_vwap = DailyVWAP(channel_ratios)
    slow_vwap = DailyVWAP(channel_ratios)

    开盘切换盘中计数 = g_params['开盘切换盘中计数']
    开盘行情斜率阈值 = g_params['开盘行情斜率阈值']
    开盘行情加速度阈值 = g_params['开盘行情加速度阈值']
    开盘行情利润回撤平仓阈值 = g_params['开盘行情利润回撤平仓阈值']

    盘中行情斜率阈值 = g_params['盘中行情斜率阈值']
    盘中行情加速度阈值 = g_params['盘中行情加速度阈值']
    盘中行情利润回撤平仓阈值 = g_params['盘中行情利润回撤平仓阈值']

    V反形态取样K线数 = g_params['V反形态取样K线数']
    V反形态左侧斜率阈值 = g_params['V反形态左侧斜率阈值']
    V反形态左侧加速度阈值 = g_params['V反形态左侧加速度阈值']
    V反形态右侧斜率阈值 = g_params['V反形态右侧斜率阈值']
    V反形态右侧加速度阈值 = g_params['V反形态右侧加速度阈值']

    TradeSW = g_params['交易方向开关']
    Lots = g_params['下单手数']
    ovprice_tick = g_params['超价跳数']
    CloseTime = g_params['收盘倒计时分钟数设置']
    sublength = g_params['订阅数据长度']
    AL=2000 if sublength>2000 else sublength#计算回溯数据长度      
    SetBarInterval(scode, K_btime, K_FastCycle,sublength,AL) #订阅信号合约    
    SetBarInterval(scode, K_btime, K_SlowCycle,sublength,AL, isTrigger=True) #订阅信号合约
    #SetBarInterval(tcode,'T', 1,1) #订阅交易合约

    SetActual()           #设置实盘运行
    SetOrderWay(1)        #设置K线走完后发单
    SetTriggerType(1)     #设置即时行情触发
    SetTriggerType(5)     #设置K线触发

# 策略触发事件每次触发时都会执行该函数
DLS=g_params['订阅数据长度']
TradeStatus=0
# 保留原有全局变量，兼容已有代码
DayBarsPos,DayStatus,DayVol,DayMoney,DayHigh,DayLow=0,0,0.0,0.0,0.0,0.0
mDayBarsPos,mDayStatus,mDayVol,mDayMoney,mDayHigh,mDayLow=0,0,0.0,0.0,0.0,0.0
TDS,BKS,SKS,BPS,SPS,HH1,LL1,JJX,mJJX=0,0,0,0,0,0,999999999,0.0,0.0
JJXQ,mJJXQ=deque([0.0],maxlen=30),deque([0.0],maxlen=30)
angleQ,angle2Q,angle3Q=deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3)
HCS,sBARS,mBARS,buys,sells=deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3)
def handle_data(context):
    global TradeStatus
    global DayBarsPos,DayStatus,DayVol,DayMoney,DayHigh,DayLow
    global mDayBarsPos,mDayStatus,mDayVol,mDayMoney,mDayHigh,mDayLow
    global TDS,BKS,SKS,BPS,SPS,HH1,LL1,JJX,mJJX
    global fast_vwap, slow_vwap

    O =Open(scode, K_btime,K_FastCycle)   # 信号合约收盘价格
    H =High(scode, K_btime,K_FastCycle)   # 信号合约最高价
    L =Low(scode, K_btime,K_FastCycle)    # 信号合约最低价
    C =Close(scode,K_btime, K_FastCycle)  # 信号合约收盘价格
    D =Date(scode,K_btime, K_FastCycle)   # 信号合约日期
    V =Vol(scode,K_btime, K_FastCycle)    # 信号合约成交量
    CS=Close(scode,K_btime, K_SlowCycle)
    VS=Vol(scode,K_btime, K_SlowCycle)
    AVPrice=Average(scode,K_btime,K_FastCycle)
    mAVPrice=Average(scode,K_btime,K_SlowCycle)
    MINPRICE=PriceTick(scode)           # 交易合约最小变动价

    HTS=1 if context.strategyStatus()=="C" else 0  
    s_CurrentBar=CurrentBar(scode, K_btime,K_FastCycle)    
    sBARS.append(s_CurrentBar)

    if sBARS[0]>0 and sBARS[1]<sBARS[2]:
        BKS=0
        SKS=0
        BPS=0
        SPS=0 
        
        # 使用VWAP类更新快速VWAP
        is_new_day = fast_vwap.update(D, V[-1], AVPrice[-1], H[-1], L[-1])
        # 同步更新全局变量，保持兼容性
        DayStatus = fast_vwap.status_date
        DayBarsPos = fast_vwap.bars_pos
        DayVol = fast_vwap.vol
        DayMoney = fast_vwap.money
        DayHigh = fast_vwap.high
        DayLow = fast_vwap.low
        JJX = fast_vwap.vwap
        
        # 如果是新的一天，重置交易状态
        if is_new_day:
            TradeStatus = 0
            mDayBarsPos = 1  # 重置慢速计数器
            
        JJXQ.append(JJX)

    m_CurrentBar=CurrentBar(scode, K_btime,K_SlowCycle)    
    mBARS.append(m_CurrentBar)
    if mBARS[0]>0 and mBARS[1]<mBARS[2]:
        BKS=0
        SKS=0
        BPS=0
        SPS=0 
        
        # 使用VWAP类更新慢速VWAP
        is_new_day = slow_vwap.update(D, VS[-1], mAVPrice[-1])
        # 同步更新全局变量，保持兼容性
        mDayStatus = slow_vwap.status_date
        mDayBarsPos = slow_vwap.bars_pos
        mDayVol = slow_vwap.vol
        mDayMoney = slow_vwap.money
        mJJX = slow_vwap.vwap
        
        # 如果是新的一天，重置交易状态
        if is_new_day:
            TradeStatus = 0
            
        mJJXQ.append(mJJX)

    if len(C)<5:
        return        
    # 计算通道
    channels = fast_vwap.get_channels(AVPrice[-1])
    JDS = channels['near_upper']
    JDX = channels['near_lower']
    JZS = channels['middle_upper']
    JZX = channels['middle_lower']
    JYS = channels['far_upper']
    JYX = channels['far_lower']

    PlotNumeric('均价',JJX,0xffff00,True,False)
    PlotNumeric('近带通道上',JDS,0x0000ff,True,False)
    PlotNumeric('近带通道下',JDX,0x0000ff,True,False)
    # PlotNumeric('中带通道上',JZS,0x00ff00,True,False)
    # PlotNumeric('中带通道下',JZX,0x00ff00,True,False)
    # PlotNumeric('远带通道上',JYS,0xff0000,True,False)
    # PlotNumeric('远带通道下',JYX,0xff0000,True,False)


    BOLLStd=STD(CS,K_BOLLCycle)
    BOOLMid=MA(CS,K_BOLLCycle)
    BOOLUp=BOOLMid+2*BOLLStd
    BOOLDown=BOOLMid-2*BOLLStd

    PlotNumeric('布林通道中',BOOLMid[-1],0xffffff,True,False)
    PlotNumeric('布林通道上',BOOLUp[-1],0x00ff00,True,False)
    PlotNumeric('布林通道下',BOOLDown[-1],0xff0000,True,False)

    # PlotNumeric('日内高点',DayHigh,0xff0000,True,False)
    # PlotNumeric('日内低点',DayLow,0x00ff00,True,False)

    # if C[-1]>JJX>DayLow:
    #     for i in (0.5,0.618,1.0,1.382,1.618,2.0):
    #         ClosePrice=JJX+(JJX-DayLow)*i
    #         PlotNumeric('多单'+str(i)+'倍止盈线',ClosePrice,0xff0000,True,False)
    # if C[-1]<JJX<DayHigh:
    #     for i in (0.5,0.618,1.0,1.382,1.618,2.0):
    #         ClosePrice=JJX-(DayHigh-JJX)*i
    #         PlotNumeric('空单'+str(i)+'倍止盈线',ClosePrice,0x00ff00,True,False)
                
    
    # 计算斜率，使用VWAP类的方法
    NPJJXQ=np.array(JJXQ)
    slopes_angles = fast_vwap.calculate_slope_angle(samples=[2, 3, 4])
    slope, angle = slopes_angles[0]
    slope2, angle2 = slopes_angles[1]
    slope3, angle3 = slopes_angles[2]
    
    if sBARS[0]>0 and sBARS[1]<sBARS[2]:
        angleQ.append(angle)
        angle2Q.append(angle2)
        angle3Q.append(angle3)
    
    # PlotNumeric('最新K线斜率', slope, 0x00ffff, False, False,0,"斜率JJX")
    # PlotNumeric('前2K线斜率', slope2, 0xff0000, False, False,0,"斜率JJX")
    # PlotNumeric('前3K线斜率', slope3, 0x00ff00, False, False,0,"斜率JJX")
    # PlotNumeric('斜率零轴', 0, 0xffffff, False, False,0,"斜率JJX")
    # PlotNumeric('最新K线角度', angle, 0x00ffff, False, False,0,"角度JJX")
    PlotNumeric('前2K线角度', angle2, 0xff0000, False, False,0,"角度JJX")
    PlotNumeric('前3K线角度', angle3, 0x00ff00, False, False,0,"角度JJX")
    PlotNumeric('角度零轴', 0, 0xffffff, False, False,0,"角度JJX")


    # AVPslope,AVPangle=calculate_slope_angle(AVPrice[-2:])
    # AVPslope2,AVPangle2=calculate_slope_angle(AVPrice[-3:])
    # AVPslope3,AVPangle3=calculate_slope_angle(AVPrice[-4:])

    # PlotNumeric('均价最新K线斜率', AVPslope, 0x00ffff, False, False,0,"斜率AVP")
    # PlotNumeric('均价前2K线斜率', AVPslope2, 0xff0000, False, False,0,"斜率AVP")
    # PlotNumeric('均价前3K线斜率', AVPslope3, 0x00ff00, False, False,0,"斜率AVP")
    # PlotNumeric('均价斜率零轴', 0, 0xffffff, False, False,0,"斜率AVP")
    # PlotNumeric('均价最新K线角度', AVPangle, 0x00ffff, False, False,0,"角度AVP")
    # PlotNumeric('均价前2K线角度', AVPangle2, 0xff0000, False, False,0,"角度AVP")
    # PlotNumeric('均价前3K线角度', AVPangle3, 0x00ff00, False, False,0,"角度AVP")
    # PlotNumeric('均价角度零轴', 0, 0xffffff, False, False,0,"角度AVP")

    if mDayBarsPos>开盘切换盘中计数:
        TradeStatus=1
    _VTS=VTS(Time())
    TradeEnbTime=TimeTo_Minutes(_VTS[3])-TimeTo_Minutes(_VTS[2])>CloseTime
    TradeOffTime=CloseTime>=TimeTo_Minutes(_VTS[3])-TimeTo_Minutes(_VTS[2])>0

    # LogInfo(D,Time(),context.kLineSlice(),TradeStatus,DayBarsPos,mDayBarsPos,Q_Last(),Q_UpdateTime())

    speed2=(C[-1]/O[-2]-1)*100
    speed3=(C[-1]/O[-3]-1)*100
    PlotBar('3K动量速率',speed3,0,0XFF0000 if speed3>0 else 0X00FF00,False,True,0,"动量速率")
    PlotNumeric('3K动量零轴',0,0xffffff,False,False,0,"动量速率")

    EveryUP3=C[-1]>O[-1] and C[-2]>O[-2] and C[-3]>O[-3] and C[-1]>C[-2] and C[-2]>C[-3]
    EveryDown3=C[-1]<O[-1] and C[-2]<O[-2] and C[-3]<O[-3] and C[-1]<C[-2] and C[-2]<C[-3]
    BKS1=TradeStatus==0 and DayBarsPos>=3 and angle3Q[2]> 开盘行情斜率阈值 and angle3Q[2]>angle3Q[1] and EveryUP3
    SKS1=TradeStatus==0 and DayBarsPos>=3 and angle3Q[2]<-开盘行情斜率阈值 and angle3Q[2]<angle3Q[1] and EveryDown3
    DayMID=DayLow+DayHigh/2
    SPS1=C[-1]<JJX and C[-1]<DayMID
    BPS1=C[-1]>JJX and C[-1]>DayMID


    BK1=TradeEnbTime and BKS1
    SK1=TradeEnbTime and SKS1
    BP1=BPS1 or TradeOffTime
    SP1=SPS1 or TradeOffTime
    if HTS==0:
        LogInfo(D,Time(),'历史回测阶段')                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               
        if  BK1 and BuyPosition() == 0:
            Buy(Lots,  C[-1]) 
        elif SK1 and SellPosition() == 0:
            SellShort(Lots, C[-1])
        if  BP1 and SellPosition() > 0:
            BuyToCover(Lots,  C[-1]) 
        elif SP1 and BuyPosition() > 0:
            Sell(Lots, C[-1])


def floattime_sum(floatin1, floatin2, len_set=12):  # 高精度浮点时间求和（精确到毫秒）
    # 设置浮点数格式，保留len_set位小数
    lensave = f"%0.{len_set}f"
    
    # 格式化浮点数并提取各时间部分
    def extract_time_parts(floatin):
        strfloat = lensave % floatin
        return int(strfloat[2:4]), int(strfloat[4:6]), int(strfloat[6:8]), int(strfloat[8:11])
    
    h1, m1, s1, ms1 = extract_time_parts(floatin1)
    h2, m2, s2, ms2 = extract_time_parts(floatin2)
    
    # 计算总和并处理进位
    total_ms = ms1 + ms2
    ms_carry = total_ms // 1000
    new_ms = total_ms % 1000
    
    total_s = s1 + s2 + ms_carry
    s_carry = total_s // 60
    new_s = total_s % 60
    
    total_m = m1 + m2 + s_carry
    m_carry = total_m // 60
    new_m = total_m % 60
    
    new_h = h1 + h2 + m_carry
    new_h = min(new_h, 99)  # 限制小时数不超过99
    
    # 组合新的浮点时间字符串并转换回浮点数
    new_str_time = f"0.{new_h:02}{new_m:02}{new_s:02}{new_ms:03}"
    return float(new_str_time)

def TimeTo_Minutes(time_in):
    timestr='%0.6f'%time_in
    hsave=int(timestr[2:4])
    msave=int(timestr[4:6])
    tcout=hsave*60+msave
    return tcout

def SessionOpenTime(contractId=''):  # 获取交易时段开盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler:continue
        tlout.append(GetSessionStartTime(contractId, i))  # 获取每个交易时段的开盘时间并加入列表
    return tlout

def SessionCloseTime(contractId=''):  # 获取交易时段收盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler-1:continue
        tlout.append(GetSessionEndTime(contractId, i))  # 获取每个交易时段的收盘时间并加入列表
    return tlout

def VTS(time_in, contractId=''):  # 根据输入时间和合约ID计算交易时段
    RTS, CTS, TSession = [], [], []  # 初始化三个列表，用于存储修正后的时间、收盘时间和交易时段
    opentimet = SessionOpenTime(contractId)  # 获取所有交易时段的开盘时间
    Closetimet = SessionCloseTime(contractId)  # 获取所有交易时段的收盘时间
    
    for open_time, close_time in zip(opentimet, Closetimet):
        if time_in > open_time:  # 判断输入时间是否在开盘时间之后
            RTS.append(time_in)  # 如果是，加入RTS列表
        else:
            RTS.append(floattime_sum(time_in, 0.24))  # 如果不是，修正时间后加入RTS列表
        
        if close_time > open_time:  # 判断收盘时间是否在开盘时间之后
            CTS.append(close_time)  # 如果是，加入CTS列表
        else:
            CTS.append(floattime_sum(close_time, 0.24))  # 如果不是，修正时间后加入CTS列表
        
        if open_time < RTS[-1] < CTS[-1]:  # 判断修正后的时间是否在交易时段内
            TSession.append(len(RTS) - 1)  # 如果是，加入TSession列表

    if len(TSession) == 1:  # 如果只有一个交易时段
        idx = TSession[0]
        return idx, opentimet[idx], RTS[idx], CTS[idx]  # 返回交易时段和相关时间
    else:
        return -1, time_in, time_in, time_in  # 否则返回默认值
def tim_trigger(BK,SK,qty,itk,tcode):#盘中实时开仓
    global BKS,SKS
    if BK and BKS==0 and (A_BuyPosition(tcode) == 0) :
        iprc = min(Q_AskPrice(tcode) +itk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        A_SendOrder(Enum_Buy(), Enum_Entry(), qty, iprc,tcode) 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单买入开仓价==>",iprc,"买入数量==>",qty)
        BKS=1    
    elif SK and SKS==0 and (A_SellPosition(tcode) == 0):    
        iprc = max(Q_BidPrice(tcode) - itk*PriceTick(tcode), Q_LowLimit(tcode))  # 对盘超价                         
        A_SendOrder(Enum_Sell(), Enum_Entry(), qty, iprc,tcode)   
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单卖出开仓价==>",iprc,"卖出数量==>",qty)
        SKS=1    

def tim_trigger_Exit(BP,SP,otk,tcode,clots):#盘中实时平仓
    global BKS,SKS,BPS,SPS
    if BP and BPS==0 and A_SellPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_SellPosition(tcode))
        prc = min(Q_AskPrice(tcode) +otk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        if ExchangeName(tcode) not in ['SHFE','INE']:    
            retExit, ExitOrderId=A_SendOrder(Enum_Buy(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodaySellPosition(tcode)
            dlots=lots-tlots            
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),lots, prc,tcode) #今仓足够平仓,上期所能交所优先超价全部平今仓    
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Buy(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Buy(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单买入平仓价==>",prc,"买入平仓数量==>",_lots)
        BPS=1  
        if SKS==1:SKS=2      
    elif SP and SPS==0 and A_BuyPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_BuyPosition(tcode))
        prc = max(Q_BidPrice(tcode) - otk*PriceTick(tcode), Q_LowLimit(tcode))
        if ExchangeName(tcode) not in ['SHFE','INE']:
            retExit, ExitOrderId=A_SendOrder(Enum_Sell(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodayBuyPosition(tcode)
            dlots=lots-tlots
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),lots, prc,tcode)   #今仓足够平仓,上期所能交所优先超价全部平今仓  
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Sell(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Sell(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单卖出平仓价==>",prc,"卖出平仓数量==>",_lots)
        SPS=1    
        if BKS==1:BKS=2   