import copy
import sys
import numpy as np


def ListToNumpy(lst):
    if lst == None:
        return np.array([])
    else:
        return np.array(lst)

def FormatArgs(args):
    if len(args) == 0:
        return None

    argStr = ""
    for arg in args:
        if isinstance(arg, str):
            argStr = argStr + ' ' + arg
        else:
            argStr = argStr + ' ' + str(arg)
    return argStr

def QuoteArgs(args):
    if len(args) == 0:
        return None

    argQte = []
    for arg in args:
        if isinstance(arg, str):
            argQte.append(arg)
    return argQte

class StrategyContext:
    def __init__(self):
        self._strategyStatus = None
        self._triggerType = None
        self._conTractNo = None
        self._kLineType = None
        self._kLineSlice = None
        self._tradeDate = None
        self._dateTimeStamp = None
        self._triggerData = None

    def strategyStatus(self):
        return self._strategyStatus

    def triggerType(self):
        return self._triggerType

    def contractNo(self):
        return self._conTractNo

    def kLineType(self):
        if self._kLineType == '\0':
            return str(None);
        return self._kLineType

    def kLineSlice(self):
        if not self._kLineSlice:
            return str(None)
        return self._kLineSlice

    def tradeDate(self):
        if not self._tradeDate:
            return str(None)
        else:
            return self._tradeDate

    def dateTimeStamp(self):
        if not self._dateTimeStamp:
            return str(None)
        else:
            return self._dateTimeStamp

    def triggerData(self):
        if not self._triggerData:
            return str(None)
        else:
            return self._triggerData

    def setCurTriggerSourceInfo(self, args):
        self._strategyStatus = copy.deepcopy(args["StrategyState"])
        self._triggerType = copy.deepcopy(args["TriggerWay"])
        self._conTractNo = copy.deepcopy(args["sContractNo"])
        self._kLineType = copy.deepcopy(args["cKlineType"])
        self._kLineSlice = copy.deepcopy(args["uKlineSlice"])
        self._tradeDate = copy.deepcopy(args["TradeDate"])
        self._dateTimeStamp = copy.deepcopy(args["DateTimeStamp"])
        self._triggerData = copy.deepcopy(args["triggerData"])