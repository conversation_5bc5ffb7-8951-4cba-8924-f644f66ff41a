import re
import numpy as np
from talib import MA
from collections import deque

# 策略参数
g_params['K线基础时间']  = 'M'     # k线基础时间
g_params['K线基础周期']  = 1       # k线周期
g_params['回测阶段预警'] = '开'    # 开为开启历史回测阶段预警信息，否则为关
g_params['订阅数据长度']  =  2000  #订阅数据长度
# 音频文件路径配置
g_params['预警音频文件夹路径'] = "D:\\Quant000150v9.5\\Quant\\Strategy\\用户策略"
g_params['预警上突破音频文件'] = "预警上突破.wav"
g_params['预警下突破音频文件'] = "预警下突破.wav"

# 套利合约配置参数
g_params['套利组合'] = "DCE|F|P|2507+DCE|F|P|2506"  # 套利组合公式
g_params['均线一'] = 20           # 均线一周期
g_params['CDP指标参数'] = 3       # CDP指标参数  
# g_params['初预警上突破点数'] = 5   # 初预警上突破点数
# g_params['初预警下突破点数'] = 5   # 初预警下突破点数
g_params['正式预警上突破点数'] = 10 # 正式预警上突破点数
g_params['正式预警下突破点数'] = 10 # 正式预警下突破点数

# 邮件配置参数
g_params['邮件发送'] = '关'        # 开为启用邮件发送，关为不发送
g_params['信息发送邮箱'] = '<EMAIL>'
g_params['邮箱SMTP地址'] = 'smtp.qq.com'
g_params['邮箱端口'] = 465
g_params['邮箱用户名'] = '<EMAIL>'
g_params['邮箱授权码'] = 'anmvczspnxonbheg'
g_params['信息接收邮箱'] = '<EMAIL>'



# 单个组合的状态变量
k_btime, k_cycle, BackTestWarning, SubDataLength, up_wav_path, down_wav_path = 0, 0, None, None, None, None
symbol_Id,均线一,CDP指标参数,初预警上突破点数,初预警下突破点数,正式预警上突破点数,正式预警下突破点数=None, None, None, None, None, None, None
邮件发送,信息发送邮箱,邮箱SMTP地址,邮箱端口,邮箱用户名,邮箱授权码,信息接收邮箱 = None, None, None, None, None, None, None

symbol_d, _CurrentBar, price_list = None, None, None
high_list, low_list, open_list = None, None, None
QMA1,sAH,sAL,QAH,QAL,sNH,sNL= None,None, None, None, None, None, None
all_contracts, parsed_formula, contract_prices, last_arb_price = None, None, None, None

CONTRACT_PATTERN = re.compile(r'(?:ZCE\|[A-Z]\|[A-Z]+\|\d{3}|(?:DCE|SHFE|INE|CFFEX|GFEX)\|[A-Z]\|[A-Z]+\|\d{4})')
# 全局变量存储邮件配置
EMAIL_CONFIG = {
    "sender": None,
    "receivers": None,
    "smtp_server": None,
    "smtp_port": None,
    "username": None,
    "password": None,
    "enabled": False
}

def initialize(context): 
    global g_params, k_btime, k_cycle, BackTestWarning, SubDataLength, up_wav_path, down_wav_path
    global symbol_Id,均线一,CDP指标参数,初预警上突破点数,初预警下突破点数,正式预警上突破点数,正式预警下突破点数
    global 邮件发送,信息发送邮箱,邮箱SMTP地址,邮箱端口,邮箱用户名,邮箱授权码,信息接收邮箱

    global symbol_d, _CurrentBar, price_list, high_list, low_list, open_list
    global QMA1,sAH,sAL,QAH,QAL,sNH,sNL
    global all_contracts, parsed_formula, contract_prices, last_arb_price
    
    k_btime = g_params['K线基础时间'] # k线基础时间取参数
    k_cycle = g_params['K线基础周期'] # k线基础周期取参数
    BackTestWarning = g_params['回测阶段预警'] # 回测阶段预警
    SubDataLength = g_params['订阅数据长度']  # 订阅数据长度
    AluSubDataLength = min(2000, SubDataLength)  # 计算数据长度  
    up_wav_path = g_params['预警音频文件夹路径'] + "\\" + g_params['预警上突破音频文件'] 
    down_wav_path = g_params['预警音频文件夹路径'] + "\\" + g_params['预警下突破音频文件'] 

    symbol_Id = g_params['套利组合'] # 套利组合
    均线一 = g_params['均线一'] # 均线一
    CDP指标参数 = g_params['CDP指标参数'] # CDP指标参数
    # 初预警上突破点数 = g_params['初预警上突破点数'] # 初预警上突破点数
    # 初预警下突破点数 = g_params['初预警下突破点数'] # 初预警下突破点数
    正式预警上突破点数 = g_params['正式预警上突破点数'] # 正式预警上突破点数
    正式预警下突破点数 = g_params['正式预警下突破点数'] # 正式预警下突破点数

    邮件发送 = g_params['邮件发送'] # 邮件发送
    信息发送邮箱 = g_params['信息发送邮箱'] # 信息发送邮箱
    邮箱SMTP地址 = g_params['邮箱SMTP地址'] # 邮箱SMTP地址
    邮箱端口 = g_params['邮箱端口'] # 邮箱端口
    邮箱用户名 = g_params['邮箱用户名'] # 邮箱用户名
    邮箱授权码 = g_params['邮箱授权码'] # 邮箱授权码
    信息接收邮箱 = g_params['信息接收邮箱'] # 信息接收邮箱

    # 初始化单个套利组合的数据结构
    QLen_Set=max(均线一,CDP指标参数)
    symbol_d = deque([], maxlen=QLen_Set)
    QMA1 = deque([], maxlen=QLen_Set)
    sAH = deque([], maxlen=QLen_Set)
    sAL = deque([], maxlen=QLen_Set)
    QAH = deque([], maxlen=QLen_Set)
    QAL = deque([], maxlen=QLen_Set)
    sNH = deque([], maxlen=QLen_Set)
    sNL = deque([], maxlen=QLen_Set)
    _CurrentBar = deque([], maxlen=QLen_Set)
    price_list = deque([], maxlen=QLen_Set)
    high_list = deque([], maxlen=QLen_Set)
    low_list = deque([], maxlen=QLen_Set)
    open_list = deque([], maxlen=QLen_Set)
    
    # 解析套利合约并预处理公式
    all_contracts, parsed_formulas = parse_arbitrage_contracts([symbol_Id], CONTRACT_PATTERN)
    parsed_formula = parsed_formulas[0]  # 只有一个公式
    
    LogInfo(f"识别到的所有合约: {all_contracts}")
    display_code = get_first_contract(symbol_Id, CONTRACT_PATTERN)
    LogInfo(f"套利组合 {symbol_Id} 的主合约是 {display_code}")

    SetBarInterval(display_code, k_btime, k_cycle, SubDataLength, AluSubDataLength) #订阅交易合约
    
    # 初始化合约价格字典和套利价格
    contract_prices = {contract: np.nan for contract in all_contracts}
    last_arb_price = np.nan
    
    # 订阅行情
    for contract in all_contracts:
        if contract == display_code:
            LogInfo(f'跳过已订阅的图表显示合约 {contract}')
            continue
        LogInfo(f'遍历订阅合约==> {contract} ')
        SetBarInterval(contract, k_btime, k_cycle, SubDataLength, AluSubDataLength) #订阅交易合约
    
    LogInfo("信息发送邮箱==>", 信息发送邮箱, "邮箱SMTP地址==>", 邮箱SMTP地址, "邮箱端口==>", 邮箱端口, "邮箱用户名==>", 邮箱用户名, "信息接收邮箱==>", 信息接收邮箱)    
    SetTriggerType(1)
    SetTriggerType(5)
    SetOrderWay(1)
    SetActual()     
        
    # 初始化时设置邮件
    setup_email(
        sender=信息发送邮箱,
        receivers=[信息接收邮箱] if isinstance(信息接收邮箱, str) else 信息接收邮箱,
        smtp_server=邮箱SMTP地址,
        smtp_port=int(邮箱端口),
        username=邮箱用户名,
        password=邮箱授权码,
        enabled=邮件发送=="开"
    )


UPStatus, DWStatus = 0, 0
BKStatus, SKStatus, BPStatus, SPStatus = 0, 0, 0, 0
def handle_data(context):
    global UPStatus, DWStatus, last_arb_price
    global sAH, sAL, QAH, QAL, sNH, sNL
    
    HTS = 1 if context.strategyStatus()=="C" else 0
    current_formula = symbol_Id
    contracts_in_formula = CONTRACT_PATTERN.findall(current_formula)
    
    # 获取各合约的OHLC数据
    contract_ohlc_data = {}
    for contract in contracts_in_formula:
        close_arr = Close(contract, k_btime, k_cycle)
        high_arr = High(contract, k_btime, k_cycle)
        low_arr = Low(contract, k_btime, k_cycle)
        open_arr = Open(contract, k_btime, k_cycle)
        
        if len(close_arr) > 0:
            contract_prices[contract] = close_arr[-1]  # 只取最后一个收盘价
            contract_ohlc_data[contract] = {
                'open': open_arr[-1] if len(open_arr) > 0 else np.nan,
                'high': high_arr[-1] if len(high_arr) > 0 else np.nan,
                'low': low_arr[-1] if len(low_arr) > 0 else np.nan,
                'close': close_arr[-1]
            }
    
    # 计算套利组合的OHLC
    arb_ohlc = calculate_arbitrage_ohlc(parsed_formula, contract_ohlc_data)
    arb_price = arb_ohlc['close']
    arb_high = arb_ohlc['high']
    arb_low = arb_ohlc['low']
    arb_open = arb_ohlc['open']
    last_arb_price = arb_price
    
    ALU_code = get_first_contract(symbol_Id, CONTRACT_PATTERN)
    ALU_CurrentBar = CurrentBar(ALU_code, k_btime, k_cycle)
    _CurrentBar.append(ALU_CurrentBar)
    
    if len(_CurrentBar) < 2:
        return
        
    K_EndTrigger = _CurrentBar[0] > 0 and _CurrentBar[-2] < _CurrentBar[-1]
    if K_EndTrigger:
        # 存储套利组合的OHLC数据
        price_list.append(arb_ohlc['close'])
        high_prcie=max(arb_ohlc['high'],arb_ohlc['open'],arb_ohlc['close'])
        high_list.append(high_prcie)
        low_prcie=min(arb_ohlc['low'],arb_ohlc['open'],arb_ohlc['close'])
        low_list.append(low_prcie)
        open_list.append(arb_ohlc['open'])
        
        # 计算均线
        npprice_list = np.array(price_list)
        len1 = int(均线一)
        Qlenset = max(len1, CDP指标参数)
        
        if len(npprice_list) >= len1:
            # 添加检查确保npprice_list不全是NAN值
            if not np.isnan(npprice_list).all():
                QMA1.append(MA(npprice_list, len1)[-1])
            else:
                QMA1.append(np.nan)
        
        # 计算CDP指标
        if len(price_list) >= 2 and len(high_list) >= 2 and len(low_list) >= 2:
            # PT = REF(High(),1) - REF(Low(),1) 前一根K线的振幅
            PT = high_list[-2] - low_list[-2]
            
            # CDP = (REF(High(),1) + REF(Low(),1) + REF(Close(),1))/3 前一根K线的中点价位
            CDP = (high_list[-2] + low_list[-2] + price_list[-2]) / 3
            
            # 计算支撑阻力位
            sAH_value = CDP + PT  # 阻力位1
            sAL_value = CDP - PT  # 支撑位1
            sNH_value = 2 * CDP - low_list[-1]  # 阻力位2 (当前低点)
            sNL_value = 2 * CDP - high_list[-1]  # 支撑位2 (当前高点)
            
            sAH.append(sAH_value)
            sAL.append(sAL_value)
            sNH.append(sNH_value)
            sNL.append(sNL_value)
        if len(sAH) >=CDP指标参数:
            sAH_arr = np.array(sAH)
            sAL_arr = np.array(sAL)
            if not np.isnan(sAH_arr).all():
                QAH.append(MA(sAH_arr, CDP指标参数)[-1])
            else:
                QAH.append(np.nan)
                
            if not np.isnan(sAL_arr).all():
                QAL.append(MA(sAL_arr, CDP指标参数)[-1])
            else:
                QAL.append(np.nan)
                
            # LogInfo(f"CDP指标计算: PT={PT:.2f}, CDP={CDP:.2f}, sAH={sAH_value:.2f}, sAL={sAL_value:.2f}, sNH={sNH_value:.2f}, sNL={sNL_value:.2f}")

    # 预警逻辑和图表显示
    if len(QMA1) > 1 and len(sAH) > 0:    
        Disptxt = '自组合套利合约'
        len1 = int(均线一)
        PlotBar('组合K线', arb_price, arb_open , RGB_Red() if arb_price > arb_open else RGB_Green(), False, True, 0, Disptxt)
        PlotStickLine('组合K线H', arb_high, arb_price if arb_price >= arb_open else arb_open, RGB_Red() if arb_price > arb_open else RGB_Green(), False, False, 0, Disptxt)    
        PlotStickLine('组合K线L', arb_low, arb_price if arb_price <  arb_open else arb_open,  RGB_Red() if arb_price > arb_open else RGB_Green(), False, False, 0, Disptxt)  
        # if arb_price==arb_open :   
        #     idx1 = CurrentBar()  
        #     PlotPartLine('组合K线一字K', idx1, arb_price,1, arb_price, 0xffffff, False, False, 1, Disptxt)
        PlotNumeric(f'均线一({len1})', QMA1[-1], 0xffffff, False, False, 0, Disptxt)
        PlotNumeric('sAH阻力位1', QAH[-1], 0xff0000, False, False, 0, Disptxt)  # 红色
        PlotNumeric('sAL支撑位1', QAL[-1], 0x00ff00, False, False, 0, Disptxt)  # 绿色
        # PlotNumeric('sNH阻力位2', sNH[-1], 0xffffff, False, False, 0, Disptxt)  # 橙色
        # PlotNumeric('sNL支撑位2', sNL[-1], 0xff30ff, False, False, 0, Disptxt)  # 蓝色

    
    if (HTS==1 or BackTestWarning=="开") and len(QMA1)>1 and not np.isnan(QMA1[-1]):
        if UPStatus==0 and arb_price>QAH[-1]+正式预警上突破点数 and price_list[-2]<=QAH[-2]+正式预警上突破点数:
            EmailTitle=f"正式预警自组合套利合约({symbol_Id})价格{arb_price}上突破"
            send_info=f"正式预警在{Date()}日{Time()}分,自组合套利合约({symbol_Id})价格{arb_price}上突破均线一==>{QMA1[-1]}，发送"
            play_wav(up_wav_path)
            send_email(EmailTitle, send_info)
            if HTS ==1:
                LogInfo(send_info,上突破已预警强制退出策略) 
         
        if DWStatus==0 and  arb_price<QAL[-1]-正式预警下突破点数 and price_list[-2]>=QAL[-2]-正式预警下突破点数:
            EmailTitle=f"正式预警自组合套利合约({symbol_Id})价格{arb_price}下突破"
            send_info=f"正式预警在{Date()}日{Time()}分,自组合套利合约({symbol_Id})价格{arb_price}下突破均线一==>{QMA1[-1]}，发送"
            play_wav(down_wav_path)
            send_email(EmailTitle, send_info)
            if HTS ==1:
                LogInfo(send_info,下突破已预警强制退出策略)



def parse_arbitrage_contracts(arbitrage_formulas, contract_pattern=None):
    if contract_pattern is None:
        contract_pattern = CONTRACT_PATTERN
    unique_contracts = set() 
    parsed_formulas = []
    for formula in arbitrage_formulas:
        contracts = contract_pattern.findall(formula)
        for contract in contracts:
            unique_contracts.add(contract)
        parsed_formula = preprocess_formula(formula, contract_pattern)
        parsed_formulas.append(parsed_formula)
    return list(unique_contracts), parsed_formulas

def preprocess_formula(formula, contract_pattern=None):
    if contract_pattern is None:
        contract_pattern = CONTRACT_PATTERN
    # 标准化公式，在操作符两边添加空格以便分割
    formula = re.sub(r'([+\-*/()])', r' \1 ', formula)
    
    # 分割成标记，保留合约代码的完整性
    tokens = []
    i = 0
    formula_len = len(formula)
    
    while i < formula_len:
        # 检查当前位置是否是合约代码的开始
        match = None
        for j in range(i, formula_len):
            possible_contract = formula[i:j+1]
            if contract_pattern.fullmatch(possible_contract):
                match = possible_contract
        
        if match:
            tokens.append(match)
            i += len(match)
        else:
            # 不是合约代码，按空格分割
            next_space = formula.find(' ', i)
            if next_space == -1:
                tokens.append(formula[i:])
                break
            if next_space > i:
                token = formula[i:next_space].strip()
                if token:
                    tokens.append(token)
            i = next_space + 1
    
    # 处理括号优先级，转换为逆波兰表达式
    output_queue = []
    operator_stack = []
    
    precedence = {'+': 1, '-': 1, '*': 2, '/': 2}
    
    for token in tokens:
        if contract_pattern.fullmatch(token):
            # 合约代码
            output_queue.append(('contract', token))
        elif token.isdigit() or (token[0] == '-' and token[1:].isdigit()):
            # 数字
            output_queue.append(('number', float(token)))
        elif token in ['+', '-', '*', '/']:
            # 操作符
            while (operator_stack and operator_stack[-1] != '(' and 
                   precedence.get(operator_stack[-1], 0) >= precedence.get(token, 0)):
                output_queue.append(('op', operator_stack.pop()))
            operator_stack.append(token)
        elif token == '(':
            operator_stack.append(token)
        elif token == ')':
            while operator_stack and operator_stack[-1] != '(':
                output_queue.append(('op', operator_stack.pop()))
            if operator_stack and operator_stack[-1] == '(':
                operator_stack.pop()  # 弹出左括号
    
    # 处理剩余的操作符
    while operator_stack:
        output_queue.append(('op', operator_stack.pop()))
    
    return output_queue

def get_first_contract(arbitrage_formula, contract_pattern=None):
    if contract_pattern is None:
        contract_pattern = CONTRACT_PATTERN
    match = contract_pattern.search(arbitrage_formula)
    if match:
        return match.group(0)
    return None



def calculate_arbitrage_price(parsed_formula, contract_prices):
    """
    使用预处理的公式高效计算套利组合价格
    
    参数:
        parsed_formula: 预处理后的公式结构
        contract_prices: 合约价格字典，键为合约代码，值为价格
    
    返回:
        price: 组合价格
    """
    stack = []
    
    for token_type, token in parsed_formula:
        if token_type == 'contract':
            if token in contract_prices:
                stack.append(contract_prices[token])
            else:
                # 如果找不到价格，使用NaN
                stack.append(np.nan)
        elif token_type == 'number':
            stack.append(token)
        elif token_type == 'op':
            if len(stack) < 2:
                # 处理单目运算符如负号
                if token == '-' and len(stack) == 1:
                    operand = stack.pop()
                    stack.append(-operand)
                continue
                
            right_operand = stack.pop()
            left_operand = stack.pop()
            
            if token == '+':
                stack.append(left_operand + right_operand)
            elif token == '-':
                stack.append(left_operand - right_operand)
            elif token == '*':
                stack.append(left_operand * right_operand)
            elif token == '/':
                # 处理除以零的情况
                if right_operand == 0:
                    stack.append(np.nan)
                else:
                    stack.append(left_operand / right_operand)
    
    return stack[0] if stack else np.nan

def calculate_arbitrage_ohlc(parsed_formula, contract_ohlc_data):
    """
    计算套利组合的开高低收价格
    
    参数:
        parsed_formula: 预处理后的公式结构
        contract_ohlc_data: 合约OHLC数据字典，键为合约代码，值为{'open': x, 'high': x, 'low': x, 'close': x}
    
    返回:
        ohlc: {'open': x, 'high': x, 'low': x, 'close': x} 套利组合的OHLC数据
    """
    def calculate_value(price_type):
        stack = []
        for token_type, token in parsed_formula:
            if token_type == 'contract':
                if token in contract_ohlc_data and price_type in contract_ohlc_data[token]:
                    stack.append(contract_ohlc_data[token][price_type])
                else:
                    stack.append(np.nan)
            elif token_type == 'number':
                stack.append(token)
            elif token_type == 'op':
                if len(stack) < 2:
                    if token == '-' and len(stack) == 1:
                        operand = stack.pop()
                        stack.append(-operand)
                    continue
                    
                right_operand = stack.pop()
                left_operand = stack.pop()
                
                if token == '+':
                    stack.append(left_operand + right_operand)
                elif token == '-':
                    stack.append(left_operand - right_operand)
                elif token == '*':
                    stack.append(left_operand * right_operand)
                elif token == '/':
                    if right_operand == 0:
                        stack.append(np.nan)
                    else:
                        stack.append(left_operand / right_operand)
        
        return stack[0] if stack else np.nan
    
    return {
        'open': calculate_value('open'),
        'high': calculate_value('high'),
        'low': calculate_value('low'),
        'close': calculate_value('close')
    }



def setup_email(sender, receivers, smtp_server, smtp_port, username, password, enabled):
    """
    设置邮件发送参数
    """
    try:
        EMAIL_CONFIG["sender"] = sender
        EMAIL_CONFIG["receivers"] = receivers if isinstance(receivers, list) else [receivers]
        EMAIL_CONFIG["smtp_server"] = smtp_server
        EMAIL_CONFIG["smtp_port"] = smtp_port
        EMAIL_CONFIG["username"] = username
        EMAIL_CONFIG["password"] = password
        EMAIL_CONFIG["enabled"] = enabled
        LogInfo("邮件设置成功")
        return True
    except Exception as e:
        LogInfo(f"邮件设置异常: {str(e)}")
        EMAIL_CONFIG["enabled"] = False
        return False

def send_email(subject, content):
    """
    发送邮件
    """
    if not EMAIL_CONFIG.get("enabled", False):
        LogInfo("邮件功能未启用，无法发送邮件")
        return False
    try:
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        from email.header import Header

        message = MIMEMultipart()
        message['From'] = EMAIL_CONFIG["sender"]
        message['To'] = ','.join(EMAIL_CONFIG["receivers"])
        message['Subject'] = Header(subject, 'utf-8')
        message.attach(MIMEText(content, 'plain', 'utf-8'))

        if EMAIL_CONFIG["smtp_port"] == 465:
            smtp = smtplib.SMTP_SSL(EMAIL_CONFIG["smtp_server"], EMAIL_CONFIG["smtp_port"])
        else:
            smtp = smtplib.SMTP(EMAIL_CONFIG["smtp_server"], EMAIL_CONFIG["smtp_port"])

        smtp.login(EMAIL_CONFIG["username"], EMAIL_CONFIG["password"])
        smtp.sendmail(EMAIL_CONFIG["sender"], EMAIL_CONFIG["receivers"], message.as_string())
        smtp.quit()
        LogInfo("邮件发送成功")
        return True
    except Exception as e:
        LogInfo(f"邮件发送异常: {str(e)}")
        return False

def play_wav(wav_path="alert.wav"):
    """
    播放wav音频文件（Windows平台）
    参数:
        wav_path: wav文件路径，默认为当前目录下alert.wav
    """
    try:
        import winsound
        winsound.PlaySound(wav_path, winsound.SND_FILENAME | winsound.SND_ASYNC)
        LogInfo(f"已播放音频提示: {wav_path}")
    except Exception as e:
        LogInfo(f"音频播放失败: {str(e)}")