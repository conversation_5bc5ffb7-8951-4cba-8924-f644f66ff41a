#!/usr/bin/env python3
"""
Graphiti MCP Server Wrapper
为Graphiti知识图谱服务提供MCP接口
"""

import asyncio
import json
import sys
from typing import Any, Dict, List, Optional

try:
    from graphiti_core import Graphiti
    from mcp.server import Server
    from mcp.types import Tool, TextContent
    import mcp.server.stdio
except ImportError as e:
    print(f"Error importing required modules: {e}", file=sys.stderr)
    sys.exit(1)

# 创建MCP服务器实例
server = Server("graphiti-mcp-server")

# 全局Graphiti实例
graphiti_instance: Optional[Graphiti] = None

@server.list_tools()
async def list_tools() -> List[Tool]:
    """列出可用的工具"""
    return [
        Tool(
            name="create_knowledge_graph",
            description="Create a new knowledge graph",
            inputSchema={
                "type": "object",
                "properties": {
                    "name": {"type": "string", "description": "Name of the knowledge graph"},
                    "description": {"type": "string", "description": "Description of the knowledge graph"}
                },
                "required": ["name"]
            }
        ),
        Tool(
            name="add_node",
            description="Add a node to the knowledge graph",
            inputSchema={
                "type": "object",
                "properties": {
                    "label": {"type": "string", "description": "Node label"},
                    "properties": {"type": "object", "description": "Node properties"}
                },
                "required": ["label"]
            }
        ),
        Tool(
            name="query_graph",
            description="Query the knowledge graph",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "Query string"}
                },
                "required": ["query"]
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """处理工具调用"""
    global graphiti_instance
    
    try:
        if name == "create_knowledge_graph":
            # 初始化Graphiti实例
            graphiti_instance = Graphiti()
            return [TextContent(
                type="text",
                text=f"Knowledge graph '{arguments.get('name', 'default')}' created successfully"
            )]
        
        elif name == "add_node":
            if not graphiti_instance:
                return [TextContent(
                    type="text",
                    text="Error: No knowledge graph created. Please create one first."
                )]
            
            # 添加节点逻辑（这里需要根据Graphiti的实际API调整）
            label = arguments.get("label", "")
            properties = arguments.get("properties", {})
            
            return [TextContent(
                type="text",
                text=f"Node '{label}' added with properties: {json.dumps(properties)}"
            )]
        
        elif name == "query_graph":
            if not graphiti_instance:
                return [TextContent(
                    type="text",
                    text="Error: No knowledge graph created. Please create one first."
                )]
            
            query = arguments.get("query", "")
            # 查询逻辑（这里需要根据Graphiti的实际API调整）
            
            return [TextContent(
                type="text",
                text=f"Query results for '{query}': [Results would be displayed here]"
            )]
        
        else:
            return [TextContent(
                type="text",
                text=f"Unknown tool: {name}"
            )]
    
    except Exception as e:
        return [TextContent(
            type="text",
            text=f"Error executing tool '{name}': {str(e)}"
        )]

async def main():
    """主函数"""
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())
