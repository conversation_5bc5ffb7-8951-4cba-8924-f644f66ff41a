# MCP服务集成到Augment插件指南

## 概述

本指南介绍如何将已安装的MCP服务集成到Augment插件中，使您能够在Augment环境中使用这些强大的AI工具。

## 已安装的MCP服务

### ✅ 可用服务

1. **Graphiti** - 知识图谱和记忆管理
   - 功能: 构建和查询知识图谱
   - 用途: 信息组织、关系分析、知识管理

2. **Opik** - ML实验跟踪和监控
   - 功能: 机器学习实验管理
   - 用途: 模型训练跟踪、性能监控、实验对比

3. **Ragie** - RAG (检索增强生成)
   - 功能: 文档检索和智能问答
   - 用途: 文档搜索、知识问答、内容生成

4. **Jupyter MCP Server** - Jupyter集成
   - 功能: Jupyter notebook集成
   - 用途: 代码执行、数据分析、可视化

## 安装步骤

### 方法1: 自动安装 (推荐)

1. **运行安装脚本**
   ```bash
   python install_mcp_to_augment.py
   ```

2. **按提示选择配置文件**
   - 脚本会自动查找Augment配置文件
   - 选择要更新的配置文件

3. **重启Augment插件**
   - 关闭并重新打开Augment
   - 或重启IDE/编辑器

### 方法2: 手动配置

1. **找到Augment配置文件**
   常见位置:
   - `~/.codegeex/agent/configs/user_mcp_config.json`
   - `~/.vscode/extensions/*/dist/agent/configs/user_mcp_config.json`
   - `~/.cursor/mcp.json`

2. **编辑配置文件**
   将 `augment_mcp_config.json` 的内容合并到用户配置中:
   ```json
   {
     "mcpServers": {
       "graphiti": {
         "command": "python",
         "args": ["D:/Microsoft VS Code/mcp_servers/mcp_servers/graphiti_server.py"],
         "description": "Graphiti knowledge graph service"
       },
       "opik": {
         "command": "python", 
         "args": ["D:/Microsoft VS Code/mcp_servers/mcp_servers/opik_server.py"],
         "description": "Opik ML tracking service"
       },
       "ragie": {
         "command": "python",
         "args": ["D:/Microsoft VS Code/mcp_servers/mcp_servers/ragie_server.py"], 
         "description": "Ragie RAG service"
       },
       "jupyter_mcp": {
         "command": "jupyter-mcp-server",
         "args": ["start", "--transport", "stdio"],
         "description": "Jupyter MCP Server"
       }
     }
   }
   ```
   {
     "mcpServers": {
       "graphiti": {
         "command": "python",
         "args": ["D:/Microsoft VS Code/mcp_servers/mcp_servers/graphiti_server.py"],
         "description": "Graphiti knowledge graph service"
       },
       "opik": {
         "command": "python", 
         "args": ["D:/Microsoft VS Code/mcp_servers/mcp_servers/opik_server.py"],
         "description": "Opik ML tracking service"
       },
       "ragie": {
         "command": "python",
         "args": ["D:/Microsoft VS Code/mcp_servers/mcp_servers/ragie_server.py"], 
         "description": "Ragie RAG service"
       }
     }
   }
## 使用方法

### 在Augment中使用MCP服务

1. **启动Augment**
   - 确保配置文件已更新
   - 重启Augment插件

2. **调用MCP工具**
   - 在对话中请求使用特定服务
   - 例如: "使用Graphiti创建一个知识图谱"

3. **服务功能示例**

   **Graphiti (知识图谱)**
   ```
   - 创建知识图谱
   - 添加节点和关系
   - 查询图谱信息
   ```

   **Opik (ML跟踪)**
   ```
   - 初始化项目
   - 记录实验数据
   - 查看实验历史
   ```

   **Ragie (RAG)**
   ```
   - 创建文档库
   - 搜索相关文档
   - 生成基于文档的答案
   ```

   **Jupyter MCP**
   ```
   - 执行Python代码
   - 数据分析和可视化
   - Notebook管理
   ```

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查Python环境是否正确
   - 确认所有依赖包已安装
   - 查看错误日志

2. **配置文件未生效**
   - 确认配置文件路径正确
   - 重启Augment插件
   - 检查JSON格式是否正确

3. **权限问题**
   - 确保脚本文件有执行权限
   - 检查文件路径是否可访问

### 调试方法

1. **查看日志**
   ```bash
   # 手动测试服务器
   python mcp_servers/graphiti_server.py
   ```

2. **验证安装**
   ```bash
   # 检查包是否正确安装
   python -c "import graphiti_core, opik, ragie; print('All packages available')"
   ```

3. **测试Jupyter MCP**
   ```bash
   # 测试Jupyter MCP服务器
   jupyter-mcp-server start --transport stdio
   ```

## 高级配置

### 环境变量设置

可以在配置文件中设置环境变量:
```json
{
  "env": {
    "GRAPHITI_LOG_LEVEL": "DEBUG",
    "OPIK_API_KEY": "your-api-key",
    "RAGIE_BASE_URL": "https://api.ragie.ai"
  }
}
```

### 自定义参数

根据需要调整服务参数:
```json
{
  "mcpSettings": {
    "timeout": 30000,
    "retryAttempts": 3,
    "logLevel": "INFO"
  }
}
```

## 注意事项

1. **依赖要求**
   - 确保所有MCP服务的依赖包已正确安装
   - Python版本兼容性 (推荐Python 3.8+)

2. **性能考虑**
   - 某些服务可能需要额外的系统资源
   - 建议根据使用情况启用相应服务

3. **安全性**
   - 某些服务可能需要API密钥
   - 请妥善保管敏感信息

## 支持

如果遇到问题，请检查:
1. MCP服务安装状态
2. Augment插件版本
3. 配置文件格式
4. 系统环境兼容性

更多信息请参考各服务的官方文档。
