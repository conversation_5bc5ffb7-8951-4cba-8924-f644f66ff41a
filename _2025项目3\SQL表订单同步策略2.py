from math import log
import os
import time
import copy
import shutil
import pymysql
import numpy as np
import pandas as pd
import threading
from queue import Queue
from collections import deque
from datetime import datetime, timedelta

g_params['配置文件夹路径'] = "C:\MT4"
g_params['配置文件名'] = "配置文件与订阅合约.xlsx"
g_params['开仓超价跳数'] = 1
g_params['平仓超价跳数'] = 2
g_params['收盘清仓倒计时分钟'] = 5

trade_order_filedir = g_params['配置文件夹路径']
trade_config_file   = trade_order_filedir+"\\"+g_params['配置文件名'] 
trade_config_DATA   =pd.read_excel(trade_config_file,sheet_name = 0)
symbol_Id =trade_config_DATA["使用合约"].dropna().str.upper()
forex_marketId=trade_config_DATA["外盘段映射货币或合约"].dropna().str.upper()
下单量映射倍数=trade_config_DATA["下单量映射倍数"].dropna()
时时价格启用=trade_config_DATA["时时价格启用"].dropna()
平台类型=trade_config_DATA["平台类型"].dropna().str.upper()
注册账号=trade_config_DATA["注册账号"].dropna()
魔术码=trade_config_DATA["魔术码"].dropna()
追单次数=trade_config_DATA["追单次数"].dropna()
追单秒数=trade_config_DATA["追单秒数"].dropna()
隔夜费=trade_config_DATA["隔夜费"].dropna()
手续费=trade_config_DATA["手续费"].dropna()
保证金比例=trade_config_DATA["保证金比例"].dropna()

服务器地址 = str(trade_config_DATA["服务器地址"].iloc[0])
服务器用户名 = str(trade_config_DATA["服务器用户名"].iloc[0])
链接数据库密码 = str(trade_config_DATA["链接数据库密码"].iloc[0])
数据库名称 = str(trade_config_DATA["数据库名称"].iloc[0])
价格表 = str(trade_config_DATA["价格表"].iloc[0])

class ConnectionPool:
    """数据库连接池管理类"""
    def __init__(self, host, user, password, database, max_connections=5):
        self.host = host
        self.user = user
        self.password = password
        self.database = database
        self.max_connections = max_connections
        self.connections = Queue(maxsize=max_connections)
        self.in_use_count = 0
        self.lock = threading.Lock()  # 线程锁保护连接计数
        
        # 初始化连接池
        LogInfo(f"正在初始化数据库连接池: {self.host}, {self.user}, {self.database}")
        for i in range(max_connections):
            try:
                conn = self.create_connection()
                self.connections.put(conn)
            except Exception as e:
                LogInfo(f"初始化连接池时发生错误: {e}")
    
    def create_connection(self):
        """创建一个新的数据库连接"""
        try:
            # 确保所有参数都是字符串类型
            conn = pymysql.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                autocommit=False  # 重要：默认不自动提交
            )
            return conn
        except Exception as e:
            LogInfo(f"创建数据库连接失败: {e}, 参数: {self.host}, {self.user}, {self.database}")
            raise
    
    def get_connection(self):
        """从连接池获取一个连接"""
        with self.lock:
            try:
                if not self.connections.empty():
                    conn = self.connections.get(block=False)
                    self.in_use_count += 1
                    return conn
                elif self.in_use_count < self.max_connections:
                    # 如果队列为空但未达到最大连接数，创建新连接
                    conn = self.create_connection()
                    self.in_use_count += 1
                    return conn
                else:
                    # 达到最大连接数，等待已有连接释放
                    LogInfo("等待数据库连接释放...")
                    conn = self.connections.get(block=True, timeout=10)
                    self.in_use_count += 1
                    return conn
            except Exception as e:
                LogInfo(f"获取数据库连接失败: {e}")
                return None
    
    def release_connection(self, conn):
        """将连接返回连接池"""
        with self.lock:
            if conn:
                try:
                    # 确保连接可用，否则重新创建
                    conn.ping(reconnect=True)
                    self.connections.put(conn)
                except:
                    # 连接已断开，创建新连接放入池中
                    try:
                        new_conn = self.create_connection()
                        self.connections.put(new_conn)
                    except Exception as e:
                        LogInfo(f"重新创建连接失败: {e}")
                finally:
                    self.in_use_count -= 1

class MySQLPool:
    def __init__(self, config, min_size=3, max_size=10):
        self.config = config
        self.min_size = min_size
        self.max_size = max_size
        self.pool = Queue(max_size)
        # 初始化连接池时捕获异常
        for _ in range(min_size):
            conn = self.create_connection()
            if conn:
                self.pool.put(conn)

    def create_connection(self):
        try:
            # 移除不必要的连接字符串构建
            # 确保密码是字符串类型
            config = self.config.copy()
            if 'password' in config and config['password'] is not None:
                config['password'] = str(config['password'])
                
            return pymysql.connect(
                host=config['host'],
                user=config['user'],
                password=config['password'],
                database=config['database'],
                charset=config['charset'],
                cursorclass=config['cursorclass']
            )
        except Exception as e:
            LogInfo(f"数据库连接创建失败: {e}")
            return None

    def get_connection(self):
        try:
            conn = self.pool.get_nowait()
            # 检查连接是否有效
            try:
                conn.ping(reconnect=True)  # 检查连接并尝试重连
                return conn
            except:
                # 连接已失效，创建新连接
                LogInfo("检测到失效连接，正在重新创建...")
                conn = self.create_connection()
                return conn
        except:
            # 池为空时创建新连接
            conn = self.create_connection()
            if conn:
                return conn
            return None

    def release_connection(self, conn):
        if conn is None:
            return
            
        if self.pool.qsize() < self.max_size:
            self.pool.put(conn)
        else:
            try:
                conn.close()
            except:
                pass

class TradeDB:
    """交易数据库接口类"""
    def __init__(self, host_or_pool, user=None, password=None, database=None):
        # 根据传入参数类型判断初始化方式
        if isinstance(host_or_pool, ConnectionPool) or hasattr(host_or_pool, 'get_connection'):
            # 如果传入的是连接池对象（向后兼容）
            self.use_pool = True
            self.db_pool = host_or_pool
            self.connection_pool = None
        else:
            # 如果传入的是连接参数
            self.use_pool = False
            self.db_pool = None
            
            # 确保所有参数都已提供
            if user is None or password is None or database is None:
                LogInfo("错误：初始化TradeDB时缺少必要参数")
                raise ValueError("初始化TradeDB需要提供host, user, password和database")
                
            # 初始化连接池
            self.connection_pool = ConnectionPool(host_or_pool, user, password, database)
            
    def execute_query(self, sql, params=None, fetch=False):
        """执行SQL查询"""
        # 根据初始化方式选择获取连接的方法
        if self.use_pool:
            conn = self.db_pool.get_connection()
        else:
            conn = self.connection_pool.get_connection()
            
        if not conn:
            LogInfo("获取数据库连接失败")
            return None if fetch else False
            
        try:
            with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, params if params else ())
                if fetch:
                    result = cursor.fetchall()
                else:
                    conn.commit()
                    result = True
                return result
        except Exception as e:
            LogInfo(f"执行SQL失败: {e}")
            if not fetch:
                try:
                    conn.rollback()
                except:
                    pass
            return None if fetch else False
        finally:
            # 根据初始化方式选择释放连接的方法
            if self.use_pool:
                self.db_pool.release_connection(conn)
            else:
                self.connection_pool.release_connection(conn)

    def execute_transaction(self, operations):
        """
        执行事务操作
        
        参数:
            operations: 包含(sql, params)元组的列表
        """
        conn = self.connection_pool.get_connection()
        if not conn:
            LogInfo("获取数据库连接失败")
            return False
            
        try:
            with conn.cursor() as cursor:
                conn.begin()
                for sql, params in operations:
                    cursor.execute(sql, params if params else ())
                conn.commit()
                return True
        except Exception as e:
            LogInfo(f"事务执行失败: {e}")
            try:
                conn.rollback()
            except:
                pass
            return False
        finally:
            self.connection_pool.release_connection(conn)

    def get_pending_orders(self, symbol):
        """获取待处理的订单"""
        sql = """
        SELECT * FROM chufakaidanbiao 
        WHERE ahuobi = %s AND bbiaoshi = 0 
        ORDER BY zengjiashijian ASC
        """
        return self.execute_query(sql, (symbol,), fetch=True)

    def update_order_status(self, order_id, status, trade_result=None):
        """更新订单状态"""
        sql = """
        UPDATE chufakaidanbiao 
        SET bbiaoshi = %s, trade_result = %s 
        WHERE adingdanhao = %s
        """
        return self.execute_query(sql, (status, trade_result, order_id))
    
    def close_all_positions(self):
        """获取所有需要平仓的持仓"""
        sql = """
        SELECT * FROM chufakaidanbiao 
        WHERE bbiaoshi = 1
        """
        return self.execute_query(sql, fetch=True)
    
    def update_price(self, table_name, symbol, price, platform_type):
        """
        更新货币价格
        
        如果platform_type为'A'，则将symbol写入huobi字段，price写入jiage字段
        否则按原有逻辑处理
        """
        try:
            if platform_type == 'A':
                # 首先检查A平台是否存在记录
                check_sql = f"""
                SELECT IntID FROM {table_name} 
                WHERE pingtai = 'A'
                """
                result = self.execute_query(check_sql, fetch=True)
                
                if result:
                    # 存在A平台记录，执行更新
                    update_sql = f"""
                    UPDATE {table_name} 
                    SET huobi = %s, jiage = %s 
                    WHERE pingtai = 'A'
                    """
                    update_result = self.execute_query(update_sql, (symbol, price))
                    
                    # if update_result:
                    #     LogInfo(f"成功更新A平台价格: 货币={symbol}, 价格={price}")
                    # else:
                    #     LogInfo(f"A平台价格更新失败: 货币={symbol}, 价格={price}")
                    
                    return update_result
                else:
                    # 不存在A平台记录，执行插入
                    insert_sql = f"""
                    INSERT INTO {table_name} (huobi, jiage, pingtai) 
                    VALUES (%s, %s, 'A')
                    """
                    insert_result = self.execute_query(insert_sql, (symbol, price))
                    
                    if insert_result:
                        LogInfo(f"成功插入A平台价格: 货币={symbol}, 价格={price}")
                    else:
                        LogInfo(f"A平台价格插入失败: 货币={symbol}, 价格={price}")
                    
                    return insert_result
            else:
                # 原有逻辑处理非A平台的情况
                # 首先检查是否存在匹配的记录
                check_sql = f"""
                SELECT IntID FROM {table_name} 
                WHERE huobi = %s AND pingtai = %s
                """
                result = self.execute_query(check_sql, (symbol, platform_type), fetch=True)
                
                if not result:
                    LogInfo(f"无法找到匹配的价格记录: 货币={symbol}, 平台={platform_type}")
                    return False
                
                # 存在匹配记录，执行更新
                update_sql = f"""
                UPDATE {table_name} 
                SET jiage = %s 
                WHERE huobi = %s AND pingtai = %s
                """
                update_result = self.execute_query(update_sql, (price, symbol, platform_type))
                
                return update_result
        except Exception as e:
            LogInfo(f"价格更新操作异常: {e}")
            return False

    def get_new_orders(self, account, magic_number):
        """获取新的开单信号"""
        sql = """
        SELECT * FROM chufakaidanbiao 
        WHERE abiaoshi = 0 AND kaicangzhuzhanghao = %s AND moshuma = %s
        """
        return self.execute_query(sql, (account, magic_number), fetch=True)

    def update_order_after_open(self, order_id, platform_order_id, open_time, magic_number, status_flag=1):
        """
        开仓后更新订单状态
        
        参数:
            order_id: 订单ID
            platform_order_id: 平台订单号
            open_time: 开仓时间
            magic_number: 魔术码
            status_flag: 订单状态标志 (1=已下单, 2=已完成)
        """
        sql = """
        UPDATE chufakaidanbiao 
        SET adingdanhao = %s, akaidanshijian = %s, abiaoshi = %s 
        WHERE IntID = %s AND moshuma = %s
        """
        return self.execute_query(sql, (platform_order_id, open_time, status_flag, order_id, magic_number))

    def insert_order_details(self, order_info):
        """
        插入新订单详细信息
        
        参数:
            order_info: 包含订单信息的字典，需要包含以下字段:
                - dingdanhao (订单号)
                - kaidanshijian (开单时间)
                - fangxaing (方向)
                - shoushu (手数)
                - huobi (货币)
                - kaidanjiage (开单价格)
                - pingtaileixing (平台类型)
                - zhushi (注释)
                - moshuma (识别码)
                - geyefei (隔夜费)
                - shouxufei (手续费)
                - yingkuijine (盈亏金额)
                - jingyingkuijine (总盈亏金额)
                - tongyibiaoshi (统一标识)
                - kaicangzhuzhanghao (开仓主账号)
        """
        # 首先检查该订单是否已存在
        check_sql = """
        SELECT dingdanhao FROM xiangxidanxinxibiao 
        WHERE dingdanhao = %s AND moshuma = %s
        """
        result = self.execute_query(check_sql, (order_info['dingdanhao'], order_info['moshuma']), fetch=True)
        
        if result:
            LogInfo(f"订单 {order_info['dingdanhao']} 已存在，将更新信息")
            return self.update_order_profit(
                order_info['dingdanhao'], 
                order_info['moshuma'],
                order_info['geyefei'],
                order_info['shouxufei'],
                order_info['yingkuijine'],
                order_info['jingyingkuijine']
            )
        
        # 订单不存在，执行插入
        insert_sql = """
        INSERT INTO xiangxidanxinxibiao (
            dingdanhao, kaidanshijian, fangxaing, shoushu, huobi, 
            kaidanjiage, pingtaileixing, zhushi, moshuma, geyefei, 
            shouxufei, yingkuijine, jingyingkuijine, tongyibiaoshi, kaicangzhuzhanghao
        ) VALUES (
            %s, %s, %s, %s, %s, 
            %s, %s, %s, %s, %s, 
            %s, %s, %s, %s, %s
        )
        """
        params = (
            order_info['dingdanhao'], order_info['kaidanshijian'], order_info['fangxaing'], 
            order_info['shoushu'], order_info['huobi'], order_info['kaidanjiage'], 
            order_info['pingtaileixing'], order_info['zhushi'], order_info['moshuma'], 
            order_info['geyefei'], order_info['shouxufei'], order_info['yingkuijine'], 
            order_info['jingyingkuijine'], order_info['tongyibiaoshi'], order_info['kaicangzhuzhanghao']
        )
        
        result = self.execute_query(insert_sql, params)
        if result:
            LogInfo(f"成功插入订单 {order_info['dingdanhao']} 的详细信息")
        else:
            LogInfo(f"插入订单 {order_info['dingdanhao']} 的详细信息失败")
        
        return result

    def update_order_profit(self, order_id, magic_number, overnight_fee, commission, profit, total_profit):
        """
        更新订单的盈亏信息
        
        参数:
            order_id: 订单号
            magic_number: 识别码
            overnight_fee: 隔夜费
            commission: 手续费
            profit: 盈亏金额
            total_profit: 总盈亏金额
        """
        update_sql = """
        UPDATE xiangxidanxinxibiao 
        SET geyefei = %s, shouxufei = %s, yingkuijine = %s, jingyingkuijine = %s 
        WHERE dingdanhao = %s AND moshuma = %s
        """
        result = self.execute_query(update_sql, (overnight_fee, commission, profit, total_profit, order_id, magic_number))
        
        if result:
            LogInfo(f"成功更新订单 {order_id} 的盈亏信息")
        else:
            LogInfo(f"更新订单 {order_id} 的盈亏信息失败")
        
        return result

    def get_open_orders(self, account, magic_number, platform_type):
        """获取所有未平仓订单"""
        sql = """
        SELECT * FROM xiangxidanxinxibiao 
        WHERE kaicangzhuzhanghao = %s 
        AND moshuma = %s 
        AND pingtaileixing = %s 
        AND pingcangbiaoshi = 0
        """
        return self.execute_query(sql, (account, magic_number, platform_type), fetch=True)

    def update_closed_order(self, order_id, magic_number):
        """将订单标记为已平仓"""
        sql = """
        UPDATE xiangxidanxinxibiao 
        SET pingcangbiaoshi = 2 
        WHERE tongyibiaoshi = %s AND moshuma = %s
        """
        result = self.execute_query(sql, (order_id, magic_number))
        if result:
            LogInfo(f"订单 {order_id} 已标记为平仓状态2")
        else:
            LogInfo(f"更新订单 {order_id} 平仓状态失败")
        return result

    def get_orders_to_close(self, account, magic_number, platform_type):
        """获取所有需要平仓的订单(pingcangbiaoshi=1或2)"""
        sql = """
        SELECT * FROM xiangxidanxinxibiao 
        WHERE kaicangzhuzhanghao = %s 
        AND moshuma = %s 
        AND pingtaileixing = %s 
        AND (pingcangbiaoshi = 1 OR pingcangbiaoshi = 2)
        ORDER BY IntID ASC
        LIMIT 1
        """
        return self.execute_query(sql, (account, magic_number, platform_type), fetch=True)

    def check_risk_control_account(self, account):
        """查询账号的风控信息是否存在"""
        sql = """
        SELECT * FROM fengkongjine 
        WHERE zhanghao = %s
        """
        return self.execute_query(sql, (account,), fetch=True)

    def insert_risk_control(self, account, platform_type, balance, equity, update_time):
        """插入新的风控信息"""
        sql = """
        INSERT INTO fengkongjine 
        (zhanghao, pingtai, yue, jingzhi, genxinshijian) 
        VALUES (%s, %s, %s, %s, %s)
        """
        result = self.execute_query(sql, (account, platform_type, balance, equity, update_time))
        if result:
            LogInfo(f"成功插入账号 {account} 的风控信息")
        else:
            LogInfo(f"插入账号 {account} 的风控信息失败")
        return result

    def update_risk_control(self, account, balance, equity, update_time):
        """更新账号的风控信息"""
        sql = """
        UPDATE fengkongjine 
        SET yue = %s, jingzhi = %s, genxinshijian = %s 
        WHERE zhanghao = %s
        """
        result = self.execute_query(sql, (balance, equity, update_time, account))
        if result:
            LogInfo(f"成功更新账号 {account} 的风控信息")
        else:
            LogInfo(f"更新账号 {account} 的风控信息失败")
        return result

    def get_chase_orders_info(self, account, magic_number):
        """获取追单信息，用于平仓决策"""
        sql = """
        SELECT d.tongyibiaoshi, d.Ocount, c.zengjiashijian, c.zhuidancishu 
        FROM chufakaidanbiao AS c 
        INNER JOIN  
        (
            SELECT b.tongyibiaoshi, count(*) AS Ocount 
            FROM 
            (
                SELECT * FROM xiangxidanxinxibiao 
                WHERE moshuma = %s AND kaicangzhuzhanghao = %s
            ) AS b
            GROUP BY b.tongyibiaoshi
        ) AS d 
        ON c.adingdanhao = d.tongyibiaoshi
        """
        return self.execute_query(sql, (magic_number, account), fetch=True)

    def check_order_exists(self, order_id, magic_number):
        """
        检查订单是否已存在于数据库中
        
        参数:
            order_id: 订单ID或统一标识
            magic_number: 魔术码
            
        返回:
            bool: 订单是否存在
        """
        try:
            sql = """
            SELECT COUNT(*) as count FROM xiangxidanxinxibiao 
            WHERE tongyibiaoshi = %s AND moshuma = %s
            """
            
            result = self.execute_query(sql, (order_id, magic_number), fetch=True)
            
            if result and len(result) > 0:
                return result[0]['count'] > 0
            return False
        except Exception as e:
            LogInfo(f"❌ 检查订单存在性时出错: {e}")
            # 表不存在时，假设订单不存在
            return False

    def add_active_order(self, account, magic_number, order_id, symbol, direction, volume, 
                         open_price, current_price, profit, open_time, platform_type, 
                         overnight_fee, commission_fee, net_profit):
        """添加活跃订单到数据库"""
        sql = """
        INSERT INTO xiangxidanxinxibiao (
            kaicangzhuzhanghao, moshuma, tongyibiaoshi, huobi, fangxiang, shuliang, kaidanjiage, 
            xianjia, yingkui, kaichangshijian, pingtai, geyefei, shouxufei, jinglirun, xiugaishijian
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW()
        )
        """
        return self.execute_query(sql, (
            account, magic_number, order_id, symbol, 
            direction, volume, open_price, current_price, profit, open_time, 
            platform_type, overnight_fee, commission_fee, net_profit
        ))

    def update_order_profit(self, order_id, magic_number, current_price, 
                            profit, overnight_fee, commission_fee, net_profit):
        """更新订单的盈亏信息"""
        sql = """
        UPDATE xiangxidanxinxibiao 
        SET xianjia = %s, yingkui = %s, geyefei = %s, shouxufei = %s, 
            jinglirun = %s, xiugaishijian = NOW() 
        WHERE tongyibiaoshi = %s AND moshuma = %s
        """
        return self.execute_query(sql, (
            current_price, profit, overnight_fee, commission_fee, 
            net_profit, order_id, magic_number
        ))

    def get_and_lock_new_orders(self, account, magic_number):
        """
        获取新的开单信号并立即锁定，防止重复处理
        
        参数:
            account: 账号
            magic_number: 魔术码
            
        返回:
            list: 获取到的订单列表
        """
        try:
            # 查询新订单，不锁定
            query_sql = """
            SELECT * FROM chufakaidanbiao 
            WHERE abiaoshi = 0 AND kaicangzhuzhanghao = %s AND moshuma = %s
            """
            orders = self.execute_query(query_sql, (account, magic_number), fetch=True)
            
            if not orders or len(orders) == 0:
                return []
                
            # 锁定订单的ID列表
            order_ids = [order['IntID'] for order in orders]
            placeholders = ', '.join(['%s'] * len(order_ids))
            current_time = time.strftime("%Y-%m-%d %H:%M:%S")
            
            # 立即标记这些订单为"处理中"(状态3)，使用事务处理
            update_sql = f"""
            UPDATE chufakaidanbiao 
            SET abiaoshi = 3, akaidanshijian = %s 
            WHERE IntID IN ({placeholders}) AND abiaoshi = 0
            """
            params = [current_time] + order_ids
            
            # 执行更新，标记为"处理中"
            result = self.execute_query(update_sql, params)
            
            if result:
                LogInfo(f"已锁定 {len(order_ids)} 个订单进行处理")
                # 再次查询以确认锁定状态
                verify_sql = f"""
                SELECT * FROM chufakaidanbiao 
                WHERE IntID IN ({placeholders}) AND abiaoshi = 3
                """
                locked_orders = self.execute_query(verify_sql, order_ids, fetch=True)
                return locked_orders if locked_orders else []
            else:
                LogInfo("锁定订单失败，可能已被其他线程锁定")
                return []
                
        except Exception as e:
            LogInfo(f"锁定订单时发生错误: {e}")
            return []

class TriggerManager:
    """
    交易触发器管理类，负责处理订单开平仓操作
    """
    def __init__(self):
        # 交易状态标记
        self.BKS = 0  # 买开状态: 0=未开仓, 1=已开仓, 2=已平仓
        self.SKS = 0  # 卖开状态: 0=未开仓, 1=已开仓, 2=已平仓
        self.BPS = 0  # 买平状态: 0=未平仓, 1=已平仓
        self.SPS = 0  # 卖平状态: 0=未平仓, 1=已平仓
    
    def reset_states(self):
        """重置所有交易状态"""
        self.BKS = 0
        self.SKS = 0
        self.BPS = 0
        self.SPS = 0
    
    # ==================== 历史开仓函数 ====================
    
    def his_trigger_long(self, qty, price, tcode):
        """
        历史多头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
        """
        if self.BKS != 0:
            return False
            
        Buy(qty, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "多单买入开仓价==>", price, "买入数量==>", qty)
        self.BKS = 1
        return True
    
    def his_trigger_short(self, qty, price, tcode):
        """
        历史空头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
        """
        if self.SKS != 0:
            return False
            
        SellShort(qty, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "空单卖出开仓价==>", price, "卖出数量==>", qty)
        self.SKS = 1
        return True
    
    def his_trigger(self, BK, SK, qty, price, tcode):
        """历史开仓兼容函数"""
        if BK:
            return self.his_trigger_long(qty, price, tcode)
        elif SK:
            return self.his_trigger_short(qty, price, tcode)
        return False
    
    # ==================== 历史平仓函数 ====================
    
    def his_trigger_exit_short(self, clots, price, tcode):
        """
        历史平仓空头持仓
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
        """
        if self.BPS != 0 or SellPosition(tcode) <= 0 or clots <= 0:
            return False
            
        _lots = min(clots, SellPosition(tcode))
        BuyToCover(_lots, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "空单买入平仓价==>", price, "买入平仓数量==>", _lots)
        self.BPS = 1
        
        # 更新卖开状态
        if self.SKS == 1:
            self.SKS = 2
        
        return True
    
    def his_trigger_exit_long(self, clots, price, tcode):
        """
        历史平仓多头持仓
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
        """
        if self.SPS != 0 or BuyPosition(tcode) <= 0 or clots <= 0:
            return False
            
        _lots = min(clots, BuyPosition(tcode))
        Sell(_lots, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "多单卖出平仓价==>", price, "卖出平仓数量==>", _lots)
        self.SPS = 1
        
        # 更新买开状态
        if self.BKS == 1:
            self.BKS = 2
        
        return True
    
    def his_trigger_Exit(self, BP, SP, long_price, short_price, clots, tcode):
        """
        历史平仓兼容函数，支持不同的多空平仓价格
        
        参数:
            BP: 是否平空仓标志
            SP: 是否平多仓标志
            long_price: 平多头价格 (卖出价)
            short_price: 平空头价格 (买入价)
            clots: 平仓手数
            tcode: 合约代码
        
        返回:
            bool: 平仓是否成功
        """
        result = False
        if BP:
            result = self.his_trigger_exit_short(clots, short_price, tcode)
        if SP:
            result = self.his_trigger_exit_long(clots, long_price, tcode) or result
        return result
    
    # ==================== 实时开仓函数 ====================
    
    def tim_trigger_long(self, qty, price, tcode):
        """
        实时多头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
            
        返回:
            (状态, 订单ID)
        """

        if self.BKS != 0:
            LogInfo(f"BKS标记为{self.BKS}，跳过多头开仓")
            return None, None
        
        # 价格检查逻辑保持不变
        upper_limit = Q_UpperLimit(tcode)
        if upper_limit <= 0:
            checked_price = price
        else:
            checked_price = min(price, upper_limit)
            if checked_price != price:
                LogInfo(f"警告: 多头开仓价格 {price} 超过涨停价 {upper_limit}，已自动调整")
        
        # 发送订单
        retEntry, EntryOrderId = A_SendOrder(Enum_Buy(), Enum_Entry(), qty, checked_price, tcode)
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "多单买入开仓价==>", checked_price, "买入数量==>", qty, "下单状态=", retEntry)
        
        # 只要有订单ID返回，无论retEntry如何，都设置标志位
        # 因为实际测试中发现retEntry可能不准确，但下单可能已成功
        if EntryOrderId:
            LogInfo(f"多头开仓订单已发送，设置BKS=1，订单号: {EntryOrderId}")
            self.BKS = 1
        else:
            LogInfo(f"多头开仓订单发送失败，BKS保持为{self.BKS}")
        
        return retEntry, EntryOrderId
    
    def tim_trigger_short(self, qty, price, tcode):
        """
        实时空头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
            
        返回:
            (状态, 订单ID)
        """
        if self.SKS != 0:
            LogInfo(f"SKS标记为{self.SKS}，跳过空头开仓")
            return None, None
        
        # 价格检查逻辑保持不变
        lower_limit = Q_LowLimit(tcode)
        if lower_limit <= 0:
            checked_price = price
        else:
            checked_price = max(price, lower_limit)
            if checked_price != price:
                LogInfo(f"警告: 空头开仓价格 {price} 低于跌停价 {lower_limit}，已自动调整")
        
        # 发送订单
        retEntry, EntryOrderId = A_SendOrder(Enum_Sell(), Enum_Entry(), qty, checked_price, tcode)
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "空单卖出开仓价==>", checked_price, "卖出数量==>", qty, "下单状态=", retEntry)
        
        # 只要有订单ID返回，无论retEntry如何，都设置标志位
        if EntryOrderId:
            LogInfo(f"空头开仓订单已发送，设置SKS=1，订单号: {EntryOrderId}")
            self.SKS = 1
        else:
            LogInfo(f"空头开仓订单发送失败，SKS保持为{self.SKS}")
        
        return retEntry, EntryOrderId
    
    def tim_trigger(self, BK, SK, qty, price, tcode):
        """实时开仓兼容函数"""
        if BK:
            return self.tim_trigger_long(qty, price, tcode)
        elif SK:
            return self.tim_trigger_short(qty, price, tcode)
        return None, None
    
    # ==================== 实时平仓函数 ====================
    
    def tim_trigger_exit_short(self, clots, price, tcode):
        """
        实时平仓空头持仓，支持返回多个订单状态
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
            
        返回:
            list: 包含所有订单信息的列表，每项为 (状态, 订单ID, 平仓类型, 平仓手数)
        """
        orders_info = []  # 用于存储所有订单的状态和ID
        
        # 只检查BPS状态，不再检查持仓，允许处理可能存在的延迟持仓
        if self.BPS != 0:
            LogInfo(f"BPS标记为{self.BPS}，跳过空头平仓")
            return orders_info
            
        # 检查空头持仓是否存在
        sell_position = A_SellPosition(tcode)
        if sell_position <= 0 or clots <= 0:
            LogInfo(f"合约 {tcode} 无空头持仓或平仓手数为0，跳过平仓")
            return orders_info
            
        _lots = min(clots, sell_position)
        LogInfo(f"准备平仓合约 {tcode} 的空头持仓，持仓量={sell_position}，平仓量={_lots}")
        
        # 价格检查逻辑保持不变
        upper_limit = Q_UpperLimit(tcode)
        if upper_limit <= 0:
            checked_price = price
        else:
            checked_price = min(price, upper_limit)
            if checked_price != price:
                LogInfo(f"警告: 平空头价格 {price} 超过涨停价 {upper_limit}，已自动调整")
        
        # 处理上期所和能源交易所的特殊情况
        if ExchangeName(tcode) not in ['SHFE', 'INE']:
            # 普通交易所情况
            retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), _lots, checked_price, tcode)
            orders_info.append((retExit, ExitOrderId, "平仓", _lots))
            LogInfo(f"发送平空头单: {tcode}, 类型=平仓, 数量={_lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
        else:
            # 上期所和能源交易所特殊处理
            lots = _lots
            tlots = A_TodaySellPosition(tcode)
            dlots = lots - tlots
            
            if tlots >= lots:
                # 今仓足够平仓,仅平今仓
                retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_ExitToday(), lots, checked_price, tcode)
                orders_info.append((retExit, ExitOrderId, "平今", lots))
                LogInfo(f"发送平空头单(平今): {tcode}, 数量={lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
            elif tlots > 0:
                # 今仓不够，分别平今仓和昨仓
                # 先平今仓部分
                TretExit, TExitOrderId = A_SendOrder(Enum_Buy(), Enum_ExitToday(), tlots, checked_price, tcode)
                orders_info.append((TretExit, TExitOrderId, "平今", tlots))
                LogInfo(f"发送平空头单(平今部分): {tcode}, 数量={tlots}, 价格={checked_price}, 订单ID={TExitOrderId}, 状态={TretExit}")
                
                # 再平昨仓部分
                YretExit, YExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), int(dlots), checked_price, tcode)
                orders_info.append((YretExit, YExitOrderId, "平昨", int(dlots)))
                LogInfo(f"发送平空头单(平昨部分): {tcode}, 数量={int(dlots)}, 价格={checked_price}, 订单ID={YExitOrderId}, 状态={YretExit}")
            elif tlots == 0:
                # 仅平昨仓
                retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), lots, checked_price, tcode)
                orders_info.append((retExit, ExitOrderId, "平昨", lots))
                LogInfo(f"发送平空头单(平昨): {tcode}, 数量={lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
        
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "空单买入平仓价==>", checked_price, "买入平仓数量==>", _lots)
        
        # 只要有任何一个订单ID返回，就更新状态
        if any(order[1] for order in orders_info):
            LogInfo(f"空头平仓订单已发送，设置BPS=1, SKS=2")
            self.BPS = 1
            if self.SKS == 1:
                self.SKS = 2
        else:
            LogInfo(f"所有空头平仓订单发送失败，BPS保持为{self.BPS}, SKS保持为{self.SKS}")
        
        return orders_info
    
    def tim_trigger_exit_long(self, clots, price, tcode):
        """
        实时平仓多头持仓，支持返回多个订单状态
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
            
        返回:
            list: 包含所有订单信息的列表，每项为 (状态, 订单ID, 平仓类型, 平仓手数)
        """
        orders_info = []  # 用于存储所有订单的状态和ID
        
        # 只检查SPS状态，不再检查持仓，允许处理可能存在的延迟持仓
        if self.SPS != 0:
            LogInfo(f"SPS标记为{self.SPS}，跳过多头平仓")
            return orders_info
            
        # 检查多头持仓是否存在
        buy_position = A_BuyPosition(tcode)
        if buy_position <= 0 or clots <= 0:
            LogInfo(f"合约 {tcode} 无多头持仓或平仓手数为0，跳过平仓")
            return orders_info
            
        _lots = min(clots, buy_position)
        LogInfo(f"准备平仓合约 {tcode} 的多头持仓，持仓量={buy_position}，平仓量={_lots}")
        
        # 价格检查逻辑保持不变
        lower_limit = Q_LowLimit(tcode)
        if lower_limit <= 0:
            checked_price = price
        else:
            checked_price = max(price, lower_limit)
            if checked_price != price:
                LogInfo(f"警告: 平多头价格 {price} 低于跌停价 {lower_limit}，已自动调整")
        
        # 处理上期所和能源交易所的特殊情况
        if ExchangeName(tcode) not in ['SHFE', 'INE']:
            # 普通交易所情况
            retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), _lots, checked_price, tcode)
            orders_info.append((retExit, ExitOrderId, "平仓", _lots))
            LogInfo(f"发送平多头单: {tcode}, 类型=平仓, 数量={_lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
        else:
            # 上期所和能源交易所特殊处理
            lots = _lots
            tlots = A_TodayBuyPosition(tcode)
            dlots = lots - tlots
            
            if tlots >= lots:
                # 今仓足够平仓,仅平今仓
                retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_ExitToday(), lots, checked_price, tcode)
                orders_info.append((retExit, ExitOrderId, "平今", lots))
                LogInfo(f"发送平多头单(平今): {tcode}, 数量={lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
            elif tlots > 0:
                # 今仓不够，分别平今仓和昨仓
                # 先平今仓部分
                TretExit, TExitOrderId = A_SendOrder(Enum_Sell(), Enum_ExitToday(), tlots, checked_price, tcode)
                orders_info.append((TretExit, TExitOrderId, "平今", tlots))
                LogInfo(f"发送平多头单(平今部分): {tcode}, 数量={tlots}, 价格={checked_price}, 订单ID={TExitOrderId}, 状态={TretExit}")
                
                # 再平昨仓部分
                YretExit, YExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), int(dlots), checked_price, tcode)
                orders_info.append((YretExit, YExitOrderId, "平昨", int(dlots)))
                LogInfo(f"发送平多头单(平昨部分): {tcode}, 数量={int(dlots)}, 价格={checked_price}, 订单ID={YExitOrderId}, 状态={YretExit}")
            elif tlots == 0:
                # 仅平昨仓
                retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), lots, checked_price, tcode)
                orders_info.append((retExit, ExitOrderId, "平昨", lots))
                LogInfo(f"发送平多头单(平昨): {tcode}, 数量={lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
        
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "多单卖出平仓价==>", checked_price, "卖出平仓数量==>", _lots)
        
        # 只要有任何一个订单ID返回，就更新状态
        if any(order[1] for order in orders_info):
            LogInfo(f"多头平仓订单已发送，设置SPS=1, BKS=2")
            self.SPS = 1
            if self.BKS == 1:
                self.BKS = 2
        else:
            LogInfo(f"所有多头平仓订单发送失败，SPS保持为{self.SPS}, BKS保持为{self.BKS}")
        
        return orders_info
    
    def tim_trigger_Exit(self, BP, SP, long_price, short_price, clots, tcode):
        """
        实时平仓兼容函数，支持不同的多空平仓价格
        
        参数:
            BP: 是否平空仓标志
            SP: 是否平多仓标志
            long_price: 平多头价格 (卖出价)
            short_price: 平空头价格 (买入价)
            clots: 平仓手数
            tcode: 合约代码
        
        返回:
            list: 所有平仓订单的信息列表
        """
        all_orders = []
        
        if BP:
            short_orders = self.tim_trigger_exit_short(clots, short_price, tcode)
            all_orders.extend(short_orders)
            
        if SP:
            long_orders = self.tim_trigger_exit_long(clots, long_price, tcode)
            all_orders.extend(long_orders)
            
        return all_orders

# symbol_d=[]
# for i in range(len(symbol_Id)):
#     symbol_d.append(copy.deepcopy(deque([0]*9,maxlen=9)))
# Tblock=[copy.deepcopy(symbol_d),copy.deepcopy(symbol_d)] 
# 订单状态字典 = {"N" : '无',"0" : '已发送',"1" : '已受理',"2" : '待触发',"3" : '已生效',"4" : '已排队',"5" : '部分成交',"6" : '完全成交',
# "7" : '待撤',"8" : '待改', "9" : '已撤单',"A" : '已撤余单',"B" : '指令失败',"C" : '待审核',"D" : '已挂起',"E" : '已申请',"F" : '无效单',"G" : '部分触发',"H" : '完全触发',"I" : '余单失败',}

CloseTime,itk,otk=0,0,0
DB_CONFIG = None
# 数据库连接池和交易数据库对象
db_pool = None
trade_db = None
# 触发管理器
trigger_manager = TriggerManager()
# 全局队列
pending_orders_queue = Queue()
order_results_queue = Queue()
# 全局订单ID映射
order_id_mapping = {}

# 添加全局变量用于跟踪待确认的订单
pending_orders = {}  # 格式: {platform_order_id: {db_order_id, magic_number, symbol, direction, timestamp}}
order_check_timeout = 300  # 订单检查超时时间（秒）

# 数据库工作线程
def db_worker():
    while True:
        try:
            # 定期从数据库读取新订单（例如每1秒）
            for symbol in symbol_Id:
                orders = trade_db.get_pending_orders(symbol)
                for order in orders:
                    pending_orders_queue.put(order)
            
            # 处理需要写回数据库的结果
            while not order_results_queue.empty():
                result = order_results_queue.get_nowait()
                trade_db.update_order_status(result['order_id'], result['status'], result['trade_result'])
            
            time.sleep(1)  # 控制查询频率
        except Exception as e:
            LogInfo(f"数据库工作线程异常: {e}")
            time.sleep(5)  # 发生异常时稍等长一些再重试

def initialize(context): 
    global g_params, DB_CONFIG, CloseTime, db_pool, trade_db, itk, otk
    itk = g_params['开仓超价跳数']
    otk = g_params['平仓超价跳数']
    for i in symbol_Id:
        LogInfo("订阅",i,"合约Tick实时行情数据")
        SetBarInterval(i,'T', 0, 100,isTrigger=True) #订阅信号合约
    # SetBarInterval("SHFE|Z|AU|MAIN",'T', 0, 100) #沪金合约交易时间最长，订阅沪金保证全交易时间可靠触发
    #for i in userNo_Id2:
    #    LogInfo("设置",i,"账户交易")
    #    SetUserNo(str(i))     
    SetActual()                      #设置实盘运行
    SetOrderWay(1)                   #设置实时发单
    SetTriggerType(1)                #设置即时行情触发
    SetTriggerType(2)                #设置交易数据触发
    #SetTriggerType(5)               #设置K线触发
    #SetTriggerType(6)               #连接状态触发
    #SetTradeDirection(0)            #设置0双向交易,1多头,2空头

    # 配置 MySQL 连接参数
    DB_CONFIG = {
        "host": 服务器地址,
        "user": 服务器用户名,
        "password": str(链接数据库密码),  # 确保密码是字符串
        "database": 数据库名称,
        "charset": "utf8mb4",
        "cursorclass": pymysql.cursors.DictCursor
    }
    
    LogInfo(f"尝试连接数据库: {DB_CONFIG['host']}, 用户: {DB_CONFIG['user']}")

    try:
        # 直接使用参数创建TradeDB对象
        LogInfo("正在创建数据库连接...")
        trade_db = TradeDB(
            服务器地址,
            服务器用户名,
            链接数据库密码,
            数据库名称
        )
        
        LogInfo("数据库连接初始化成功")
        return True
    except Exception as e:
        LogInfo(f"数据库连接初始化失败: {e}")
        # 打印详细的错误堆栈
        import traceback
        LogInfo(traceback.format_exc())
        return False
    
    # 使用try-except包装数据库初始化
    try:
        db_pool = MySQLPool(DB_CONFIG)
        trade_db = TradeDB(db_pool)
        LogInfo("数据库连接池创建成功")
    except Exception as e:
        LogInfo(f"数据库连接失败: {e}")
        # 创建空的数据库对象，让策略能继续运行
        db_pool = None
        trade_db = None
    
    CloseTime = g_params['收盘清仓倒计时分钟']
    LogInfo("策略初始化完成，数据库连接已建立")

    # 启动数据库工作线程
    db_thread = threading.Thread(target=db_worker)
    db_thread.daemon = True
    db_thread.start()
    

DateQ,strategyStatusQ=deque([0,0],maxlen=3),deque([0,0],maxlen=3) 
BKS,SKS,BPS,SPS=0,0,0,0
def handle_data(context):
    global g_params, db_pool, trade_db
    # 检查行情数据是否准备好
    HTS=1 if context.strategyStatus()=="C" else 0  
    if CurrentBar(symbol_Id[0],'T', 0) == 0:
        return
    # 检查数据库连接
    if trade_db is None:
        LogInfo("数据库未连接，仅执行非数据库功能")
        return
        
    for i in range(len(symbol_Id)):
        symbol = symbol_Id[i]
        # 获取对应的外盘映射和平台类型
        platform_type = 平台类型[i] if i < len(平台类型) else ""
        current_setting = 时时价格启用[i] if i < len(时时价格启用) else ""
        price_update_enabled = (current_setting or current_setting == "TRUE" or current_setting == "1" or current_setting == "YES")
        # LogInfo('处理合约:', symbol, '平台类型:', platform_type, '价格更新:', price_update_enabled)
        
        # 获取该合约的最新价格
        last_priceA = Close(symbol,'T', 0)
        if len(last_priceA) == 0:
            LogInfo(f"合约 {symbol} 无有效价格，跳过")
            continue
        last_price = last_priceA[-1] if HTS==1 else Q_Last(symbol)
        
        # 如果启用实时价格更新且有外盘映射信息，则更新价格
        if price_update_enabled and platform_type:
            try:
                # 将价格格式化为字符串，保留适当的小数位数
                formatted_price = f"{last_price:.2f}"
                # 更新数据库中的价格
                update_result = trade_db.update_price(价格表,symbol, formatted_price, platform_type)
                # LogInfo(f"更新价格结果: {update_result} - {symbol} 价格 {formatted_price} 平台 {platform_type}")
            except Exception as e:
                LogInfo(f"更新价格失败: {e}")
        

    if HTS==1:
        # 检查待确认的订单
        check_pending_orders(context)
        # 处理新的开单信号
        process_new_orders(context,HTS,itk,otk)
    
        # 处理活跃订单，更新信息到数据库
        process_active_orders(context)
    
        # 检测已平仓订单
        check_closed_orders(context)

        # 从数据库中检测并平仓标记为需要平仓的订单
        close_orders_from_db(context, HTS, otk)

        # 更新账户风控信息
        update_risk_control_info(context)

        # 检测追单平仓
        check_chase_orders(context, HTS, otk)

def floattime_sum(floatin1, floatin2, len_set=12):  # 高精度浮点时间求和（精确到毫秒）
    # 设置浮点数格式，保留len_set位小数
    lensave = f"%0.{len_set}f"
    
    # 格式化浮点数并提取各时间部分
    def extract_time_parts(floatin):
        strfloat = lensave % floatin
        return int(strfloat[2:4]), int(strfloat[4:6]), int(strfloat[6:8]), int(strfloat[8:11])
    
    h1, m1, s1, ms1 = extract_time_parts(floatin1)
    h2, m2, s2, ms2 = extract_time_parts(floatin2)
    
    # 计算总和并处理进位
    total_ms = ms1 + ms2
    ms_carry = total_ms // 1000
    new_ms = total_ms % 1000
    
    total_s = s1 + s2 + ms_carry
    s_carry = total_s // 60
    new_s = total_s % 60
    
    total_m = m1 + m2 + s_carry
    m_carry = total_m // 60
    new_m = total_m % 60
    
    new_h = h1 + h2 + m_carry
    new_h = min(new_h, 99)  # 限制小时数不超过99
    
    # 组合新的浮点时间字符串并转换回浮点数
    new_str_time = f"0.{new_h:02}{new_m:02}{new_s:02}{new_ms:03}"
    return float(new_str_time)

def TimeTo_Minutes(time_in):
    timestr='%0.6f'%time_in
    hsave=int(timestr[2:4])
    msave=int(timestr[4:6])
    tcout=hsave*60+msave
    return tcout

def SessionOpenTime(contractId=''):  # 获取交易时段开盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler:continue
        tlout.append(GetSessionStartTime(contractId, i))  # 获取每个交易时段的开盘时间并加入列表
    return tlout

def SessionCloseTime(contractId=''):  # 获取交易时段收盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler-1:continue
        tlout.append(GetSessionEndTime(contractId, i))  # 获取每个交易时段的收盘时间并加入列表
    return tlout

def time_string_to_seconds(time_str):
    """
    Convert a time string in the format 'YYYYMMDDHHMMSSmmm' to the number of seconds
    since the Unix epoch (1970-01-01 00:00:00 UTC).
    
    Parameters:
        time_str (str): Time string in the format 'YYYYMMDDHHMMSSmmm'.
    
    Returns:
        int: Total seconds since the Unix epoch.
    """
    if time_str == "0":
        return 0  # Return 0 or an appropriate default value for historical phases or no real-time data
    
    # Parse the time string
    year = int(time_str[0:4])
    month = int(time_str[4:6])
    day = int(time_str[6:8])
    hour = int(time_str[8:10])
    minute = int(time_str[10:12])
    second = int(time_str[12:14])
    millisecond = int(time_str[14:17])
    
    # Create a datetime object
    dt = datetime(year, month, day, hour, minute, second, millisecond * 1000)
    
    # Calculate the total seconds since the Unix epoch
    epoch = datetime(1970, 1, 1)
    total_seconds = (dt - epoch) // timedelta(seconds=1)  # Use integer division to get whole seconds
    
    return total_seconds


def time_int_to_seconds(time_int):
    """
    Convert a time integer in the format YYYYMMDDHHMMSSmmm to the number of seconds
    since the Unix epoch (1970-01-01 00:00:00 UTC).
    
    Parameters:
        time_int (int): Time integer in the format YYYYMMDDHHMMSSmmm.
    
    Returns:
        int: Total seconds since the Unix epoch.
    """
    if time_int == 0:
        return 0  # Return 0 for historical phases or no real-time data
    
    # Extract each component directly using integer arithmetic
    milliseconds = time_int % 1000
    time_int //= 1000
    seconds = time_int % 100
    time_int //= 100
    minutes = time_int % 100
    time_int //= 100
    hours = time_int % 100
    time_int //= 100
    day = time_int % 100
    time_int //= 100
    month = time_int % 100
    year = time_int // 100
    
    # Create a datetime object
    dt = datetime(year, month, day, hours, minutes, seconds, milliseconds * 1000)
    
    # Calculate the total seconds since the Unix epoch
    epoch = datetime(1970, 1, 1)
    total_seconds = (dt - epoch) // timedelta(seconds=1)  # Use integer division to get whole seconds
    
    return total_seconds

def VTS(time_in, contractId=''):  # 根据输入时间和合约ID计算交易时段
    RTS, CTS, TSession = [], [], []  # 初始化三个列表，用于存储修正后的时间、收盘时间和交易时段
    opentimet = SessionOpenTime(contractId)  # 获取所有交易时段的开盘时间
    Closetimet = SessionCloseTime(contractId)  # 获取所有交易时段的收盘时间
    for open_time, close_time in zip(opentimet, Closetimet):
        if time_in > open_time:  # 判断输入时间是否在开盘时间之后
            RTS.append(time_in)  # 如果是，加入RTS列表
        else:
            RTS.append(floattime_sum(time_in, 0.24))  # 如果不是，修正时间后加入RTS列表
        
        if close_time > open_time:  # 判断收盘时间是否在开盘时间之后
            CTS.append(close_time)  # 如果是，加入CTS列表
        else:
            CTS.append(floattime_sum(close_time, 0.24))  # 如果不是，修正时间后加入CTS列表
        
        if open_time < RTS[-1] < CTS[-1]:  # 判断修正后的时间是否在交易时段内
            TSession.append(len(RTS) - 1)  # 如果是，加入TSession列表

    if len(TSession) == 1:  # 如果只有一个交易时段
        idx = TSession[0]
        return idx, opentimet[idx], RTS[idx], CTS[idx]  # 返回交易时段和相关时间
    else:
        return -1, time_in, time_in, time_in  # 否则返回默认值
    

def process_new_orders(context,_HTS,_itk,_otk):
    """处理新的开单信号并执行开仓操作"""
    global db_pool, trade_db, pending_orders
    try:
        account_count = len(注册账号)        
        # 检查注册账号和魔术码是否为空
        if account_count == 0 or len(魔术码) == 0:
            LogInfo("错误: 注册账号或魔术码未设置")
            return
            
        # 确保二者长度相同
        if account_count != len(魔术码):
            LogInfo(f"警告: 注册账号数量({account_count})与魔术码数量({len(魔术码)})不一致")
            
        # 创建一个空列表存储所有查询到的订单
        all_orders = []
        
        # 遍历所有注册账号和魔术码对
        LogInfo(f"开始查询 {account_count} 个账号的订单信号")
        
        for i in range(account_count):
            account = int(注册账号[i])
            magic_number = int(魔术码[i])
            
            if not account or not magic_number:
                LogInfo(f"跳过空账号或魔术码: 索引 {i}")
                continue
                
            LogInfo(f"查询账号 {account} 魔术码 {magic_number} 的订单")
            
            # 使用新的原子操作函数，查询并锁定新订单
            new_orders = trade_db.get_and_lock_new_orders(account, magic_number)
            if new_orders:
                LogInfo(f"账号 {account} 发现并锁定 {len(new_orders)} 个新开单信号")
                # 将查询结果添加到总订单列表中
                all_orders.extend(new_orders)
        
        # 如果没有找到任何订单，直接返回
        if not all_orders:
            LogInfo("未发现任何新开单信号")
            return
            
        LogInfo(f"总共发现 {len(all_orders)} 个新开单信号，开始处理")
        
        # 处理每个订单
        for order in all_orders:
            try:
                # 提取订单信息
                order_id = order['IntID']  # 序号=0
                symbol_from_db = order['ahuobi']   # 货币=2
                direction = order['afangxiang']  # 方向=3
                slots = float(order['ashoushu'])  # 手数=4
                order_account = order['kaicangzhuzhanghao']  # 获取订单关联的账号
                order_magic = order['moshuma']  # 获取订单关联的魔术码
                # 检查合约是否存在于订阅的合约列表中
                found_match = False
                for i in range(len(symbol_Id)):
                    # 直接比较subscribed_symbol与数据库中的symbol
                    subscribed_symbol = symbol_Id[i]
                    if subscribed_symbol == symbol_from_db:
                        found_match = True
                        OrderSymbol = subscribed_symbol
                        lots = int(slots * 下单量映射倍数[i]) if i < len(下单量映射倍数) else int(slots)
                        LogInfo(f"处理新开单信号:序号 {order_id} 账号 {order_account} 合约 {OrderSymbol}, 方向 {direction}, 手数 {lots}")
                        # 获取最新价格
                        last_priceA = Close(OrderSymbol, 'T', 0)
                        if len(last_priceA) == 0:
                            LogInfo(f"合约 {OrderSymbol} 无有效价格，跳过")
                            continue
                        last_price = last_priceA[-1] if _HTS==1 else Q_Last(OrderSymbol)
                        if last_price <= 0:
                            LogInfo(f"警告: 合约 {OrderSymbol} 无有效价格，跳过")
                            continue
                        # 执行开仓
                        LogInfo(f"跟踪执行开仓0: 合约 {OrderSymbol}, 方向 {direction}, 手数 {lots} , BKS标记 {trigger_manager.BKS}, SKS标记 {trigger_manager.SKS}")
                        platform_order_id = None
                        if direction == "B" or direction == 1:  # 买入
                            if _HTS==1:
                                ipr=last_price+_itk*PriceTick(OrderSymbol)
                                ret, platform_order_id = trigger_manager.tim_trigger_long(lots, ipr, OrderSymbol)
                            else:
                                ret=0
                                platform_order_id=999999
                                trigger_manager.his_trigger_long(lots, last_price, OrderSymbol)
                            LogInfo(f"买入开仓: 合约 {OrderSymbol}, 手数: {lots}, 价格: {last_price}, 订单号: {platform_order_id}")
                        elif direction == "S" or direction == 2:  # 卖出
                            if _HTS==1:
                                ipr=last_price-_itk*PriceTick(OrderSymbol)
                                ret, platform_order_id = trigger_manager.tim_trigger_short(lots, ipr, OrderSymbol)
                            else:
                                ret=0
                                platform_order_id=999999
                                trigger_manager.his_trigger_short(lots, last_price, OrderSymbol)
                            LogInfo(f"卖出开仓: 合约 {OrderSymbol}, 手数: {lots}, 价格: {last_price}, 订单号: {platform_order_id}")
                        else:
                            LogInfo(f"未知交易方向: {direction}")
                            continue

                        LogInfo(f"跟踪执行开仓1: 合约 {OrderSymbol}, 方向 {direction}, 手数 {lots} , BKS标记 {trigger_manager.BKS}, SKS标记 {trigger_manager.SKS}")
                        
                        # 检查下单请求是否成功发送
                        if ret == 0 and platform_order_id:
                            # 下单请求发送成功，但可能尚未成交
                            current_time = time.strftime("%Y-%m-%d %H:%M:%S")
                            current_day = time.strftime("%m%d")
                            _platform_order_id = current_day + str(platform_order_id)
                            
                            if _HTS == 1:
                                # 实盘交易下，将订单添加到待确认队列中
                                pending_orders[platform_order_id] = {
                                    'db_order_id': order_id,
                                    'magic_number': order_magic,
                                    'symbol': OrderSymbol,
                                    'direction': direction,
                                    'timestamp': time.time(),
                                    'status': 'pending'
                                }
                                # 更新为"已下单"状态(1)，注意订单之前的状态是"处理中"(3)
                                update_result = trade_db.update_order_after_open(
                                    order_id, _platform_order_id, current_time, order_magic, 1
                                )
                                LogInfo(f"订单已下单，等待确认成交: ID={order_id}, 平台订单号={platform_order_id}")
                            else:
                                # 回测模式下，直接更新为"已完成"状态(2)
                                update_result = trade_db.update_order_after_open(
                                    order_id, _platform_order_id, current_time, order_magic, 2
                                )
                                if update_result:
                                    LogInfo(f"订单状态已更新: ID={order_id}, 平台订单号={_platform_order_id}")
                                else:
                                    LogInfo(f"订单状态更新失败: ID={order_id}")
                        else:
                            LogInfo(f"开仓失败: {OrderSymbol}, 方向: {direction}, 手数: {lots}")
                        break  # 找到匹配项后跳出循环
                if not found_match:
                    LogInfo(f"警告: 合约 {symbol_from_db} 不在订阅列表中，跳过")
            except Exception as e:
                LogInfo(f"处理订单时发生错误: {e}, 订单ID: {order.get('IntID', 'unknown')}")
                
    except Exception as e:
        LogInfo(f"处理开单信号时发生错误: {e}")

def check_pending_orders(context):
    """检查待确认订单的成交状态"""
    global pending_orders, trade_db
    
    current_time = time.time()
    orders_to_remove = []
    
    # 定义订单状态常量
    ORDER_FILLED = '6'       # 完全成交
    ORDER_PARTIAL = '5'      # 部分成交
    ORDER_CANCELLED = '9'    # 已撤单
    ORDER_FAILED = 'B'       # 指令失败
    ORDER_INVALID = 'F'      # 无效单
    
    for platform_order_id, order_info in pending_orders.items():
        try:
            # 检查订单是否已超时
            if current_time - order_info['timestamp'] > order_check_timeout:
                LogInfo(f"订单 {platform_order_id} 确认超时，将从待确认队列中移除")
                orders_to_remove.append(platform_order_id)
                continue
                
            # 使用A_OrderStatus检查订单状态
            order_status = A_OrderStatus(platform_order_id)
            LogInfo(f"订单 {platform_order_id} 当前状态: {order_status}")
            
            # 如果订单不存在或状态为空
            if not order_status:
                # 等待下一次检查
                continue
                
            # 订单已成交（完全成交或部分成交）
            if order_status == ORDER_FILLED or order_status == ORDER_PARTIAL:
                # 获取订单信息
                db_order_id = order_info['db_order_id']
                magic_number = order_info['magic_number']
                symbol = order_info['symbol']
                direction = order_info['direction']
                
                # 获取当前时间作为成交时间
                current_time_str = time.strftime("%Y-%m-%d %H:%M:%S")
                current_day = time.strftime("%m%d")
                # 格式化平台订单ID
                sessionId, orderNo=A_GetOrderNo(platform_order_id)
                _platform_order_id = current_day + str(sessionId)
                
                # 更新数据库中的订单状态为"已完成"(2)
                update_result = trade_db.update_order_after_open(
                    db_order_id, _platform_order_id, current_time_str, magic_number, 2
                )
                
                if update_result:
                    LogInfo(f"订单 {platform_order_id} 已成交，数据库已更新为已完成状态")
                    # 重置BKS或SKS标志
                    if direction == "B" or direction == 1:
                        # 重置买入标志
                        LogInfo(f"重置 {symbol} 的BKS标志")
                        trigger_manager.BKS = 0
                    else:
                        # 重置卖出标志
                        LogInfo(f"重置 {symbol} 的SKS标志")
                        trigger_manager.SKS = 0
                    
                    # 标记为已处理，准备移除
                    orders_to_remove.append(platform_order_id)
                else:
                    LogInfo(f"订单 {platform_order_id} 数据库更新失败")
                    
            # 订单已被取消或失败
            elif order_status in [ORDER_CANCELLED, ORDER_FAILED, ORDER_INVALID]:
                LogInfo(f"订单 {platform_order_id} 已取消或失败，状态: {order_status}")
                orders_to_remove.append(platform_order_id)
                
        except Exception as e:
            LogInfo(f"检查订单 {platform_order_id} 状态时出错: {e}")
    
    # 从待确认队列中移除已处理的订单
    for order_id in orders_to_remove:
        pending_orders.pop(order_id, None)

def process_active_orders(context):
    """
    处理当前活跃订单，将新订单添加到数据库并更新已有订单的盈亏信息
    
    使用当前策略关联的唯一交易账户处理所有订单
    """
    global db_pool, trade_db, 注册账号, 魔术码, 平台类型, 隔夜费, 手续费
    
    try:
        # 获取当前策略关联的交易账户
        account_number = A_AccountID()
        if not account_number:
            LogInfo("无法获取当前交易账户信息")
            return
            
        LogInfo(f"当前交易账户: {account_number}")
        
        account_idx = 0
            
        # 获取对应魔术码
        magic_number = 魔术码[account_idx] if account_idx < len(魔术码) else ""
        if not magic_number:
            LogInfo(f"警告: 账户 {account_number} 未设置魔术码，无法处理订单")
            return
            
        LogInfo(f"使用账户 {account_number} 和魔术码 {magic_number} 处理活跃订单")
        
        # 检查所有合约的持仓情况
        for i in range(len(symbol_Id)):
            symbol = symbol_Id[i]
            platform_type = 平台类型[i] if i < len(平台类型) else ""
            
            # 获取当前合约的所有持仓订单
            long_orders = A_BuyOrderNo(symbol)  # 获取多头持仓订单
            short_orders = A_SellOrderNo(symbol)  # 获取空头持仓订单
            
            all_orders = []
            if long_orders is not None:
                all_orders.extend(long_orders)
            if short_orders is not None:
                all_orders.extend(short_orders)
            
            if not all_orders:
                continue
                
            LogInfo(f"合约 {symbol} 发现 {len(all_orders)} 个活跃订单")
            
            # 预先计算费用信息
            overnight_fee = 0.0
            overnight_fee = float(隔夜费[account_idx])
                
            commission_fee = 0.0
            commission_fee = float(手续费[account_idx])
            
            # 遍历处理每个订单
            for order in all_orders:
                try:
                    # 获取订单详情
                    order_number = order
                    order_direction = "BUY" if A_OrderBuyOrSell(order) == "B" else "SELL"
                    
                    # 获取订单成交明细列表
                    filled_details = A_OrderFilledList(order)
                    
                    if not filled_details:
                        LogInfo(f"订单 {order} 无成交明细，跳过处理")
                        continue
                    
                    # 计算总成交量和加权平均成交价
                    total_volume = 0
                    weighted_price_sum = 0
                    earliest_match_time = None
                    
                    for detail in filled_details:
                        match_qty = float(detail.get('MatchQty', 0))
                        match_price = float(detail.get('MatchPrice', 0))
                        match_time_str = detail.get('MatchDateTime', '')
                        
                        # 累加成交量和价格乘积
                        total_volume += match_qty
                        weighted_price_sum += match_price * match_qty
                        
                        # 追踪最早的成交时间
                        if match_time_str:
                            try:
                                match_time = datetime.strptime(match_time_str, "%Y-%m-%d %H:%M:%S")
                                if earliest_match_time is None or match_time < earliest_match_time:
                                    earliest_match_time = match_time
                            except Exception as e:
                                LogInfo(f"解析成交时间出错: {e}, 使用当前时间")
                    
                    # 计算加权平均成交价
                    if total_volume > 0:
                        order_open_price = weighted_price_sum / total_volume
                    else:
                        order_open_price = 0
                        
                    # 使用总成交量
                    order_volume = total_volume
                    
                    # 获取成交时间，如果解析失败则使用当前时间
                    if earliest_match_time:
                        order_open_time = earliest_match_time.strftime("%Y-%m-%d %H:%M:%S")
                    else:
                        order_open_time = time.strftime("%Y-%m-%d %H:%M:%S")
                    
                    LogInfo(f"订单 {order} 成交明细: 方向={order_direction}, 总量={order_volume}, 均价={order_open_price}, 时间={order_open_time}")
                    
                    # 计算当前盈亏
                    current_price = Q_Last(symbol)
                    if current_price <= 0:
                        LogInfo(f"合约 {symbol} 无有效价格，跳过计算盈亏")
                        continue
                        
                    if order_direction == "BUY":
                        profit_points = current_price - order_open_price
                    else:  # SELL
                        profit_points = order_open_price - current_price
                        
                    # 获取点值和合约乘数
                    point_value = ContractUnit(symbol)  # 点值
                    contract_multiplier = PriceTick(symbol)  # 价格最小变动单位
                    
                    if point_value == 0:
                        LogInfo(f"合约 {symbol} 点值为0，跳过计算盈亏")
                        continue
                        
                    # 计算盈亏金额
                    profit_amount = profit_points * order_volume * point_value
                    
                    # 净盈亏 = 盈亏金额 - 手续费 - 隔夜费
                    net_profit = profit_amount - commission_fee - overnight_fee
                    
                    # 构建唯一的订单标识 (添加日期前缀确保唯一性)
                    unique_order_id = time.strftime("%Y-%m-%d@") + str(order_number)
                    
                    # 检查订单是否已存在于数据库
                    order_exists = trade_db.check_order_exists(unique_order_id, magic_number)
                    
                    if not order_exists:
                        # 订单不存在，添加到数据库
                        LogInfo(f"添加新订单到数据库: {unique_order_id}, 合约: {symbol}, 方向: {order_direction}, 手数: {order_volume}")
                        
                        trade_db.add_active_order(
                            account_number, magic_number, unique_order_id, symbol, 
                            order_direction, order_volume, order_open_price, 
                            current_price, profit_amount, order_open_time, 
                            platform_type, overnight_fee, commission_fee, net_profit
                        )
                    else:
                        # 订单已存在，更新盈亏信息
                        trade_db.update_order_profit(
                            unique_order_id, magic_number, current_price, 
                            profit_amount, overnight_fee, commission_fee, net_profit
                        )
                except Exception as e:
                    LogInfo(f"处理订单 {order} 时发生错误: {e}")
    
    except Exception as e:
        LogInfo(f"处理活跃订单时发生错误: {e}")

def check_closed_orders(context):
    """检查是否有订单已经平仓，更新数据库状态"""
    global db_pool, trade_db, 注册账号, 魔术码, 平台类型
    
    try:
        # 遍历所有账号、魔术码和平台类型组合
        account_count = min(len(注册账号), len(魔术码), len(平台类型))
        
        for i in range(account_count):
            account = 注册账号[i] if i < len(注册账号) else ""
            magic_number = 魔术码[i] if i < len(魔术码) else ""
            platform = 平台类型[i] if i < len(平台类型) else ""
            
            if not account or not magic_number or not platform:
                continue
                
            # 查询未平仓订单
            open_orders = trade_db.get_open_orders(account, magic_number, platform)
            if not open_orders:
                continue
                
            LogInfo(f"账号 {account} 在平台 {platform} 有 {len(open_orders)} 个未平仓订单")
            
            # 创建当前持仓订单号集合用于快速查找
            active_order_ids = set()
            
            # 获取所有合约的持仓订单号
            for j in range(len(symbol_Id)):
                symbol = symbol_Id[j]
                
                # 获取多头和空头持仓
                long_orders = A_BuyOrderNo(symbol)
                short_orders = A_SellOrderNo(symbol)
                
                # 收集所有活跃订单ID
                if long_orders:
                    for order in long_orders:
                        active_order_ids.add(order)
                        
                if short_orders:
                    for order in short_orders:
                        active_order_ids.add(order)
            
            # 检查每个未平仓订单是否还存在持仓
            for order in open_orders:
                order_id = order['tongyibiaoshi']  # 使用统一标识作为订单号
                
                # 如果订单不在活跃订单中，将其标记为已平仓
                if order_id not in active_order_ids:
                    LogInfo(f"发现已平仓订单: {order_id}，但在数据库中标记为未平仓，现在更新")
                    trade_db.update_closed_order(order_id, magic_number)
    
    except Exception as e:
        LogInfo(f"检测平仓订单时发生错误: {e}")

def close_orders_from_db(context, HTS, otk):
    """根据数据库中的标记执行平仓操作"""
    global db_pool, trade_db, 注册账号, 魔术码, 平台类型, trigger_manager
    
    try:
        # 遍历所有账号、魔术码和平台类型组合
        account_count = min(len(注册账号), len(魔术码), len(平台类型))
        
        for i in range(account_count):
            account = 注册账号[i] if i < len(注册账号) else ""
            magic_number = 魔术码[i] if i < len(魔术码) else ""
            platform = 平台类型[i] if i < len(平台类型) else ""
            
            if not account or not magic_number or not platform:
                continue
                
            # 查询需要平仓的订单(每次只取一个)
            orders_to_close = trade_db.get_orders_to_close(account, magic_number, platform)
            if not orders_to_close:
                continue
                
            # 一次只处理一个订单
            order = orders_to_close[0]
            order_id = order['tongyibiaoshi']  # 使用统一标识作为订单号
            LogInfo(f"发现需要平仓的订单: {order_id}")
            
            # 获取当前所有活跃订单
            active_orders = {}
            for j in range(len(symbol_Id)):
                symbol = symbol_Id[j]
                
                # 获取多头和空头持仓
                long_orders = A_BuyOrderNo(symbol)
                short_orders = A_SellOrderNo(symbol)
                
                # 收集所有活跃订单
                if long_orders:
                    for order_no in long_orders:
                        active_orders[str(order_no)] = {
                            'symbol': symbol,
                            'direction': 'BUY',
                            'volume': A_OrderFilledLot(order_no)  # 获取实际成交量
                        }
                        
                if short_orders:
                    for order_no in short_orders:
                        active_orders[str(order_no)] = {
                            'symbol': symbol,
                            'direction': 'SELL',
                            'volume': A_OrderFilledLot(order_no)  # 获取实际成交量
                        }
            
            try:
                # 提取订单ID的纯数字部分（如果有日期前缀）
                pure_order_id = order_id
                if '@' in order_id:
                    pure_order_id = order_id.split('@')[1]
                
                # 检查订单是否存在于活跃订单中
                if pure_order_id not in active_orders:
                    LogInfo(f"订单 {order_id} 不在活跃订单中，可能已被平仓")
                    trade_db.update_closed_order(order_id, magic_number)
                    continue
                
                # 获取订单信息
                active_order = active_orders[pure_order_id]
                symbol = active_order['symbol']
                direction = active_order['direction']
                volume = active_order['volume']
                
                # 计算平仓价格
                tick_size = PriceTick(symbol)
                if direction == 'BUY':
                    # 平多头，需要卖出价格(通常低于当前价)
                    long_price = max(Q_BidPrice(symbol) - otk * tick_size, Q_LowLimit(symbol))
                    short_price = 0  # 不使用
                else:  # SELL
                    # 平空头，需要买入价格(通常高于当前价)
                    short_price = min(Q_AskPrice(symbol) + otk * tick_size, Q_UpperLimit(symbol))
                    long_price = 0  # 不使用
                
                # 执行平仓操作
                if direction == 'BUY':
                    LogInfo(f"平仓多头订单: {order_id}, 合约: {symbol}, 手数: {volume}")
                    if HTS == 1:
                        # 使用trigger_manager执行实时平仓
                        orders_info = trigger_manager.tim_trigger_Exit(False, True, long_price, short_price, volume, symbol)
                        ret = any(info[0] for info in orders_info) if orders_info else False
                    else:
                        # 使用trigger_manager执行历史平仓
                        ret = trigger_manager.his_trigger_Exit(False, True, long_price, short_price, volume, symbol)
                else:  # SELL
                    LogInfo(f"平仓空头订单: {order_id}, 合约: {symbol}, 手数: {volume}")
                    if HTS == 1:
                        # 使用trigger_manager执行实时平仓
                        orders_info = trigger_manager.tim_trigger_Exit(True, False, long_price, short_price, volume, symbol)
                        ret = any(info[0] for info in orders_info) if orders_info else False
                    else:
                        # 使用trigger_manager执行历史平仓
                        ret = trigger_manager.his_trigger_Exit(True, False, long_price, short_price, volume, symbol)
                
                # 更新订单状态为已平仓
                if ret:
                    trade_db.update_closed_order(order_id, magic_number)
                    LogInfo(f"订单 {order_id} 已成功平仓，更新状态完成")
                else:
                    LogInfo(f"订单 {order_id} 平仓失败")
                    
            except Exception as e:
                LogInfo(f"平仓订单 {order_id} 时发生错误: {e}")
    
    except Exception as e:
        LogInfo(f"从数据库平仓订单时发生错误: {e}")

def update_risk_control_info(context):
    """更新当前账户风控信息到数据库"""
    global db_pool, trade_db, 注册账号, 平台类型
    
    try:
        # 获取当前策略关联的唯一账户ID
        account_number = A_AccountID()
        if not account_number:
            LogInfo("无法获取当前账户信息")
            return
            
        LogInfo(f"获取账户 {account_number} 的风控信息")
        
        # 获取当前时间
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 在注册账号中查找匹配的平台信息
        platform = "未知平台"
        for i in range(len(注册账号)):
            if normalize_account_number(account_number) == normalize_account_number(注册账号[i]):
                platform = 平台类型[i] if i < len(平台类型) else "未知平台"
                break
                
        # 获取账户余额和净值（直接调用，无需传递参数）
        balance = A_Available()  # 账户可用资金
        equity = A_Assets()      # 账户总资产(净值)
        
        LogInfo(f"账号 {account_number} 当前余额: {balance}, 净值: {equity}")
        _account_number = normalize_account_number(account_number)
        
        # 查询账号是否已存在于风控表中
        existing_record = trade_db.check_risk_control_account(_account_number)
        
        if not existing_record:
            # 账号不存在，插入新记录
            trade_db.insert_risk_control(_account_number, platform, balance, equity, current_time)
            LogInfo(f"账号 {account_number} 风控信息已插入")
        else:
            # 账号已存在，更新记录
            trade_db.update_risk_control(_account_number, balance, equity, current_time)
            LogInfo(f"账号 {account_number} 风控信息已更新")
                
    except Exception as e:
        LogInfo(f"更新风控信息时发生错误: {e}")

def check_chase_orders(context, HTS, otk):
    """检测需要根据追单条件平仓的订单"""
    global db_pool, trade_db, 注册账号, 魔术码, 追单次数, 追单秒数, trigger_manager
    
    try:
        # 获取当前北京时间
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        current_timestamp = time.mktime(time.strptime(current_time, "%Y-%m-%d %H:%M:%S"))
        
        # 获取当前交易账户
        account_number = A_AccountID()
        if not account_number:
            LogInfo("无法获取当前账户信息")
            return
            
        # 查找对应的魔术码和追单设置
        account_idx = -1
        for i in range(len(注册账号)):
            if str(normalize_account_number(account_number)) == str(注册账号[i]):
                account_idx = i
                break
                
        if account_idx == -1:
            LogInfo(f"账户 {account_number} 未在配置中找到，使用默认索引0")
            account_idx = 0
            
        magic_number = 魔术码[account_idx] if account_idx < len(魔术码) else ""
        max_chase_count = int(追单次数[account_idx]) if account_idx < len(追单次数) else 3  # 默认追单次数为3
        chase_seconds = int(追单秒数[account_idx]) if account_idx < len(追单秒数) else 300  # 默认追单秒数为300（5分钟）
        
        if not magic_number:
            LogInfo(f"账户 {account_number} 未设置魔术码，无法处理追单")
            return
                
        # 查询追单信息
        chase_orders = trade_db.get_chase_orders_info(account_number, magic_number)
        if not chase_orders:
            return
            
        LogInfo(f"检测到 {len(chase_orders)} 个追单记录")
        
        # 获取当前所有活跃订单
        active_orders = {}
        for j in range(len(symbol_Id)):
            symbol = symbol_Id[j]
            
            # 获取多头和空头持仓
            long_orders = A_BuyOrderNo(symbol)
            short_orders = A_SellOrderNo(symbol)
            
            # 收集所有活跃订单
            if long_orders:
                for order_no in long_orders:
                    active_orders[str(order_no)] = {
                        'symbol': symbol,
                        'direction': 'BUY',
                        'volume': A_OrderFilledLot(order_no)  # 获取实际成交量
                    }
                    
            if short_orders:
                for order_no in short_orders:
                    active_orders[str(order_no)] = {
                        'symbol': symbol,
                        'direction': 'SELL',
                        'volume': A_OrderFilledLot(order_no)  # 获取实际成交量
                    }
        
        # 检查每个追单记录
        for order in chase_orders:
            # 提取订单ID的纯数字部分（如果有日期前缀）
            order_id = order['tongyibiaoshi']
            pure_order_id = order_id
            if '@' in order_id:
                pure_order_id = order_id.split('@')[1]
                
            count = order['Ocount']
            chase_count = order['zhuidancishu']
            
            # 检查增加时间
            if 'zengjiashijian' in order and order['zengjiashijian']:
                add_time_str = order['zengjiashijian']
                try:
                    # 解析时间字符串为时间戳
                    add_time = time.mktime(time.strptime(add_time_str, "%Y-%m-%d %H:%M:%S"))
                    time_diff = current_timestamp - add_time
                    
                    LogInfo(f"订单 {order_id} 距离增加时间已过 {time_diff} 秒，追单次数 {chase_count}")
                    
                    # 条件1: 检查时间差是否超过追单秒数
                    if count == 1 and time_diff >= chase_seconds:
                        LogInfo(f"订单 {order_id} 距离增加时间 {time_diff} 秒超过设定的 {chase_seconds} 秒，准备平仓")
                        close_chase_order(order_id, pure_order_id, active_orders, magic_number, HTS, otk)
                        continue
                except Exception as e:
                    LogInfo(f"解析订单 {order_id} 的时间时发生错误: {e}")
            
            # 条件2: 检查追单次数是否超过设定值
            if chase_count is not None and chase_count >= max_chase_count:
                LogInfo(f"订单 {order_id} 追单次数 {chase_count} 已达到或超过设定的 {max_chase_count} 次，准备平仓")
                close_chase_order(order_id, pure_order_id, active_orders, magic_number, HTS, otk)
    
    except Exception as e:
        LogInfo(f"检测追单平仓时发生错误: {e}")

def close_chase_order(order_id, pure_order_id, active_orders, magic_number, HTS, otk):
    """平仓追单订单"""
    global trade_db, trigger_manager
    
    try:
        # 检查订单是否存在于活跃订单中
        if pure_order_id not in active_orders:
            LogInfo(f"订单 {order_id} 不在活跃订单中，可能已被平仓")
            trade_db.update_closed_order(order_id, magic_number)
            return
        
        # 获取订单信息
        active_order = active_orders[pure_order_id]
        symbol = active_order['symbol']
        direction = active_order['direction']
        volume = active_order['volume']
        
        # 计算平仓价格
        tick_size = PriceTick(symbol)
        if direction == 'BUY':
            # 平多头，需要卖出价格(通常低于当前价)
            long_price = max(Q_BidPrice(symbol) - otk * tick_size, Q_LowLimit(symbol))
            short_price = 0  # 不使用
        else:  # SELL
            # 平空头，需要买入价格(通常高于当前价)
            short_price = min(Q_AskPrice(symbol) + otk * tick_size, Q_UpperLimit(symbol))
            long_price = 0  # 不使用
        
        # 执行平仓操作
        if direction == 'BUY':
            LogInfo(f"平仓追单多头订单: {order_id}, 合约: {symbol}, 手数: {volume}")
            if HTS == 1:
                # 使用trigger_manager执行实时平仓
                orders_info = trigger_manager.tim_trigger_Exit(False, True, long_price, short_price, volume, symbol)
                ret = any(info[0] for info in orders_info) if orders_info else False
            else:
                # 使用trigger_manager执行历史平仓
                ret = trigger_manager.his_trigger_Exit(False, True, long_price, short_price, volume, symbol)
        else:  # SELL
            LogInfo(f"平仓追单空头订单: {order_id}, 合约: {symbol}, 手数: {volume}")
            if HTS == 1:
                # 使用trigger_manager执行实时平仓
                orders_info = trigger_manager.tim_trigger_Exit(True, False, long_price, short_price, volume, symbol)
                ret = any(info[0] for info in orders_info) if orders_info else False
            else:
                # 使用trigger_manager执行历史平仓
                ret = trigger_manager.his_trigger_Exit(True, False, long_price, short_price, volume, symbol)
        
        # 更新订单状态为已平仓
        if ret:
            trade_db.update_closed_order(order_id, magic_number)
            LogInfo(f"追单订单 {order_id} 已成功平仓，更新状态完成")
        else:
            LogInfo(f"追单订单 {order_id} 平仓失败")
            
    except Exception as e:
        LogInfo(f"平仓追单订单 {order_id} 时发生错误: {e}")

def A_BuyOrderNo(contractNo):
    """
    获取指定合约的所有多头持仓订单
    
    参数:
        contractNo (str): 合约代码
        
    返回:
        list: 多头持仓订单对象列表
    """
    try:
        all_orders = A_AllOrderNo(contractNo)
        buy_position_orders = []
        
        for order_id in all_orders:
            # 检查是否为开仓
            entry_status = A_OrderEntryOrExit(order_id)
            # 检查是否为买入
            buy_status = A_OrderBuyOrSell(order_id)
            
            # 如果是开仓买入，则添加到多头持仓列表
            if entry_status == 'O' and buy_status == 'B':
                # 获取订单号并添加到列表
                buy_position_orders.append(order_id)
        
        return buy_position_orders
    except Exception as e:
        LogInfo(f"获取多头持仓订单时发生错误: {e}")
        return []

def A_SellOrderNo(contractNo):
    """
    获取指定合约的所有空头持仓订单
    
    参数:
        contractNo (str): 合约代码
        
    返回:
        list: 空头持仓订单对象列表
    """
    try:
        all_orders = A_AllOrderNo(contractNo)
        sell_position_orders = []
        
        for order_id in all_orders:
            # 检查是否为开仓
            entry_status = A_OrderEntryOrExit(order_id)
            # 检查是否为卖出
            sell_status = A_OrderBuyOrSell(order_id)
            
            # 如果是开仓卖出，则添加到空头持仓列表
            if entry_status == 'O' and sell_status == 'S':
                # 获取订单号并添加到列表
                sell_position_orders.append(order_id)
        
        return sell_position_orders
    except Exception as e:
        LogInfo(f"获取空头持仓订单时发生错误: {e}")
        return []

def normalize_account_number(account_str):
    """
    将账户名标准化为整数：
    - 如果账户名第一位是字母，只提取数字部分
    - 如果全是数字，直接转为整数
    
    参数:
        account_str: 账户名字符串
    
    返回:
        int: 转换后的账户号码
    """
    try:
        # 转为字符串以确保可以处理
        account_str = str(account_str).strip()
        
        # 如果为空则返回0
        if not account_str:
            return 0
            
        # 检查第一个字符是否为字母
        if account_str[0].isalpha():
            # 提取字母后的所有字符
            digits_part = account_str[1:]
            # 过滤出数字部分
            digits_only = ''.join(c for c in digits_part if c.isdigit())
            return int(digits_only) if digits_only else 0
        else:
            # 过滤出数字部分
            digits_only = ''.join(c for c in account_str if c.isdigit())
            return int(digits_only) if digits_only else 0
    except Exception as e:
        LogInfo(f"账户名转换错误: {e}, 原始值: {account_str}")
        return 0
    
def update_contract_prices(symbols, platform_types, price_update_settings, price_table, db, prices_dict, is_realtime=True):
    """
    更新合约价格到数据库
    
    参数:
        symbols: 合约列表
        platform_types: 平台类型列表
        price_update_settings: 价格更新启用标志列表
        price_table: 价格表名称
        db: 数据库操作对象
        prices_dict: 价格字典，格式为 {symbol: price}
        is_realtime: 是否为实盘模式，默认为True
    """
    updated_count = 0
    error_count = 0
    
    try:
        for i in range(len(symbols)):
            symbol = symbols[i]
            
            # 获取对应的外盘映射和平台类型
            platform_type = platform_types[i] if i < len(platform_types) else ""
            current_setting = price_update_settings[i] if i < len(price_update_settings) else ""
            price_update_enabled = (current_setting or current_setting == "TRUE" or 
                                   current_setting == "1" or current_setting == "YES")
            
            # 如果不启用价格更新或无平台类型，则跳过
            if not price_update_enabled or not platform_type:
                continue
                
            # 从传入的价格字典中获取价格
            if symbol not in prices_dict:
                LogInfo(f"合约 {symbol} 无有效价格数据，跳过")
                continue
                
            last_price = prices_dict[symbol]
            
            try:
                # 将价格格式化为字符串，保留适当的小数位数
                formatted_price = f"{last_price:.2f}"
                
                # 更新数据库中的价格
                update_result = db.update_price(price_table, symbol, formatted_price, platform_type)
                
                if update_result:
                    updated_count += 1
                
            except Exception as e:
                error_count += 1
                LogInfo(f"更新价格失败: {symbol}, {platform_type}, 错误: {e}")
                
        if updated_count > 0:
            LogInfo(f"价格更新完成: 成功更新 {updated_count} 个合约价格, 失败 {error_count} 个")
            
    except Exception as e:
        LogInfo(f"价格更新过程中发生错误: {e}")