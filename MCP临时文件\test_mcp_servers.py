#!/usr/bin/env python3
"""
测试MCP服务器
验证已安装的MCP服务器是否能正常启动和响应
"""

import asyncio
import json
import subprocess
import sys
import time
from pathlib import Path

def test_package_imports():
    """测试包导入"""
    print("=== 测试包导入 ===")
    
    packages = {
        "graphiti_core": "Graphiti Core",
        "opik": "Opik",
        "ragie": "Ragie", 
        "mcp": "MCP Core",
        "jupyter_mcp_server": "Jupyter MCP Server"
    }
    
    results = {}
    for package, name in packages.items():
        try:
            __import__(package)
            print(f"✅ {name}: 导入成功")
            results[package] = True
        except ImportError as e:
            print(f"❌ {name}: 导入失败 - {e}")
            results[package] = False
    
    return results

def test_jupyter_mcp_command():
    """测试Jupyter MCP命令"""
    print("\n=== 测试Jupyter MCP命令 ===")
    
    try:
        result = subprocess.run(
            ["jupyter-mcp-server", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            print("✅ Jupyter MCP Server: 命令可用")
            return True
        else:
            print(f"❌ Jupyter MCP Server: 命令失败 - {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ Jupyter MCP Server: 命令超时")
        return False
    except FileNotFoundError:
        print("❌ Jupyter MCP Server: 命令未找到")
        return False
    except Exception as e:
        print(f"❌ Jupyter MCP Server: 错误 - {e}")
        return False

def test_mcp_server_syntax(server_path):
    """测试MCP服务器脚本语法"""
    server_name = Path(server_path).stem
    print(f"\n=== 测试 {server_name} 语法 ===")
    
    try:
        result = subprocess.run(
            [sys.executable, "-m", "py_compile", server_path],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            print(f"✅ {server_name}: 语法检查通过")
            return True
        else:
            print(f"❌ {server_name}: 语法错误 - {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {server_name}: 检查失败 - {e}")
        return False

async def test_mcp_server_startup(server_path, timeout=5):
    """测试MCP服务器启动"""
    server_name = Path(server_path).stem
    print(f"\n=== 测试 {server_name} 启动 ===")
    
    try:
        # 启动服务器进程
        process = await asyncio.create_subprocess_exec(
            sys.executable, server_path,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        # 等待一小段时间看是否能正常启动
        try:
            await asyncio.wait_for(process.wait(), timeout=timeout)
            # 如果进程立即退出，检查是否有错误
            stderr = await process.stderr.read()
            if stderr:
                print(f"❌ {server_name}: 启动失败 - {stderr.decode()}")
                return False
            else:
                print(f"✅ {server_name}: 启动成功（进程正常退出）")
                return True
        except asyncio.TimeoutError:
            # 超时意味着进程还在运行，这是好的
            print(f"✅ {server_name}: 启动成功（进程运行中）")
            process.terminate()
            await process.wait()
            return True
            
    except Exception as e:
        print(f"❌ {server_name}: 启动测试失败 - {e}")
        return False

def check_config_file():
    """检查配置文件"""
    print("\n=== 检查配置文件 ===")
    
    config_path = Path("augment_mcp_config.json")
    if not config_path.exists():
        print("❌ 配置文件不存在: augment_mcp_config.json")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if "mcpServers" not in config:
            print("❌ 配置文件缺少 mcpServers 部分")
            return False
        
        servers = config["mcpServers"]
        print(f"✅ 配置文件包含 {len(servers)} 个服务:")
        for name in servers.keys():
            print(f"   - {name}")
        
        return True
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 MCP服务器测试工具")
    print("=" * 50)
    
    # 测试包导入
    import_results = test_package_imports()
    
    # 测试Jupyter MCP命令
    jupyter_result = test_jupyter_mcp_command()
    
    # 检查配置文件
    config_result = check_config_file()
    
    # 测试MCP服务器脚本
    server_dir = Path("mcp_servers")
    if not server_dir.exists():
        print(f"\n❌ MCP服务器目录不存在: {server_dir}")
        return False
    
    server_files = list(server_dir.glob("*_server.py"))
    if not server_files:
        print(f"\n❌ 未找到MCP服务器脚本在: {server_dir}")
        return False
    
    syntax_results = []
    startup_results = []
    
    for server_file in server_files:
        # 语法检查
        syntax_ok = test_mcp_server_syntax(server_file)
        syntax_results.append(syntax_ok)
        
        # 启动测试（仅当语法正确时）
        if syntax_ok:
            startup_ok = await test_mcp_server_startup(server_file)
            startup_results.append(startup_ok)
        else:
            startup_results.append(False)
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    total_tests = len(import_results) + 1 + 1 + len(syntax_results) + len(startup_results)
    passed_tests = (
        sum(import_results.values()) + 
        int(jupyter_result) + 
        int(config_result) + 
        sum(syntax_results) + 
        sum(startup_results)
    )
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！MCP服务器已准备就绪。")
        return True
    else:
        print(f"\n⚠️  有 {total_tests - passed_tests} 个测试失败，请检查上述错误信息。")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 测试过程中发生错误: {e}")
        sys.exit(1)
