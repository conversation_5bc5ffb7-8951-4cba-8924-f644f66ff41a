import time
import talib as ta
import numpy as np 
import numba as nb 
import pandas as pd
from collections import deque
g_params['信号合约'] = 'SHFE|Z|RB|MAIN'     # 信号计算的合约品种,一般用指数
g_params['K线基础时间'] = 'M'     # k线基础时间,tick和秒填"T",分钟填"M",日线填"D"
g_params['K线基础周期'] =  5      # k线周期,K线基础时间的倍数,15分钟填写"15"

g_params['交易方向开关']  = 0  #大于0开多，小于0开空，等于0双向开仓
g_params['下单量']  = 1 #下单量
g_params['下单超价跳数']  = 0  #下单超价跳数
g_params['止盈跳数'] = 30     
g_params['止损跳数'] = 20      
g_params['止损模式开关'] = 1  #大于0时中周期止损，否则小周期止损      
g_params['收盘倒计时分钟数设置'] = 5#距离收盘时间范围不开仓，并清仓    
g_params['订阅数据长度'] = 2000     #数据订阅数据长度

class CHL:
    def __init__(self, length, WindowLen=2, shift=1):
        """
        初始化CHL类，用于寻找和记录K线的高低点
        
        参数:
        length: 记录点的最大长度
        WindowLen: 窗口大小，默认为2
        shift: 偏移量，默认为1
        """
        self.status = 0       # 状态标志：1为找到高点，-1为找到低点，0为未找到
        self.length = length  # 记录的最大长度
        self.WindowLen = WindowLen  # 窗口大小
        self.shift = -shift   # 负数偏移量
        
        # 初始化队列，用于存储高点索引、低点索引、高点值和低点值
        self.HIndex = deque([-1]*self.length, maxlen=self.length)  # 高点索引
        self.LIndex = deque([-1]*self.length, maxlen=self.length)  # 低点索引
        self.HValue = deque([-1]*self.length, maxlen=self.length)  # 高点值
        self.LValue = deque([-1]*self.length, maxlen=self.length)  # 低点值
        
    def NewK(self, Hi, Li, Ii):
        """
        处理新的K线数据，寻找高低点
        
        参数:
        Hi: 高点序列
        Li: 低点序列
        Ii: 当前可用的数据长度
        """
        # 重置状态
        self.status = 0
        
        # 计算窗口大小和范围
        window = self.WindowLen * 2 - 1
        start_idx = self.shift - (window - 1)
        end_idx = self.shift + 1
        
        # 边界检查
        if start_idx < -Ii:
            # 如果起始索引超出了可用数据范围，则调整
            start_idx = -Ii   
        # 边界检查 - 确保end_idx在合理范围内
        if end_idx > 0:
            end_idx = 0
            
        # 防止索引越界
        if len(Hi) <= abs(start_idx) or len(Li) <= abs(start_idx):
            return self.status
            
        # 检查当前shift索引是否有效
        target_index = self.shift
        if target_index < -len(Hi) or target_index >= 0:
            return self.status
            
        # 获取窗口内的数据
        try:
            hi_window = Hi[start_idx:end_idx]
            li_window = Li[start_idx:end_idx]
            
            # 判断窗口数据是否为空
            if len(hi_window) == 0 or len(li_window) == 0:
                return self.status
                
            # 计算最大值和最小值
            _max = max(hi_window)
            _min = min(li_window)
            
            # 检查当前K线是否是最高点或最低点
            is_high = Hi[target_index] == _max
            is_low = Li[target_index] == _min
            
            # 高点和低点不会同时出现，优先判断高点
            if is_high:
                self.status = 1
                self.HIndex.append(self.shift)
                self.HValue.append(_max)
            elif is_low:
                self.status = -1
                self.LIndex.append(self.shift)
                self.LValue.append(_min)
        except IndexError:
            # 捕获任何可能的索引错误
            pass
                    
        return self.status
    
    def GetHData(self):
        """获取高点值序列"""
        return self.HValue
        
    def GetHIndex(self):
        """获取高点索引序列"""
        return self.HIndex
        
    def GetLData(self):
        """获取低点值序列"""
        return self.LValue
        
    def GetLIndex(self):
        """获取低点索引序列"""
        return self.LIndex
        
    def GetStatus(self):
        """获取当前状态"""
        return self.status
        
    def GetData(self):
        """获取高低点数据，兼容旧接口"""
        return self.HValue, self.LValue
        
    def GetIndex(self):
        """获取索引，兼容旧接口"""
        indexes = []
        for i in range(min(len(self.HIndex), len(self.LIndex))):
            if self.HIndex[i] != -1:
                indexes.append(self.HIndex[i])
            elif self.LIndex[i] != -1:
                indexes.append(self.LIndex[i])
        return deque(indexes, maxlen=self.length)


sCHL1,mCHL1=None,None 
scode,tcode,k_btime,k_cycle,m_btime,m_cycle,l_btime,l_cycle,MM_CYCLE="","",0,0,0,0,0,0,0
trade_sw,lots_o1,ovprice_tick,stopwin_tick,stoplost_tick,stoplost_mode_sw,ops,CloseTime=0,0,0,0,0,0,0,0
def initialize(context): 
    global g_params,sCHL1,mCHL1,scode,tcode,k_btime,k_cycle,m_btime,m_cycle,l_btime,l_cycle,MM_CYCLE
    global trade_sw,lots_o1,ovprice_tick,stopwin_tick,stoplost_tick,stoplost_mode_sw,ops,CloseTime
    scode   = g_params['信号合约']    # 信号品种取参数
    tcode = scode
    k_btime = g_params['K线基础时间'] # k线基础时间取参数
    k_cycle = g_params['K线基础周期'] # k线基础周期取参数
    m_btime = g_params['中周期k线时间']
    m_cycle = g_params['中周期k线周期']
    l_btime = g_params['大周期k线时间']
    l_cycle = g_params['大周期k线周期']
    MM_CYCLE= g_params['高低点统计周期']
    trade_sw=g_params['交易方向开关']    #大于0开多，小于0开空，等于0双向开仓
    lots_o1= g_params['下单量']          #  下单量
    ovprice_tick =g_params['下单超价跳数']    #下单超价跳数
    stopwin_tick =g_params['止盈跳数']
    stoplost_tick=g_params['止损跳数']
    stoplost_mode_sw= g_params['高低点止损模式开关']
    ops = g_params['平仓后是否退出策略'] 
    CloseTime = g_params['收盘倒计时分钟数设置']
    sCHL1=CHL(MM_CYCLE*2)
    mCHL1=CHL(MM_CYCLE*2)
    sublength = g_params['订阅数据长度']
    AL=600 #计算回溯数据长度        
    SetBarInterval(scode, k_btime, k_cycle,sublength,sublength) #订阅信号合约    
    SetBarInterval(scode, m_btime, m_cycle,sublength,sublength) #订阅中周期信号合约
    SetBarInterval(scode, l_btime, l_cycle,sublength,sublength) #订阅大周期信号合约
    SetActual()           #设置实盘运行
    SetOrderWay(1)        #设置K线走完后发单
    SetTriggerType(5)     #设置K线触发

# 策略触发事件每次触发时都会执行该函数
    
HCS,sBARS,mBARS,lBARS,buys,sells=deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3)
TDS,BKS,SKS,BPS,SPS,HH,LL,mHH,mLL,hh_stop,ll_stop,HS,LS,m_OVO,l_OVO,_MP,_BuyPosition,_SellPosition,_AVP,BT1,ST1=0,0,0,0,0,0,999999999,0,999999999,0,0,0,0,0,0,0,0,0,0,0,0
def handle_data(context):
    global TDS,BKS,SKS,BPS,SPS,HH,LL,mHH,mLL,hh_stop,ll_stop,HS,LS,m_OVO,l_OVO,_MP,_BuyPosition,_SellPosition,_AVP,BT1,ST1
    O=Open( scode, k_btime, k_cycle)     # 信号合约开盘价格
    H=High( scode, k_btime, k_cycle)     # 信号合约最高价格
    L=Low(  scode, k_btime, k_cycle)     # 信号合约最低价格
    C=Close(scode, k_btime, k_cycle)      # 信号合约收盘价格

    mO=Open( scode, m_btime, m_cycle)   # 中周期k线开盘价
    mH=High( scode, m_btime, m_cycle)   # 中周期k线最高价
    mL=Low(  scode, m_btime, m_cycle)   # 中周期k线最低价
    # mC=Close(scode, m_btime, m_cycle)   # 中周期k线收盘价

    # lO=Open( scode, l_btime, l_cycle)    # 大周期k线开盘价
    # lH=High( scode, l_btime, l_cycle)   # 大周期k线最高价
    # lL=Low(  scode, l_btime, l_cycle)   # 大周期k线最低价
    # lC=Close(scode, l_btime, l_cycle)   # 大周期k线收盘价
    MINPRICE=PriceTick(scode)             # 合约最小变动价

    #if context.strategyStatus() == 'C':
    #    buyprice  = min(Q_AskPrice(tcode)+ovprice_tick*MINPRICE, Q_UpperLimit(tcode))  # 买超价
    #    sellprice = max(Q_BidPrice(tcode)-ovprice_tick*MINPRICE, Q_LowLimit(tcode))    # 卖超价
    #else:
    #    buyprice  = C[-1]+ovprice_tick*MINPRICE  # 买超价
    #    sellprice = C[-1]-ovprice_tick*MINPRICE  # 卖超价
    #KDKDKDKDKDKDKDKDKDKDKDKDKDKDKDKDKDKDKDKDKDKDKD//


    #指数计算指标
    if  len(C)<MM_CYCLE*2:
       return    
    HTS=1 if context.strategyStatus()=="C" else 0   
    s_CurrentBar=CurrentBar(scode, k_btime, k_cycle)
    m_CurrentBar=CurrentBar(scode, m_btime, m_cycle)
    l_CurrentBar=CurrentBar(scode, l_btime, l_cycle)   
    sBARS.append(s_CurrentBar)
    mBARS.append(m_CurrentBar)
    lBARS.append(l_CurrentBar)
    if  sBARS[0]>0 and sBARS[1]<sBARS[2]:
        # if BKS==1 and A_BuyPosition(tcode)==0: BKS=0
        # if SKS==1 and A_SellPosition(tcode)==0: SKS=0 
        BKS=0
        SKS=0
        BPS=0
        SPS=0 
        sCHL1.NewK(H,L,s_CurrentBar)  
        LogInfo(len(HH),HH[-1],LL[-1])
         
    HH,LL=sCHL1.GetData()
    PlotNumeric("高点",HH[-1],0xff0000,True,False)
    PlotNumeric("低点",LL[-1],0xaaff00,True,False)

    # #主连下单，计算止损止盈价格线
    # MP=MarketPosition(tcode)   #持仓方向
    # MC=CurrentContracts(tcode) #持仓手数
    # AVP=AvgEntryPrice(tcode)   #EntryPrice(tcode) #持仓均价/第一笔建仓价
    # HCS.append(context.strategyStatus())
    # _VTS=VTS(Time())
    # TradeEnbTime=TimeTo_Minutes(_VTS[3])-TimeTo_Minutes(_VTS[2])>CloseTime
    # TradeOffTime=CloseTime>=TimeTo_Minutes(_VTS[3])-TimeTo_Minutes(_VTS[2])>0

    # BK1 =TradeEnbTime and C[-1-HTS]>=l_OVO and C[-1-HTS]>m_OVO and C[-1-HTS]>H[-2-HTS]
    # SK1 =TradeEnbTime and C[-1-HTS]<=l_OVO and C[-1-HTS]<m_OVO and C[-1-HTS]<L[-2-HTS]

    # if context.strategyStatus()=="C" and  ExchangeStatus(ExchangeName(tcode)) in ('1','2','3'): # and QuoteSvrState() == 1 and TradeSvrState()==1
    #     if  BKS==0 and trade_sw>=0 and BK1:
    #         tim_trigger(True,False,lots_o1,ovprice_tick,tcode)
    #         BKS=1
    #         ll_stop=L[-1-HTS]-stoplost_tick*MINPRICE      
    #     elif SKS==0 and trade_sw<=0 and SK1:
    #         tim_trigger(False,True,lots_o1,ovprice_tick,tcode)
    #         SKS=1
    #         hh_stop=H[-1-HTS]+stoplost_tick*MINPRICE       
    #     buys.append(A_BuyPosition(tcode))   
    #     sells.append(A_SellPosition(tcode)) 
    #     if ops>0:
    #         if ll_stop>0 and buys[1]-buys[2]==lots_o1:
    #             BT1+=1
    #             if BT1>=ops and trade_sw>0:
    #                 LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"已下多单",BT1,"次",策略多单到达累计交易上限退出策略)     
    #         if hh_stop>0 and sells[1]-sells[2]==lots_o1:
    #             ST1+=1
    #             if ST1>=ops and trade_sw<0:   
    #                 LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"已下空单",ST1,"次",策略空单到达累计交易上限退出策略)
    #         if  BT1>=ops and ST1>=ops and trade_sw==0 :
    #             LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,策略多空单均到达累计交易上限退出策略)

    #     if A_BuyPosition(tcode)==0:
    #         ll_stop= 0
    #     if A_SellPosition(tcode)==0:
    #         hh_stop= 0

    #     if A_BuyPosition(tcode)>0 and ll_stop>0: 
    #         # if ll_stop==0:
    #         #     LogInfo(警告警告你有策略启动前的多单老仓未平掉退出策略请平掉老仓再启动策略)
    #         TLL=mLL if stoplost_mode_sw>0 else LL
    #         _TLL = max(TLL,ll_stop+stoplost_tick*MINPRICE)
    #         ll_stop=_TLL - stoplost_tick*MINPRICE
    #         # PlotNumeric("多单止损线",ll_stop,0xaaff66,True,False)

    #         PlotPartLine("多单止损线",CurrentBar(),ll_stop,2,ll_stop,0x00ff00)
    #         if C[-1]<ll_stop:
    #             BKS=1
    #             LogInfo(tcode,"多单止损",lots_o1,"手")
    #             tim_trigger_Exit(False,True,ovprice_tick,tcode,lots_o1)     
    #         elif C[-1]>A_BuyAvgPrice(tcode)+stopwin_tick*MINPRICE:        
    #             BKS=1
    #             LogInfo(tcode,"多单止赢",lots_o1,"手")
    #             tim_trigger_Exit(False,True,ovprice_tick,tcode,lots_o1)     
    #         elif  TradeOffTime:
    #             BKS=1
    #             LogInfo(tcode,"收盘前平多仓",lots_o1,"手")
    #             tim_trigger_Exit(False,True,ovprice_tick,tcode,lots_o1)      
    #     if A_SellPosition(tcode)>0 and hh_stop>0: 
    #         # if hh_stop==0:
    #         #     LogInfo(警告警告你有策略启动前的空单老仓未平掉退出策略请平掉老仓再启动策略)
    #         THH=mHH if stoplost_mode_sw>0 else HH
    #         _THH = min(THH,hh_stop-stoplost_tick*MINPRICE)
    #         hh_stop=_THH + stoplost_tick*MINPRICE
    #         # PlotNumeric("空单止损线",hh_stop,0xffaa66,True,False)

    #         PlotPartLine("空单止损线",CurrentBar(),hh_stop,2,hh_stop,0xff0000)
    #         if C[-1]>hh_stop:
    #             SKS=1
    #             LogInfo(tcode,"空单止损",lots_o1,"手")
    #             tim_trigger_Exit(True,False,ovprice_tick,tcode,lots_o1)       
    #         elif C[-1]<A_SellAvgPrice()-stopwin_tick*MINPRICE:        
    #             SKS=1
    #             LogInfo(tcode,"空单止赢",lots_o1,"手")
    #             tim_trigger_Exit(True,False,ovprice_tick,tcode,lots_o1)    
    #         elif  TradeOffTime:
    #             SKS=1
    #             LogInfo(tcode,"收盘前平空仓",lots_o1,"手")
    #             tim_trigger_Exit(True,False,ovprice_tick,tcode,lots_o1)   
    #     # LogInfo(Time(), ExchangeName(tcode),context.strategyStatus() ,  ExchangeStatus(ExchangeName(tcode)),_VTS,TradeEnbTime,TradeOffTime,TimeTo_Minutes(_VTS[3])-TimeTo_Minutes(_VTS[2]))

def floattime_sum(floatin1, floatin2, len_set=12):  # 高精度浮点时间求和（精确到毫秒）
    # 设置浮点数格式，保留len_set位小数
    lensave = f"%0.{len_set}f"
    
    # 格式化浮点数并提取各时间部分
    def extract_time_parts(floatin):
        strfloat = lensave % floatin
        return int(strfloat[2:4]), int(strfloat[4:6]), int(strfloat[6:8]), int(strfloat[8:11])
    
    h1, m1, s1, ms1 = extract_time_parts(floatin1)
    h2, m2, s2, ms2 = extract_time_parts(floatin2)
    
    # 计算总和并处理进位
    total_ms = ms1 + ms2
    ms_carry = total_ms // 1000
    new_ms = total_ms % 1000
    
    total_s = s1 + s2 + ms_carry
    s_carry = total_s // 60
    new_s = total_s % 60
    
    total_m = m1 + m2 + s_carry
    m_carry = total_m // 60
    new_m = total_m % 60
    
    new_h = h1 + h2 + m_carry
    new_h = min(new_h, 99)  # 限制小时数不超过99
    
    # 组合新的浮点时间字符串并转换回浮点数
    new_str_time = f"0.{new_h:02}{new_m:02}{new_s:02}{new_ms:03}"
    return float(new_str_time)

def TimeTo_Minutes(time_in):
    timestr='%0.6f'%time_in
    hsave=int(timestr[2:4])
    msave=int(timestr[4:6])
    tcout=hsave*60+msave
    return tcout

def SessionOpenTime(contractId=''):  # 获取交易时段开盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler:continue
        tlout.append(GetSessionStartTime(contractId, i))  # 获取每个交易时段的开盘时间并加入列表
    return tlout

def SessionCloseTime(contractId=''):  # 获取交易时段收盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler-1:continue
        tlout.append(GetSessionEndTime(contractId, i))  # 获取每个交易时段的收盘时间并加入列表
    return tlout

def VTS(time_in, contractId=''):  # 根据输入时间和合约ID计算交易时段
    RTS, CTS, TSession = [], [], []  # 初始化三个列表，用于存储修正后的时间、收盘时间和交易时段
    opentimet = SessionOpenTime(contractId)  # 获取所有交易时段的开盘时间
    Closetimet = SessionCloseTime(contractId)  # 获取所有交易时段的收盘时间
    
    for open_time, close_time in zip(opentimet, Closetimet):
        if time_in >= open_time:  # 判断输入时间是否在开盘时间之后
            RTS.append(time_in)  # 如果是，加入RTS列表
        else:
            RTS.append(floattime_sum(time_in, 0.24))  # 如果不是，修正时间后加入RTS列表
        
        if close_time >= open_time:  # 判断收盘时间是否在开盘时间之后
            CTS.append(close_time)  # 如果是，加入CTS列表
        else:
            CTS.append(floattime_sum(close_time, 0.24))  # 如果不是，修正时间后加入CTS列表
        
        if open_time <= RTS[-1] <= CTS[-1]:  # 判断修正后的时间是否在交易时段内
            TSession.append(len(RTS) - 1)  # 如果是，加入TSession列表

    if len(TSession) == 1:  # 如果只有一个交易时段
        idx = TSession[0]
        return idx, opentimet[idx], RTS[idx], CTS[idx]  # 返回交易时段和相关时间
    else:
        return -1, time_in, time_in, time_in  # 否则返回默认值
def tim_trigger(BK,SK,qty,itk,tcode):#盘中实时开仓
    global BKS,SKS
    if BK and BKS==0 and (A_BuyPosition(tcode) == 0) :
        iprc = min(Q_AskPrice(tcode) +itk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        A_SendOrder(Enum_Buy(), Enum_Entry(), qty, iprc,tcode) 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单买入开仓价==>",iprc,"买入数量==>",qty)
        BKS=1    
    elif SK and SKS==0 and (A_SellPosition(tcode) == 0):    
        iprc = max(Q_BidPrice(tcode) - itk*PriceTick(tcode), Q_LowLimit(tcode))  # 对盘超价                         
        A_SendOrder(Enum_Sell(), Enum_Entry(), qty, iprc,tcode)   
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单卖出开仓价==>",iprc,"卖出数量==>",qty)
        SKS=1    

def tim_trigger_Exit(BP,SP,otk,tcode,clots):#盘中实时平仓
    global BKS,SKS,BPS,SPS
    if BP and BPS==0 and A_SellPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_SellPosition(tcode))
        prc = min(Q_AskPrice(tcode) +otk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        if ExchangeName(tcode) not in ['SHFE','INE']:    
            retExit, ExitOrderId=A_SendOrder(Enum_Buy(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodaySellPosition(tcode)
            dlots=lots-tlots            
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),lots, prc,tcode) #今仓足够平仓,上期所能交所优先超价全部平今仓    
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Buy(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Buy(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单买入平仓价==>",prc,"买入平仓数量==>",_lots)
        BPS=1  
        if SKS==1:SKS=2      
    elif SP and SPS==0 and A_BuyPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_BuyPosition(tcode))
        prc = max(Q_BidPrice(tcode) - otk*PriceTick(tcode), Q_LowLimit(tcode))
        if ExchangeName(tcode) not in ['SHFE','INE']:
            retExit, ExitOrderId=A_SendOrder(Enum_Sell(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodayBuyPosition(tcode)
            dlots=lots-tlots
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),lots, prc,tcode)   #今仓足够平仓,上期所能交所优先超价全部平今仓  
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Sell(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Sell(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单卖出平仓价==>",prc,"卖出平仓数量==>",_lots)
        SPS=1    
        if BKS==1:BKS=2   