import talib
import math
import numpy as np
import pandas as pd
from collections import deque

g_params['K线基础时间']  = 'M'   # k线基础时间
g_params['K线基础周期']  = 30     # k线周期
g_params['选择显示合约序号']  = 1 # 选择显示合约序号
g_params['FastLength']  = 22    # 快周期
g_params['MidLength']   = 60    # 中周期
g_params['SlowLength']  = 120   # 慢周期
g_params['基准市值']     = 100   # 基准市值(万)
g_params['止盈']  =  2  #市值大于基准市值的2%止盈
g_params['补仓']  = -2  #市值小于基准市值的2%补仓
g_params['疯牛乖离阈值']  =  100  #疯牛乖离阈值
g_params['疯熊乖离阈值']  = -100  #疯熊乖离阈值
g_params['订阅数据长度']  =  20000  #订阅数据长度


g_params['配置文件夹路径'] = "D:\stock_trade"
g_params['配置文件名'] = "配置文件与股票ETF代码.xlsx"
trade_order_filedir = g_params['配置文件夹路径']
trade_config_file   = trade_order_filedir+"\\"+g_params['配置文件名'] 
trade_config_DATA   =pd.read_excel(trade_config_file,sheet_name = 0)
symbol_Id =trade_config_DATA["股票ETF代码"].dropna()
资金权重=trade_config_DATA["资金权重"].dropna()
是否交易=trade_config_DATA["是否交易"].dropna()
初始总权益=trade_config_DATA["初始总权益(万)"].dropna()
浮盈减仓启控比例=trade_config_DATA["浮盈减仓启控比例(%)"].dropna()
浮亏加仓启控比例=trade_config_DATA["浮亏加仓启控比例(%)"].dropna()
class TriggerManager:
    """
    交易触发器管理类，负责处理订单开平仓操作
    """
    def __init__(self):
        # 交易状态标记
        self.BKS = 0  # 买开状态: 0=未开仓, 1=已开仓, 2=已平仓
        self.SKS = 0  # 卖开状态: 0=未开仓, 1=已开仓, 2=已平仓
        self.BPS = 0  # 买平状态: 0=未平仓, 1=已平仓
        self.SPS = 0  # 卖平状态: 0=未平仓, 1=已平仓
    
    def reset_states(self):
        """重置所有交易状态"""
        self.BKS = 0
        self.SKS = 0
        self.BPS = 0
        self.SPS = 0
    
    # ==================== 历史开仓函数 ====================
    
    def his_trigger_long(self, qty, price, tcode):
        """
        历史多头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
        """
        if self.BKS != 0:
            return False
            
        Buy(qty, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "多单买入开仓价==>", price, "买入数量==>", qty)
        self.BKS = 1
        return True
    
    def his_trigger_short(self, qty, price, tcode):
        """
        历史空头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
        """
        if self.SKS != 0:
            return False
            
        SellShort(qty, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "空单卖出开仓价==>", price, "卖出数量==>", qty)
        self.SKS = 1
        return True
    
    def his_trigger(self, BK, SK, qty, price, tcode):
        """历史开仓兼容函数"""
        if BK:
            return self.his_trigger_long(qty, price, tcode)
        elif SK:
            return self.his_trigger_short(qty, price, tcode)
        return False
    
    # ==================== 历史平仓函数 ====================
    
    def his_trigger_exit_short(self, clots, price, tcode):
        """
        历史平仓空头持仓
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
        """
        if self.BPS != 0 or SellPosition(tcode) <= 0 or clots <= 0:
            return False
            
        _lots = min(clots, SellPosition(tcode))
        BuyToCover(_lots, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "空单买入平仓价==>", price, "买入平仓数量==>", _lots)
        self.BPS = 1
        
        # 更新卖开状态
        if self.SKS == 1:
            self.SKS = 2
        
        return True
    
    def his_trigger_exit_long(self, clots, price, tcode):
        """
        历史平仓多头持仓
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
        """
        if self.SPS != 0 or BuyPosition(tcode) <= 0 or clots <= 0:
            return False
            
        _lots = min(clots, BuyPosition(tcode))
        Sell(_lots, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "多单卖出平仓价==>", price, "卖出平仓数量==>", _lots)
        self.SPS = 1
        
        # 更新买开状态
        if self.BKS == 1:
            self.BKS = 2
        
        return True
    
    def his_trigger_Exit(self, BP, SP, long_price, short_price, clots, tcode):
        """
        历史平仓兼容函数，支持不同的多空平仓价格
        
        参数:
            BP: 是否平空仓标志
            SP: 是否平多仓标志
            long_price: 平多头价格 (卖出价)
            short_price: 平空头价格 (买入价)
            clots: 平仓手数
            tcode: 合约代码
        
        返回:
            bool: 平仓是否成功
        """
        result = False
        if BP:
            result = self.his_trigger_exit_short(clots, short_price, tcode)
        if SP:
            result = self.his_trigger_exit_long(clots, long_price, tcode) or result
        return result
    
    # ==================== 实时开仓函数 ====================
    
    def tim_trigger_long(self, qty, price, tcode):
        """
        实时多头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
            
        返回:
            (状态, 订单ID)
        """
        if self.BKS != 0 or A_BuyPosition(tcode) > 0:
            return None, None
            
        # 价格检查，确保不超过涨停价
        upper_limit = Q_UpperLimit(tcode)
        checked_price = min(price, upper_limit)
        if checked_price != price:
            LogInfo(f"警告: 多头开仓价格 {price} 超过涨停价 {upper_limit}，已自动调整")
        
        retEntry, EntryOrderId = A_SendOrder(Enum_Buy(), Enum_Entry(), qty, checked_price, tcode)
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "多单买入开仓价==>", checked_price, "买入数量==>", qty)
        
        if retEntry:
            self.BKS = 1
            
        return retEntry, EntryOrderId
    
    def tim_trigger_short(self, qty, price, tcode):
        """
        实时空头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
            
        返回:
            (状态, 订单ID)
        """
        if self.SKS != 0 or A_SellPosition(tcode) > 0:
            return None, None
            
        # 价格检查，确保不低于跌停价
        lower_limit = Q_LowLimit(tcode)
        checked_price = max(price, lower_limit)
        if checked_price != price:
            LogInfo(f"警告: 空头开仓价格 {price} 低于跌停价 {lower_limit}，已自动调整")
        
        retEntry, EntryOrderId = A_SendOrder(Enum_Sell(), Enum_Entry(), qty, checked_price, tcode)
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "空单卖出开仓价==>", checked_price, "买入数量==>", qty)
        
        if retEntry:
            self.SKS = 1
            
        return retEntry, EntryOrderId
    
    def tim_trigger(self, BK, SK, qty, price, tcode):
        """实时开仓兼容函数"""
        if BK:
            return self.tim_trigger_long(qty, price, tcode)
        elif SK:
            return self.tim_trigger_short(qty, price, tcode)
        return None, None
    
    # ==================== 实时平仓函数 ====================
    
    def tim_trigger_exit_short(self, clots, price, tcode):
        """
        实时平仓空头持仓，支持返回多个订单状态
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
            
        返回:
            list: 包含所有订单信息的列表，每项为 (状态, 订单ID, 平仓类型, 平仓手数)
        """
        orders_info = []  # 用于存储所有订单的状态和ID
        
        if self.BPS != 0 or A_SellPosition(tcode) <= 0 or clots <= 0:
            return orders_info
            
        _lots = min(clots, A_SellPosition(tcode))
        
        # 价格检查，确保不超过涨停价
        upper_limit = Q_UpperLimit(tcode)
        checked_price = min(price, upper_limit)
        if checked_price != price:
            LogInfo(f"警告: 平空头价格 {price} 超过涨停价 {upper_limit}，已自动调整")
        
        # 处理上期所和能源交易所的特殊情况
        if ExchangeName(tcode) not in ['SHFE', 'INE']:
            # 普通交易所情况
            retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), _lots, checked_price, tcode)
            orders_info.append((retExit, ExitOrderId, "平仓", _lots))
        else:
            # 上期所和能源交易所特殊处理
            lots = _lots
            tlots = A_TodaySellPosition(tcode)
            dlots = lots - tlots
            
            if tlots >= lots:
                # 今仓足够平仓,仅平今仓
                retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_ExitToday(), lots, checked_price, tcode)
                orders_info.append((retExit, ExitOrderId, "平今", lots))
            elif tlots > 0:
                # 今仓不够，分别平今仓和昨仓
                # 先平今仓部分
                TretExit, TExitOrderId = A_SendOrder(Enum_Buy(), Enum_ExitToday(), tlots, checked_price, tcode)
                orders_info.append((TretExit, TExitOrderId, "平今", tlots))
                
                # 再平昨仓部分
                YretExit, YExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), int(dlots), checked_price, tcode)
                orders_info.append((YretExit, YExitOrderId, "平昨", int(dlots)))
            elif tlots == 0:
                # 仅平昨仓
                retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), lots, checked_price, tcode)
                orders_info.append((retExit, ExitOrderId, "平昨", lots))
        
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "空单买入平仓价==>", checked_price, "买入平仓数量==>", _lots)
        
        # 只要有订单成功，更新状态
        if any(order[0] for order in orders_info):
            self.BPS = 1
            if self.SKS == 1:
                self.SKS = 2
        
        return orders_info
    
    def tim_trigger_exit_long(self, clots, price, tcode):
        """
        实时平仓多头持仓，支持返回多个订单状态
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
            
        返回:
            list: 包含所有订单信息的列表，每项为 (状态, 订单ID, 平仓类型, 平仓手数)
        """
        orders_info = []  # 用于存储所有订单的状态和ID
        
        if self.SPS != 0 or A_BuyPosition(tcode) <= 0 or clots <= 0:
            return orders_info
            
        _lots = min(clots, A_BuyPosition(tcode))
        
        # 价格检查，确保不低于跌停价
        lower_limit = Q_LowLimit(tcode)
        checked_price = max(price, lower_limit)
        if checked_price != price:
            LogInfo(f"警告: 平多头价格 {price} 低于跌停价 {lower_limit}，已自动调整")
        
        # 处理上期所和能源交易所的特殊情况
        if ExchangeName(tcode) not in ['SHFE', 'INE']:
            # 普通交易所情况
            retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), _lots, checked_price, tcode)
            orders_info.append((retExit, ExitOrderId, "平仓", _lots))
        else:
            # 上期所和能源交易所特殊处理
            lots = _lots
            tlots = A_TodayBuyPosition(tcode)
            dlots = lots - tlots
            
            if tlots >= lots:
                # 今仓足够平仓,仅平今仓
                retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_ExitToday(), lots, checked_price, tcode)
                orders_info.append((retExit, ExitOrderId, "平今", lots))
            elif tlots > 0:
                # 今仓不够，分别平今仓和昨仓
                # 先平今仓部分
                TretExit, TExitOrderId = A_SendOrder(Enum_Sell(), Enum_ExitToday(), tlots, checked_price, tcode)
                orders_info.append((TretExit, TExitOrderId, "平今", tlots))
                
                # 再平昨仓部分
                YretExit, YExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), int(dlots), checked_price, tcode)
                orders_info.append((YretExit, YExitOrderId, "平昨", int(dlots)))
            elif tlots == 0:
                # 仅平昨仓
                retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), lots, checked_price, tcode)
                orders_info.append((retExit, ExitOrderId, "平昨", lots))
        
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "多单卖出平仓价==>", checked_price, "卖出平仓数量==>", _lots)
        
        # 只要有订单成功，更新状态
        if any(order[0] for order in orders_info):
            self.SPS = 1
            if self.BKS == 1:
                self.BKS = 2
        
        return orders_info
    
    def tim_trigger_Exit(self, BP, SP, long_price, short_price, clots, tcode):
        """
        实时平仓兼容函数，支持不同的多空平仓价格
        
        参数:
            BP: 是否平空仓标志
            SP: 是否平多仓标志
            long_price: 平多头价格 (卖出价)
            short_price: 平空头价格 (买入价)
            clots: 平仓手数
            tcode: 合约代码
        
        返回:
            list: 所有平仓订单的信息列表
        """
        all_orders = []
        
        if BP:
            short_orders = self.tim_trigger_exit_short(clots, short_price, tcode)
            all_orders.extend(short_orders)
            
        if SP:
            long_orders = self.tim_trigger_exit_long(clots, long_price, tcode)
            all_orders.extend(long_orders)
            
        return all_orders
display_code,k_btime,k_cycle,SetDisplayNo,FastLength,MidLength,SlowLength,BaseMargin,Profit,AddMargin,BullishLimit,BearishLimit='',0,0,0,0,0,0,0,0,0,0,0
def initialize(context): 
    global g_params,display_code,k_btime,k_cycle,SetDisplayNo,FastLength,MidLength,SlowLength,BaseMargin,Profit,AddMargin,BullishLimit,BearishLimit 
    k_btime = g_params['K线基础时间'] # k线基础时间取参数
    k_cycle = g_params['K线基础周期'] # k线基础周期取参数
    SetDisplayNo = g_params['选择显示合约序号']-1 # 选择显示合约序号
    FastLength = g_params['FastLength']   # 快周期
    MidLength = g_params['MidLength']     # 中周期
    SlowLength = g_params['SlowLength']   # 慢周期
    BaseMargin = g_params['基准市值']      # 基准市值
    Profit = g_params['止盈']      # 止盈
    AddMargin = g_params['补仓']   # 补仓
    BullishLimit = g_params['疯牛乖离阈值']   # 疯牛乖离阈值
    BearishLimit = g_params['疯熊乖离阈值']   # 疯熊乖离阈值 

    DaySubDataLength = g_params['订阅数据长度']  # 订阅日线数据长度
    SubDataLength = int(DaySubDataLength*8)  # 订阅数据长度
    AluSubDataLength = min(2000,SubDataLength)  # 计算数据长度  
    setNo=min(SetDisplayNo,len(symbol_Id)-1) 
    display_code=stock_code_mapping(int(symbol_Id[setNo]))







    
    LogInfo('图表显示合约==>',display_code)
    SetBarInterval(display_code, k_btime, k_cycle,SubDataLength,AluSubDataLength) #订阅交易合约
    for i in symbol_Id:
        tcode=stock_code_mapping(i)
        SetBarInterval(tcode, 'D', 1 ,DaySubDataLength,AluSubDataLength) #订阅日线数据
        if tcode==display_code:
            continue
        LogInfo("订阅",i,"的合约"+tcode)
        SetBarInterval(tcode, k_btime, k_cycle,SubDataLength,AluSubDataLength) #订阅交易合约
    SetTriggerType(1)
    SetTriggerType(5)
    SetOrderWay(1)
    SetActual()
BKS,SKS,BPS,SPS=0,0,0,0
sBARS=deque([0,0],maxlen=3)
VM0,VM1=0,0
triggermanager=TriggerManager()
def handle_data(context):
    global BKS,SKS,BPS,SPS,VM0,VM1
    for i in range(len(symbol_Id)):
        tcode=stock_code_mapping(int(symbol_Id[i]))
        C = Close(tcode, k_btime, k_cycle)
        CD = Close(tcode, 'D', 1)
        HTS=1 if context.strategyStatus()=="C" else 0
        if len(CD) < SlowLength:
            return
        FastLine = talib.MA(CD, FastLength) #快线周期均值
        MidLine = talib.MA(CD, MidLength)   #中线周期均值
        SlowLine = talib.MA(CD, SlowLength) #慢线周期均值
        MA12 = talib.MA(C,12)
        if tcode==display_code:
            PlotNumeric("FastLine", FastLine[-1-HTS], 0xFFFFFF)
            PlotNumeric("MidLine", MidLine[-1-HTS], 0x00AAAA)
            PlotNumeric("SlowLine", SlowLine[-1-HTS], 0xFF0000)
            RS1=(MA12[-1-HTS]/MidLine[-1-HTS]-1)*1000
            PlotNumeric("乖离", RS1, 0x00FFFF,False, False,0,"乖离指标")
            PlotNumeric("零轴", 0, 0xFFFFFF,False, False,0,"乖离指标")
        # 执行下单操作
        #//------------------------历史发单------------------------//   
        # LOTS=math.floor(BaseMargin*10000/(C[-1]*ContractUnit(tcode)*100+FEE))*100    

        # if context.strategyStatus() != 'C':
        #     BKS1 = MidLine[-1-HTS] > SlowLine[-1-HTS]
        #     SKS1 = MidLine[-1-HTS] < SlowLine[-1-HTS]

        #     if VM0==0 and BKS1:#多头建仓
        #         VM0=BaseMargin*10000
        #         Buy(LOTS,C[-1])
        #         ELOTS=BuyPosition()-LOTS; 

        #     elif SKS1:
        #         SellShort(LOTS, C[-1])
        #     return
        # #进入实时行情，清理掉策略仓
        # if MarketPosition() > 0 and A_BuyPosition(tcode)==0:
        #     Sell(N, Open()[-1])
        #     LogInfo("进入实时行情，清理掉策略多仓")
        #     return
        # elif MarketPosition() < 0 and A_SellPosition(tcode)==0:
        #     BuyToCover(N, Open()[-1])
        #     LogInfo("进入实时行情，清理掉策略空仓")
            # return

    
    # #//------------------------实时处理------------------------//

    # # if ExchangeStatus(ExchangeName()) != '3':
    # #     return
    # BKS2 = AvgValue1[-3] <= AvgValue2[-3] and AvgValue1[-2] > AvgValue2[-2]
    # SKS2 = AvgValue1[-3] >= AvgValue2[-3] and AvgValue1[-2] < AvgValue2[-2]   
    # #//------------------------变量赋值------------------------//
    # s_CurrentBar=CurrentBar(tcode, k_btime, k_cycle)
    # sBARS.append(s_CurrentBar)
    # if sBARS[0]>0 and sBARS[1]<sBARS[2]:
    #     if BKS>=1 and A_BuyPosition(tcode)==0: 
    #         BKS=0
    #         SPS=0  
    #     if SKS>=1 and A_SellPosition(tcode)==0: 
    #         SKS=0 
    #         BPS=0
    # if trade_sw>=0:
    #     tim_trigger(BKS2,False,N,20,tcode)
    # if trade_sw<=0:
    #     tim_trigger(False,SKS2,N,20,tcode)
    # if BPS==0:    
    #     tim_trigger_Exit(BKS2,False,20,tcode,N)
    # if SPS==0:    
    #     tim_trigger_Exit(False,SKS2,20,tcode,N)

def tim_trigger(BK,SK,qty,itk,tcode):#盘中实时开仓
    global BKS,SKS
    if BK and BKS==0 and (A_BuyPosition(tcode) == 0) :
        iprc = min(Q_AskPrice(tcode) +itk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        A_SendOrder(Enum_Buy(), Enum_Entry(), qty, iprc,tcode) 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单买入开仓价==>",iprc,"买入数量==>",qty)
        BKS=1    
    elif SK and SKS==0 and (A_SellPosition(tcode) == 0):    
        iprc = max(Q_BidPrice(tcode) - itk*PriceTick(tcode), Q_LowLimit(tcode))  # 对盘超价                         
        A_SendOrder(Enum_Sell(), Enum_Entry(), qty, iprc,tcode)   
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单卖出开仓价==>",iprc,"卖出数量==>",qty)
        SKS=1    

def tim_trigger_Exit(BP,SP,otk,tcode,clots):#盘中实时平仓
    global BKS,SKS,BPS,SPS
    if BP and BPS==0 and A_SellPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_SellPosition(tcode))
        prc = min(Q_AskPrice(tcode) +otk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        if ExchangeName(tcode) not in ['SHFE','INE']:    
            retExit, ExitOrderId=A_SendOrder(Enum_Buy(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodaySellPosition(tcode)
            dlots=lots-tlots            
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),lots, prc,tcode) #今仓足够平仓,上期所能交所优先超价全部平今仓    
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Buy(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Buy(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单买入平仓价==>",prc,"买入平仓数量==>",_lots)
        BPS=1  
        if SKS==1:SKS=2      
    elif SP and SPS==0 and A_BuyPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_BuyPosition(tcode))
        prc = max(Q_BidPrice(tcode) - otk*PriceTick(tcode), Q_LowLimit(tcode))
        if ExchangeName(tcode) not in ['SHFE','INE']:
            retExit, ExitOrderId=A_SendOrder(Enum_Sell(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodayBuyPosition(tcode)
            dlots=lots-tlots
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),lots, prc,tcode)   #今仓足够平仓,上期所能交所优先超价全部平今仓  
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Sell(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Sell(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单卖出平仓价==>",prc,"卖出平仓数量==>",_lots)
        SPS=1    
        if BKS==1:BKS=2   



from typing import Union
def stock_code_mapping(code: Union[int, str]) -> str:
    # 整数处理分支（进一步优化）
    if isinstance(code, int):
        if not (1 <= code <= 999999):
            raise ValueError("输入必须为6位以下正整数")
        
        # 格式化代码字符串（只做一次）
        code_str = f"{code:06d}"
        
        # 快速分类 - 使用整数除法和模运算
        first_digit = code // 100000
        first_two = code // 10000
        first_three = code // 1000
        
        # 沪市股票 (6开头)
        if first_digit == 6:
            if first_three == 688:
                return f"SSE|T|KSHARES|{code_str}"  # 科创板
            elif first_three in {600, 601, 603, 605}:
                return f"SSE|T|ASHARES|{code_str}"      # 沪主板
            
        # 深主板 (0,1,3开头或4-9开头)
        if first_three  in {0, 1, 3}:
            return f"SZSE|T|ASHARES|{code_str}"    # 深主板            
        # 中小板 (002开头)    
        if first_three == 2:
            return f"SZSE|T|SMESHARES|{code_str}"  # 中小板
        # 创业板 (30开头)
        if first_two == 30:
            return f"SZSE|T|CHSHARES|{code_str}"   # 创业板   
        # 深B股 (200开头)
        if first_three == 200:
            return f"SZSE|T|BSHARES|{code_str}"    # 深B股
        # ETF (159开头)
        if first_three == 159:
            return f"SZSE|T|FUNDS|{code_str}"      # ETF
             
        # 基金 (5开头)
        if first_digit == 5:
            return f"SSE|T|FUNDS|{code_str}"       # 沪基金
            
        # REITs (16-18开头)
        if first_two in {16, 18}:
            return f"SZSE|T|FUNDS|{code_str}"      # REITs
            
        # 沪B股 (9开头)
        if first_digit == 9:
            return f"SSE|T|BSHARES|{code_str}"     # 沪B股
            
        # 北交所和新三板
        if first_three in {830, 831, 832, 833, 834, 835, 836, 837, 838, 839, 
                          870, 871, 872, 873, 874, 875, 876, 877, 878, 879, 
                          920, 921, 922, 923, 924, 925, 926, 927, 928, 929}:
            return f"BJSE|T|STOCK|{code_str}"
        if first_three in {400, 430, 830}:
            return f"NEEQ|T|OTC|{code_str}"
            
        return f"UNKNOWN|{code_str}"
    
    # 字符串处理分支（原逻辑）
    elif isinstance(code, str):
        if not (code.isdigit() and len(code) == 6):
            raise ValueError("输入必须为6位数字字符串")

        if code.startswith('688'):
            return f"SSE|T|KSHARES|{code}"
        elif code.startswith(('600','601','603','605')):
            return f"SSE|T|ASHARES|{code}"
        elif code.startswith('5'):
            return f"SSE|T|FUNDS|{code}"
        elif code.startswith('900'):
            return f"SSE|T|BSHARES|{code}"
        elif code.startswith('159'):
            return f"SZSE|T|FUNDS|{code}"
        elif code.startswith(('000','001','003')):
            return f"SZSE|T|ASHARES|{code}"
        elif code.startswith('002'):
            return f"SZSE|T|SMESHARES|{code}"
        elif code.startswith('30'):
            return f"SZSE|T|CHSHARES|{code}"
        elif code.startswith('200'):
            return f"SZSE|T|BSHARES|{code}"
        elif code.startswith(('16','18')):
            return f"SZSE|T|FUNDS|{code}"
        elif code.startswith(('83','87','920')):
            return f"BJSE|T|STOCK|{code}"
        elif code.startswith(('400','430','830')):
            return f"NEEQ|T|OTC|{code}"
        else:
            return f"UNKNOWN|{code}"
    
    else:
        raise TypeError("输入类型必须为int或str")
    
def stock_index_code_mapping(code: Union[int, str]) -> str:
    if isinstance(code, int):
        code_str = f"{code:06d}"
        prefix_three = code // 1000
        if prefix_three == 399:
            return f"SZSE|T|INDEX|{code_str}"
        if prefix_three == 0:
            return f"SSE|T|INDEX|{code_str}"
    elif isinstance(code, str):
        if code.startswith('399'):
            return f"SZSE|T|INDEX|{code}"
        if code.startswith('000'):
            return f"SSE|T|INDEX|{code}"
