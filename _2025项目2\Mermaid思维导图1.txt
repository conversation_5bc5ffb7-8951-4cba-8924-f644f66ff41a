mindmap
  root((SP策略开平仓描述 V1.2))
    开盘追单
      正常条件("开盘后N个3分钟周期内不开仓")
      极端行情
         1分钟数据("采用1分钟数据计时")
         盘前条件("满足盘前开单条件")
         价格速度("价格速度 >> n点/分钟")
         SP斜率("SP斜率 > 70°")
         转折角("1分钟反向拐角 < 5°")
         开仓("市价开单")
      平仓条件
         利润回撤("利润回撤 n% 止损")
         维稳("最高/最低价维持20秒无回撤")
    盘中定义
         起始("从第16分钟开始")
         周期("3分钟K线监测（5根K线+2分钟窗口）")
    V反开单平仓
      V反定义
         价格趋势("价格沿SP一边、靠近SP线")
         冲反行为("冲过去后迅速弹回")
         击穿限制("不得穿越SP近带")
      V反开仓
         中轴("选取最接近SP的K线为中轴")
         左侧计算("左侧3根K线求最高价 - SP = M")
         速度要求("左侧速度 = M/3 > 门槛")
         观察窗口("右侧5根K线（15分钟）")
         触发条件("反弹突破 M/2 并站稳 1 分钟")
         斜率加权("SP斜率满足时直接突破 M")
      V反止损
         止损策略("SP跌破反向近带或跌破 N 分钟")
    击穿SP开平仓
         贯穿定义("符合SP斜率/拐角条件的K线计分")
         中轴分析("以击穿K线为中轴，左侧取3根K线求 M")
         突破观察("右侧5根K线检测突破 M/2 并站稳")
         快速触发("直接突破 M 值立即市价单")
    V反+击穿止盈
         BOLL中轨法则
             持仓("不回撤破中轨且站稳超过3分钟继续持仓")
             全平("超过BOLL反向近带全平仓")
         极远值法则
             远带区域("价格进入远带或远带外")
             历史参照("参考N天经验及SP斜率*极远点差")
             分批/全平("逐步平仓或一次性平仓锁利")
         移动止盈法则("最大利润回撤1%-100%触发止盈")
         多法则关系("移动止盈与中轨条件需同时满足；极远值为充分条件")
    SP定义("SP为交易合约的日内均价线")



说明
开盘追单
在开盘早期（如09:30后5个3分钟周期内），常规情况下不主动开仓，但在遇到极端行情（采用1分钟数据检测，满足速度、SP斜率及转角条件）时，会直接市价单开仓，并设置相应的止盈止损条件。
盘中定义
从第16分钟开始，以3分钟K线为基础构造观察窗口（例如取前5根K线加上后2分钟的数据），判断是否存在开仓信号。
V反开单平仓
定义了V反模式，即价格沿着SP一边运行后迅速反弹。通过选取最接近SP的K线作为中轴，左侧3根K线计算关键点差M，再观察右侧15分钟内的反弹情况判断是否开仓，同时设定相应止损规则。
击穿SP开平仓
当符合SP斜率或拐角条件的K线中，若出现价格突破左侧3根K线计算的差值的一半且站稳，则触发击穿策略，直接市价单开仓平仓。
V反+击穿止盈
引入了多重止盈方法：
① BOLL中轨法则——只要价格不回撤破中轨且站稳，继续持仓；一旦回撤突破则全平。
② 极远值法则——参考历史经验及SP斜率，判断价格进入远带区域后逐步或一次性平仓锁利。
③ 移动止盈法则——按最大利润回撤比例平仓。
多法则之间存在“且”与“充分条件”的关系，确保在极端行情中锁定最大利润。
SP定义
文档中所提到的SP是指交易合约的日内均价线，为整个策略的参考基准。
这份思维导图将文档中的各个关键点、流程和判断逻辑清晰展示出来，便于理解和后续实现时作为参考。