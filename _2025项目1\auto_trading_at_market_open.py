# QMT智能拆单开盘自动交易启动脚本
import os
import sys
import time
import datetime
import traceback
import pandas as pd
from xtquant import xtdata
from xtquant import xtconstant

# 设置日志文件路径
log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                       f"auto_trading_{datetime.datetime.now().strftime('%Y%m%d')}.log")

# 重定向标准输出和标准错误到日志文件
sys.stdout = open(log_file, 'a')
sys.stderr = open(log_file, 'a')

# 记录启动信息
print("="*50)
print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 自动交易程序启动")
print("="*50)

def log_message(msg):
    """记录日志，包含时间戳"""
    timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {msg}")

def is_trading_day(date=None):
    """判断给定日期是否为交易日"""
    if date is None:
        date = datetime.datetime.now().date()
    
    try:
        # 获取上证指数代码的交易日历
        trading_days = xtdata.get_trading_dates('SH', '2022-01-01', '2030-12-31')
        trading_days = [day.date() for day in trading_days]
        return date in trading_days
    except Exception as e:
        log_message(f"检查交易日异常: {str(e)}")
        # 如果无法获取交易日历，使用备选方案：工作日判断
        return date.weekday() < 5  # 非周末

# 导入智能拆单模块
try:
    from SmartOrderManager2 import smart_order_manager_example
    
    log_message("成功导入智能拆单模块")
except Exception as e:
    log_message(f"导入智能拆单模块异常: {str(e)}\n{traceback.format_exc()}")
    print("程序将在60秒后退出...")
    time.sleep(60)
    sys.exit(1)

# 主循环
try:
    log_message("A股开盘自动交易程序已启动")
    
    while True:
        now = datetime.datetime.now()
        current_date = now.date()
        
        # 判断当前是否为交易日
        if not is_trading_day(current_date):
            log_message(f"今日 {current_date} 不是交易日，等待至明天")
            # 计算到明天凌晨的秒数
            tomorrow = datetime.datetime.combine(current_date + datetime.timedelta(days=1), 
                                               datetime.time(0, 5, 0))
            sleep_seconds = (tomorrow - now).total_seconds()
            time.sleep(min(sleep_seconds, 3600))  # 最多睡眠1小时，然后重新检查
            continue
        
        # 计算距离今天开盘的时间
        market_open_time = datetime.datetime.combine(current_date, datetime.time(9, 30, 0))
        # 如果当天已过开盘时间
        if now > market_open_time + datetime.timedelta(minutes=1):
            log_message(f"今日 {current_date} 开盘时间已过，等待至明天")
            # 计算到明天凌晨的秒数
            tomorrow = datetime.datetime.combine(current_date + datetime.timedelta(days=1), 
                                               datetime.time(0, 5, 0))
            sleep_seconds = (tomorrow - now).total_seconds()
            time.sleep(min(sleep_seconds, 3600))  # 最多睡眠1小时，然后重新检查
            continue
        
        # 如果还未到开盘时间
        if now < market_open_time:
            seconds_to_open = (market_open_time - now).total_seconds()
            
            # 如果距离开盘时间超过5分钟，每5分钟检查一次
            if seconds_to_open > 300:
                log_message(f"距离今日开盘还有 {seconds_to_open/60:.1f} 分钟，继续等待")
                time.sleep(min(seconds_to_open, 300))
                continue
            
            # 如果距离开盘时间小于5分钟，每秒检查一次
            log_message(f"距离开盘还有 {seconds_to_open:.0f} 秒，准备执行交易")
            time.sleep(max(0, seconds_to_open - 2))  # 提前2秒准备
        
        # 已到达开盘时间
        log_message("市场开盘，开始执行智能拆单操作")
        
        try:
            # 执行智能拆单示例
            smart_order_manager_example()
            log_message("智能拆单操作执行完成")
        except Exception as e:
            error_msg = traceback.format_exc()
            log_message(f"执行智能拆单操作异常: {str(e)}\n{error_msg}")
        
        # 继续等待到明天
        log_message(f"今日操作已完成，等待至明天")
        tomorrow = datetime.datetime.combine(current_date + datetime.timedelta(days=1), 
                                           datetime.time(0, 5, 0))
        sleep_seconds = (tomorrow - now).total_seconds()
        time.sleep(min(sleep_seconds, 3600))  # 最多睡眠1小时，然后重新检查

except KeyboardInterrupt:
    log_message("程序被手动中断")
except Exception as e:
    error_msg = traceback.format_exc()
    log_message(f"程序运行异常: {str(e)}\n{error_msg}")
    
    # 保持程序不退出，便于查看错误
    print("程序将在60秒后退出...")
    time.sleep(60) 