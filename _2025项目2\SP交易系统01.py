from re import M
import time
import talib as ta
import numpy as np 
import numba as nb
from collections import deque
from datetime import datetime, timedelta

g_params['信号合约'] = 'CFFEX|Z|IC|MAIN'     # 信号计算的合约品种
g_params['基础时间'] = 'M'   # k线基础时间
g_params['基础快周期'] =  1  # 基础快周期
g_params['基础慢周期'] =  3  # 基础慢周期
g_params['布林通道周期'] =  26  # 布林通道周期

g_params['1分钟角度取样K线数'] = 3# 1分钟角度取样K线数
g_params['3分钟角度取样K线数'] = 3# 3分钟角度取样K线数

g_params['近带通道宽度比例'] = 0.3# 均价线近带通道宽度比例
g_params['中带通道宽度比例'] = 0.6# 均价线中带通道宽度比例
g_params['远带通道宽度比例'] = 0.8# 均价线远带通道宽度比例

g_params['开盘切换盘中计数'] = 5# 开盘切换盘中计数  
g_params['开盘行情角度阈值'] = 60# 开盘行情角度阈值
g_params['开盘行情速度阈值'] = 3 # 开盘行情速度阈值(跳数)
g_params['开盘行情回撤止盈启动阈值'] = 0.02# 开盘行情回撤止盈启动阈值
g_params['开盘行情利润回撤平仓比例'] = 0.9# 开盘行情利润回撤平仓比例

g_params['盘中行情角度阈值'] = 60# 盘中行情角度阈值
g_params['盘中行情速度阈值'] = 3# 盘中行情速度阈值(跳数)
g_params['盘中行情回撤止盈启动阈值'] = 0.02# 盘中行情回撤止盈启动阈值
g_params['盘中行情利润回撤平仓比例'] = 0.8# 盘中行情利润回撤平仓比例

g_params['V反形态左侧取样K线数'] = 3# V反形态左侧取样K线数
# g_params['V反形态左侧角度阈值'] = 50# V反形态左侧角度阈值
g_params['V反形态左侧速度阈值'] = 3# V反形态左侧速度阈值(跳数)
g_params['V反形态右侧取样K线数'] = 3# V反形态右侧取样K线数
# g_params['V反形态右侧角度阈值'] = 60# V反形态右侧角度阈值
g_params['V反形态右侧速度阈值'] = 3# V反形态右侧速度阈值(跳数)
g_params['V反形态模式选择'] = 3# V反形态模式选择
g_params['V反形态右侧中点站稳时长'] = 1# V反形态右侧中点站稳分钟数

g_params['交易方向开关']  = 0  #大于0开多，小于0开空，等于0双向开仓
g_params['下单手数']  = 1  #Lots
g_params['超价跳数']  = 2  #ovprice_tick
g_params['收盘倒计时分钟数设置'] = 1#距离收盘时间范围不开仓，并清仓    
g_params['订阅数据长度']  =  2000  #订阅数据长度

@nb.njit(cache=True) 
def REF(x,m):#前向回溯函数
    l=len(x)
    if l>m:
        if m>0:
            return np.hstack((np.full(m,x[0]),x[:-m])) 
        else:return x    
    else:return np.full(l,x[0])  

def calculate_slope_angle(price, interval=1):
    x = np.arange(len(price)) * interval  # 计算时间间隔
    slope, _ = np.polyfit(x, price, 1)    # 线性拟合计算角度
    angle = np.degrees(np.arctan(slope))  # 计算角度对应的角度
    return slope, angle
def HHV(x,m):
    if m>1:
        return ta.MAX(x,m)
    else:return x
def LLV(x,m):
    if m>1:
        return ta.MIN(x,m)
    else:return x
def MA(x,m):
    if m>1:
        return ta.MA(x,m)
    else:return x
def SUM(x,m):
    if m>1:
        return ta.SUM(x,m)
    else:return x
def STD(x,m):
    if m>1:
        return ta.STDDEV(x,m)
    else:return x
def LINEARREG_SLOPE(x,m):
    if m>1:
        return ta.LINEARREG_SLOPE(x,m)
    else:return x
def LINEARREG_ANGLE(x,m):
    if m>1:
        return ta.LINEARREG_ANGLE(x,m)
    else:return x
def V_TopPattern(NpArrH,NpArrL,ModeSetting=0,LeftSampling=2,RightSampling=2,LeftThreshold=0,RightThreshold=0):
    HLength=len(NpArrH)
    LLength=len(NpArrL)
    TotalLength=LeftSampling+RightSampling+1
    if min(HLength,LLength) <TotalLength or HLength!=LLength:
        return False
    max_high = np.nanmax(NpArrH[-TotalLength:])
    left_low = np.nanmin(NpArrL[-TotalLength:-RightSampling-1])
    right_low = np.nanmin(NpArrL[-RightSampling:])
    if max_high-left_low<LeftThreshold or max_high-right_low<RightThreshold:
        return False
    _MID=(NpArrL[-RightSampling-1]+NpArrH[-RightSampling-1])/2 
    mode=min(ModeSetting,3)   
    modeenable=mode==0 or (mode==1 and left_low==NpArrL[-TotalLength]) or (mode==2 and right_low==NpArrL[-1]) or (mode==3 and left_low==NpArrL[-TotalLength] and right_low==NpArrL[-1])
    vtop=max_high==NpArrH[-RightSampling-1] and max(left_low,right_low)<_MID and modeenable
    return vtop
def V_BottomPattern(NpArrH,NpArrL,ModeSetting=0,LeftSampling=2,RightSampling=2,LeftThreshold=0,RightThreshold=0):
    HLength=len(NpArrH)
    LLength=len(NpArrL)
    TotalLength=LeftSampling+RightSampling+1
    if min(HLength,LLength) <TotalLength or HLength!=LLength:
        return False
    min_low = np.nanmin(NpArrL[-TotalLength:])
    left_high = np.nanmax(NpArrH[-TotalLength:-RightSampling-1])
    right_high = np.nanmax(NpArrH[-RightSampling:])
    if left_high-min_low<LeftThreshold or right_high-min_low<RightThreshold:
        return False
    _MID=(NpArrL[-RightSampling-1]+NpArrH[-RightSampling-1])/2 
    mode=min(ModeSetting,3)   
    modeenable=mode==0 or (mode==1 and left_high==NpArrH[-TotalLength]) or (mode==2 and right_high==NpArrH[-1]) or (mode==3 and left_high==NpArrH[-TotalLength] and right_high==NpArrH[-1])
    vbottom=min_low==NpArrL[-RightSampling-1] and min(left_high,right_high)>_MID and modeenable
    return vbottom
def V_TopPatternEntity(NpArrO,NpArrC,ModeSetting=0,LeftSampling=2,RightSampling=2,LeftThreshold=0,RightThreshold=0):
    OLength=len(NpArrO)
    CLength=len(NpArrC)
    TotalLength=LeftSampling+RightSampling+1
    if min(OLength,CLength) <TotalLength or OLength!=CLength:
        return False
    MAXBAR=np.maximum(NpArrO,NpArrC)
    MINBAR=np.minimum(NpArrO,NpArrC)
    max_high = np.nanmax(MAXBAR[-TotalLength:])
    left_low = np.nanmin(MINBAR[-TotalLength:-RightSampling-1])
    right_low = np.nanmin(MINBAR[-RightSampling:])
    left_high = np.nanmax(MAXBAR[-TotalLength:-RightSampling-1])
    # right_high = np.nanmax(MAXBAR[-RightSampling:])
    if max_high-left_low<LeftThreshold or max_high-right_low<RightThreshold:
        return False
    _MID=(MINBAR[-RightSampling-1]+MAXBAR[-RightSampling-1])/2 
    mode=min(ModeSetting,3)   
    modeenable=mode==0 or (mode==1 and left_low==MINBAR[-TotalLength]) or (mode==2 and right_low==MINBAR[-1]) or (mode==3 and left_low==MINBAR[-TotalLength] and right_low==MINBAR[-1])
    vtop=max_high==MAXBAR[-RightSampling-1] and left_high<max_high and max(left_low,right_low)<_MID and modeenable
    return vtop
def V_BottomPatternEntity(NpArrO,NpArrC,ModeSetting=0,LeftSampling=2,RightSampling=2,LeftThreshold=0,RightThreshold=0):
    OLength=len(NpArrO)
    CLength=len(NpArrC)
    TotalLength=LeftSampling+RightSampling+1
    if min(OLength,CLength) <TotalLength or OLength!=CLength:
        return False
    MAXBAR=np.maximum(NpArrO,NpArrC)
    MINBAR=np.minimum(NpArrO,NpArrC)  
    min_low = np.nanmin(MINBAR[-TotalLength:])
    left_high = np.nanmax(MAXBAR[-TotalLength:-RightSampling-1])
    right_high = np.nanmax(MAXBAR[-RightSampling:])
    left_low = np.nanmin(MINBAR[-TotalLength:-RightSampling-1])
    # right_low = np.nanmin(MINBAR[-RightSampling:])
    if left_high-min_low<LeftThreshold or right_high-min_low<RightThreshold:
        return False
    _MID=(MINBAR[-RightSampling-1]+MAXBAR[-RightSampling-1])/2 
    mode=min(ModeSetting,3)   
    modeenable=mode==0 or (mode==1 and left_high==MAXBAR[-TotalLength]) or (mode==2 and right_high==MAXBAR[-1]) or (mode==3 and left_high==MAXBAR[-TotalLength] and right_high==MAXBAR[-1])
    vbottom=min_low==MINBAR[-RightSampling-1] and left_low>min_low and min(left_high,right_high)>_MID and modeenable
    return vbottom
def is_consecutive_up(opens, closes, length):
    """
    Check if there are consecutive up candles for the given length, where each close is higher than the open,
    and each close is higher than the previous close.
    
    Parameters:
        opens (np.array): Array of open prices.
        closes (np.array): Array of close prices.
        length (int): Number of consecutive candles to check.
    
    Returns:
        bool: True if there are consecutive up candles, False otherwise.
    """
    if len(opens) < length or len(closes) < length:
        return False
    # Check if all close prices are greater than open prices for the given length
    consecutive_up = np.all(closes[-length:] > opens[-length:])
    # Check if each close is higher than the previous close
    consecutive_closes_up = np.all(np.diff(closes[-length:]) > 0)
    return consecutive_up and consecutive_closes_up

def is_consecutive_down(opens, closes, length):
    """
    Check if there are consecutive down candles for the given length, where each close is lower than the open,
    and each close is lower than the previous close.
    
    Parameters:
        opens (np.array): Array of open prices.
        closes (np.array): Array of close prices.
        length (int): Number of consecutive candles to check.
    
    Returns:
        bool: True if there are consecutive down candles, False otherwise.
    """
    if len(opens) < length or len(closes) < length:
        return False
    # Check if all close prices are less than open prices for the given length
    consecutive_down = np.all(closes[-length:] < opens[-length:])
    # Check if each close is lower than the previous close
    consecutive_closes_down = np.all(np.diff(closes[-length:]) < 0)
    return consecutive_down and consecutive_closes_down
def check_last_elements(queue, num_elements=3):
    """
    Check if there is any 'True' in the last 'num_elements' of the deque.
    
    Parameters:
        queue (collections.deque): The deque to check.
        num_elements (int): Number of elements from the end to check for True.
    
    Returns:
        bool: True if any of the specified last elements is True, False otherwise.
    """
    npqueue=np.array(queue)
    return any(npqueue[-min(num_elements, npqueue.size):])
scode,K_btime,K_FastCycle,K_SlowCycle,K_BOLLCycle,min1_angle,min3_angle='',0,0,0,0,0,0
近带通道宽度比例,中带通道宽度比例,远带通道宽度比例=0.0,0.0,0.0
开盘切换盘中计数,开盘行情角度阈值,开盘行情速度阈值,开盘行情回撤止盈启动阈值,开盘行情利润回撤平仓比例=0,0,0,0,0
盘中行情角度阈值,盘中行情速度阈值,盘中行情回撤止盈启动阈值,盘中行情利润回撤平仓比例=0,0,0,0
VRL_Left,VRL_Right,VRL_Left_Mangle,VRL_Right_Mangle,VRL_Left_Speed,VRL_Right_Speed,VRL_Mode,VRL_KeepUp=0,0,0,0,0,0,0,0
TradeSW,Lots,ovprice_tick,CloseTime,sublength=0,0,0,0,0
def initialize(context): 
    global g_params,scode,K_btime,K_FastCycle,K_SlowCycle,K_BOLLCycle,min1_angle,min3_angle
    global 近带通道宽度比例,中带通道宽度比例,远带通道宽度比例
    global 开盘切换盘中计数,开盘行情角度阈值,开盘行情速度阈值,开盘行情回撤止盈启动阈值,开盘行情利润回撤平仓比例
    global 盘中行情角度阈值,盘中行情速度阈值,盘中行情回撤止盈启动阈值,盘中行情利润回撤平仓比例
    global VRL_Left,VRL_Right,VRL_Left_Mangle,VRL_Right_Mangle,VRL_Left_Speed,VRL_Right_Speed,VRL_Mode,VRL_KeepUp
    global TradeSW,Lots,ovprice_tick,CloseTime,sublength

    scode   = g_params['信号合约'] # 
    K_btime = g_params['基础时间'] # 
    K_FastCycle = g_params['基础快周期'] # 
    K_SlowCycle = g_params['基础慢周期'] #
    K_BOLLCycle = g_params['布林通道周期']
    min1_angle = g_params['1分钟角度取样K线数']
    min3_angle = g_params['3分钟角度取样K线数']

    近带通道宽度比例 = g_params['近带通道宽度比例']
    中带通道宽度比例 = g_params['中带通道宽度比例']
    远带通道宽度比例 = g_params['远带通道宽度比例']

    开盘切换盘中计数 = g_params['开盘切换盘中计数']
    开盘行情角度阈值 = g_params['开盘行情角度阈值']
    开盘行情速度阈值 = g_params['开盘行情速度阈值']
    开盘行情回撤止盈启动阈值 = g_params['开盘行情回撤止盈启动阈值']
    开盘行情利润回撤平仓比例 = g_params['开盘行情利润回撤平仓比例']

    盘中行情角度阈值 = g_params['盘中行情角度阈值']
    盘中行情速度阈值 = g_params['盘中行情速度阈值']
    盘中行情回撤止盈启动阈值 = g_params['盘中行情回撤止盈启动阈值']
    盘中行情利润回撤平仓比例 = g_params['盘中行情利润回撤平仓比例']

    VRL_Left = g_params['V反形态左侧取样K线数']
    VRL_Right = g_params['V反形态右侧取样K线数']
    # VRL_Left_Mangle = g_params['V反形态左侧角度阈值']
    # VRL_Right_Mangle = g_params['V反形态右侧角度阈值']
    VRL_Left_Speed = g_params['V反形态左侧速度阈值']
    VRL_Right_Speed = g_params['V反形态右侧速度阈值']
    VRL_Mode = g_params['V反形态模式选择']
    VRL_KeepUp = g_params['V反形态右侧中点站稳时长']

    TradeSW = g_params['交易方向开关']
    Lots = g_params['下单手数']
    ovprice_tick = g_params['超价跳数']
    CloseTime = g_params['收盘倒计时分钟数设置']
    sublength = g_params['订阅数据长度']
    AL=2000 if sublength>2000 else sublength#计算回溯数据长度      
    SetBarInterval(scode, K_btime, K_FastCycle,sublength,AL) #订阅信号合约    
    SetBarInterval(scode, K_btime, K_SlowCycle,sublength,AL, isTrigger=True) #订阅信号合约
    #SetBarInterval(tcode,'T', 1,1) #订阅交易合约

    SetActual()           #设置实盘运行
    SetOrderWay(1)        #设置K线走完后发单
    SetTriggerType(1)     #设置即时行情触发
    SetTriggerType(5)     #设置K线触发

# 策略触发事件每次触发时都会执行该函数
DLS=g_params['订阅数据长度']
TradeStatus,BKpostion_status,SKpostion_status=0,0,0
DayBarsPos,DayStatus,DayVol,DayMoney,DayHigh,DayLow=0,0,0.0,0.0,0.0,0.0
mDayBarsPos,mDayStatus,mDayVol,mDayMoney,mDayHigh,mDayLow=0,0,0.0,0.0,0.0,0.0
TDS,BKS,SKS,BPS,SPS,HH1,LL1,BKPH,SKPL,BKT1,SKT1,BKT21,SKT21=0,0,0,0,0,0,999999999,0.0,0.0,0.0,0.0,0,0
JJX,mJJX=0.0,0.0
V_HH,V_LL=0.0,0.0
JJXQ,mJJXQ,VJJXQ,JDSQ,JDXQ=deque([0,0],maxlen=30),deque([0,0],maxlen=30),deque([0,0],maxlen=30),deque([0,0],maxlen=30),deque([0,0],maxlen=30)
V_TopQ,V_BottomQ,mV_TopQ,mV_BottomQ=deque([0]*3,maxlen=3),deque([0]*3,maxlen=3),deque([0]*6,maxlen=3),deque([0]*6,maxlen=3)
HCS,sBARS,mBARS,buys,sells=deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3)
def handle_data(context):
    global TradeStatus,BKpostion_status,SKpostion_status
    global DayBarsPos,DayStatus,DayVol,DayMoney,DayHigh,DayLow
    global mDayBarsPos,mDayStatus,mDayVol,mDayMoney,mDayHigh,mDayLow
    global TDS,BKS,SKS,BPS,SPS,HH1,LL1,BKPH,SKPL,BKT1,SKT1,BKT21,SKT21
    global JJX,mJJX
    global V_HH,V_LL

    O =Open(scode, K_btime,K_FastCycle)   # 信号合约收盘价格
    H =High(scode, K_btime,K_FastCycle)   # 信号合约最高价
    L =Low(scode, K_btime,K_FastCycle)    # 信号合约最低价
    C =Close(scode,K_btime, K_FastCycle)  # 信号合约收盘价格
    D =Date(scode,K_btime, K_FastCycle)   # 信号合约日期
    V =Vol(scode,K_btime, K_FastCycle)    # 信号合约成交量
    OS=Open(scode,K_btime, K_SlowCycle)
    HS=High(scode,K_btime, K_SlowCycle)
    LS=Low(scode,K_btime, K_SlowCycle)
    CS=Close(scode,K_btime, K_SlowCycle)
    VS=Vol(scode,K_btime, K_SlowCycle)
    AVPrice=Average(scode,K_btime,K_FastCycle)
    mAVPrice=Average(scode,K_btime,K_SlowCycle)
    MINPRICE=PriceTick(scode)           # 交易合约最小变动价

    HTS=1 if context.strategyStatus()=="C" else 0  
    s_CurrentBar=CurrentBar(scode, K_btime,K_FastCycle)    
    sBARS.append(s_CurrentBar)

    if sBARS[0]>0 and sBARS[1]<sBARS[2]:
        BKS=0
        SKS=0
        BPS=0
        SPS=0 
        if D==DayStatus:
            DayBarsPos+=1     
            DayVol+=V[-1]
            DayMoney+=V[-1]*AVPrice[-1]
            DayHigh=max(DayHigh,H[-1])
            DayLow=min(DayLow,L[-1])            
        else:
            DayStatus=D
            DayBarsPos=1
            mDayBarsPos=1
            DayVol=V[-1]
            DayMoney=V[-1]*AVPrice[-1]
            DayHigh=H[-1]
            DayLow=L[-1]
            TradeStatus=0
        JJX=DayMoney/DayVol if DayVol>0 else AVPrice[-1]
        JJXQ.append(JJX)

    m_CurrentBar=CurrentBar(scode, K_btime,K_SlowCycle)    
    mBARS.append(m_CurrentBar)
    if mBARS[0]>0 and mBARS[1]<mBARS[2]:
        BKS=0
        SKS=0
        BPS=0
        SPS=0 
        if D==mDayStatus:
            mDayBarsPos+=1
            mDayVol+=VS[-1]
            mDayMoney+=VS[-1]*mAVPrice[-1]
        else:
            mDayStatus=D
            mDayBarsPos=1
            mDayVol=VS[-1]
            mDayMoney=VS[-1]*mAVPrice[-1]
            TradeStatus=0            
        mJJX=mDayMoney/mDayVol if mDayVol>0 else mAVPrice[-1]
        mJJXQ.append(mJJX)

    if TradeStatus==0 and mDayBarsPos>开盘切换盘中计数:
        TradeStatus=1
        PlotStickLine("开盘/盘中转换", DayHigh, DayLow, 0xffff00, True, False, 0)

    if len(C)<5:
        return  
    T_JJX=mJJX if TradeStatus==1 else JJX
    JDS=T_JJX+近带通道宽度比例/100*AVPrice[-1]
    JDX=T_JJX-近带通道宽度比例/100*AVPrice[-1]
    JZS=T_JJX+中带通道宽度比例/100*AVPrice[-1]
    JZX=T_JJX-中带通道宽度比例/100*AVPrice[-1]
    JYS=T_JJX+远带通道宽度比例/100*AVPrice[-1]
    JYX=T_JJX-远带通道宽度比例/100*AVPrice[-1]

    if mBARS[0]>0 and mBARS[1]<mBARS[2]:
        JDSQ.append(JDS)
        JDXQ.append(JDX)

    PlotNumeric('均价',T_JJX,0xffff00,True,False)
    PlotNumeric('近带通道上',JDS,0x00ffff,True,False)
    PlotNumeric('近带通道下',JDX,0x00ffff,True,False)
    # PlotNumeric('中带通道上',JZS,0x00ff00,True,False)
    # PlotNumeric('中带通道下',JZX,0x00ff00,True,False)
    # PlotNumeric('远带通道上',JYS,0xffff00,True,False)
    # PlotNumeric('远带通道下',JYX,0xffff00,True,False)


    BOLLStd=STD(CS,K_BOLLCycle)
    BOOLMid=MA(CS,K_BOLLCycle)
    BOOLUp=BOOLMid+2*BOLLStd
    BOOLDown=BOOLMid-2*BOLLStd

    PlotNumeric('布林通道中',BOOLMid[-1],0xffffff,True,False)
    # PlotNumeric('布林通道上',BOOLUp[-1],0x00ff00,True,False)
    # PlotNumeric('布林通道下',BOOLDown[-1],0xff0000,True,False)

    # PlotNumeric('日内高点',DayHigh,0xff0000,True,False)
    # PlotNumeric('日内低点',DayLow,0x00ff00,True,False)

    # if C[-1]>JJX>DayLow:
    #     for i in (0.5,0.618,1.0,1.382,1.618,2.0):
    #         ClosePrice=JJX+(JJX-DayLow)*i
    #         PlotNumeric('多单'+str(i)+'倍止盈线',ClosePrice,0xff0000,True,False)
    # if C[-1]<JJX<DayHigh:
    #     for i in (0.5,0.618,1.0,1.382,1.618,2.0):
    #         ClosePrice=JJX-(DayHigh-JJX)*i
    #         PlotNumeric('空单'+str(i)+'倍止盈线',ClosePrice,0x00ff00,True,False)
                
    
    # 计算角度
    NPJJXQ=np.array(JJXQ)
    angle =LINEARREG_ANGLE(NPJJXQ,min1_angle)
    NPmJJXQ=np.array(mJJXQ) 
    mangle =LINEARREG_ANGLE(NPmJJXQ,min3_angle)

    PlotNumeric('1分钟最新K线角度', angle[-1], 0x00ffff, False, False,0,"均价线角度")
    PlotNumeric('3分钟前3K线角度', mangle[-1], 0xffff00, False, False,0,"均价线角度")
    PlotNumeric('角度零轴', 0, 0xffffff, False, False,0,"均价线角度")

    # AVPslope,AVPangle=calculate_slope_angle(AVPrice[-2:])
    # AVPslope2,AVPangle2=calculate_slope_angle(AVPrice[-3:])
    # AVPslope3,AVPangle3=calculate_slope_angle(AVPrice[-4:])
    # PlotNumeric('均价最新K线角度', AVPangle, 0x00ffff, False, False,0,"角度AVP")
    # PlotNumeric('均价前2K线角度', AVPangle2, 0xff0000, False, False,0,"角度AVP")
    # PlotNumeric('均价前3K线角度', AVPangle3, 0x00ff00, False, False,0,"角度AVP")
    # PlotNumeric('均价角度零轴', 0, 0xffffff, False, False,0,"角度AVP")


    _VTS=VTS(Time())
    OT=SessionOpenTime(scode)
    CT=SessionCloseTime(scode)
    trade_times=len(CT)
    TradeEnbTime=TimeTo_Minutes(_VTS[3])-TimeTo_Minutes(_VTS[2])>CloseTime
    TradeOffTime=CloseTime>=TimeTo_Minutes(CT[trade_times-1])-TimeTo_Minutes(Time())

    DayMIDPrice=(DayLow+DayHigh)/2 #日内价格中线
    speed1=(C[-1]/O[-1]-1)*100 #1K动量速度(%)
    speed2=(C[-1]/O[-2]-1)*100 #2K动量速度(%)
    speed3=(C[-1]/O[-3]-1)*100 #3K动量速度(%)
    PlotBar('3K动量速度',speed3,0,0XFF0000 if speed3>0 else 0X00FF00,False,True,0,"动量速度")
    PlotNumeric('3K动量零轴',0,0xffffff,False,False,0,"动量速度")

    # 开盘行情开仓条件
    EveryUP3=is_consecutive_up(O,C,3) #三连阳
    EveryDown3=is_consecutive_down(O,C,3) #三连阴
    BKS1=TradeStatus==0 and DayBarsPos>=2 and angle[-1]> 开盘行情角度阈值 and angle[-1]>=angle[-2] and EveryUP3 and C[-1]<JDS
    SKS1=TradeStatus==0 and DayBarsPos>=2 and angle[-1]<-开盘行情角度阈值 and angle[-1]<=angle[-2] and EveryDown3 and C[-1]>JDX

    # 盘中行情开仓条件     
    if len(JDXQ)<VRL_Left+VRL_Right+1:
        return
    TotalLength=VRL_Left+VRL_Right+1
    _HH=np.nanmax(HS[-TotalLength:])
    _LL=np.nanmin(LS[-TotalLength:])
    _angle =LINEARREG_ANGLE(NPmJJXQ,TotalLength)
    V_Top1=V_TopPattern(HS,LS,VRL_Mode,VRL_Left,VRL_Right,VRL_Left_Speed*MINPRICE,VRL_Right_Speed*MINPRICE) and _HH>JDX
    V_Bottom1=V_BottomPattern(HS,LS,VRL_Mode,VRL_Left,VRL_Right,VRL_Left_Speed*MINPRICE,VRL_Right_Speed*MINPRICE) and _LL<JDS
    
    V_Top2=V_TopPatternEntity(OS,CS,VRL_Mode,VRL_Left,VRL_Right,VRL_Left_Speed*MINPRICE,VRL_Right_Speed*MINPRICE)
    V_Bottom2=V_BottomPatternEntity(OS,CS,VRL_Mode,VRL_Left,VRL_Right,VRL_Left_Speed*MINPRICE,VRL_Right_Speed*MINPRICE)
    _seconds=time_int_to_seconds(context.dateTimeStamp())
    if  V_Top1:
        V_HH=_HH
    if  V_Bottom1:
        V_LL=_LL
    if sBARS[0]>0 and sBARS[1]<sBARS[2]:
        V_TopQ.append(V_Top1)
        V_BottomQ.append(V_Bottom1)
    if mBARS[0]>0 and mBARS[1]<mBARS[2]:
        mV_TopQ.append(V_Top1)
        mV_BottomQ.append(V_Bottom1)

    BKCondition21=C[-1]>=V_LL+VRL_Left_Speed*MINPRICE/2
    SKCondition21=C[-1]<=V_HH-VRL_Right_Speed*MINPRICE/2
    if BKCondition21 and check_last_elements(mV_BottomQ,3) and BKT21==0:
        BKT21=_seconds
    if BKT21>0 and _seconds-BKT21<VRL_KeepUp*60 and not BKCondition21:
        BKT21=0
    if SKCondition21 and check_last_elements(mV_TopQ,3) and SKT21==0:
        SKT21=_seconds
    if SKT21>0 and _seconds-SKT21<VRL_KeepUp*60 and not SKCondition21:
        SKT21=0   
    BKS21=TradeStatus==1 and mangle[-1]>0 and mangle[-1]>mangle[-2] and check_last_elements(mV_BottomQ,3) and min(O[-1],C[-1])>JDX and BKT21>0 and _seconds-BKT21>=VRL_KeepUp*60 
    SKS21=TradeStatus==1 and mangle[-1]<0 and mangle[-1]<mangle[-2] and check_last_elements(mV_TopQ,3) and max(O[-1],C[-1])<JDS and SKT21>0  and _seconds-SKT21>=VRL_KeepUp*60

    if SKS21:
        # LogInfo(D,'日',Time(),'V反形态顶部开仓',_seconds,BKT21)
        PlotBar("V反形态顶部", H[-VRL_Right-1], L[-VRL_Right-1], 0xffff00, True, False, VRL_Right)
    if BKS21:
        # LogInfo(D,'日',Time(),'V反形态底部开仓'),_seconds,SKT21
        PlotBar("V反形态底部", H[-VRL_Right-1], L[-VRL_Right-1], 0xff00ff, True, False, VRL_Right)

    LogInfo(D,'日',Time(),SessionOpenTime(scode),'===',SessionCloseTime(scode))
    BK1=TradeEnbTime and BKS1
    SK1=TradeEnbTime and SKS1
    BK21=TradeEnbTime and BKS21
    SK21=TradeEnbTime and SKS21
    if HTS==0:
        # LogInfo(D,'日',Time(),'历史回测阶段',_VTS) 
        # 开盘行情开仓                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
        if  BK1 and BuyPosition(scode) == 0:
            LogInfo(D,'日',Time(),'开盘行情多单开仓')
            Buy(Lots,  C[-1])
            BKPH=0 
            BKT1=0  
            BKpostion_status= 1
        elif SK1 and SellPosition(scode) == 0:
            LogInfo(D,'日',Time(),'开盘行情空单开仓')
            SellShort(Lots, C[-1])
            SKPL=0
            SKT1=0
            SKpostion_status= 1
        # 盘中行情开仓
        elif BK21 and BuyPosition(scode) == 0:
            LogInfo(D,'日',Time(),'V反形态多单开仓')
            Buy(Lots,  C[-1])
            BKPH=0 
            BKT1=0  
            BKT21=0
            BKpostion_status= 2
        elif SK21 and SellPosition(scode) == 0:
            LogInfo(D,'日',Time(),'V反形态空单开仓')
            SellShort(Lots, C[-1])
            SKPL=0
            SKT1=0
            SKT21=0
            SKpostion_status= 2


        if  SellPosition(scode) > 0:
            _seconds=time_int_to_seconds(context.dateTimeStamp())
            if SKPL==0:
                SKPL=AvgEntryPrice(scode)
            else:
                SKPL=min(L[-1],SKPL)  
            EntryPrice=AvgEntryPrice(scode)
            if SKT1==0 and C[-1]>DayMIDPrice:
                SKT1=_seconds
            elif SKT1>0 and _seconds-SKT1<20 and C[-1]<DayMIDPrice:
                SKT1=0

            BP1=TradeOffTime or C[-1]>JDS 
            BP2=SKpostion_status==1 and SKT1>0 and _seconds-SKT1>=20
            BP3=SKpostion_status==1 and SKPL>0 and SKPL<JJX-(DayHigh-JJX)*1.618 and C[-1]>O[-1] #and C[-1]<JJX-(DayHigh-JJX)*1.5 
            BP4=SKpostion_status==1 and (SKPL/EntryPrice-1)<-开盘行情回撤止盈启动阈值 and (EntryPrice-C[-1])<(EntryPrice-SKPL)*开盘行情利润回撤平仓比例
            if BP1:
                LogInfo(D,'日',Time(),'空单收盘平仓或信号平仓 盈利高点点数=>>',EntryPrice-SKPL,'实盈点数=>>',EntryPrice-C[-1])
                BuyToCover(Lots, C[-1])
            elif BP2:
                LogInfo(D,'日',Time(),'空单最新价大于中轨持续20秒平仓 盈利高点点数=>>',EntryPrice-SKPL,'实盈点数=>>',EntryPrice-C[-1])
                BuyToCover(Lots, C[-1])
            elif BP3:
                LogInfo(D,'日',Time(),'空单1.618倍止盈线平仓 盈利高点点数=>>',EntryPrice-SKPL,'实盈点数=>>',EntryPrice-C[-1])
                BuyToCover(Lots, C[-1])
            elif BP4:
                LogInfo(D,'日',Time(),'空单止盈回撤平仓 盈利高点点数=>>',EntryPrice-SKPL,'实盈点数=>>',EntryPrice-C[-1])
                BuyToCover(Lots, C[-1])
            BP24=SKpostion_status==2 and (SKPL/EntryPrice-1)<-开盘行情回撤止盈启动阈值 and (EntryPrice-C[-1])<(EntryPrice-SKPL)*开盘行情利润回撤平仓比例 and C[-1]>O[-1]
            if BP24:
                LogInfo(D,'日',Time(),'V反形态空单止盈回撤平仓 盈利高点点数=>>',EntryPrice-SKPL,'实盈点数=>>',EntryPrice-C[-1])
                BuyToCover(Lots, C[-1])
        elif BuyPosition(scode) > 0:
            _seconds=time_int_to_seconds(context.dateTimeStamp())
            if BKPH==0:
                BKPH=AvgEntryPrice(scode)
            else:
                BKPH=max(H[-1],BKPH)  
            EntryPrice=AvgEntryPrice(scode)

            if BKT1==0 and C[-1]<DayMIDPrice:
                BKT1=_seconds
            elif BKT1>0 and _seconds-BKT1<20 and C[-1]>DayMIDPrice:
                BKT1=0
            SP1=TradeOffTime or C[-1]<JDX 
            SP2=BKpostion_status==1 and BKT1>0 and _seconds-BKT1>=20
            SP3=BKpostion_status==1 and BKPH>0 and BKPH>JJX+(JJX-DayLow)*1.618 and C[-1]<O[-1] #and C[-1]<JJX+(JJX-DayLow)*1.5
            SP4=BKpostion_status==1 and (BKPH/EntryPrice-1)> 开盘行情回撤止盈启动阈值 and (C[-1]-EntryPrice)<(BKPH-EntryPrice)*开盘行情利润回撤平仓比例
            if SP1:
                LogInfo(D,'日',Time(),'多单收盘平仓或信号平仓 盈利高点点数=>>',BKPH-EntryPrice,'实盈点数=>>',C[-1]-EntryPrice)
                Sell(Lots, C[-1])
            elif SP2:
                LogInfo(D,'日',Time(),'多单最新价小于中轨持续20秒平仓 盈利高点点数=>>',BKPH-EntryPrice,'实盈点数=>>',C[-1]-EntryPrice)
                Sell(Lots, C[-1])
            elif SP3:
                LogInfo(D,'日',Time(),'多单1.618倍止盈线平仓 盈利高点点数=>>',BKPH-EntryPrice,'实盈点数=>>',C[-1]-EntryPrice)
                Sell(Lots, C[-1])
            elif SP4:
                LogInfo(D,'日',Time(),'多单止盈回撤平仓 盈利高点点数=>>',BKPH-EntryPrice,'实盈点数=>>',C[-1]-EntryPrice)
                Sell(Lots, C[-1])
            SP24=BKpostion_status==2 and (BKPH/EntryPrice-1)> 开盘行情回撤止盈启动阈值 and (C[-1]-EntryPrice)<(BKPH-EntryPrice)*开盘行情利润回撤平仓比例 and C[-1]<O[-1]
            if SP24:
                LogInfo(D,'日',Time(),'V反形态多单止盈回撤平仓 盈利高点点数=>>',BKPH-EntryPrice,'实盈点数=>>',C[-1]-EntryPrice)
                Sell(Lots, C[-1])
        if BKT1>0 and BuyPosition(scode) == 0:
            BKT1=0
        if SKT1>0 and SellPosition(scode) == 0:
            SKT1=0


def floattime_sum(floatin1, floatin2, len_set=12):  # 高精度浮点时间求和（精确到毫秒）
    # 设置浮点数格式，保留len_set位小数
    lensave = f"%0.{len_set}f"
    
    # 格式化浮点数并提取各时间部分
    def extract_time_parts(floatin):
        strfloat = lensave % floatin
        return int(strfloat[2:4]), int(strfloat[4:6]), int(strfloat[6:8]), int(strfloat[8:11])
    
    h1, m1, s1, ms1 = extract_time_parts(floatin1)
    h2, m2, s2, ms2 = extract_time_parts(floatin2)
    
    # 计算总和并处理进位
    total_ms = ms1 + ms2
    ms_carry = total_ms // 1000
    new_ms = total_ms % 1000
    
    total_s = s1 + s2 + ms_carry
    s_carry = total_s // 60
    new_s = total_s % 60
    
    total_m = m1 + m2 + s_carry
    m_carry = total_m // 60
    new_m = total_m % 60
    
    new_h = h1 + h2 + m_carry
    new_h = min(new_h, 99)  # 限制小时数不超过99
    
    # 组合新的浮点时间字符串并转换回浮点数
    new_str_time = f"0.{new_h:02}{new_m:02}{new_s:02}{new_ms:03}"
    return float(new_str_time)

def TimeTo_Minutes(time_in):
    timestr='%0.6f'%time_in
    hsave=int(timestr[2:4])
    msave=int(timestr[4:6])
    tcout=hsave*60+msave
    return tcout

def SessionOpenTime(contractId=''):  # 获取交易时段开盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler:continue
        tlout.append(GetSessionStartTime(contractId, i))  # 获取每个交易时段的开盘时间并加入列表
    return tlout

def SessionCloseTime(contractId=''):  # 获取交易时段收盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler-1:continue
        tlout.append(GetSessionEndTime(contractId, i))  # 获取每个交易时段的收盘时间并加入列表
    return tlout

def time_string_to_seconds(time_str):
    """
    Convert a time string in the format 'YYYYMMDDHHMMSSmmm' to the number of seconds
    since the Unix epoch (1970-01-01 00:00:00 UTC).
    
    Parameters:
        time_str (str): Time string in the format 'YYYYMMDDHHMMSSmmm'.
    
    Returns:
        int: Total seconds since the Unix epoch.
    """
    if time_str == "0":
        return 0  # Return 0 or an appropriate default value for historical phases or no real-time data
    
    # Parse the time string
    year = int(time_str[0:4])
    month = int(time_str[4:6])
    day = int(time_str[6:8])
    hour = int(time_str[8:10])
    minute = int(time_str[10:12])
    second = int(time_str[12:14])
    millisecond = int(time_str[14:17])
    
    # Create a datetime object
    dt = datetime(year, month, day, hour, minute, second, millisecond * 1000)
    
    # Calculate the total seconds since the Unix epoch
    epoch = datetime(1970, 1, 1)
    total_seconds = (dt - epoch) // timedelta(seconds=1)  # Use integer division to get whole seconds
    
    return total_seconds

from datetime import datetime, timedelta

def time_int_to_seconds(time_int):
    """
    Convert a time integer in the format YYYYMMDDHHMMSSmmm to the number of seconds
    since the Unix epoch (1970-01-01 00:00:00 UTC).
    
    Parameters:
        time_int (int): Time integer in the format YYYYMMDDHHMMSSmmm.
    
    Returns:
        int: Total seconds since the Unix epoch.
    """
    if time_int == 0:
        return 0  # Return 0 for historical phases or no real-time data
    
    # Extract each component directly using integer arithmetic
    milliseconds = time_int % 1000
    time_int //= 1000
    seconds = time_int % 100
    time_int //= 100
    minutes = time_int % 100
    time_int //= 100
    hours = time_int % 100
    time_int //= 100
    day = time_int % 100
    time_int //= 100
    month = time_int % 100
    year = time_int // 100
    
    # Create a datetime object
    dt = datetime(year, month, day, hours, minutes, seconds, milliseconds * 1000)
    
    # Calculate the total seconds since the Unix epoch
    epoch = datetime(1970, 1, 1)
    total_seconds = (dt - epoch) // timedelta(seconds=1)  # Use integer division to get whole seconds
    
    return total_seconds

def VTS(time_in, contractId=''):  # 根据输入时间和合约ID计算交易时段
    RTS, CTS, TSession = [], [], []  # 初始化三个列表，用于存储修正后的时间、收盘时间和交易时段
    opentimet = SessionOpenTime(contractId)  # 获取所有交易时段的开盘时间
    Closetimet = SessionCloseTime(contractId)  # 获取所有交易时段的收盘时间
    for open_time, close_time in zip(opentimet, Closetimet):
        if time_in > open_time:  # 判断输入时间是否在开盘时间之后
            RTS.append(time_in)  # 如果是，加入RTS列表
        else:
            RTS.append(floattime_sum(time_in, 0.24))  # 如果不是，修正时间后加入RTS列表
        
        if close_time > open_time:  # 判断收盘时间是否在开盘时间之后
            CTS.append(close_time)  # 如果是，加入CTS列表
        else:
            CTS.append(floattime_sum(close_time, 0.24))  # 如果不是，修正时间后加入CTS列表
        
        if open_time < RTS[-1] < CTS[-1]:  # 判断修正后的时间是否在交易时段内
            TSession.append(len(RTS) - 1)  # 如果是，加入TSession列表

    if len(TSession) == 1:  # 如果只有一个交易时段
        idx = TSession[0]
        return idx, opentimet[idx], RTS[idx], CTS[idx]  # 返回交易时段和相关时间
    else:
        return -1, time_in, time_in, time_in  # 否则返回默认值
def tim_trigger(BK,SK,qty,itk,tcode):#盘中实时开仓
    global BKS,SKS
    if BK and BKS==0 and (A_BuyPosition(tcode) == 0) :
        iprc = min(Q_AskPrice(tcode) +itk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        A_SendOrder(Enum_Buy(), Enum_Entry(), qty, iprc,tcode) 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单买入开仓价==>",iprc,"买入数量==>",qty)
        BKS=1    
    elif SK and SKS==0 and (A_SellPosition(tcode) == 0):    
        iprc = max(Q_BidPrice(tcode) - itk*PriceTick(tcode), Q_LowLimit(tcode))  # 对盘超价                         
        A_SendOrder(Enum_Sell(), Enum_Entry(), qty, iprc,tcode)   
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单卖出开仓价==>",iprc,"卖出数量==>",qty)
        SKS=1    

def tim_trigger_Exit(BP,SP,otk,tcode,clots):#盘中实时平仓
    global BKS,SKS,BPS,SPS
    if BP and BPS==0 and A_SellPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_SellPosition(tcode))
        prc = min(Q_AskPrice(tcode) +otk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        if ExchangeName(tcode) not in ['SHFE','INE']:    
            retExit, ExitOrderId=A_SendOrder(Enum_Buy(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodaySellPosition(tcode)
            dlots=lots-tlots            
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),lots, prc,tcode) #今仓足够平仓,上期所能交所优先超价全部平今仓    
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Buy(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Buy(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单买入平仓价==>",prc,"买入平仓数量==>",_lots)
        BPS=1  
        if SKS==1:SKS=2      
    elif SP and SPS==0 and A_BuyPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_BuyPosition(tcode))
        prc = max(Q_BidPrice(tcode) - otk*PriceTick(tcode), Q_LowLimit(tcode))
        if ExchangeName(tcode) not in ['SHFE','INE']:
            retExit, ExitOrderId=A_SendOrder(Enum_Sell(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodayBuyPosition(tcode)
            dlots=lots-tlots
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),lots, prc,tcode)   #今仓足够平仓,上期所能交所优先超价全部平今仓  
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Sell(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Sell(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单卖出平仓价==>",prc,"卖出平仓数量==>",_lots)
        SPS=1    
        if BKS==1:BKS=2   