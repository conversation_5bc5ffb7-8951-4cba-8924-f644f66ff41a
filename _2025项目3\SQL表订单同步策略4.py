from math import log
import os
import time
import copy
import pymysql
import threading
import pandas as pd
from queue import Queue
from collections import deque
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_HALF_UP
import shelve


g_params['配置文件夹路径'] = "C:\MT4"
g_params['配置文件名'] = "配置文件与订阅合约.xlsx"
g_params['开仓超价跳数'] = 1
g_params['平仓超价跳数'] = 2
g_params['收盘清仓倒计时分钟'] = 5

trade_order_filedir = g_params['配置文件夹路径']
trade_config_file   = trade_order_filedir+"\\"+g_params['配置文件名'] 
trade_config_DATA   =pd.read_excel(trade_config_file,sheet_name = 0)
symbol_Id =trade_config_DATA["使用合约"].dropna().str.upper()
forex_marketId=trade_config_DATA["外盘段映射货币或合约"].dropna().str.upper()
下单量映射倍数=trade_config_DATA["下单量映射倍数"].dropna()
时时价格启用=trade_config_DATA["时时价格启用"].dropna()
平台类型=trade_config_DATA["平台类型"].dropna().str.upper()
注册账号=trade_config_DATA["注册账号"].dropna()
魔术码=trade_config_DATA["魔术码"].dropna()
统一标识前缀=trade_config_DATA["统一标识前缀"].dropna()
追单次数=trade_config_DATA["追单次数"].dropna()
追单秒数=trade_config_DATA["追单秒数"].dropna()
隔夜费=trade_config_DATA["隔夜费"].dropna()
手续费=trade_config_DATA["手续费"].dropna()
保证金比例=trade_config_DATA["保证金比例"].dropna()

服务器地址 = str(trade_config_DATA["服务器地址"].iloc[0])
服务器用户名 = str(trade_config_DATA["服务器用户名"].iloc[0])
链接数据库密码 = str(trade_config_DATA["链接数据库密码"].iloc[0])
数据库名称 = str(trade_config_DATA["数据库名称"].iloc[0])
价格表 = str(trade_config_DATA["价格表"].iloc[0])

class ConnectionPool:
    """数据库连接池管理类"""
    def __init__(self, host, user, password, database, max_connections=5):
        self.host = host
        self.user = user
        self.password = password
        self.database = database
        self.max_connections = max_connections
        self.connections = Queue(maxsize=max_connections)
        self.in_use_count = 0
        self.lock = threading.Lock()  # 线程锁保护连接计数
        
        # 初始化连接池
        LogInfo(f"正在初始化数据库连接池: {self.host}, {self.user}, {self.database}")
        for i in range(max_connections):
            try:
                conn = self.create_connection()
                self.connections.put(conn)
            except Exception as e:
                LogInfo(f"初始化连接池时发生错误: {e}")
    
    def create_connection(self):
        """创建一个新的数据库连接"""
        try:
            connection = pymysql.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                # 增加超时设置
                connect_timeout=10,
                read_timeout=30,
                write_timeout=30
            )
            return connection
        except Exception as e:
            LogInfo(f"创建数据库连接失败: {e}, 参数: {self.host}, {self.user}, {self.database}")
            return None
    
    def get_connection(self):
        """从连接池获取一个连接"""
        with self.lock:
            try:
                if not self.connections.empty():
                    conn = self.connections.get(block=False)
                    self.in_use_count += 1
                    return conn
                elif self.in_use_count < self.max_connections:
                    # 如果队列为空但未达到最大连接数，创建新连接
                    conn = self.create_connection()
                    self.in_use_count += 1
                    return conn
                else:
                    # 达到最大连接数，等待已有连接释放
                    LogInfo("等待数据库连接释放...")
                    conn = self.connections.get(block=True, timeout=10)
                    self.in_use_count += 1
                    return conn
            except Exception as e:
                LogInfo(f"获取数据库连接失败: {e}")
                return None
    
    def release_connection(self, conn):
        """将连接返回连接池"""
        with self.lock:
            if conn:
                try:
                    # 确保连接可用，否则重新创建
                    conn.ping(reconnect=True)
                    self.connections.put(conn)
                except:
                    # 连接已断开，创建新连接放入池中
                    try:
                        new_conn = self.create_connection()
                        self.connections.put(new_conn)
                    except Exception as e:
                        LogInfo(f"重新创建连接失败: {e}")
                finally:
                    self.in_use_count -= 1

class MySQLPool:
    def __init__(self, config, min_size=3, max_size=10):
        self.config = config
        self.min_size = min_size
        self.max_size = max_size
        self.pool = Queue(max_size)
        # 初始化连接池时捕获异常
        for _ in range(min_size):
            conn = self.create_connection()
            if conn:
                self.pool.put(conn)

    def create_connection(self):
        try:
            # 移除不必要的连接字符串构建
            # 确保密码是字符串类型
            config = self.config.copy()
            if 'password' in config and config['password'] is not None:
                config['password'] = str(config['password'])
                
            return pymysql.connect(
                host=config['host'],
                user=config['user'],
                password=config['password'],
                database=config['database'],
                charset=config['charset'],
                cursorclass=config['cursorclass']
            )
        except Exception as e:
            LogInfo(f"数据库连接创建失败: {e}")
            return None

    def get_connection(self):
        try:
            conn = self.pool.get_nowait()
            # 检查连接是否有效
            try:
                conn.ping(reconnect=True)  # 检查连接并尝试重连
                return conn
            except:
                # 连接已失效，创建新连接
                LogInfo("检测到失效连接，正在重新创建...")
                conn = self.create_connection()
                return conn
        except:
            # 池为空时创建新连接
            conn = self.create_connection()
            if conn:
                return conn
            return None

    def release_connection(self, conn):
        if conn is None:
            return
            
        if self.pool.qsize() < self.max_size:
            self.pool.put(conn)
        else:
            try:
                conn.close()
            except:
                pass

class TradeDB:
    """交易数据库接口类"""
    def __init__(self, host_or_pool, user=None, password=None, database=None):
        # 根据传入参数类型判断初始化方式
        if isinstance(host_or_pool, ConnectionPool) or hasattr(host_or_pool, 'get_connection'):
            # 如果传入的是连接池对象（向后兼容）
            self.use_pool = True
            self.db_pool = host_or_pool
            self.connection_pool = None
        else:
            # 如果传入的是连接参数
            self.use_pool = False
            self.db_pool = None
            
            # 确保所有参数都已提供
            if user is None or password is None or database is None:
                LogInfo("错误:初始化TradeDB时缺少必要参数")
                raise ValueError("初始化TradeDB需要提供host, user, password和database")
                
            # 初始化连接池
            self.connection_pool = ConnectionPool(host_or_pool, user, password, database)
            
    def execute_query(self, sql, params=None, fetch=False):
        """执行SQL查询"""
        # 根据初始化方式选择获取连接的方法
        if self.use_pool:
            conn = self.db_pool.get_connection()
        else:
            conn = self.connection_pool.get_connection()
            
        if not conn:
            LogInfo("获取数据库连接失败")
            return None if fetch else False
            
        try:
            with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, params if params else ())
                if fetch:
                    result = cursor.fetchall()
                else:
                    conn.commit()
                    result = True
                return result
        except Exception as e:
            LogInfo(f"执行SQL失败: {e}")
            if not fetch:
                try:
                    conn.rollback()
                except:
                    pass
            return None if fetch else False
        finally:
            # 根据初始化方式选择释放连接的方法
            if self.use_pool:
                self.db_pool.release_connection(conn)
            else:
                self.connection_pool.release_connection(conn)

    def execute_transaction(self, operations):
        """
        执行事务操作
        
        参数:
            operations: 包含(sql, params)元组的列表
        """
        conn = self.connection_pool.get_connection()
        if not conn:
            LogInfo("获取数据库连接失败")
            return False
            
        try:
            with conn.cursor() as cursor:
                conn.begin()
                for sql, params in operations:
                    cursor.execute(sql, params if params else ())
                conn.commit()
                return True
        except Exception as e:
            LogInfo(f"事务执行失败: {e}")
            try:
                conn.rollback()
            except:
                pass
            return False
        finally:
            self.connection_pool.release_connection(conn)

    def get_pending_orders(self, symbol):
        """获取待处理的订单"""
        sql = """
        SELECT * FROM chufakaidanbiao 
        WHERE ahuobi = %s AND bbiaoshi = 0 
        ORDER BY zengjiashijian ASC
        """
        return self.execute_query(sql, (symbol,), fetch=True)

    def update_order_status(self, order_id, status, trade_result=None):
        """更新订单状态"""
        sql = """
        UPDATE chufakaidanbiao 
        SET bbiaoshi = %s, trade_result = %s 
        WHERE adingdanhao = %s
        """
        return self.execute_query(sql, (status, trade_result, order_id))
    
    def close_all_positions(self):
        """获取所有需要平仓的持仓"""
        sql = """
        SELECT * FROM chufakaidanbiao 
        WHERE bbiaoshi = 1
        """
        return self.execute_query(sql, fetch=True)
    
    def update_price(self, table_name, symbol, price, platform_type):
        """
        更新货币价格
        
        如果platform_type为'A'，则将symbol写入huobi字段，price写入jiage字段
        否则按原有逻辑处理
        """
        try:
            if platform_type == 'A':
                # 首先检查A平台是否存在记录
                check_sql = f"""
                SELECT IntID FROM {table_name} 
                WHERE pingtai = 'A'
                """
                result = self.execute_query(check_sql, fetch=True)
                
                if result:
                    # 存在A平台记录，执行更新
                    update_sql = f"""
                    UPDATE {table_name} 
                    SET huobi = %s, jiage = %s 
                    WHERE pingtai = 'A'
                    """
                    update_result = self.execute_query(update_sql, (symbol, price))
                    
                    # if update_result:
                    #     LogInfo(f"成功更新A平台价格: 货币={symbol}, 价格={price}")
                    # else:
                    #     LogInfo(f"A平台价格更新失败: 货币={symbol}, 价格={price}")
                    
                    return update_result
                else:
                    # 不存在A平台记录，执行插入
                    insert_sql = f"""
                    INSERT INTO {table_name} (huobi, jiage, pingtai) 
                    VALUES (%s, %s, 'A')
                    """
                    insert_result = self.execute_query(insert_sql, (symbol, price))
                    
                    if insert_result:
                        LogInfo(f"成功插入A平台价格: 货币={symbol}, 价格={price}")
                    else:
                        LogInfo(f"A平台价格插入失败: 货币={symbol}, 价格={price}")
                    
                    return insert_result
            else:
                # 原有逻辑处理非A平台的情况
                # 首先检查是否存在匹配的记录
                check_sql = f"""
                SELECT IntID FROM {table_name} 
                WHERE huobi = %s AND pingtai = %s
                """
                result = self.execute_query(check_sql, (symbol, platform_type), fetch=True)
                
                if not result:
                    LogInfo(f"无法找到匹配的价格记录: 货币={symbol}, 平台={platform_type}")
                    return False
                
                # 存在匹配记录，执行更新
                update_sql = f"""
                UPDATE {table_name} 
                SET jiage = %s 
                WHERE huobi = %s AND pingtai = %s
                """
                update_result = self.execute_query(update_sql, (price, symbol, platform_type))
                
                return update_result
        except Exception as e:
            LogInfo(f"价格更新操作异常: {e}")
            return False

    def get_new_orders(self, account, magic_number):
        """获取新的开单信号"""
        sql = """
        SELECT * FROM chufakaidanbiao 
        WHERE abiaoshi = 0 AND kaicangzhuzhanghao = %s AND moshuma = %s
        """
        return self.execute_query(sql, (account, magic_number), fetch=True)

    def update_order_after_open(self, order_id, platform_order_id, open_time, magic_number, status_flag=1):
        """
        开仓后更新订单状态
        
        参数:
            order_id: 订单ID
            platform_order_id: 平台订单号
            open_time: 开仓时间
            magic_number: 魔术码
            status_flag: 订单状态标志 (1=已下单, 2=已完成)
        """
        sql = """
        UPDATE chufakaidanbiao 
        SET adingdanhao = %s, akaidanshijian = %s, abiaoshi = %s 
        WHERE IntID = %s AND moshuma = %s
        """
        return self.execute_query(sql, (platform_order_id, open_time, status_flag, order_id, magic_number))


    def get_open_orders(self, account, magic_number, platform_type):
        """获取所有未平仓订单"""
        sql = """
        SELECT * FROM xiangxidanxinxibiao 
        WHERE kaicangzhuzhanghao = %s 
        AND moshuma = %s 
        AND pingtaileixing = %s 
        AND pingcangbiaoshi = 0
        """
        return self.execute_query(sql, (account, magic_number, platform_type), fetch=True)

    def update_closed_order(self, order_id, magic_number, status_flag=2):
        """将订单标记为已平仓
        
        参数:
            order_id: 订单ID
            magic_number: 魔术码
            status_flag: 平仓标志状态，默认为2
        """
        sql = """
        UPDATE xiangxidanxinxibiao 
        SET pingcangbiaoshi = %s 
        WHERE tongyibiaoshi = %s AND moshuma = %s
        """
        result = self.execute_query(sql, (status_flag, order_id, magic_number))
        if result:
            LogInfo(f"订单 {order_id} 已标记为平仓状态{status_flag}")
        else:
            LogInfo(f"更新订单 {order_id} 平仓状态失败")
        return result

    def get_long_orders_to_close(self, account, magic_number, platform_type):
        """获取并锁定需要平仓的多头订单(pingcangbiaoshi=1)"""
        try:
            # 确保参数类型正确
            _account = int(account)
            magic_str = str(int(magic_number))
            
            # 使用MySQL事务和FOR UPDATE锁确保原子性操作
            conn = None
            cursor = None
            orders = []
            
            try:
                # 根据初始化方式获取数据库连接
                if self.use_pool:
                    conn = self.db_pool.get_connection()
                else:
                    conn = self.connection_pool.get_connection()
                
                if not conn:
                    LogInfo("获取数据库连接失败")
                    return []
                
                cursor = conn.cursor(pymysql.cursors.DictCursor)
                
                # 开始事务
                conn.begin()
                
                # 查询需要平仓的多头订单
                sql = """
                SELECT * FROM xiangxidanxinxibiao 
                WHERE kaicangzhuzhanghao = %s 
                AND moshuma = %s 
                AND pingtaileixing = %s 
                AND fangxaing = 'B'
                AND (pingcangbiaoshi = 1 OR pingcangbiaoshi = 2)
                ORDER BY IntID ASC
                LIMIT 1
                FOR UPDATE
                """
                
                cursor.execute(sql, (_account, magic_str, platform_type))
                orders = cursor.fetchall()
                
                current_time = time.strftime("%Y-%m-%d %H:%M:%S")
                # 如果找到订单，标记为处理中
                if orders and len(orders) > 0:
                    update_sql = """
                    UPDATE xiangxidanxinxibiao 
                    SET pingcangbiaoshi = -1, 
                        pingcangshijian = %s
                    WHERE IntID = %s
                    """

                    for order in orders:
                        cursor.execute(update_sql, (current_time, order['IntID']))
                    
                    # 提交事务
                    conn.commit()
                    LogInfo(f"已锁定多头平仓订单: {len(orders)} 个")
                else:
                    # 如果没有找到订单，也提交事务释放锁
                    conn.commit()
                
                return orders
                
            except Exception as e:
                # 发生异常时回滚事务
                if conn:
                    try:
                        conn.rollback()
                    except:
                        pass
                LogInfo(f"查询和锁定多头平仓订单时出错: {e}")
                return []
                
            finally:
                # 关闭游标和释放连接
                if cursor:
                    try:
                        cursor.close()
                    except:
                        pass
                
                if conn:
                    try:
                        if self.use_pool:
                            self.db_pool.release_connection(conn)
                        else:
                            self.connection_pool.release_connection(conn)
                    except:
                        pass
                
        except Exception as e:
            LogInfo(f"获取多头平仓订单处理过程中出错: {e}")
            return []

    def get_short_orders_to_close(self, account, magic_number, platform_type):
        """获取并锁定需要平仓的空头订单(pingcangbiaoshi=1)"""
        try:
            # 确保参数类型正确
            _account = int(account)
            magic_str = str(int(magic_number))
            
            # 使用MySQL事务和FOR UPDATE锁确保原子性操作
            conn = None
            cursor = None
            orders = []
            
            try:
                # 根据初始化方式获取数据库连接
                if self.use_pool:
                    conn = self.db_pool.get_connection()
                else:
                    conn = self.connection_pool.get_connection()
                
                if not conn:
                    LogInfo("获取数据库连接失败")
                    return []
                
                cursor = conn.cursor(pymysql.cursors.DictCursor)
                
                # 开始事务
                conn.begin()
                
                # 查询需要平仓的空头订单
                sql = """
                SELECT * FROM xiangxidanxinxibiao 
                WHERE kaicangzhuzhanghao = %s 
                AND moshuma = %s 
                AND pingtaileixing = %s 
                AND fangxaing = 'S'
                AND (pingcangbiaoshi = 1 OR pingcangbiaoshi = 2)
                ORDER BY IntID ASC
                LIMIT 1
                FOR UPDATE
                """
                
                cursor.execute(sql, (_account, magic_str, platform_type))
                orders = cursor.fetchall()
                
                current_time = time.strftime("%Y-%m-%d %H:%M:%S")
                # 如果找到订单，标记为处理中
                if orders and len(orders) > 0:
                    update_sql = """
                    UPDATE xiangxidanxinxibiao 
                        SET pingcangbiaoshi = -1, 
                        pingcangshijian = %s
                    WHERE IntID = %s
                    """
                    for order in orders:
                        cursor.execute(update_sql, (current_time, order['IntID']))
                    
                    # 提交事务
                    conn.commit()
                    LogInfo(f"已锁定空头平仓订单: {len(orders)} 个")
                else:
                    # 如果没有找到订单，也提交事务释放锁
                    conn.commit()
                
                return orders
                
            except Exception as e:
                # 发生异常时回滚事务
                if conn:
                    try:
                        conn.rollback()
                    except:
                        pass
                LogInfo(f"查询和锁定空头平仓订单时出错: {e}")
                return []
                
            finally:
                # 关闭游标和释放连接
                if cursor:
                    try:
                        cursor.close()
                    except:
                        pass
                
                if conn:
                    try:
                        if self.use_pool:
                            self.db_pool.release_connection(conn)
                        else:
                            self.connection_pool.release_connection(conn)
                    except:
                        pass
                
        except Exception as e:
            LogInfo(f"获取空头平仓订单处理过程中出错: {e}")
            return []

    def get_orders_to_close(self, account, magic_number, platform_type):
        """获取所有需要平仓的订单(pingcangbiaoshi=1或2)，保留此函数以保持向后兼容"""
        sql = """
        SELECT * FROM xiangxidanxinxibiao 
        WHERE kaicangzhuzhanghao = %s 
        AND moshuma = %s 
        AND pingtaileixing = %s 
        AND (pingcangbiaoshi = 1 OR pingcangbiaoshi = 2)
        ORDER BY IntID ASC
        LIMIT 1
        """
        return self.execute_query(sql, (account, magic_number, platform_type), fetch=True)

    def check_risk_control_account(self, account):
        """查询账号的风控信息是否存在"""
        sql = """
        SELECT * FROM fengkongjine 
        WHERE zhanghao = %s
        """
        return self.execute_query(sql, (account,), fetch=True)

    def insert_risk_control(self, account, platform_type, balance, equity, update_time):
        """插入新的风控信息"""
        sql = """
        INSERT INTO fengkongjine 
        (zhanghao, pingtai, yue, jingzhi, genxinshijian) 
        VALUES (%s, %s, %s, %s, %s)
        """
        result = self.execute_query(sql, (account, platform_type, balance, equity, update_time))
        if result:
            LogInfo(f"成功插入账号 {account} 的风控信息")
        else:
            LogInfo(f"插入账号 {account} 的风控信息失败")
        return result

    def update_risk_control(self, account, balance, equity, update_time):
        """更新账号的风控信息"""
        sql = """
        UPDATE fengkongjine 
        SET yue = %s, jingzhi = %s, genxinshijian = %s 
        WHERE zhanghao = %s
        """
        result = self.execute_query(sql, (balance, equity, update_time, account))
        if result:
            LogInfo(f"成功更新账号 {account} 的风控信息")
        else:
            LogInfo(f"更新账号 {account} 的风控信息失败")
        return result

    def get_chase_orders_info(self, account, magic_number):
        """获取追单信息，用于平仓决策"""
        sql = """
        SELECT adingdanhao , zengjiashijian, zhuidancishu 
        FROM chufakaidanbiao
        WHERE abiaoshi = 2 AND bbiaoshi >= 0 AND bbiaoshi != 2
        AND kaicangzhuzhanghao = %s AND moshuma = %s
        """
        result = self.execute_query(sql, (account, magic_number), fetch=True)
        # 记录返回的结果结构，帮助调试
        if result and len(result) > 0:
            LogInfo(f"获取到 {len(result)} 条追单信息，第一条: {result[0]}")
        return result

    def check_order_exists(self, order_id, magic_number):
        """
        检查订单是否已存在于数据库中
        
        参数:
            order_id: 订单ID或统一标识
            magic_number: 魔术码
            
        返回:
            bool: 订单是否存在
        """
        try:
            sql = """
            SELECT COUNT(*) as count FROM xiangxidanxinxibiao 
            WHERE dingdanhao = %s AND moshuma = %s
            """
            
            result = self.execute_query(sql, (order_id, magic_number), fetch=True)
            
            if result and len(result) > 0:
                return result[0]['count'] > 0
            return False
        except Exception as e:
            LogInfo(f"❌ 检查订单存在性时出错: {e}")
            # 表不存在时，假设订单不存在
            return False

    def update_order_profit(self, order_id, magic_number, overnight_fee, commission, profit, total_profit):
        """
        更新订单的盈亏信息
        
        参数:
            order_id: 订单ID
            magic_number: 魔术码
            overnight_fee: 隔夜费
            commission: 手续费
            profit: 盈亏金额
            total_profit: 总盈亏金额（包括成本）
            
        返回:
            bool: 成功返回True，失败返回False
        """
        # 构建更新SQL
        update_sql = """
        UPDATE xiangxidanxinxibiao 
        SET geyefei = %s, shouxufei = %s, yingkuijine = %s, jingyingkuijine = %s 
        WHERE dingdanhao = %s AND moshuma = %s
        """
        
        params = (
            overnight_fee, commission, profit, total_profit,
            order_id, magic_number
        )
        
        try:
            # 执行SQL更新操作
            self.execute_query(update_sql, params)
            LogInfo(f"成功更新订单 {order_id} 的盈亏信息")
            return True
        except Exception as e:
            LogInfo(f"更新订单 {order_id} 的盈亏信息时发生错误: {e}")
            return False

    def get_and_lock_new_orders(self, account, magic_number):
        """
        获取新的开单信号并立即锁定，防止重复处理
        
        参数:
            account: 账号
            magic_number: 魔术码
            
        返回:
            list: 获取到的订单列表
        """
        try:
            # 确保参数类型正确
            _account = int(account)
            magic_str = str(int(magic_number))
            
            # 使用MySQL事务和FOR UPDATE锁确保原子性操作
            conn = None
            cursor = None
            orders = []
            
            try:
                # 根据初始化方式获取数据库连接
                if self.use_pool:
                    conn = self.db_pool.get_connection()
                else:
                    conn = self.connection_pool.get_connection()
                
                if not conn:
                    LogInfo("获取数据库连接失败")
                    return []
                
                cursor = conn.cursor(pymysql.cursors.DictCursor)
                
                # 开始事务
                conn.begin()
                
                # 使用FOR UPDATE锁定查询结果，防止其他事务同时访问
                query_sql = """
                SELECT * FROM chufakaidanbiao 
                WHERE abiaoshi = 0 AND kaicangzhuzhanghao = %s AND moshuma = %s
                LIMIT 5
                FOR UPDATE
                """
                cursor.execute(query_sql, (_account, magic_str))
                orders = cursor.fetchall()
                
                if not orders or len(orders) == 0:
                    # 如果没有订单，回滚事务并返回空列表
                    conn.rollback()
                    return []
                
                # 锁定订单的ID列表
                order_ids = [order['IntID'] for order in orders]
                placeholders = ', '.join(['%s'] * len(order_ids))
                current_time = time.strftime("%Y-%m-%d %H:%M:%S")
                
                # 更新订单状态为处理中
                update_sql = f"""
                UPDATE chufakaidanbiao 
                SET abiaoshi = -1, akaidanshijian = %s 
                WHERE IntID IN ({placeholders}) AND abiaoshi = 0
                """
                params = [current_time] + order_ids
                cursor.execute(update_sql, params)
                
                # 检查更新影响的行数，确保成功锁定
                updated_rows = cursor.rowcount
                if updated_rows != len(order_ids):
                    LogInfo(f"警告: 尝试锁定 {len(order_ids)} 个订单，但只有 {updated_rows} 个成功锁定")
                
                # 记录被锁定的订单ID，用于调试
                LogInfo(f"成功锁定订单: {order_ids}")
                
                # 提交事务
                conn.commit()
                
                # 过滤出实际被锁定的订单
                if updated_rows < len(order_ids):
                    # 再次查询实际锁定的订单
                    recheck_sql = f"""
                    SELECT * FROM chufakaidanbiao 
                    WHERE IntID IN ({placeholders}) AND abiaoshi = -1
                    """
                    cursor.execute(recheck_sql, order_ids)
                    orders = cursor.fetchall()
                
                return orders
                
            except Exception as e:
                # 发生异常时回滚事务
                if conn:
                    try:
                        conn.rollback()
                    except:
                        pass
                LogInfo(f"锁定新订单时发生事务错误: {e}")
                return []
                
            finally:
                # 确保关闭游标和释放连接
                if cursor:
                    try:
                        cursor.close()
                    except:
                        pass
                if conn:
                    # 根据初始化方式释放连接
                    try:
                        if self.use_pool:
                            self.db_pool.release_connection(conn)
                        else:
                            self.connection_pool.release_connection(conn)
                    except:
                        pass
                
        except Exception as e:
            LogInfo(f"锁定订单时发生错误: {e}")
            return []

    def insert_order_details(self, order_info):
        """
        插入新订单详细信息
        
        参数:
            order_info: 包含订单信息的字典，需要包含相关字段
            
        返回:
            bool: 成功返回True，失败返回False
        """
        # 首先检查该订单是否已存在
        check_sql = """
        SELECT dingdanhao FROM xiangxidanxinxibiao 
        WHERE dingdanhao = %s AND moshuma = %s
        """
        result = self.execute_query(check_sql, (order_info['dingdanhao'], order_info['moshuma']), fetch=True)
        
        if result:
            LogInfo(f"订单 {order_info['dingdanhao']} 已存在，将更新信息")
            return self.update_order_profit(
                order_info['dingdanhao'], 
                order_info['moshuma'],
                order_info.get('geyefei', 0),
                order_info.get('shouxufei', 0),
                order_info.get('yingkuijine', 0),
                order_info.get('jingyingkuijine', 0)
            )
        
        # 确保所有必要字段都存在，对缺失字段使用默认值
        required_fields = [
            'dingdanhao', 'kaidanshijian', 'fangxaing', 'shoushu', 'huobi', 
            'kaidanjiage', 'pingtaileixing', 'zhushi', 'moshuma', 'geyefei', 
            'shouxufei', 'yingkuijine', 'jingyingkuijine','pingcangbiaoshi',
             'pingcangshijian', 'tongyibiaoshi', 'kaicangzhuzhanghao', 'huilv', 'baozhengjin'
        ] 
        # 构建SQL插入语句
        sql = """
        INSERT INTO xiangxidanxinxibiao (
            dingdanhao, kaidanshijian, fangxaing, shoushu, huobi, 
            kaidanjiage, pingtaileixing, zhushi, moshuma, geyefei, 
            shouxufei, yingkuijine, jingyingkuijine, pingcangbiaoshi,
            pingcangshijian, tongyibiaoshi, kaicangzhuzhanghao, huilv, baozhengjin
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """
        
        # 准备参数数据
        params = (
            order_info['dingdanhao'],
            order_info['kaidanshijian'],
            order_info['fangxaing'],
            order_info['shoushu'],
            order_info['huobi'],
            order_info['kaidanjiage'],
            order_info['pingtaileixing'],
            order_info['zhushi'],
            order_info['moshuma'],
            order_info['geyefei'],
            order_info['shouxufei'],
            order_info['yingkuijine'],
            order_info['jingyingkuijine'],
            order_info['pingcangbiaoshi'],
            order_info['pingcangshijian'],
            order_info['tongyibiaoshi'],
            order_info['kaicangzhuzhanghao'],
            order_info['huilv'],
            order_info['baozhengjin']
        )
        
        try:
            # 执行SQL插入操作
            self.execute_query(sql, params)
            LogInfo(f"成功插入订单 {order_info['dingdanhao']} 详细信息")
            return True
        except Exception as e:
            LogInfo(f"插入订单详细信息时发生错误: {e}")
            return False

    def get_orders_remarks(self, order_ids):
        """
        批量获取多个订单ID的备注信息
        
        参数:
            order_ids: 订单ID列表或字典，如果是字典，则使用字典的键作为订单ID
            
        返回:
            dict: 键为订单ID，值为对应的备注信息。
                 如果所有订单都能获取到备注，返回完整字典；
                 如果有任何订单无法获取备注，返回空字典。
        """
        if not order_ids:
            return {}
            
        # 检查输入类型并提取订单ID
        if isinstance(order_ids, dict):
            # 提取字典的键作为订单ID列表
            id_list = list(order_ids.keys())
        else:
            # 假设是列表或可迭代对象
            id_list = list(order_ids)
            
        if not id_list:
            return {}
            
        # 初始化结果字典
        results = {}
        try:
            # 构建SQL查询
            placeholders = ', '.join(['%s'] * len(id_list))
            main_sql = f"""
            SELECT adingdanhao, IntID, zhushi 
            FROM chufakaidanbiao 
            WHERE adingdanhao IN ({placeholders})
            """
            # 执行查询
            main_result = self.execute_query(main_sql, id_list, fetch=True)
            # 如果没有结果，立即尝试备用查询方式
            if not main_result:
                # 尝试一个一个地查询每个ID
                for order_id in id_list:
                    like_sql = """
                    SELECT adingdanhao, IntID, zhushi 
                    FROM chufakaidanbiao 
                    WHERE adingdanhao LIKE %s
                    ORDER BY IntID DESC
                    LIMIT 1
                    """
                    like_result = self.execute_query(like_sql, (f"%{order_id}%",), fetch=True)
                    
                    if like_result and len(like_result) > 0:
                        remark = like_result[0].get('zhushi', '')
                        if not remark:  # 如果备注为空
                            return {}
                            
                        int_id = like_result[0].get('IntID', '')
                        if int_id:
                            results[order_id] = f"{int_id}*{remark}"
                        else:
                            results[order_id] = remark
                
                # 检查是否所有ID都找到了备注
                if len(results) == len(id_list):
                    return results
                else:
                    return {}
            
            # 处理主查询结果
            for row in main_result:
                order_id = row.get('adingdanhao', '')
                int_id = row.get('IntID', '')
                remark = row.get('zhushi', '')
                
                # 如果备注为空，立即返回空字典
                if order_id and order_id in id_list and not remark:
                    return {}
                    
                if order_id and order_id in id_list and remark:
                    if int_id:
                        results[order_id] = f"{int_id}*{remark}"
                    else:
                        results[order_id] = remark
            
            # 检查是否所有ID都找到了备注
            if len(results) == len(id_list):
                return results
            else:
                return {}
                
        except Exception as e:
            # 发生异常返回空字典
            return {}

    def get_order_remark(self, order_id):
        """
        获取指定订单ID对应的注释字段内容，实现可靠的重试机制和多重获取策略
        
        参数:
            order_id: 订单ID，匹配chufakaidanbiao表的adingdanhao字段
            
        返回:
            str: 匹配订单的zhushi字段内容，前缀为"IntID*"，如果没有找到匹配记录则返回空字符串
        """
        # 通过批量获取函数获取单个订单的备注
        results = self.get_orders_remarks([order_id])
        return results.get(order_id, "")

    def reset_locked_order(self, order_id):
        """
        重置被锁定的订单状态，将abiaoshi从-1恢复为0
        
        参数:
            order_id: 订单的IntID
            
        返回:
            bool: 操作成功返回True，失败返回False
        """
        try:
            # 使用参数化查询防止SQL注入
            sql = """
            UPDATE chufakaidanbiao 
            SET abiaoshi = 0, akaidanshijian = NULL 
            WHERE IntID = %s AND abiaoshi = -1
            """
            
            # 执行SQL并带有重试机制
            result = self.execute_query_with_retry(sql, (order_id,))
            
            if result:
                LogInfo(f"订单 {order_id} 锁定状态已重置")
                return True
            else:
                LogInfo(f"订单 {order_id} 锁定状态重置失败")
                return False
                
        except Exception as e:
            LogInfo(f"重置订单锁定状态时发生错误: {e}")
            return False

    def check_db_connection(self):
        """检查数据库连接池健康状态"""
        try:
            # 简单查询测试连接
            test_sql = "SELECT 1"
            result = self.execute_query(test_sql, fetch=True)
            
            if result:
                return True
            else:
                # 连接可能有问题，尝试刷新连接池
                LogInfo("数据库连接测试失败，尝试刷新连接池")
                return False
        except Exception as e:
            LogInfo(f"数据库连接检查失败: {e}")
            return False
            
    def find_locked_orders(self, account, magic_number):
        """
        查找已锁定但未处理的订单（abiaoshi=-1）
        
        参数:
            account: 账号
            magic_number: 魔术码
            
        返回:
            list: 已锁定的订单列表
        """
        try:
            # 确保参数类型正确
            _account = int(account)
            magic_str = str(int(magic_number))
            
            # 查询已锁定的订单
            query_sql = """
            SELECT * FROM chufakaidanbiao 
            WHERE abiaoshi = -1 AND kaicangzhuzhanghao = %s AND moshuma = %s
            """
            locked_orders = self.execute_query(query_sql, (_account, magic_str), fetch=True)
            
            if locked_orders and len(locked_orders) > 0:
                LogInfo(f"发现 {len(locked_orders)} 个已锁定但未处理的订单")
                return locked_orders
            return []
                
        except Exception as e:
            LogInfo(f"查询已锁定订单时发生错误: {e}")
            return []

    def refresh_connection_pool(self):
        """刷新连接池中的连接"""
        try:
            LogInfo("尝试刷新数据库连接池")
            # 根据当前使用的连接池类型进行适当处理
            if hasattr(self, 'pool') and self.pool:
                # 释放所有连接并重新创建
                # 这里的具体实现取决于您的连接池类的设计
                return True
            return False
        except Exception as e:
            LogInfo(f"刷新连接池失败: {e}")
            return False

    def reset_expired_locked_orders(self, timeout_minutes=30):
        """
        重置长时间处于锁定状态的订单
        
        参数:
            timeout_minutes: 超时时间(分钟)，默认30分钟
            
        返回:
            int: 重置的订单数量
        """
        try:
            # 计算超时时间点
            timeout_point = datetime.now() - timedelta(minutes=timeout_minutes)
            timeout_str = timeout_point.strftime("%Y-%m-%d %H:%M:%S")
            
            # 查询长时间处于锁定状态的订单
            query_sql = """
            SELECT IntID FROM chufakaidanbiao 
            WHERE abiaoshi = -1 AND akaidanshijian < %s
            """
            expired_orders = self.execute_query(query_sql, (timeout_str,), fetch=True)
            
            if not expired_orders or len(expired_orders) == 0:
                return 0
                
            # 重置这些订单的状态
            order_ids = [order['IntID'] for order in expired_orders]
            reset_count = 0
            
            for order_id in order_ids:
                if self.reset_locked_order(order_id):
                    reset_count += 1
            
            if reset_count > 0:
                LogInfo(f"已重置 {reset_count} 个长时间锁定的订单状态")
            
            return reset_count
            
        except Exception as e:
            LogInfo(f"重置过期锁定订单时发生错误: {e}")
            return 0

    def check_asset_account(self, account):
        """查询账号在资产表中是否存在"""
        sql = """
        SELECT * FROM zichanbiao 
        WHERE zhanghao = %s
        """
        return self.execute_query(sql, (account,), fetch=True)

    def insert_asset_info(self, account, platform, balance, equity, update_time):
        """插入新的资产信息"""
        sql = """
        INSERT INTO zichanbiao 
        (zhanghao, pingtai, yue, jingzhi, gengxinshjian) 
        VALUES (%s, %s, %s, %s, %s)
        """
        result = self.execute_query(sql, (account, platform, balance, equity, update_time))
        if result:
            LogInfo(f"成功插入账号 {account} 的资产信息")
        else:
            LogInfo(f"插入账号 {account} 的资产信息失败")
        return result

    def update_asset_info(self, account, balance, equity, update_time):
        """更新账号的资产信息"""
        sql = """
        UPDATE zichanbiao 
        SET yue = %s, jingzhi = %s, gengxinshjian = %s 
        WHERE zhanghao = %s
        """
        result = self.execute_query(sql, (balance, equity, update_time, account))
        if result:
            LogInfo(f"成功更新账号 {account} 的资产信息")
        else:
            LogInfo(f"更新账号 {account} 的资产信息失败")
        return result

    def execute_query_with_retry(self, sql, params=None, fetch=False, max_retries=3, retry_delay=1):
        """
        执行SQL查询，带有重试机制
        
        参数:
            sql: SQL语句
            params: SQL参数
            fetch: 是否获取结果
            max_retries: 最大重试次数
            retry_delay: 重试间隔(秒)
            
        返回:
            与execute_query相同的结果
        """
        retry_count = 0
        last_error = None
        
        # 重试循环
        while retry_count < max_retries:
            try:
                # 获取连接
                if self.use_pool:
                    conn = self.db_pool.get_connection()
                else:
                    conn = self.connection_pool.get_connection()
                    
                if not conn:
                    LogInfo("获取数据库连接失败")
                    time.sleep(retry_delay)
                    retry_count += 1
                    continue
                    
                # 执行查询    
                with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                    cursor.execute(sql, params if params else ())
                    if fetch:
                        result = cursor.fetchall()
                    else:
                        conn.commit()
                        result = True
                        
                    # 成功执行，返回结果
                    return result
                    
            except pymysql.err.OperationalError as e:
                # 记录锁超时等操作错误，准备重试
                if e.args[0] in (1205, 1213):  # 锁等待超时错误或死锁错误
                    last_error = e
                    LogInfo(f"数据库操作错误(尝试 {retry_count+1}/{max_retries}): {e}")
                    time.sleep(retry_delay)  # 休眠一段时间后重试
                else:
                    # 其他操作错误，不重试
                    LogInfo(f"数据库操作错误(不可重试): {e}")
                    if not fetch:
                        try:
                            conn.rollback()
                        except:
                            pass
                    return None if fetch else False
                    
            except Exception as e:
                # 其他错误，不重试
                LogInfo(f"执行SQL失败: {e}")
                if not fetch:
                    try:
                        conn.rollback()
                    except:
                        pass
                return None if fetch else False
                
            finally:
                # 释放连接
                if 'conn' in locals() and conn:
                    if self.use_pool:
                        self.db_pool.release_connection(conn)
                    else:
                        self.connection_pool.release_connection(conn)
                        
            # 增加重试计数    
            retry_count += 1
            
        # 所有重试都失败
        LogInfo(f"执行SQL在 {max_retries} 次尝试后仍然失败: {last_error}")
        return None if fetch else False

class TriggerManager:
    """
    交易触发器管理类，负责处理订单开平仓操作
    """
    def __init__(self):
        # 无需内部状态标记，外部由SQL表状态控制
        pass
    
    def reset_states(self):
        """保留此方法做为兼容接口，但不执行任何操作"""
        pass
    
    # ==================== 历史开仓函数 ====================
    
    def his_trigger_long(self, qty, price, tcode):
        """
        历史多头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
        """
        # 移除状态检查，直接执行开仓
        Buy(qty, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "多单买入开仓价==>", price, "买入数量==>", qty)
        return True
    
    def his_trigger_short(self, qty, price, tcode):
        """
        历史空头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
        """
        # 移除状态检查，直接执行开仓
        SellShort(qty, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "空单卖出开仓价==>", price, "卖出数量==>", qty)
        return True
    
    def his_trigger(self, BK, SK, qty, price, tcode):
        """历史开仓兼容函数"""
        if BK and not SK:
            return self.his_trigger_long(qty, price, tcode)
        elif SK and not BK:
            return self.his_trigger_short(qty, price, tcode)
        else:
            return False
    
    # ==================== 历史平仓函数 ====================
    
    def his_trigger_exit_short(self, clots, price, tcode):
        """
        历史平仓空头持仓
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
        """
        # 只保留必要的持仓检查
        if SellPosition(tcode) <= 0 or clots <= 0:
            return False
            
        _lots = min(clots, SellPosition(tcode))
        BuyToCover(_lots, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "空单买入平仓价==>", price, "买入平仓数量==>", _lots)
        return True
    
    def his_trigger_exit_long(self, clots, price, tcode):
        """
        历史平仓多头持仓
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
        """
        # 只保留必要的持仓检查
        if BuyPosition(tcode) <= 0 or clots <= 0:
            return False
            
        _lots = min(clots, BuyPosition(tcode))
        Sell(_lots, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "多单卖出平仓价==>", price, "卖出平仓数量==>", _lots)
        return True
    
    def his_trigger_Exit(self, BP, SP, long_price, short_price, clots, tcode):
        """
        历史平仓兼容函数
        
        参数:
            BP: 是否平空仓标志
            SP: 是否平多仓标志
            long_price: 平多头价格 (卖出价)
            short_price: 平空头价格 (买入价)
            clots: 平仓手数
            tcode: 合约代码
            
        返回:
            bool: 平仓操作是否成功
        """
        result = False
        
        # 避免同时平多空，优先平空头
        if BP and not SP:
            result = self.his_trigger_exit_short(clots, short_price, tcode)
            LogInfo(f"历史平仓: 平空头, 合约={tcode}, 数量={clots}, 价格={short_price}, 结果={'成功' if result else '失败'}")
        elif SP and not BP:
            result = self.his_trigger_exit_long(clots, long_price, tcode)
            LogInfo(f"历史平仓: 平多头, 合约={tcode}, 数量={clots}, 价格={long_price}, 结果={'成功' if result else '失败'}")
        elif BP and SP:
            # 如果同时设置了BP和SP，发出警告
            LogInfo(f"警告: 同时设置了平多头和平空头标志，根据策略不执行操作")
            return False 
        
        return result
    
    # ==================== 实时开仓函数 ====================
    
    def tim_trigger_long(self, qty, price, tcode, order_type='2'):
        """
        实时多头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
            order_type: 订单类型，默认'2'为限价单，'1'为市价单
            
        返回:
            (状态, 订单ID)
        """
        # 移除状态检查，直接执行开仓
        
        # 对于市价单，保留传入价格不变，便于模拟账户测试
        if order_type == '1':
            checked_price = price
            LogInfo(f"市价单模式:合约 {tcode} 多头开仓使用市价单，记录价格 {price}")
        else:
            # 价格检查逻辑保持不变
            upper_limit = Q_UpperLimit(tcode)
            if upper_limit <= 0:
                checked_price = price
            else:
                checked_price = min(price, upper_limit)
                if checked_price != price:
                    LogInfo(f"警告: 多头开仓价格 {price} 超过涨停价 {upper_limit}，已自动调整")
        
        # 发送订单，添加订单类型参数
        retEntry, EntryOrderId = A_SendOrder(Enum_Buy(), Enum_Entry(), qty, checked_price, tcode, '', order_type)
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "多单买入开仓价==>", checked_price, "买入数量==>", qty, "订单类型==>", "市价单" if order_type=='1' else "限价单", "下单状态=", retEntry)
        
        if EntryOrderId:
            LogInfo(f"多头开仓订单已发送，订单号: {EntryOrderId}")
        else:
            LogInfo(f"多头开仓订单发送失败")
        
        return retEntry, EntryOrderId
    
    def tim_trigger_short(self, qty, price, tcode, order_type='2'):
        """
        实时空头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
            order_type: 订单类型，默认'2'为限价单，'1'为市价单
            
        返回:
            (状态, 订单ID)
        """
        # 移除状态检查，直接执行开仓
        
        # 对于市价单，保留传入价格不变，便于模拟账户测试
        if order_type == '1':
            checked_price = price
            LogInfo(f"市价单模式:合约 {tcode} 空头开仓使用市价单，记录价格 {price}")
        else:
            # 限价单价格检查逻辑保持不变
            lower_limit = Q_LowLimit(tcode)
            if lower_limit <= 0:
                checked_price = price
            else:
                checked_price = max(price, lower_limit)
                if checked_price != price:
                    LogInfo(f"警告: 空头开仓价格 {price} 低于跌停价 {lower_limit}，已自动调整")
        
        # 发送订单，添加订单类型参数
        retEntry, EntryOrderId = A_SendOrder(Enum_Sell(), Enum_Entry(), qty, checked_price, tcode, '', order_type)
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "空单卖出开仓价==>", checked_price, "卖出数量==>", qty, "订单类型==>", "市价单" if order_type=='1' else "限价单", "下单状态=", retEntry)
        
        if EntryOrderId:
            LogInfo(f"空头开仓订单已发送，订单号: {EntryOrderId}")
        else:
            LogInfo(f"空头开仓订单发送失败")
        
        return retEntry, EntryOrderId
    
    def tim_trigger(self, BK, SK, qty, price, tcode, order_type='2'):
        """
        实时开仓兼容函数
        
        参数:
            BK: 是否开多单
            SK: 是否开空单
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
            order_type: 订单类型，默认'2'为限价单，'1'为市价单
            
        返回:
            返回一个列表：[(status, order_id)]，包含所有成功或失败的订单信息
        """
        result = []
        
        # 避免双向下单，分别使用单向下单函数
        if BK:
            ret, order_id = self.tim_trigger_long(qty, price, tcode, order_type)
            result.append((ret, order_id))
            LogInfo(f"多头开仓: 合约={tcode}, 数量={qty}, 价格={price}, 订单ID={order_id}, 状态={ret}")
        if SK:
            ret, order_id = self.tim_trigger_short(qty, price, tcode, order_type)
            result.append((ret, order_id))
            LogInfo(f"空头开仓: 合约={tcode}, 数量={qty}, 价格={price}, 订单ID={order_id}, 状态={ret}")
        
        # 返回所有订单结果
        return result
    
    # ==================== 实时平仓函数 ====================
    
    def tim_trigger_exit_short(self, clots, price, tcode, order_type='2'):
        """
        实时平仓空头持仓，支持返回多个订单状态
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
            order_type: 订单类型，默认'2'为限价单，'1'为市价单
            
        返回:
            list: 包含所有订单信息的列表，每项为 (状态, 订单ID, 平仓类型, 平仓手数)
        """
        orders_info = []  # 用于存储所有订单的状态和ID
        MarketType = check_contract_prefix(tcode)
        sell_position = A_SellPosition(tcode)
        # 检查持仓是否存在
        if clots <= 0 or (sell_position <= 0 and MarketType == '2'):
            LogInfo(f"合约 {tcode} 无空头持仓或平仓手数为0，跳过平仓")
            return orders_info
            
        _lots = min(clots, sell_position) if MarketType == '2' else clots
        LogInfo(f"准备平仓合约 {tcode} 的空头持仓，持仓量={sell_position}，平仓量={_lots}")
        
        # 对于市价单，保留传入价格不变，便于模拟账户测试
        if order_type == '1':
            checked_price = price
            LogInfo(f"市价单模式:合约 {tcode} 平空头使用市价单，记录价格 {price}")
        else:
            # 限价单价格检查逻辑保持不变
            upper_limit = Q_UpperLimit(tcode)
            if upper_limit <= 0:
                checked_price = price
            else:
                checked_price = min(price, upper_limit)
                if checked_price != price:
                    LogInfo(f"警告: 平空头价格 {price} 超过涨停价 {upper_limit}，已自动调整")
        
        # 交易所特殊处理逻辑保持不变
        if ExchangeName(tcode) not in ['SHFE', 'INE']:
            # 普通交易所情况
            retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), _lots, checked_price, tcode, '', order_type)
            orders_info.append((retExit, ExitOrderId, "平仓", _lots))
            LogInfo(f"发送平空头单: {tcode}, 类型=平仓, 数量={_lots}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={ExitOrderId}, 状态={retExit}")
        else:
            # 上期所和能源交易所特殊处理
            lots = _lots
            tlots = A_TodaySellPosition(tcode)
            dlots = lots - tlots
            
            if tlots >= lots:
                # 今仓足够平仓,仅平今仓
                retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_ExitToday(), lots, checked_price, tcode, '', order_type)
                orders_info.append((retExit, ExitOrderId, "平今", lots))
                LogInfo(f"发送平空头单(平今): {tcode}, 数量={lots}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={ExitOrderId}, 状态={retExit}")
            elif tlots > 0:
                # 今仓不够，分别平今仓和昨仓
                # 先平今仓部分
                TretExit, TExitOrderId = A_SendOrder(Enum_Buy(), Enum_ExitToday(), tlots, checked_price, tcode, '', order_type)
                orders_info.append((TretExit, TExitOrderId, "平今", tlots))
                LogInfo(f"发送平空头单(平今部分): {tcode}, 数量={tlots}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={TExitOrderId}, 状态={TretExit}")
                
                # 再平昨仓部分
                YretExit, YExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), int(dlots), checked_price, tcode, '', order_type)
                orders_info.append((YretExit, YExitOrderId, "平昨", int(dlots)))
                LogInfo(f"发送平空头单(平昨部分): {tcode}, 数量={int(dlots)}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={YExitOrderId}, 状态={YretExit}")
            elif tlots == 0:
                # 仅平昨仓
                retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), lots, checked_price, tcode, '', order_type)
                orders_info.append((retExit, ExitOrderId, "平昨", lots))
                LogInfo(f"发送平空头单(平昨): {tcode}, 数量={lots}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={ExitOrderId}, 状态={retExit}")
        
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "空单买入平仓价==>", checked_price, "买入平仓数量==>", _lots)
        
        if any(order[1] for order in orders_info):
            LogInfo(f"空头平仓订单已发送成功")
        else:
            LogInfo(f"所有空头平仓订单发送失败")
        
        return orders_info
    
    def tim_trigger_exit_long(self, clots, price, tcode, order_type='2'):
        """
        实时平仓多头持仓
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
            order_type: 订单类型，默认'2'为限价单，'1'为市价单
            
        返回:
            list: 订单信息列表
        """
        orders_info = []  # 用于存储所有订单的状态和ID
        MarketType = check_contract_prefix(tcode)
        buy_position = A_BuyPosition(tcode)
        # 只检查持仓是否存在
        if clots <= 0 or (buy_position <= 0 and MarketType == '2'):
            LogInfo(f"合约 {tcode} 无多头持仓或平仓手数为0，跳过平仓")
            return orders_info
            
        _lots = min(clots, buy_position) if MarketType == '2' else clots
        LogInfo(f"准备平仓合约 {tcode} 的多头持仓，持仓量={buy_position}，平仓量={_lots}")
        
        # 如果是市价单且价格不为0，设置价格为0
        if order_type == '1' and price != 0:
            checked_price = 0
            LogInfo(f"市价单模式:合约 {tcode} 平多头使用市价单")
        else:
            # 价格检查逻辑保持不变
            lower_limit = Q_LowLimit(tcode)
            if lower_limit <= 0:
                checked_price = price
            else:
                checked_price = max(price, lower_limit)
                if checked_price != price:
                    LogInfo(f"警告: 平多头价格 {price} 低于跌停价 {lower_limit}，已自动调整")
        
        # 交易所特殊处理逻辑保持不变
        if ExchangeName(tcode) not in ['SHFE', 'INE']:
            # 普通交易所情况
            retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), _lots, checked_price, tcode, '', order_type)
            orders_info.append((retExit, ExitOrderId, "平仓", _lots))
            LogInfo(f"发送平多头单: {tcode}, 类型=平仓, 数量={_lots}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={ExitOrderId}, 状态={retExit}")
        else:
            # 上期所和能源交易所特殊处理
            lots = _lots
            tlots = A_TodayBuyPosition(tcode)
            dlots = lots - tlots
            
            if tlots >= lots:
                # 今仓足够平仓,仅平今仓
                retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_ExitToday(), lots, checked_price, tcode, '', order_type)
                orders_info.append((retExit, ExitOrderId, "平今", lots))
                LogInfo(f"发送平多头单(平今): {tcode}, 数量={lots}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={ExitOrderId}, 状态={retExit}")
            elif tlots > 0:
                # 今仓不够，分别平今仓和昨仓
                # 先平今仓部分
                TretExit, TExitOrderId = A_SendOrder(Enum_Sell(), Enum_ExitToday(), tlots, checked_price, tcode, '', order_type)
                orders_info.append((TretExit, TExitOrderId, "平今", tlots))
                LogInfo(f"发送平多头单(平今部分): {tcode}, 数量={tlots}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={TExitOrderId}, 状态={TretExit}")
                
                # 再平昨仓部分
                YretExit, YExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), int(dlots), checked_price, tcode, '', order_type)
                orders_info.append((YretExit, YExitOrderId, "平昨", int(dlots)))
                LogInfo(f"发送平多头单(平昨部分): {tcode}, 数量={int(dlots)}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={YExitOrderId}, 状态={YretExit}")
            elif tlots == 0:
                # 仅平昨仓
                retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), lots, checked_price, tcode, '', order_type)
                orders_info.append((retExit, ExitOrderId, "平昨", lots))
                LogInfo(f"发送平多头单(平昨): {tcode}, 数量={lots}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={ExitOrderId}, 状态={retExit}")
        
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "多单卖出平仓价==>", checked_price, "卖出平仓数量==>", _lots)
        
        if any(order[1] for order in orders_info):
            LogInfo(f"多头平仓订单已发送成功")
        else:
            LogInfo(f"所有多头平仓订单发送失败")
        
        return orders_info
    
    def tim_trigger_Exit(self, BP, SP, long_price, short_price, clots, tcode, order_type='2'):
        """
        实时平仓兼容函数
        
        参数:
            BP: 是否平空仓标志
            SP: 是否平多仓标志
            long_price: 平多头价格 (卖出价)
            short_price: 平空头价格 (买入价)
            clots: 平仓手数
            tcode: 合约代码
            order_type: 订单类型，默认'2'为限价单，'1'为市价单
            
        返回:
            list: 所有平仓订单的信息列表
        """
        all_orders = []
        
        # 避免同时平多空，优先平空头
        if BP:
            short_orders = self.tim_trigger_exit_short(clots, short_price, tcode, order_type)
            all_orders.extend(short_orders)
            LogInfo(f"实时平仓: 平空头, 合约={tcode}, 数量={clots}, 价格={short_price}, 订单数={len(short_orders)}")
        if SP:
            long_orders = self.tim_trigger_exit_long(clots, long_price, tcode, order_type)
            all_orders.extend(long_orders)
            LogInfo(f"实时平仓: 平多头, 合约={tcode}, 数量={clots}, 价格={long_price}, 订单数={len(long_orders)}")
            
        return all_orders

# symbol_d=[]
# for i in range(len(symbol_Id)):
#     symbol_d.append(copy.deepcopy(deque([0]*9,maxlen=9)))
# Tblock=[copy.deepcopy(symbol_d),copy.deepcopy(symbol_d)] 
# 订单状态字典 = {"N" : '无',"0" : '已发送',"1" : '已受理',"2" : '待触发',"3" : '已生效',"4" : '已排队',"5" : '部分成交',"6" : '完全成交',
# "7" : '待撤',"8" : '待改', "9" : '已撤单',"A" : '已撤余单',"B" : '指令失败',"C" : '待审核',"D" : '已挂起',"E" : '已申请',"F" : '无效单',"G" : '部分触发',"H" : '完全触发',"I" : '余单失败',}

CloseTime,itk,otk=0,0,0
DB_CONFIG = None
# 数据库连接池和交易数据库对象
db_pool = None
trade_db = None
trigger_manager = None
# 触发管理器
trigger_manager = TriggerManager()
# 全局队列
pending_orders_queue = Queue()
order_results_queue = Queue()
# 全局订单ID映射
order_id_mapping = {}

order_check_timeout = 300  # 订单检查超时时间（秒）

# 全局变量，用于实现定时检查
last_check_time = 0

# 全局变量定义

should_terminate = False
last_update_time = 0
# 添加全局变量用于跟踪待确认的订单
pending_orders = {}  # 格式: {platform_order_id: {db_order_id, magic_number, symbol, direction, timestamp}}
pending_long_orders_to_db = {}
pending_short_orders_to_db = {}
pending_close_orders = {}  
pending_long_close_orders = {}
pending_short_close_orders = {}

# 数据库工作线程
def db_worker():
    while True:
        try:
            # 定期从数据库读取新订单（例如每1秒）
            for symbol in symbol_Id:
                orders = trade_db.get_pending_orders(symbol)
                for order in orders:
                    pending_orders_queue.put(order)
            
            # 处理需要写回数据库的结果
            while not order_results_queue.empty():
                result = order_results_queue.get_nowait()
                trade_db.update_order_status(result['order_id'], result['status'], result['trade_result'])
            
            time.sleep(1)  # 控制查询频率
        except Exception as e:
            LogInfo(f"数据库工作线程异常: {e}")
            time.sleep(5)  # 发生异常时稍等长一些再重试

def initialize(context):
    """初始化函数，类似于平台的OnInit"""
    global db_pool, trade_db, trigger_manager, pending_orders, last_check_time
    
    # 加载订单状态字典
    load_order_dictionaries()
    
    # 初始化时间
    last_check_time = time.time()
    
    # 初始化数据库连接池和交易数据库接口
    global g_params, DB_CONFIG, CloseTime, db_pool, trade_db, itk, otk
    itk = g_params['开仓超价跳数']
    otk = g_params['平仓超价跳数']
    for i in symbol_Id:
        LogInfo("订阅",i,"合约Tick实时行情数据")
        SetBarInterval(i,'T', 0, 100,isTrigger=True) #订阅信号合约
    # SetBarInterval("SHFE|Z|AU|MAIN",'T', 0, 100) #沪金合约交易时间最长，订阅沪金保证全交易时间可靠触发
    #for i in userNo_Id2:
    #    LogInfo("设置",i,"账户交易")
    #    SetUserNo(str(i))     
    SetActual()                      #设置实盘运行
    SetOrderWay(1)                   #设置实时发单
    SetTriggerType(1)                #设置即时行情触发
    SetTriggerType(2)                #设置交易数据触发
    SetTriggerType(3, 500)
    #SetTriggerType(5)               #设置K线触发
    #SetTriggerType(6)               #连接状态触发
    #SetTradeDirection(0)            #设置0双向交易,1多头,2空头

    # 配置 MySQL 连接参数
    DB_CONFIG = {
        "host": 服务器地址,
        "user": 服务器用户名,
        "password": str(链接数据库密码),  # 确保密码是字符串
        "database": 数据库名称,
        "charset": "utf8mb4",
        "cursorclass": pymysql.cursors.DictCursor
    }
    
    LogInfo(f"尝试连接数据库: {DB_CONFIG['host']}, 用户: {DB_CONFIG['user']}")

    try:
        # 直接使用参数创建TradeDB对象
        LogInfo("正在创建数据库连接...")
        trade_db = TradeDB(
            服务器地址,
            服务器用户名,
            链接数据库密码,
            数据库名称
        )
        
        LogInfo("数据库连接初始化成功")
        return True
    except Exception as e:
        LogInfo(f"数据库连接初始化失败: {e}")
        # 打印详细的错误堆栈
        import traceback
        LogInfo(traceback.format_exc())
        return False
    
    # 使用try-except包装数据库初始化
    try:
        db_pool = MySQLPool(DB_CONFIG)
        trade_db = TradeDB(db_pool)
        LogInfo("数据库连接池创建成功")
    except Exception as e:
        LogInfo(f"数据库连接失败: {e}")
        # 创建空的数据库对象，让策略能继续运行
        db_pool = None
        trade_db = None
    
    CloseTime = g_params['收盘清仓倒计时分钟']
    LogInfo("策略初始化完成，数据库连接已建立")

    # 启动数据库工作线程
    db_thread = threading.Thread(target=db_worker)
    db_thread.daemon = True
    db_thread.start()
    
    # 首次清理可能遗留的锁定订单
    if trade_db:
        try:
            trade_db.reset_expired_locked_orders()
        except Exception as e:
            LogInfo(f"初始化时清理锁定订单失败: {e}")
    
    # 不能使用RegisterTimer，将在handle_data中实现定时检查
    LogInfo("初始化完成，将在handle_data中定期检查新订单")
check_interval=10
DateQ,strategyStatusQ=deque([0,0],maxlen=3),deque([0,0],maxlen=3) 
BKS,SKS,BPS,SPS=0,0,0,0
def handle_data(context):
    """
    策略核心处理函数，每个周期执行一次
    在此实现定时器功能用于检查新订单
    """
    global last_check_time, check_interval, db_pool, trade_db, trigger_manager
    
    # 检查行情数据是否准备好
    HTS=1 if context.strategyStatus()=="C" else 0  
    if CurrentBar(symbol_Id[0],'T', 0) == 0:
        return
    
    for i in range(len(symbol_Id)):
        symbol = symbol_Id[i]
        # 获取对应的外盘映射和平台类型
        platform_type = 平台类型[i] if i < len(平台类型) else ""
        current_setting = 时时价格启用[i] if i < len(时时价格启用) else ""
        price_update_enabled = (current_setting or current_setting == "TRUE" or current_setting == "1" or current_setting == "YES")
        # LogInfo('处理合约:', symbol, '平台类型:', platform_type, '价格更新:', price_update_enabled)
        
        # 获取该合约的最新价格
        last_priceA = Close(symbol,'T', 0)
        if len(last_priceA) == 0:
            LogInfo(f"合约 {symbol} 无有效价格，跳过")
            continue
        last_price = Q_Last(symbol) if HTS==1 else last_priceA[-1]
        
        # 如果启用实时价格更新且有外盘映射信息，则更新价格
        if price_update_enabled and platform_type:
            try:
                # 将价格格式化为字符串，保留适当的小数位数
                formatted_price = round_decimal(last_price, 2)
                # 更新数据库中的价格
                update_result = trade_db.update_price(价格表,symbol, formatted_price, platform_type)
                # LogInfo(f"更新价格结果: {update_result} - {symbol} 价格 {formatted_price} 平台 {platform_type}")
            except Exception as e:
                LogInfo(f"更新价格失败: {e}")
        
    if HTS==1:        
        # 检查数据库连接
        if trade_db is None:
            LogInfo("数据库未连接，仅执行非数据库功能")
            return

        # 每次循环都快速检查是否有新的待处理订单
        quick_check_result = quick_check_pending_orders(context)
        if quick_check_result:
            process_new_orders(context,HTS,itk,otk)

        current_time = time.time()
        # 检查是否到达检查间隔
        if current_time - last_check_time >= check_interval:
            last_check_time = current_time  # 更新上次检查时间

            # 检查数据库连接
            if trade_db:
                try:
                    # 清理过期锁定订单
                    trade_db.reset_expired_locked_orders(timeout_minutes=10)

                    # 手动触发订单处理
                    process_new_orders(context,HTS,itk,otk)
                except Exception as e:
                    LogInfo(f"定期检查时发生错误: {e}")

        # 处理待确认的订单$$
        check_pending_orders(context)
        
        # 处理待确认的平仓订单
        check_closed_orders(context)
        
        # 处理新的开单信号$$
        process_new_orders(context,HTS,itk,otk)
    
        # 处理活跃订单，更新信息到数据库$$
        process_active_orders(context)

        # 从数据库中检测并平仓标记为需要平仓的订单
        close_orders_from_db(context, HTS, otk)

        # 更新账户风控信息
        update_risk_control_info(context)

        # 检测追单平仓
        check_chase_orders(context, HTS, otk)
            
        try:
            # 在函数结束前保存当前订单状态（无论是否为实盘模式）
            save_order_dictionaries()
        except Exception as e:
            LogInfo(f"handle_data函数发生错误: {e}")
            import traceback
            LogInfo(traceback.format_exc())
            

def check_contract_prefix(contract: str)->str:
    valid_prefixes = ('CBOT', 'CME', 'COMEX', 'NYMEX', 'LME', 'ICUS', 'ICEU', 'SGX')
    return '1' if contract.startswith(valid_prefixes) else '2'

def round_decimal(number, decimal_places=2):
    """
    使用 Decimal 模块对浮点数进行四舍五入，保留指定位数的小数。
    
    参数:
        number (float/str/Decimal): 要处理的数字（建议传入字符串以避免浮点误差）
        decimal_places (int): 保留的小数位数（默认为2）
        
    返回:
        Decimal: 四舍五入后的 Decimal 对象
    """
    # 将输入转换为 Decimal（推荐传入字符串以避免浮点误差）
    if not isinstance(number, Decimal):
        number = Decimal(str(number))
    
    # 构造量化模板（如 "0.00" 表示保留2位小数）
    quantize_str = "0." + "0" * decimal_places
    rounded_num = number.quantize(Decimal(quantize_str), rounding=ROUND_HALF_UP)
    
    return rounded_num
def floattime_sum(floatin1, floatin2, len_set=12):  # 高精度浮点时间求和（精确到毫秒）
    # 设置浮点数格式，保留len_set位小数
    lensave = f"%0.{len_set}f"
    
    # 格式化浮点数并提取各时间部分
    def extract_time_parts(floatin)->tuple[int, int, int, int]:
        strfloat = lensave % floatin
        return int(strfloat[2:4]), int(strfloat[4:6]), int(strfloat[6:8]), int(strfloat[8:11])
    
    h1, m1, s1, ms1 = extract_time_parts(floatin1)
    h2, m2, s2, ms2 = extract_time_parts(floatin2)
    
    # 计算总和并处理进位
    total_ms = ms1 + ms2
    ms_carry = total_ms // 1000
    new_ms = total_ms % 1000
    
    total_s = s1 + s2 + ms_carry
    s_carry = total_s // 60
    new_s = total_s % 60
    
    total_m = m1 + m2 + s_carry
    m_carry = total_m // 60
    new_m = total_m % 60
    
    new_h = h1 + h2 + m_carry
    new_h = min(new_h, 99)  # 限制小时数不超过99
    
    # 组合新的浮点时间字符串并转换回浮点数
    new_str_time = f"0.{new_h:02}{new_m:02}{new_s:02}{new_ms:03}"
    return float(new_str_time)

def TimeTo_Minutes(time_in):
    timestr='%0.6f'%time_in
    hsave=int(timestr[2:4])
    msave=int(timestr[4:6])
    tcout=hsave*60+msave
    return tcout

def SessionOpenTime(contractId=''):  # 获取交易时段开盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler:continue
        tlout.append(GetSessionStartTime(contractId, i))  # 获取每个交易时段的开盘时间并加入列表
    return tlout

def SessionCloseTime(contractId=''):  # 获取交易时段收盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler-1:continue
        tlout.append(GetSessionEndTime(contractId, i))  # 获取每个交易时段的收盘时间并加入列表
    return tlout

def time_string_to_seconds(time_str):
    """
    Convert a time string in the format 'YYYYMMDDHHMMSSmmm' to the number of seconds
    since the Unix epoch (1970-01-01 00:00:00 UTC).
    
    Parameters:
        time_str (str): Time string in the format 'YYYYMMDDHHMMSSmmm'.
    
    Returns:
        int: Total seconds since the Unix epoch.
    """
    if time_str == "0":
        return 0  # Return 0 or an appropriate default value for historical phases or no real-time data
    
    # Parse the time string
    year = int(time_str[0:4])
    month = int(time_str[4:6])
    day = int(time_str[6:8])
    hour = int(time_str[8:10])
    minute = int(time_str[10:12])
    second = int(time_str[12:14])
    millisecond = int(time_str[14:17])
    
    # Create a datetime object
    dt = datetime(year, month, day, hour, minute, second, millisecond * 1000)
    
    # Calculate the total seconds since the Unix epoch
    epoch = datetime(1970, 1, 1)
    total_seconds = (dt - epoch) // timedelta(seconds=1)  # Use integer division to get whole seconds
    
    return total_seconds


def time_int_to_seconds(time_int):
    """
    Convert a time integer in the format YYYYMMDDHHMMSSmmm to the number of seconds
    since the Unix epoch (1970-01-01 00:00:00 UTC).
    
    Parameters:
        time_int (int): Time integer in the format YYYYMMDDHHMMSSmmm.
    
    Returns:
        int: Total seconds since the Unix epoch.
    """
    if time_int == 0:
        return 0  # Return 0 for historical phases or no real-time data
    
    # Extract each component directly using integer arithmetic
    milliseconds = time_int % 1000
    time_int //= 1000
    seconds = time_int % 100
    time_int //= 100
    minutes = time_int % 100
    time_int //= 100
    hours = time_int % 100
    time_int //= 100
    day = time_int % 100
    time_int //= 100
    month = time_int % 100
    year = time_int // 100
    
    # Create a datetime object
    dt = datetime(year, month, day, hours, minutes, seconds, milliseconds * 1000)
    
    # Calculate the total seconds since the Unix epoch
    epoch = datetime(1970, 1, 1)
    total_seconds = (dt - epoch) // timedelta(seconds=1)  # Use integer division to get whole seconds
    
    return total_seconds

def VTS(time_in, contractId=''):  # 根据输入时间和合约ID计算交易时段
    RTS, CTS, TSession = [], [], []  # 初始化三个列表，用于存储修正后的时间、收盘时间和交易时段
    opentimet = SessionOpenTime(contractId)  # 获取所有交易时段的开盘时间
    Closetimet = SessionCloseTime(contractId)  # 获取所有交易时段的收盘时间
    for open_time, close_time in zip(opentimet, Closetimet):
        if time_in > open_time:  # 判断输入时间是否在开盘时间之后
            RTS.append(time_in)  # 如果是，加入RTS列表
        else:
            RTS.append(floattime_sum(time_in, 0.24))  # 如果不是，修正时间后加入RTS列表
        
        if close_time > open_time:  # 判断收盘时间是否在开盘时间之后
            CTS.append(close_time)  # 如果是，加入CTS列表
        else:
            CTS.append(floattime_sum(close_time, 0.24))  # 如果不是，修正时间后加入CTS列表
        
        if open_time < RTS[-1] < CTS[-1]:  # 判断修正后的时间是否在交易时段内
            TSession.append(len(RTS) - 1)  # 如果是，加入TSession列表

    if len(TSession) == 1:  # 如果只有一个交易时段
        idx = TSession[0]
        return idx, opentimet[idx], RTS[idx], CTS[idx]  # 返回交易时段和相关时间
    else:
        return -1, time_in, time_in, time_in  # 否则返回默认值
    

def process_new_orders(context, _HTS, _itk, _otk):
    """处理新下单信号"""
    global db_pool, trade_db, trigger_manager, A_AccountID, pending_orders
    
    try:
        # 获取当前账户ID
        account_number = normalize_account_number(A_AccountID())
        if not account_number:
            LogInfo("无法获取当前账户信息，跳过下单处理")
            return
            
        # 检查对应的魔术码
        account_idx = -1
        for i in range(len(注册账号)):
            if account_number == int(注册账号[i]):
                account_idx = i
                break
                
        if account_idx == -1:
            LogInfo(f"账户 {account_number} 未在配置中找到")
            return
            
        magic_number = str(int(魔术码[account_idx]))
        if not magic_number:
            LogInfo(f"账户 {account_number} 未设置魔术码，无法处理订单")
            return
            
        LogInfo(f"process_new_orders==>当前账户: {account_number}, 魔术码: {magic_number}")
        
        # 获取并锁定新订单
        new_orders = trade_db.get_and_lock_new_orders(account_number, magic_number)
        
        if not new_orders or len(new_orders) == 0:
            return
            
        LogInfo(f"获取到 {len(new_orders)} 个新订单待处理")
        
        # 创建一个集合，记录已处理的订单ID
        processed_order_ids = set()
        
        # 处理每个新订单
        for order in new_orders:
            try:
                # 检查订单ID是否已经处理过，防止重复处理
                order_id = order['IntID']
                # if order_id in processed_order_ids:
                #     LogInfo(f"订单 {order_id} 已处理，跳过")
                #     continue
                
                # 获取订单详情
                order_direction = order['afangxiang']  # 方向
                OrderSymbol = order['ahuobi']       # 合约
                order_volume = int(order['ashoushu'])       # 手数
                order_account = order['kaicangzhuzhanghao']  # 获取订单关联的账号
                order_magic = order['moshuma']  # 获取订单关联的魔术码
                
                # 记录订单信息
                LogInfo(f"处理新订单: ID={order_id}, 方向={order_direction}, 合约={OrderSymbol}, 手数={order_volume} 账号={order_account} 魔术码={order_magic}")
                
                # 检查合约前缀，用于确定市场类型
                MarketType = check_contract_prefix(OrderSymbol)
                LogInfo(f"合约 {OrderSymbol} 的市场类型为 {MarketType}")
                
                # 准备下单参数
                is_buy = order_direction == 'B'
                is_sell = order_direction == 'S'  
                last_priceA = Close(OrderSymbol, 'T', 0)
                if len(last_priceA) == 0:
                    LogInfo(f"合约 {OrderSymbol} 无有效价格，跳过")
                    continue
                last_price =Q_Last(OrderSymbol) if _HTS==1 else last_priceA[-1]
                if last_price <= 0:
                    LogInfo(f"警告: 合约 {OrderSymbol} 无有效价格，跳过")
                    continue      
                # 执行下单操作
                ret_code = None
                platform_order_id = None
                
                if _HTS == 1:  # 实时交易
                    if is_buy:            
                        # 直接调用多头开仓函数
                        ipr=last_price+_itk*PriceTick(OrderSymbol)
                        LogInfo(f"执行实时下单开多: ID={order_id}, 方向={order_direction}, 合约={OrderSymbol}, 手数={order_volume} 账号={order_account} 魔术码={order_magic}")
                        ret_code, platform_order_id = trigger_manager.tim_trigger_long(order_volume, ipr, OrderSymbol, MarketType)
                    elif is_sell:
                        # 直接调用空头开仓函数
                        ipr=last_price-_itk*PriceTick(OrderSymbol)
                        LogInfo(f"执行实时下单开空: ID={order_id}, 方向={order_direction}, 合约={OrderSymbol}, 手数={order_volume} 账号={order_account} 魔术码={order_magic}")
                        ret_code, platform_order_id = trigger_manager.tim_trigger_short(order_volume, ipr, OrderSymbol, MarketType)
                    else:
                        LogInfo(f"未知的订单方向: {order_direction}")
                        continue    
                else:  # 回测交易
                    if is_buy:
                        ret_code = 0
                        platform_order_id = 777777
                        LogInfo(f"执行回测下单开多: ID={order_id}, 方向={order_direction}, 合约={OrderSymbol}, 手数={order_volume} 账号={order_account} 魔术码={order_magic}")
                        trigger_manager.his_trigger_long(order_volume, ipr, OrderSymbol)
                    elif is_sell:
                        ret_code = 0
                        platform_order_id = 888888
                        LogInfo(f"执行回测下单开空: ID={order_id}, 方向={order_direction}, 合约={OrderSymbol}, 手数={order_volume} 账号={order_account} 魔术码={order_magic}")
                        trigger_manager.his_trigger_short(order_volume, ipr, OrderSymbol)
                    else:
                        LogInfo(f"未知的订单方向: {order_direction}")
                        continue

                # 检查下单请求是否成功发送
                if ret_code == 0 and platform_order_id:
                    # 下单请求发送成功，但可能尚未成交
                    current_time = time.strftime("%Y-%m-%d %H:%M:%S")
                    current_day = time.strftime("%m%d")
                    _platform_order_id = current_day+'@'+ str(platform_order_id)
                    
                    if _HTS == 1:
                        # 实盘交易下，将订单添加到待确认队列中
                        pending_orders[platform_order_id] = {
                            'db_order_id': order_id,
                            'magic_number': order_magic,
                            'symbol': OrderSymbol,
                            'direction': order_direction,
                            'timestamp': time.time(),
                            'status': 'pending'
                        }
                        # 更新为"已下单"状态(1)，注意订单之前的状态是"处理中"(-1)
                        update_result = trade_db.update_order_after_open(
                            order_id, _platform_order_id, current_time, order_magic, 1
                        )
                        LogInfo(f"订单已下单，等待确认成交: ID={order_id}, 平台订单号={_platform_order_id}")
                    else:
                        # 回测模式下，直接更新为"已完成"状态(2)
                        update_result = trade_db.update_order_after_open(
                            order_id, _platform_order_id, current_time, order_magic, 2
                        )
                        if update_result:
                            LogInfo(f"订单状态已更新: ID={order_id}, 平台订单号={_platform_order_id}")
                        else:
                            LogInfo(f"订单状态更新失败: ID={order_id}")
                else:
                    LogInfo(f"开仓失败: {OrderSymbol}, 方向: {order_direction}, 手数: {order_volume}")
                    # 处理下单失败的情况，将订单状态重置为0（未处理状态），以便系统可以重新尝试处理
                    try:
                        # 执行重置操作
                        reset_result = trade_db.reset_locked_order(order_id)
                        if reset_result:
                            LogInfo(f"已重置失败的订单状态: ID={order_id}，稍后将重新尝试")
                        else:
                            LogInfo(f"重置订单状态失败: ID={order_id}")
                    except Exception as e:
                        LogInfo(f"重置订单状态时出错: ID={order_id}, 错误: {e}")

            except Exception as e:
                LogInfo(f"处理订单时发生错误: {e}")
                import traceback
                LogInfo(traceback.format_exc())
    except Exception as e:
        LogInfo(f"处理新订单时发生错误: {e}")
        import traceback
        LogInfo(traceback.format_exc())

def check_pending_orders(context):
    """检查待确认订单的成交状态"""
    global pending_orders, trade_db
    global pending_long_orders_to_db, pending_short_orders_to_db  # 新增全局变量
     
    current_time = time.time()
    orders_to_remove = []
    
    # 定义订单状态常量
    ORDER_FILLED = '6'       # 完全成交
    ORDER_PARTIAL = '5'      # 部分成交
    ORDER_CANCELLED = '9'    # 已撤单
    ORDER_FAILED = 'B'       # 指令失败
    ORDER_INVALID = 'F'      # 无效单
    ORDER_CANCELING = '7'    # 待撤
    ORDER_MODIFYING = '8'    # 待改
    ORDER_REMAINDER_FAILED = 'I'  # 余单失败
    
    tongyiLogoOrefix = str(int(统一标识前缀[0]))
    for platform_order_id, order_info in pending_orders.items():
        try:
            # 检查订单是否已超时
            if current_time - order_info['timestamp'] > order_check_timeout:
                LogInfo(f"订单 {platform_order_id} 确认超时，将从待确认队列中移除")
                orders_to_remove.append(platform_order_id)
                continue
        
            # 使用A_OrderStatus检查订单状态
            order_status = A_OrderStatus(platform_order_id)
            LogInfo(f"订单 {platform_order_id} 当前状态: {order_status}")
            
            # 如果订单不存在或状态为空
            if not order_status:
                # 等待下一次检查
                continue
                
            # 订单已成交（完全成交或部分成交）
            if order_status == ORDER_FILLED: #or order_status == ORDER_PARTIAL:
                # 获取订单信息
                db_order_id = order_info['db_order_id']
                magic_number = order_info['magic_number']
                symbol = order_info['symbol']
                direction = order_info.get('direction', '')
                
                # 获取当前时间作为成交时间
                current_time_str = time.strftime("%Y-%m-%d %H:%M:%S")
                current_day = time.strftime("%m%d")
                # 格式化平台订单ID
                sessionId, orderNo=A_GetOrderNo(platform_order_id)
                # 如果sessionId小于五位数，则用前导0填充到五位数
                if sessionId < 1000:
                    sessionId_str = str(sessionId).zfill(4)
                else:
                    sessionId_str = str(sessionId)
                _platform_order_id = tongyiLogoOrefix + current_day + sessionId_str
                
                # 更新数据库中的订单状态为"已完成"(2)
                update_result = trade_db.update_order_after_open(
                    db_order_id, _platform_order_id, current_time_str, magic_number, 2
                )
                
                if update_result:
                    LogInfo(f"订单 {platform_order_id} 已成交，数据库已更新为已完成状态")
                    
                    # 将订单信息存储到对应的待确认队列
                    _order_id = str(platform_order_id)
                    _order_volume=A_OrderFilledLot(_order_id)
                    _order_price=A_OrderFilledPrice(_order_id)
                    order_filled_list = A_OrderFilledList(_order_id)
                    # 只检查长度，避免对数组进行布尔判断
                    try:
                        if len(order_filled_list) > 0:
                            OrderFilledList = order_filled_list[0]
                            order_symbol = symbol
                            order_volume = _order_volume
                            order_open_price = _order_price
                            order_open_time = OrderFilledList['MatchDateTime']
                            sessionId, orderNo = A_GetOrderNo(_order_id)
                            
                            # 根据交易方向存储到不同队列
                            order_info_to_store = {
                                'aorder_id': _platform_order_id,
                                'volume': order_volume,
                                'price': order_open_price,
                                'symbol': order_symbol,
                                'time': order_open_time,
                                'sessionId': sessionId,
                                'orderNo': orderNo
                            }
                            
                            if direction == 'B':
                                LogInfo(f"将买入订单 {platform_order_id} 添加到pending_long_orders_to_db")
                                pending_long_orders_to_db[platform_order_id] = order_info_to_store
                            elif direction == 'S':
                                LogInfo(f"将卖出订单 {platform_order_id} 添加到pending_short_orders_to_db")
                                pending_short_orders_to_db[platform_order_id] = order_info_to_store
                    except Exception as e:
                        LogInfo(f"处理订单成交信息时出错: {e}")
                    
                    # 标记为已处理，准备移除
                    orders_to_remove.append(platform_order_id)
                else:
                    LogInfo(f"订单 {platform_order_id} 数据库更新失败")
                    
            # 订单已被取消或失败
            elif order_status in (7, 8, 9 ,'B','F','I'):
                LogInfo(f"订单 {platform_order_id} 已取消或失败，状态: {order_status}")
                
                # 获取订单信息，重置数据库状态
                db_order_id = order_info['db_order_id']
                magic_number = order_info['magic_number']
                
                try:
                    # 更新数据库订单状态为取消/失败状态(3)
                    current_time_str = time.strftime("%Y-%m-%d %H:%M:%S")
                    update_result = trade_db.update_order_after_open(
                        db_order_id, f"CANCELLED-{platform_order_id}", current_time_str, magic_number, 3
                    )
                    
                    if update_result:
                        LogInfo(f"订单 {platform_order_id} 已取消/失败，数据库状态已更新")
                    else:
                        LogInfo(f"更新取消/失败订单状态失败: {platform_order_id}")
                        
                    # 尝试重置订单锁定状态，允许系统重新处理
                    reset_result = trade_db.reset_locked_order(db_order_id)
                    if reset_result:
                        LogInfo(f"已重置取消/失败订单的锁定状态: ID={db_order_id}")
                    else:
                        LogInfo(f"重置取消/失败订单锁定状态失败: ID={db_order_id}")
                except Exception as e:
                    LogInfo(f"处理取消/失败订单时出错: {platform_order_id}, 错误: {e}")
                
                # 标记为已处理，准备从待确认队列中移除
                orders_to_remove.append(platform_order_id)            
        except Exception as e:
            LogInfo(f"检查订单 {platform_order_id} 状态时出错: {e}")
    
    # 从待确认队列中移除已处理的订单
    for order_id in orders_to_remove:
        LogInfo(f"从待确认队列中移除订单: {order_id}")
        pending_orders.pop(order_id, None)

def process_active_orders(context):
    """处理当前持仓订单"""
    global trade_db, trigger_manager, symbol_Id, forex_marketId
    global pending_long_orders_to_db, pending_short_orders_to_db, pending_orders
    global 隔夜费, 手续费, 保证金比例, 下单量映射倍数, 平台类型, 注册账号, 魔术码, 统一标识前缀
    try:
        tongyiLogoOrefix = str(int(统一标识前缀[0]))
        
        # 获取当前账户ID
        account_number = normalize_account_number(A_AccountID())
        if not account_number:
            LogInfo("无法获取当前账户信息，跳过持仓订单处理")
            return
            
        LogInfo(f"process_active_orders==>当前交易账户: {account_number}")
        
        account_idx = 0
        magic_number = 0
        platform_type = ""
        overnight_fee = 0
        commission_fee = 0
        margin_rate = 0.1
        
        # 在注册账号中查找匹配的账号和平台信息
        # 查找匹配的账号
        for i in range(len(注册账号)):
            if account_number == int(注册账号[i]):
                account_idx = i
                magic_number = int(魔术码[i]) if i < len(魔术码) else 0
                platform_type = 平台类型[i] if i < len(平台类型) else ""
                overnight_fee = 隔夜费[i] if i < len(隔夜费) else 0
                commission_fee = 手续费[i] if i < len(手续费) else 0
                margin_rate = 保证金比例[i] if i < len(保证金比例) else 0.1
                break
        magic_number = str(magic_number)                  
        if not magic_number:
            LogInfo(f"警告: 账户 {account_number} 未设置魔术码，无法处理订单")
            return
         # 处理多头订单
        if len(pending_long_orders_to_db) > 0:
            LogInfo(f"使用账户 {account_number} 和魔术码 {magic_number} 处理活跃多头订单，共 {len(pending_long_orders_to_db)} 个")
            pending_long_orders_to_db_list=[V['aorder_id'] for V in pending_long_orders_to_db.values()]
            pending_long_orders_remarks=trade_db.get_orders_remarks(pending_long_orders_to_db_list)
            if len(pending_long_orders_remarks) > 0:
                for order_id, order_data in list(pending_long_orders_to_db.items()):
                    try:
                        LogInfo(f"处理pending_long_orders_to_db中的多头订单 {order_id}")
                        # 从订单数据获取信息
                        order_volume = order_data['volume']
                        order_open_price = order_data['price']
                        order_open_time = order_data['time']
                        sessionId = order_data['sessionId']
                        orderNo = order_data['orderNo']
                        symbol = order_data['symbol']
                        aorder_id = order_data['aorder_id']

                        LogInfo(f"处理多头订单 {order_id}: 合约={symbol}, 成交量={order_volume}, 价格={order_open_price}")  
                        # 获取当前价格和计算盈亏
                        current_price = Q_BidPrice(symbol)
                        price_diff = current_price - order_open_price

                        # 根据点数和合约价值计算盈亏金额
                        profit_amount = price_diff * ContractUnit(symbol) * order_volume

                        # 根据保证金比例计算占用资金
                        required_margin = order_open_price * ContractUnit(symbol) * order_volume * margin_rate

                        current_overnight_fee = overnight_fee * order_volume

                        current_commission_fee = commission_fee * order_volume
                        # 计算订单成本(隔夜费 + 手续费)
                        total_cost = current_overnight_fee + current_commission_fee

                        # 计算净盈亏(盈亏金额 - 总成本)
                        net_profit = price_diff * ContractUnit(symbol) * order_volume - total_cost

                        # 生成统一订单标识
                        current_day = time.strftime("%m%d")
                        if sessionId < 1000:
                            sessionId_str = str(sessionId).zfill(4)
                        else:
                            sessionId_str = str(sessionId)
                        unique_order_id = tongyiLogoOrefix + current_day + sessionId_str

                        # 检查订单是否已存在于数据库
                        order_exists = trade_db.check_order_exists(unique_order_id, magic_number)

                        # 从预先获取的备注字典中获取订单注释
                        order_remark = pending_long_orders_remarks.get(aorder_id, "")

                        # 准备订单详细信息字典
                        order_details = {
                            'dingdanhao': unique_order_id,           # 平台订单号
                            'kaidanshijian': order_open_time,        # 开单时间
                            'fangxaing': 'B',                        # 方向
                            'shoushu': order_volume,                 # 手数
                            'huobi': symbol,                         # 合约
                            'kaidanjiage': round_decimal(order_open_price),         # 开单价格
                            'pingtaileixing': platform_type,         # 平台类型
                            'zhushi': order_remark,                  # 使用查询到的注释
                            'moshuma': magic_number,                 # 魔术码
                            'geyefei': round_decimal(current_overnight_fee),        # 隔夜费
                            'shouxufei': round_decimal(current_commission_fee),     # 手续费
                            'yingkuijine': round_decimal(profit_amount),            # 盈亏金额
                            'jingyingkuijine': round_decimal(net_profit),           # 总盈亏金额
                            'pingcangbiaoshi': 0,                    # 平仓标志
                            'pingcangshijian': None,                 # 平仓时间
                            'tongyibiaoshi': unique_order_id,        # 统一标识
                            'kaicangzhuzhanghao': account_number,    # 开仓主账号
                            'huilv': round_decimal(0),                              # 汇率
                            'baozhengjin': round_decimal(required_margin)           # 保证金
                        }

                        if not order_exists:
                            # 订单不存在，添加到数据库
                            LogInfo(f"添加新订单到数据库: {unique_order_id}, 合约: {symbol}, 方向: B, 手数: {order_volume}")
                            result = trade_db.insert_order_details(order_details)
                            if not result:
                                LogInfo(f"订单 {unique_order_id} 添加失败")
                            else:
                                LogInfo(f"从pending_long_orders_to_db中删除订单 {order_id}")
                                pending_long_orders_to_db.pop(order_id, None)
                        else:
                            # 订单已存在，使用insert_order_details更新盈亏信息
                            result = trade_db.insert_order_details(order_details)
                            if not result:
                                LogInfo(f"订单 {unique_order_id} 更新失败")
                            else:
                                LogInfo(f"从pending_long_orders_to_db中删除订单 {order_id}")
                                pending_long_orders_to_db.pop(order_id, None)
                    except Exception as e:
                        LogInfo(f"处理多头订单 {order_id} 时发生错误: {e}")
                
        # 处理空头订单
        if len(pending_short_orders_to_db) > 0:
            LogInfo(f"使用账户 {account_number} 和魔术码 {magic_number} 处理活跃空头订单，共 {len(pending_short_orders_to_db)} 个")
            pending_short_orders_to_db_list=[V['aorder_id'] for V in pending_short_orders_to_db.values()]
            pending_short_orders_remarks=trade_db.get_orders_remarks(pending_short_orders_to_db_list)
            if len(pending_short_orders_remarks) > 0:
                for order_id, order_data in list(pending_short_orders_to_db.items()):
                    try:
                        LogInfo(f"处理pending_short_orders_to_db中的空头订单 {order_id}")

                        # 从订单数据获取信息
                        order_volume = order_data['volume']
                        order_open_price = order_data['price']
                        order_open_time = order_data['time']
                        sessionId = order_data['sessionId']
                        orderNo = order_data['orderNo']
                        symbol = order_data['symbol']
                        aorder_id = order_data['aorder_id']

                        LogInfo(f"处理空头订单 {order_id}: 合约={symbol}, 成交量={order_volume}, 价格={order_open_price}")
                        # 获取当前价格和计算盈亏
                        current_price = Q_AskPrice(symbol)
                        price_diff = order_open_price - current_price

                        # 根据点数和合约价值计算盈亏金额
                        profit_amount = price_diff * ContractUnit(symbol) * order_volume

                        # 根据保证金比例计算占用资金
                        required_margin = order_open_price * ContractUnit(symbol) * order_volume * margin_rate

                        current_overnight_fee = overnight_fee * order_volume

                        current_commission_fee = commission_fee * order_volume
                        # 计算订单成本(隔夜费 + 手续费)
                        total_cost = current_overnight_fee + current_commission_fee

                        # 计算净盈亏(盈亏金额 - 总成本)
                        net_profit = price_diff * ContractUnit(symbol) * order_volume - total_cost

                        # 生成统一订单标识
                        current_day = time.strftime("%m%d")
                        if sessionId < 1000:
                            sessionId_str = str(sessionId).zfill(4)
                        else:
                            sessionId_str = str(sessionId)
                        unique_order_id = tongyiLogoOrefix + current_day + sessionId_str

                        # 检查订单是否已存在于数据库
                        order_exists = trade_db.check_order_exists(unique_order_id, magic_number)

                        # 从预先获取的备注字典中获取订单注释
                        order_remark = pending_short_orders_remarks.get(aorder_id, "")

                        # 准备订单详细信息字典
                        order_details = {
                            'dingdanhao': unique_order_id,           # 平台订单号
                            'kaidanshijian': order_open_time,        # 开单时间
                            'fangxaing': 'S',                        # 方向
                            'shoushu': order_volume,                 # 手数
                            'huobi': symbol,                         # 合约
                            'kaidanjiage': round_decimal(order_open_price),         # 开单价格
                            'pingtaileixing': platform_type,         # 平台类型
                            'zhushi': order_remark,                  # 使用查询到的注释
                            'moshuma': magic_number,                 # 魔术码
                            'geyefei': round_decimal(current_overnight_fee),        # 隔夜费
                            'shouxufei': round_decimal(current_commission_fee),     # 手续费
                            'yingkuijine': round_decimal(profit_amount),            # 盈亏金额
                            'jingyingkuijine': round_decimal(net_profit),           # 总盈亏金额
                            'pingcangbiaoshi': 0,                    # 平仓标志
                            'pingcangshijian': None,                 # 平仓时间
                            'tongyibiaoshi': unique_order_id,        # 统一标识
                            'kaicangzhuzhanghao': account_number,    # 开仓主账号
                            'huilv': round_decimal(0),                              # 汇率
                            'baozhengjin': round_decimal(required_margin)           # 保证金
                        }

                        if not order_exists:
                            # 订单不存在，添加到数据库
                            LogInfo(f"添加新订单到数据库: {unique_order_id}, 合约: {symbol}, 方向: S, 手数: {order_volume}")
                            result = trade_db.insert_order_details(order_details)
                            if not result:
                                LogInfo(f"订单 {unique_order_id} 添加失败")
                            else:
                                LogInfo(f"从pending_short_orders_to_db中删除订单 {order_id}")
                                pending_short_orders_to_db.pop(order_id, None)
                        else:
                            # 订单已存在，使用insert_order_details更新盈亏信息
                            result = trade_db.insert_order_details(order_details)
                            if not result:
                                LogInfo(f"订单 {unique_order_id} 更新失败")
                            else:
                                LogInfo(f"从pending_short_orders_to_db中删除订单 {order_id}")
                                pending_short_orders_to_db.pop(order_id, None)
                    except Exception as e:
                        LogInfo(f"处理空头订单 {order_id} 时发生错误: {e}")
    except Exception as e:
        LogInfo(f"处理活跃订单时发生错误: {e}")
        import traceback
        LogInfo(traceback.format_exc())

def close_orders_from_db(context, HTS, otk):
    """根据数据库中的标记执行平仓操作"""
    global pending_long_close_orders, pending_short_close_orders
    global db_pool, trade_db, 注册账号, 魔术码, 平台类型, trigger_manager, pending_orders, pending_close_orders
    
    # 如果全局变量不存在，则初始化
    if 'pending_long_close_orders' not in globals():
        global pending_long_close_orders
        pending_long_close_orders = {}
    
    if 'pending_short_close_orders' not in globals():
        global pending_short_close_orders
        pending_short_close_orders = {}
    
    try:
        # 记录处理结果
        orders_closed = 0
        order_fails = 0
        
        # 遍历所有账号、魔术码和平台类型组合
        account_count = min(len(注册账号), len(魔术码), len(平台类型))
        
        for i in range(account_count):
            account = int(注册账号[i]) if i < len(注册账号) else ""
            magic_number = str(int(魔术码[i])) if i < len(魔术码) else 0
            platform = 平台类型[i] if i < len(平台类型) else ""
            if not account or not magic_number or not platform:
                continue
            # 查询需要平仓的订单
            long_orders_to_close = trade_db.get_long_orders_to_close(account, magic_number, platform)
            short_orders_to_close = trade_db.get_short_orders_to_close(account, magic_number, platform)
            # 处理多头平仓订单
            if long_orders_to_close:
                LogInfo(f"查询到待平仓多头订单: {len(long_orders_to_close) if long_orders_to_close else 0} 个")
                for order in long_orders_to_close:
                    try:
                        # 获取订单详情
                        order_id = order['dingdanhao']     # 订单号
                        symbol = order['huobi']            # 合约
                        volume = int(order['shoushu'])     # 手数
                        open_price = order['kaidanjiage']  # 开单价格
                        
                        LogInfo(f"处理多头平仓订单: ID={order_id}, 合约={symbol}, 手数={volume}")
                        
                        # 检查订单是否已在处理中
                        if order_id in pending_long_close_orders:
                            LogInfo(f"多头订单 {order_id} 已在平仓处理队列中，跳过")
                            continue
                        # 计算平仓价格
                        tick_size = PriceTick(symbol)
                        last_price = Q_BidPrice(symbol)  # 使用卖一价平多头
                        if last_price <= 0:
                            LogInfo(f"警告: 合约 {symbol} 无有效价格，跳过平仓")
                            # 解锁订单，恢复原状态
                            trade_db.update_closed_order(order_id, magic_number, 1)
                            continue
                            
                        # 计算平仓价格（考虑跌停价）
                        exit_price = last_price - otk * tick_size
                        # 识别市场类型切换订单类型
                        MarketType = check_contract_prefix(symbol)
                        # 执行平仓操作
                        if HTS == 1:  # 实时交易
                            # 使用tim_trigger_exit_long执行平仓
                            orders_info = trigger_manager.tim_trigger_exit_long(volume, exit_price, symbol, MarketType)
                            # 检查平仓订单发送结果
                            success_orders = [(info[1], info[2] if len(info) > 2 else "平仓") 
                                           for info in orders_info if info[0] == 0 and info[1]]
                            
                            if success_orders:
                                LogInfo(f"多头平仓订单已发送: {success_orders}")
                                                                
                                # 记录平仓订单信息
                                for exit_order_id, exit_type in success_orders:
                                    # 将订单加入多头平仓队列
                                    pending_long_close_orders[order_id] = {
                                        'platform_order_id': exit_order_id,
                                        'symbol': symbol,
                                        'volume': volume,
                                        'price': exit_price,
                                        'open_price': open_price,
                                        'magic_number': magic_number,
                                        'timestamp': time.time(),
                                        'status': 'pending',
                                        'exit_type': exit_type
                                    }    
                                orders_closed += 1
                                # 更新订单状态为待成交(3)
                                trade_db.update_closed_order(order_id, magic_number, 3)
                            else:
                                LogInfo(f"多头平仓订单发送失败: {order_id}")
                                # 解锁订单，恢复原状态
                                trade_db.update_closed_order(order_id, magic_number, 1)
                                order_fails += 1
                        else:  # 回测模式
                            if trigger_manager.his_trigger_exit_long(volume, exit_price, symbol):
                                # 回测模式直接更新状态
                                trade_db.update_closed_order(order_id, magic_number,5)
                                orders_closed += 1
                                LogInfo(f"回测模式多头平仓成功: {order_id}")
                            else:
                                order_fails += 1
                                LogInfo(f"回测模式多头平仓失败: {order_id}")
                                
                    except Exception as e:
                        LogInfo(f"处理多头平仓订单 {order.get('dingdanhao', '未知')} 时出错: {e}")
                        order_fails += 1
            
            # 处理空头平仓订单
            if short_orders_to_close:
                LogInfo(f"查询到待平仓空头订单: {len(short_orders_to_close) if short_orders_to_close else 0} 个")
                for order in short_orders_to_close:
                    try:
                        # 获取订单详情
                        order_id = order['dingdanhao']     # 订单号
                        symbol = order['huobi']            # 合约
                        volume = int(order['shoushu'])     # 手数
                        open_price = order['kaidanjiage']  # 开单价格
                        LogInfo(f"处理空头平仓订单: ID={order_id}, 合约={symbol}, 手数={volume}")
                        
                        # 检查订单是否已在处理中
                        if order_id in pending_short_close_orders:
                            LogInfo(f"空头订单 {order_id} 已在平仓处理队列中，跳过")
                            continue
                        # 计算平仓价格
                        tick_size = PriceTick(symbol)
                        last_price = Q_AskPrice(symbol)  # 使用买一价平空头
                        if last_price <= 0:
                            LogInfo(f"警告: 合约 {symbol} 无有效价格，跳过平仓")
                            # 解锁订单，恢复原状态
                            trade_db.update_closed_order(order_id, magic_number, 1)
                            continue
                            
                        # 计算平仓价格（考虑涨停价）
                        exit_price = last_price + otk * tick_size
                        # 识别市场类型切换订单类型
                        MarketType = check_contract_prefix(symbol)
                        # 执行平仓操作
                        if HTS == 1:  # 实时交易
                            # 使用tim_trigger_exit_short执行平仓
                            orders_info = trigger_manager.tim_trigger_exit_short(volume, exit_price, symbol, MarketType)
                            # 检查平仓订单发送结果
                            success_orders = [(info[1], info[2] if len(info) > 2 else "平仓") 
                                           for info in orders_info if info[0] == 0 and info[1]]
                            
                            if success_orders:
                                LogInfo(f"空头平仓订单已发送: {success_orders}")
                                # 记录平仓订单信息
                                for exit_order_id, exit_type in success_orders:
                                    # 将订单加入空头平仓队列
                                    pending_short_close_orders[order_id] = {
                                        'platform_order_id': exit_order_id,
                                        'symbol': symbol,
                                        'volume': volume,
                                        'price': exit_price,
                                        'open_price': open_price,
                                        'magic_number': magic_number,
                                        'timestamp': time.time(),
                                        'status': 'pending',
                                        'exit_type': exit_type
                                    }
                                # 更新订单状态为待成交(3)
                                trade_db.update_closed_order(order_id, magic_number, 3)
                                orders_closed += 1
                            else:
                                LogInfo(f"空头平仓订单发送失败: {order_id}")
                                # 解锁订单，恢复原状态
                                trade_db.update_closed_order(order_id, magic_number, 1)
                                order_fails += 1
                        else:  # 回测模式
                            if trigger_manager.his_trigger_exit_short(volume, exit_price, symbol):
                                # 回测模式直接更新状态
                                trade_db.update_closed_order(order_id, magic_number,5)
                                orders_closed += 1
                                LogInfo(f"回测模式空头平仓成功: {order_id}")
                            else:
                                # 解锁订单，恢复原状态
                                trade_db.update_closed_order(order_id, magic_number, 1)
                                order_fails += 1
                                LogInfo(f"回测模式空头平仓失败: {order_id}")
                                
                    except Exception as e:
                        LogInfo(f"处理空头平仓订单 {order.get('dingdanhao', '未知')} 时出错: {e}")
                        order_fails += 1
            
        if orders_closed > 0 or order_fails > 0:
            LogInfo(f"平仓操作完成: 成功发送 {orders_closed} 个订单, 失败 {order_fails} 个")
                
    except Exception as e:
        LogInfo(f"从数据库平仓订单时发生错误: {e}")
        import traceback
        LogInfo(traceback.format_exc())

def check_closed_orders(context):
    """检查和处理平仓订单的状态"""
    global pending_close_orders, pending_long_close_orders, pending_short_close_orders
    global db_pool, trade_db, 注册账号, 魔术码, 平台类型, trigger_manager
    
    # 确保全局变量初始化
    if 'pending_close_orders' not in globals():
        global pending_close_orders
        pending_close_orders = {}
    
    if 'pending_long_close_orders' not in globals():
        global pending_long_close_orders
        pending_long_close_orders = {}
    
    if 'pending_short_close_orders' not in globals():
        global pending_short_close_orders
        pending_short_close_orders = {}
    # 定义订单状态常量
    ORDER_FILLED = '6'       # 完全成交
    
    try:
        # 记录当前时间，用于计算订单等待时间
        current_time = time.time()
        
        # 需要从队列中移除的订单ID
        orders_to_remove_from_close = []
        long_orders_to_remove = []
        short_orders_to_remove = []
        overnight_fee  = 隔夜费[0] if len(隔夜费)>0 else 0
        commission_fee = 手续费[0] if len(手续费)>0 else 0
        # 处理多头平仓订单
        if len(pending_long_close_orders) > 0:
            LogInfo(f"检查多头平仓订单，共 {len(pending_long_close_orders)} 个")
            for original_order_id, order_info in list(pending_long_close_orders.items()):
                try:
                    # 获取平台订单ID和相关信息
                    platform_order_id = order_info.get('platform_order_id', '')
                    magic_number = order_info.get('magic_number', '')
                    timestamp = order_info.get('timestamp', 0)
                    open_price = float(order_info.get('open_price', 0))
                    order_volume = order_info.get('volume', 0)
                    order_symbol = order_info.get('symbol', '')
                    if not platform_order_id:
                        LogInfo(f"多头平仓订单 {original_order_id} 缺少平台订单ID，将移除")
                        long_orders_to_remove.append(original_order_id)
                        continue
                    
                    # 检查订单是否已等待太久（超过5分钟）
                    wait_time = current_time - timestamp
                    if wait_time > 300:  # 5分钟 = 300秒
                        LogInfo(f"多头平仓订单 {original_order_id} 等待时间过长 ({wait_time:.2f}秒)，将移除")
                        long_orders_to_remove.append(original_order_id)
                        # 恢复原订单为待平仓状态
                        trade_db.update_closed_order(original_order_id, magic_number, 1)
                        continue
                    
                    # 检查订单成交状态
                    order_status = A_OrderStatus(platform_order_id)
                    LogInfo(f"多头平仓订单 {platform_order_id} (原订单:{original_order_id}) 当前状态: {order_status} ,魔数:{magic_number}")

                    # 根据订单状态进行处理
                    if order_status == ORDER_FILLED:  # 6表示订单已成交
                        # 获取成交信息
                        filled_list = A_OrderFilledList(platform_order_id)
                        if len(filled_list) > 0:
                            filled_info = filled_list[0]
                            close_price = filled_info.get('MatchPrice', 0)
                            close_time = filled_info.get('MatchDateTime', '')
                            price_diff = close_price - open_price
                            profit_amount = price_diff * ContractUnit(order_symbol) * order_volume
                            # 计算订单成本(隔夜费 + 手续费)
                            total_cost = overnight_fee + commission_fee
                            # 计算净盈亏(盈亏金额 - 总成本)
                            net_profit = (price_diff * ContractUnit(order_symbol)-total_cost) * order_volume
                            # 更新数据库中的平仓信息
                            update_sql = """
                            UPDATE xiangxidanxinxibiao 
                            SET pingcangshijian = %s,
                                yingkuijine = %s,
                                jingyingkuijine = %s
                            WHERE dingdanhao = %s AND moshuma = %s
                            """
                            # 执行更新
                            result = trade_db.execute_query(
                                update_sql, 
                                (close_time, profit_amount, net_profit, original_order_id, magic_number)
                            )
                            trade_db.update_closed_order(original_order_id, magic_number, 5)
                            if result:
                                LogInfo(f"多头平仓订单 {platform_order_id} 已成交，数据库已更新为已完成状态")
                                long_orders_to_remove.append(original_order_id)
                            else:
                                LogInfo(f"更新多头平仓订单 {platform_order_id} 状态失败")

                    elif order_status in (7, 8, 9 ,'B','F','I'):  # 7=已撤单, 8=部分成交, 9=错误
                        # 订单被取消或出错，更新回原始状态
                        trade_db.update_closed_order(original_order_id, magic_number, 1)
                        LogInfo(f"多头平仓订单 {platform_order_id} 状态异常({order_status})，恢复原订单 {original_order_id} 为待平仓状态")

                        # 从队列中移除
                        long_orders_to_remove.append(original_order_id)

                    # 如果订单状态为0-5，表示仍在处理中，继续等待

                except Exception as e:
                    LogInfo(f"处理多头平仓订单 {original_order_id} 时出错: {e}")
                    # 等待下一轮处理，不从队列中移除
        
        # 处理空头平仓订单
        if len(pending_short_close_orders) > 0:
            LogInfo(f"检查空头平仓订单，共 {len(pending_short_close_orders)} 个")
            for original_order_id, order_info in list(pending_short_close_orders.items()):
                try:
                    # 获取平台订单ID和相关信息
                    platform_order_id = order_info.get('platform_order_id', '')
                    magic_number = order_info.get('magic_number', '')
                    timestamp = order_info.get('timestamp', 0)
                    open_price = float(order_info.get('open_price', 0))
                    order_volume = order_info.get('volume', 0)
                    order_symbol = order_info.get('symbol', '')
                    if not platform_order_id:
                        LogInfo(f"空头平仓订单 {original_order_id} 缺少平台订单ID，将移除")
                        short_orders_to_remove.append(original_order_id)
                        continue
                    
                    # 检查订单是否已等待太久（超过5分钟）
                    wait_time = current_time - timestamp
                    if wait_time > 300:  # 5分钟 = 300秒
                        LogInfo(f"空头平仓订单 {original_order_id} 等待时间过长 ({wait_time:.2f}秒)，将移除")
                        short_orders_to_remove.append(original_order_id)
                        # 恢复原订单为待平仓状态
                        trade_db.update_closed_order(original_order_id, magic_number, 1)
                        continue
                    
                    # 检查订单成交状态
                    order_status = A_OrderStatus(platform_order_id)
                    LogInfo(f"空头平仓订单 {platform_order_id} (原订单:{original_order_id}) 当前状态: {order_status}")

                    # 根据订单状态进行处理
                    if order_status == ORDER_FILLED:  # 6表示订单已成交
                        # 获取成交信息
                        filled_list = A_OrderFilledList(platform_order_id)
                        if len(filled_list) > 0:
                            filled_info = filled_list[0]
                            close_price = filled_info.get('MatchPrice', 0)
                            close_time = filled_info.get('MatchDateTime', '')
                            price_diff = open_price - close_price
                            profit_amount = price_diff * ContractUnit(order_symbol) * order_volume
                            # 计算订单成本(隔夜费 + 手续费)
                            total_cost = overnight_fee + commission_fee
                            # 计算净盈亏(盈亏金额 - 总成本)
                            net_profit = (price_diff * ContractUnit(order_symbol)-total_cost) * order_volume
                            # 更新数据库中的平仓信息
                            update_sql = """
                            UPDATE xiangxidanxinxibiao 
                            SET pingcangshijian = %s,
                                yingkuijine = %s,
                                jingyingkuijine = %s
                            WHERE dingdanhao = %s AND moshuma = %s
                            """
                            # 执行更新
                            result = trade_db.execute_query(
                                update_sql, 
                                (close_time, profit_amount, net_profit, original_order_id, magic_number)
                            )
                            trade_db.update_closed_order(original_order_id, magic_number, 5)
                            if result:
                                LogInfo(f"空头平仓订单 {platform_order_id} 已成交，数据库已更新为已完成状态")
                                short_orders_to_remove.append(original_order_id)
                            else:   
                                LogInfo(f"更新空头平仓订单 {platform_order_id} 状态失败")

                    elif order_status in (7, 8, 9 ,'B','F','I'):  # 7=已撤单, 8=部分成交, 9=错误
                        # 订单被取消或出错，更新回原始状态
                        trade_db.update_closed_order(original_order_id, magic_number, 1)
                        LogInfo(f"空头平仓订单 {platform_order_id} 状态异常({order_status})，恢复原订单 {original_order_id} 为待平仓状态")

                        # 从队列中移除
                        short_orders_to_remove.append(original_order_id)
                    # 如果订单状态为0-5，表示仍在处理中，继续等待

                except Exception as e:
                    LogInfo(f"处理空头平仓订单 {original_order_id} 时出错: {e}")
                    # 等待下一轮处理，不从队列中移除
        
        # 从待处理队列中移除已处理的订单
        for order_id in long_orders_to_remove:
            if order_id in pending_long_close_orders:
                LogInfo(f"从pending_long_close_orders中移除订单: {order_id}")
                pending_long_close_orders.pop(order_id, None)
        
        for order_id in short_orders_to_remove:
            if order_id in pending_short_close_orders:
                LogInfo(f"从pending_short_close_orders中移除订单: {order_id}")
                pending_short_close_orders.pop(order_id, None)
                
        # 输出待处理订单数量统计
        long_pending = len(pending_long_close_orders)
        short_pending = len(pending_short_close_orders)
        
        if long_pending > 0 or short_pending > 0:
            LogInfo(f"当前待确认平仓订单: 多头 {long_pending} 个, 空头 {short_pending} 个")
            
    except Exception as e:
        LogInfo(f"检查平仓订单状态时发生错误: {e}")
        import traceback
        LogInfo(traceback.format_exc())


def update_risk_control_info(context):
    """更新当前账户风控信息和资产信息到数据库"""
    global db_pool, trade_db, 注册账号, 平台类型
    
    try:
        # 获取当前策略关联的唯一账户ID
        account_number = normalize_account_number(A_AccountID())
        if not account_number:
            LogInfo("无法获取当前账户信息")
            return
            
        LogInfo(f"获取账户 {account_number} 的风控和资产信息")
        
        # 获取当前时间
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 在注册账号中查找匹配的平台信息
        platform = 平台类型[0] if len(平台类型) > 0 else  "未知平台"
                
        # 获取账户相关数据
        account_balance = round_decimal(A_Available())     # 账户可用资金
        account_equity = round_decimal(A_Assets())         # 账户总资产(净值)
        
        # 更新风控数据
        if trade_db.check_risk_control_account(account_number):
            # 更新现有记录
            trade_db.update_risk_control(account_number, account_balance, account_equity, current_time)
        else:
            # 插入新记录
            trade_db.insert_risk_control(account_number, platform, account_balance, account_equity, current_time)
            
        # 更新资产数据
        if trade_db.check_asset_account(account_number):
            # 更新现有记录
            trade_db.update_asset_info(account_number, account_balance, account_equity, current_time)
        else:
            # 插入新记录
            trade_db.insert_asset_info(account_number, platform, account_balance, account_equity, current_time)

        # 更新xiangxidanxinxibiao表中的订单盈亏信息
        try:
            # 查询需要更新的订单记录（pingcangbiaoshi值在0-4之间的记录）
            query_sql = """
            SELECT dingdanhao, huobi, kaidanjiage, fangxaing, shoushu
            FROM xiangxidanxinxibiao
            WHERE pingcangbiaoshi >= 0 AND pingcangbiaoshi < 5
            """
            
            records = trade_db.execute_query(query_sql, fetch=True)
            
            if records and len(records) > 0:
                # 批量更新操作列表
                update_operations = []
                overnight_fee = 隔夜费[0] if len(隔夜费)>0 else 0
                commission_fee = 手续费[0] if len(手续费)>0 else 0
                for record in records:
                    order_id = record.get('dingdanhao')
                    symbol = record.get('huobi')
                    open_price = float(record.get('kaidanjiage', 0))
                    direction = record.get('fangxaing', 0)  # B为多头，S为空头
                    volume = float(record.get('shoushu', 0))
                    current_price = Q_Last(symbol)
                    t_overnight_fee = overnight_fee * volume
                    t_commission_fee = commission_fee * volume
                    # 计算盈亏金额
                    if direction == 'B':  # 多头
                        profit = (current_price - open_price) * volume*ContractUnit(symbol)
                    elif direction == 'S':  # 空头
                        profit = (open_price - current_price) * volume*ContractUnit(symbol)
                    else:
                        profit = 0
                    
                    _profit =round_decimal(profit)
                    # 计算净盈亏（这里简单示例，实际可能需要考虑手续费等）
                    net_profit = profit-t_overnight_fee-t_commission_fee
                    _net_profit = round_decimal(net_profit)
                    
                    # 创建更新SQL
                    update_sql = """
                    UPDATE xiangxidanxinxibiao
                    SET yingkuijine = %s, jingyingkuijine = %s
                    WHERE dingdanhao = %s and huobi = %s
                    """
                    # 添加到批量操作列表
                    update_operations.append((update_sql, (_profit, _net_profit, order_id, symbol)))
                
                # 执行批量更新
                if update_operations:
                    trade_db.execute_transaction(update_operations)
                    LogInfo(f"已更新 {len(update_operations)} 条订单的盈亏信息")
            
        except Exception as e:
            LogInfo(f"更新订单盈亏信息时发生错误: {e}")
            import traceback
            LogInfo(traceback.format_exc())
        
    except Exception as e:
        LogInfo(f"更新风控和资产信息时发生错误: {e}")
        import traceback
        LogInfo(traceback.format_exc())

def check_chase_orders(context, HTS, otk):
    """检测需要根据追单条件平仓的订单"""
    global db_pool, trade_db, 注册账号, 魔术码, 追单次数, 追单秒数, trigger_manager, pending_orders
    
    try:
        # 获取当前北京时间
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        current_timestamp = time.mktime(time.strptime(current_time, "%Y-%m-%d %H:%M:%S"))
        
        # 获取当前交易账户
        account_number = normalize_account_number(A_AccountID())
        if not account_number:
            LogInfo("无法获取当前账户信息")
            return
            
        # 查找对应的魔术码和追单设置
        account_idx = -1
        for i in range(len(注册账号)):
            if account_number == int(注册账号[i]):
                account_idx = i
                break
                
        if account_idx == -1:
            LogInfo(f"账户 {account_number} 未在配置中找到，使用默认索引0")
            account_idx = 0
            
        magic_number = str(int(魔术码[account_idx])) if account_idx < len(魔术码) else 0
        max_chase_count = int(追单次数[account_idx]) if account_idx < len(追单次数) else 3  # 默认追单次数为3
        chase_seconds = int(追单秒数[account_idx]) if account_idx < len(追单秒数) else 300  # 默认追单秒数为300（5分钟）
        if not magic_number:
            LogInfo(f"账户 {account_number} 未设置魔术码，无法处理追单")
            return
                
        # 查询追单信息
        chase_orders = trade_db.get_chase_orders_info(account_number, magic_number)
        if not chase_orders:
            return
            
        LogInfo(f"check_chase_orders==>账号 {account_number} 有 {len(chase_orders)} 个需要追单的订单")
        
        # 用于存储符合条件的订单ID列表
        orders_to_process = []
        
        # 遍历并筛选符合条件的订单
        for chase_order in chase_orders:
            try:
                # 获取订单ID和追单信息
                order_id = chase_order['adingdanhao']
                chase_count = int(chase_order.get('zhuidancishu', 0))
                chase_time = chase_order.get('zengjiashijian')
                
                # 检查是否符合追单条件
                should_process = False
                
                # 条件1: 达到最大追单次数
                if chase_count >= max_chase_count:
                    LogInfo(f"订单 {order_id} 已达到最大追单次数 {max_chase_count}")
                    should_process = True
                
                # 条件2: 超过追单时间
                elif chase_time:
                    try:
                        # 解析时间字符串
                        if '-' in chase_time:
                            chase_timestamp = time.mktime(time.strptime(chase_time, "%Y-%m-%d %H:%M:%S"))
                        elif '.' in chase_time:
                            chase_timestamp = time.mktime(time.strptime(chase_time, "%Y.%m.%d %H:%M:%S"))
                        else:
                            LogInfo(f"无法识别的日期格式: {chase_time}")
                            continue
                            
                        time_diff = current_timestamp - chase_timestamp
                        
                        if time_diff >= chase_seconds:
                            LogInfo(f"订单 {order_id} 超过追单时间 {chase_seconds} 秒")
                            should_process = True
                            
                            # 更新追单次数
                            trade_db.execute_query(
                                "UPDATE chufakaidanbiao SET zhuidancishu = zhuidancishu + 1, zengjiashijian = %s WHERE adingdanhao = %s",
                                (current_time, order_id)
                            )
                    except Exception as e:
                        LogInfo(f"处理时间格式 {chase_time} 时出错: {e}")
                
                # 如果满足任一条件，将订单ID添加到列表
                if should_process:
                    orders_to_process.append(order_id)
                
            except Exception as e:
                LogInfo(f"处理追单订单 {chase_order.get('dingdanhao', '未知')} 时出错: {e}")
                import traceback
                LogInfo(traceback.format_exc())
        
        # 如果有符合条件的订单，进行批量更新
        if orders_to_process:
            LogInfo(f"需要处理的追单订单数量: {len(orders_to_process)}")
            
            try:
                # 生成SQL占位符字符串用于IN查询
                placeholders = ', '.join(['%s'] * len(orders_to_process))
                
                # 1. 更新xiangxidanxinxibiao表 - 将pingcangbiaoshi=0的更新为2
                update_xiangxi_sql = f"""
                UPDATE xiangxidanxinxibiao 
                SET pingcangbiaoshi = 2 
                WHERE dingdanhao IN ({placeholders}) 
                AND pingcangbiaoshi = 0
                """
                result_xiangxi = trade_db.execute_query(update_xiangxi_sql, orders_to_process)
                LogInfo(f"更新了 {result_xiangxi.rowcount if hasattr(result_xiangxi, 'rowcount') else '未知'} 条详细订单信息表记录")
                
                # 2. 更新chufakaidanbiao表 - 将bbiaoshi更新为-1
                update_chufa_sql = f"""
                UPDATE chufakaidanbiao 
                SET bbiaoshi = -1 
                WHERE adingdanhao IN ({placeholders})
                """
                result_chufa = trade_db.execute_query(update_chufa_sql, orders_to_process)
                LogInfo(f"更新了 {result_chufa.rowcount if hasattr(result_chufa, 'rowcount') else '未知'} 条触发开单表记录")
                
            except Exception as e:
                LogInfo(f"批量更新订单状态时出错: {e}")
                import traceback
                LogInfo(traceback.format_exc())
    
    except Exception as e:
        LogInfo(f"检测追单平仓时发生错误: {e}")
        import traceback
        LogInfo(traceback.format_exc())



def normalize_account_number(account_str):
    """
    将账户名标准化:
    - 如果需要提取数字部分，则提取
    - 否则保持原样返回
    
    参数:
        account_str: 账户名字符串
    
    返回:
        int: 转换后的账户号码
    """
    try:
        # 转为字符串以确保可以处理
        account_str = str(account_str).strip()
        
        # 如果为空则返回0
        if not account_str:
            return 0
            
        # 检查第一个字符是否为字母
        if account_str[0].isalpha():
            # 提取字母后的所有字符
            digits_part = account_str[1:]
            # 过滤出数字部分
            digits_only = ''.join(c for c in digits_part if c.isdigit())
            return int(digits_only) if digits_only else 0
        else:
            # 过滤出数字部分
            digits_only = ''.join(c for c in account_str if c.isdigit())
            return int(digits_only) if digits_only else 0
    except Exception as e:
        LogInfo(f"账户名转换错误: {e}, 原始值: {account_str}")
        return 0


def quick_check_pending_orders(context):
    """快速检查是否有新的待处理订单，不受时间间隔限制"""
    global trade_db, 注册账号, 魔术码
    
    # 如果交易数据库未初始化，则跳过
    if trade_db is None:
        return False
    
    try:
        # 先直接检查数据库连接状态
        connection_ok = trade_db.check_db_connection()
        if not connection_ok:
            LogInfo("数据库连接检查失败，尝试刷新连接")
            trade_db.refresh_connection_pool()
            return False

        # 执行针对特定账号和魔术码的查询
        for i in range(len(注册账号)):
            try:
                account = int(注册账号[i])
                magic_number = str(int(魔术码[i]))
                
                # 使用直接SQL查询检查未处理订单
                direct_query = """
                SELECT COUNT(*) as cnt FROM chufakaidanbiao 
                WHERE abiaoshi = 0 AND kaicangzhuzhanghao = %s AND moshuma = %s
                """
                result = trade_db.execute_query_with_retry(direct_query, (account, magic_number), fetch=True)
                
                if result and len(result) > 0:
                    count = result[0]['cnt']
                    if count > 0:
                        LogInfo(f"快速检测到 {count} 个新待处理订单，立即处理")
                        return True
            except Exception as e:
                LogInfo(f"检查账号 {注册账号[i]} 的订单时出错: {e}")
    except Exception as e:
        LogInfo(f"快速检查新订单时出错: {e}")
    
    return False

def check_pending_close_orders(context):
    """
    此函数已被更新的check_closed_orders函数取代，保留此函数签名以向后兼容。
    """
    # 简单调用新函数来保持向后兼容性
    LogInfo("check_pending_close_orders函数已被优化合并到check_closed_orders，调用新函数")
    check_closed_orders(context)

def close_chase_order(order_id, pure_order_id, order_info, magic_number, HTS, otk):
    """平仓追单订单"""
    global trade_db, trigger_manager, pending_close_orders
    
    try:
        # 检查订单信息是否有效
        if not order_info:
            LogInfo(f"订单 {order_id} 信息无效，可能已被平仓")
            # trade_db.update_closed_order(order_id, magic_number)
            return
        
        # 获取订单信息
        symbol = order_info['symbol']
        direction = order_info['direction']
        volume = order_info['volume']
        MarketType = check_contract_prefix(symbol)
        
        # 生成平仓ID (使用原始订单ID + 时间戳)
        close_id = f"close_{order_id}_{int(time.time())}"
        
        # 计算平仓价格
        tick_size = PriceTick(symbol)
        if direction == 'BUY':
            # 平多头，需要卖出价格(通常低于当前价)
            long_price = max(Q_BidPrice(symbol) - otk * tick_size, Q_LowLimit(symbol))
            short_price = 0  # 不使用
        else:  # SELL
            # 平空头，需要买入价格(通常高于当前价)
            short_price = min(Q_AskPrice(symbol) + otk * tick_size, Q_UpperLimit(symbol))
            long_price = 0  # 不使用
        
        # 执行平仓操作
        ret = False
        close_order_ids = []  # 存储实际的平仓订单ID
        
        if direction == 'B':
            LogInfo(f"平仓追单多头订单: {order_id}, 合约: {symbol}, 手数: {volume}")
            if HTS == 1:
                # 使用单向函数平多头
                orders_info = trigger_manager.tim_trigger_exit_long(volume, long_price, symbol, '2')
                # A_SendOrder成功时返回的ret_code=0
                success_orders = [(info[1], info[2] if len(info) > 2 else "平仓") for info in orders_info if info[0] == 0 and info[1]]
                close_order_ids = [order_id for order_id, _ in success_orders]
                
                if success_orders:
                    ret = True
                    LogInfo(f"多头平仓订单已发送，订单IDs: {close_order_ids}")
                    
                    # 添加到平仓待确认队列
                    for exit_order_id, exit_type in success_orders:
                        pending_close_orders[str(exit_order_id)] = {
                            'original_order_id': order_id,
                            'symbol': symbol,
                            'direction': direction,
                            'volume': volume,
                            'price': long_price,
                            'magic_number': magic_number,
                            'timestamp': time.time(),
                            'status': 'pending',
                            'exit_type': exit_type
                        }
                        LogInfo(f"平仓订单 {exit_order_id} ({exit_type}) 已加入待确认队列")
                else:
                    LogInfo(f"多头平仓订单发送失败")
            else:
                # 历史模式下平多头
                ret = trigger_manager.his_trigger_exit_long(volume, long_price, symbol)
                if ret:
                    LogInfo(f"回测平仓多头订单 {order_id} 成功")
        if direction == 'S': # SELL
            LogInfo(f"平仓追单空头订单: {order_id}, 合约: {symbol}, 手数: {volume}")
            if HTS == 1:
                # 使用单向函数平空头
                orders_info = trigger_manager.tim_trigger_exit_short(volume, short_price, symbol, '2')
                # A_SendOrder成功时返回的ret_code=0
                success_orders = [(info[1], info[2] if len(info) > 2 else "平仓") for info in orders_info if info[0] == 0 and info[1]]
                close_order_ids = [order_id for order_id, _ in success_orders]
                
                if success_orders:
                    ret = True
                    LogInfo(f"空头平仓订单已发送，订单IDs: {close_order_ids}")
                    
                    # 添加到平仓待确认队列
                    for exit_order_id, exit_type in success_orders:
                        pending_close_orders[str(exit_order_id)] = {
                            'original_order_id': order_id,
                            'symbol': symbol,
                            'direction': direction,
                            'volume': volume,
                            'price': short_price,
                            'magic_number': magic_number,
                            'timestamp': time.time(),
                            'status': 'pending',
                            'exit_type': exit_type
                        }
                        LogInfo(f"平仓订单 {exit_order_id} ({exit_type}) 已加入待确认队列")
                else:
                    LogInfo(f"空头平仓订单发送失败")
            else:
                # 历史模式下平空头
                ret = trigger_manager.his_trigger_exit_short(volume, short_price, symbol)
                if ret:
                    LogInfo(f"回测平仓空头订单 {order_id} 成功")
        
        # 更新订单状态为已平仓
        if ret:
            # 如果是实盘且成功发送平仓订单，先不更新状态，等待订单确认
            if HTS == 1 and close_order_ids:
                LogInfo(f"追单订单 {order_id} 已发送平仓请求，等待确认")
            else:
                # 回测模式或其他情况直接更新状态
                # trade_db.update_closed_order(order_id, magic_number)
                LogInfo(f"追单订单 {order_id} 已成功平仓，更新状态完成")
        else:
            LogInfo(f"追单订单 {order_id} 平仓失败")
            
    except Exception as e:
        LogInfo(f"平仓追单订单 {order_id} 时发生错误: {e}")
        import traceback
        LogInfo(traceback.format_exc())

def A_BuyOrderNo(contractNo):
    """
    获取指定合约的所有多头持仓订单
    
    参数:
        contractNo (str): 合约代码
        
    返回:
        list: 多头持仓订单对象列表
    """
    try:
        all_orders = A_AllOrderNo(contractNo)
        buy_position_orders = []
        for order_id in all_orders:
            # 检查是否为开仓
            entry_status = A_OrderEntryOrExit(order_id)
            # 检查是否为买入
            buy_status = A_OrderBuyOrSell(order_id)
            
            # 如果是开仓买入，则添加到多头持仓列表
            if entry_status == 'O' and buy_status == 'B':
                # 获取订单号并添加到列表
                buy_position_orders.append(order_id)
        
        return buy_position_orders
    except Exception as e:
        LogInfo(f"获取多头持仓订单时发生错误: {e}")
        return []

def A_SellOrderNo(contractNo):
    """
    获取指定合约的所有空头持仓订单
    
    参数:
        contractNo (str): 合约代码
        
    返回:
        list: 空头持仓订单对象列表
    """
    try:
        all_orders = A_AllOrderNo(contractNo)
        sell_position_orders = []
        
        for order_id in all_orders:
            # 检查是否为开仓
            entry_status = A_OrderEntryOrExit(order_id)
            # 检查是否为卖出
            sell_status = A_OrderBuyOrSell(order_id)
            
            # 如果是开仓卖出，则添加到空头持仓列表
            if entry_status == 'O' and sell_status == 'S':
                # 获取订单号并添加到列表
                sell_position_orders.append(order_id)
        
        return sell_position_orders
    except Exception as e:
        LogInfo(f"获取空头持仓订单时发生错误: {e}")
        return []
    
    
def update_contract_prices(symbols, platform_types, price_update_settings, price_table, db, prices_dict, is_realtime=True):
    """
    批量更新多个合约的价格信息到数据库
    
    参数:
        symbols: 合约列表
        platform_types: 平台类型列表
        price_update_settings: 价格更新设置列表
        price_table: 价格表名
        db: 数据库连接对象
        prices_dict: 价格字典 {symbol: price}
        is_realtime: 是否是实时更新
    """
    if db is None:
        LogInfo("数据库连接无效，无法更新价格")
        return
        
    try:
        updated_count = 0
        error_count = 0
        
        for i in range(len(symbols)):
            if i >= len(platform_types) or i >= len(price_update_settings):
                continue
                
            symbol = symbols[i]
            platform_type = platform_types[i]
            price_update_enabled = price_update_settings[i]
            
            if not price_update_enabled:
                continue
                
            if symbol not in prices_dict:
                continue
                
            last_price = prices_dict[symbol]
            if not last_price:
                continue
                
            try:
                # 将价格格式化为字符串，保留适当的小数位数
                formatted_price = f"{last_price:.2f}"
                
                # 更新数据库中的价格
                update_result = db.update_price(price_table, symbol, formatted_price, platform_type)
                
                if update_result:
                    updated_count += 1
                
            except Exception as e:
                error_count += 1
                LogInfo(f"更新价格失败: {symbol}, {platform_type}, 错误: {e}")
                
        if updated_count > 0:
            LogInfo(f"价格更新完成: 成功更新 {updated_count} 个合约价格, 失败 {error_count} 个")
            
    except Exception as e:
        LogInfo(f"价格更新过程中发生错误: {e}")
    
# 定义加载订单状态的函数
def load_order_dictionaries():
    """从持久化存储中加载订单状态字典"""
    global pending_orders, pending_long_orders_to_db, pending_short_orders_to_db
    global pending_close_orders, pending_long_close_orders, pending_short_close_orders
    global trade_order_filedir
    
    # 确保目录存在
    if not os.path.exists(trade_order_filedir):
        os.makedirs(trade_order_filedir)
    
    # 数据文件路径
    db_path = os.path.join(trade_order_filedir, "order_data")
    
    try:
        # 打开shelve数据库
        with shelve.open(db_path) as db:
            # 读取各个字典，如果不存在则使用空字典
            pending_orders = db.get('pending_orders', {})
            pending_long_orders_to_db = db.get('pending_long_orders_to_db', {})
            pending_short_orders_to_db = db.get('pending_short_orders_to_db', {})
            pending_close_orders = db.get('pending_close_orders', {})
            pending_long_close_orders = db.get('pending_long_close_orders', {})
            pending_short_close_orders = db.get('pending_short_close_orders', {})
            
        LogInfo(f"已从持久化存储加载订单状态字典")
        LogInfo(f"等待中订单: {len(pending_orders)}, 等待关闭订单: {len(pending_close_orders)}")
        
    except Exception as e:
        LogInfo(f"加载订单状态字典时发生错误: {e}")
        # 出错时使用空字典
        pending_orders = {}
        pending_long_orders_to_db = {}
        pending_short_orders_to_db = {}
        pending_close_orders = {}
        pending_long_close_orders = {}
        pending_short_close_orders = {}

# 定义保存订单状态的函数
def save_order_dictionaries():
    """将当前订单状态字典保存到持久化存储"""
    global pending_orders, pending_long_orders_to_db, pending_short_orders_to_db
    global pending_close_orders, pending_long_close_orders, pending_short_close_orders
    global trade_order_filedir
    
    # 确保目录存在
    if not os.path.exists(trade_order_filedir):
        os.makedirs(trade_order_filedir)
    
    # 数据文件路径
    db_path = os.path.join(trade_order_filedir, "order_data")
    
    try:
        # 打开shelve数据库并写入数据
        with shelve.open(db_path) as db:
            db['pending_orders'] = pending_orders
            db['pending_long_orders_to_db'] = pending_long_orders_to_db
            db['pending_short_orders_to_db'] = pending_short_orders_to_db
            db['pending_close_orders'] = pending_close_orders 
            db['pending_long_close_orders'] = pending_long_close_orders
            db['pending_short_close_orders'] = pending_short_close_orders
            
    except Exception as e:
        LogInfo(f"保存订单状态字典时发生错误: {e}")
        import traceback
        LogInfo(traceback.format_exc())
