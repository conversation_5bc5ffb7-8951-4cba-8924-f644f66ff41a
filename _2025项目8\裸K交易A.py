import time
import talib
import numpy as np 
from collections import deque
#参数区
g_params['交易合约'] = 'SHFE|Z|AU|MAIN'  # 交易下单的合约品种,一般用主连
g_params['K线基础时间']     = 'M'        # k线基础时间,tick和秒填“T”,分钟填“M”,日线填“D”
g_params['K线基础周期']     =  1         # k线周期,K线基础时间的倍数,15分钟填写“15”
g_params['标志K实体跳数']   =  4         # 标志K实体跳数
g_params['确认K突破速率起始秒数'] = 30  # 确认K突破速率起始秒数
g_params['确认K突破速率终止秒数'] = 50  # 确认K突破速率终止秒数
g_params['终止信号十字星计数上限'] = 5  # 终止信号十字星计数上限

g_params['交易方向开关'] = 0   #大于0开多，小于0开空，等于0双向开仓
g_params['下单超价跳数'] = 1   #下单超价跳数
g_params['首单下单量']   = 1   #首单下单量
g_params['加仓1判断分钟数']  = 5  #加仓1判断分钟数
g_params['加仓1下单倍率']    = 1  #加仓1下单倍率
g_params['加仓2判断分钟数']  = 5  #加仓2判断分钟数
g_params['加仓2下单倍率']    = 1  #加仓2下单倍率


g_params['固定止盈跳数'] = 15   #固定止盈跳数   
g_params['固定止损跳数'] =  5   #固定止损跳数  
g_params['动态止损止盈1回撤跳数']  = 3#动态止损止盈1回撤跳数
g_params['动态止损止盈2标志K跳数'] = 3#动态止损止盈2标志K跳数
g_params['动态止损止盈模式切换分钟数'] = 5   #动态止损止盈模式切换分钟数
g_params['收盘倒计时分钟数设置'] = 1#距离收盘时间范围不开仓，并清仓    
  
订阅数据长度=2000 
scode, k_btime, k_cycle, flag_K_tick, confirmation_K_start_seconds, confirmation_K_end_seconds,termination_signal_doji_count_limit='','',0,0,0,0,0
trade_sw, ovprice_tick, lots_o1,lots_add_1_judgmental_minute,lots_add_1_order_multiplier,lots_add_2_judgmental_minute,lots_add_2_order_multiplier=0,0,0,0,0,0,0
fixed_take_profit_tick,fixed_stop_loss_tick,Dynamic_loss_take_profit_1_Retracement_tick,Dynamic_loss_take_profit_2_Flag_K_tick,Dynamic_loss_take_profit_mode_switching_minutes,CloseTime=0,0,0,0,0,0
def initialize(context): 
    global g_params, scode, k_btime, k_cycle, flag_K_tick, confirmation_K_start_seconds, confirmation_K_end_seconds,termination_signal_doji_count_limit
    global trade_sw, ovprice_tick, lots_o1,lots_add_1_judgmental_minute,lots_add_1_order_multiplier,lots_add_2_judgmental_minute,lots_add_2_order_multiplier
    global fixed_take_profit_tick,fixed_stop_loss_tick,Dynamic_loss_take_profit_1_Retracement_tick,Dynamic_loss_take_profit_2_Flag_K_tick,Dynamic_loss_take_profit_mode_switching_minutes,CloseTime
    scode = g_params['交易合约']
    k_btime = g_params['K线基础时间']
    k_cycle = g_params['K线基础周期']
    flag_K_tick = g_params['标志K实体跳数']
    confirmation_K_start_seconds = g_params['确认K突破速率起始秒数']
    confirmation_K_end_seconds = g_params['确认K突破速率终止秒数']
    termination_signal_doji_count_limit = g_params['终止信号十字星计数上限']

    trade_sw = g_params['交易方向开关']
    ovprice_tick = g_params['下单超价跳数']
    lots_o1 = g_params['首单下单量']
    lots_add_1_judgmental_minute = g_params['加仓1判断分钟数']
    lots_add_1_order_multiplier = g_params['加仓1下单倍率']
    lots_add_2_judgmental_minute = g_params['加仓2判断分钟数']
    lots_add_2_order_multiplier = g_params['加仓2下单倍率']

    fixed_take_profit_tick = g_params['固定止盈跳数']
    fixed_stop_loss_tick = g_params['固定止损跳数']
    Dynamic_loss_take_profit_1_Retracement_tick = g_params['动态止损止盈1回撤跳数']
    Dynamic_loss_take_profit_2_Flag_K_tick = g_params['动态止损止盈2标志K跳数']
    Dynamic_loss_take_profit_mode_switching_minutes = g_params['动态止损止盈模式切换分钟数']
    CloseTime = g_params['收盘倒计时分钟数设置']

    SetBarInterval(scode, k_btime, k_cycle) #订阅交易合约
    SetBarInterval(scode,'T', 1,isTrigger=True) #订阅交易合约
    SetActual()           #设置实盘运行
    SetOrderWay(1)        #设置K线走完后发单
    SetTriggerType(1)
    SetTriggerType(2)
    SetTriggerType(5)     #设置K线触发
# 策略触发事件每次触发时都会执行该函数

BKS,SKS,BPS,SPS,K_status,K_time,HH,LL,_2_Flag_K_LL,_2_Flag_K_HH=0,0,0,0,0,0,0,0,0,0
BKStatus,SKStatus,BPStatus,SPStatus,BKProfitability,SKProfitability,BK_time,SK_time=0,0,0,0,0,0,0,0
sBARS,buys,sells=deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3)
def handle_data(context):
    global BKS,SKS,BPS,SPS,K_status,K_time,HH,LL
    global BKStatus,SKStatus,BPStatus,SPStatus,BKProfitability,SKProfitability,BK_time,SK_time
    O=Open( scode, k_btime, k_cycle)     # 交易合约开盘价格
    H=High( scode, k_btime, k_cycle)     # 交易合约最高价格
    L=Low(  scode, k_btime, k_cycle)     # 交易合约最低价格
    C=Close(scode, k_btime, k_cycle)     # 交易合约收盘价格
    tcode = scode
    absolute_seconds = int(time.time())
    absolute_minutes = int(absolute_seconds / 60)
    MINPRICE=PriceTick(tcode)            # 交易合约最小变动价
    HTS=1 if context.strategyStatus()=="C" else 0   
    s_CurrentBar=CurrentBar(scode, k_btime, k_cycle)
    sBARS.append(s_CurrentBar)
    K_EndTrigger = sBARS[0]>0 and sBARS[1]<sBARS[2]    
    if s_CurrentBar<2:return
    if A_BuyPosition(tcode)==0 and C[-2]>=O[-2]+flag_K_tick*MINPRICE:# and K_status<=0:
        K_status = 1
        K_time = absolute_seconds
        HH=H[-2]
        LL=L[-2]
    elif K_status>0 and K_EndTrigger:
        K_status+=1
        
    if A_SellPosition(tcode)==0 and C[-2]<=O[-2]-flag_K_tick*MINPRICE:# and K_status>=0:
        K_status = -1
        K_time = absolute_seconds
        HH=H[-2]
        LL=L[-2]    
    elif K_status<0 and K_EndTrigger:
        K_status-=1
    if abs(K_status)==termination_signal_doji_count_limit+1:
        K_status=0

    if K_EndTrigger:
        if BKS>=1 and A_BuyPosition(tcode)==0: BKS=0
        if SKS>=1 and A_SellPosition(tcode)==0: SKS=0 
        BPS=0
        SPS=0     
        # LogInfo("sBARS=>",sBARS,"BKS=>",BKS,"SKS=>",SKS)       

    #主连下单，计算止损止盈价格线
    MP=MarketPosition(tcode)   #持仓方向
    MC=CurrentContracts(tcode) #持仓手数
    AVP=AvgEntryPrice(tcode)   #EntryPrice(tcode) #持仓均价/第一笔建仓价
    # HCS.append(context.strategyStatus())
    _VTS=VTS(Time())
    TradeEnbTime=TimeTo_Minutes(_VTS[3])-TimeTo_Minutes(_VTS[2])>CloseTime
    TradeOffTime=CloseTime>=TimeTo_Minutes(_VTS[3])-TimeTo_Minutes(_VTS[2])>0

    PlotBar('K_status',  0,K_status , RGB_Red() if K_status > 0 else RGB_Green(), False, True,0,"标志K跟踪")     
    PlotNumeric('_K_status' , K_status , RGB_Red() if K_status > 0 else RGB_Green(), False, False,0,"标志K跟踪")
    PlotNumeric('0' , 0 , 0xffffff, False, False,0,"标志K跟踪")
    PlotNumeric('HH', HH, 0xff0000, True, False, 0, "标志K跟踪主图")
    PlotNumeric('LL', LL, 0x00ff00, True, False, 0, "标志K跟踪主图")

    time_control=confirmation_K_start_seconds <= (absolute_seconds-K_time) <= confirmation_K_end_seconds
    BK1 = TradeEnbTime and K_status== 1 and time_control and C[-1]>=HH+(flag_K_tick-1)*MINPRICE
    SK1 = TradeEnbTime and K_status==-1 and time_control and C[-1]<=LL-(flag_K_tick-1)*MINPRICE
    # BK1 = TradeEnbTime and K_status== 1 and time_control and C[-1]>=O[-1]+(flag_K_tick-1)*MINPRICE
    # SK1 = TradeEnbTime and K_status==-1 and time_control and C[-1]<=O[-1]-(flag_K_tick-1)*MINPRICE
    BK2 = TradeEnbTime and K_status== 2 and C[-2]>O[-2] and C[-1]>LL
    SK2 = TradeEnbTime and K_status==-2 and C[-2]<O[-2] and C[-1]<HH
    BK3 = TradeEnbTime and K_status> 1 and C[-2]>O[-2] and C[-1]>LL
    SK3 = TradeEnbTime and K_status<-1 and C[-2]<O[-2] and C[-1]<HH
    BK  = BK1 or BK2 or BK3
    SK  = SK1 or SK2 or SK3

    if context.strategyStatus()=="C"  and  ExchangeStatus(ExchangeName(tcode)) in ('1','2','3'): #QuoteSvrState() == 1 and TradeSvrState()==1
        if  BKS==0 and trade_sw>=0 and BK:
            LogInfo(tcode, "开多仓首单", lots_o1, "手")
            tim_trigger(True,False,lots_o1,ovprice_tick,tcode)
            BKS=1
            BK_time=absolute_minutes
            BKStatus=0
            SPStatus=0
            BKProfitability=0
  
        if SKS==0 and trade_sw<=0 and SK:
            LogInfo(tcode, "开空仓首单", lots_o1, "手")
            tim_trigger(False,True,lots_o1,ovprice_tick,tcode)
            SKS=1
            SK_time=absolute_minutes
            SKStatus=0
            BPStatus=0
            SKProfitability=0
    
        buys.append(A_BuyPosition(tcode))   
        sells.append(A_SellPosition(tcode)) 

        if A_BuyPosition(tcode)>0: 
            K_status=0
            _AVP=A_BuyAvgPrice(tcode)
            profit_diff=(C[-1]-_AVP)/MINPRICE
            BKProfitability=max(BKProfitability,profit_diff)
            if (C[-1]-O[-1])/MINPRICE>=Dynamic_loss_take_profit_2_Flag_K_tick:
                _2_Flag_K_LL=L[-1]
            true_lots= A_BuyPosition(tcode)
            hh_stop = _AVP + fixed_take_profit_tick*MINPRICE
            ll_stop = _AVP - fixed_stop_loss_tick*MINPRICE
            PlotPartLine("固定止损线",CurrentBar(),ll_stop,2,ll_stop,0x00ff00)
            PlotPartLine("固定止盈线", CurrentBar(), hh_stop, 2, hh_stop, 0xff0000)
            if C[-1]<ll_stop:
                BKS=1
                LogInfo(tcode,"多单止损",true_lots,"手")
                tim_trigger_Exit(False,True,ovprice_tick,tcode,true_lots)     
            elif C[-1]>hh_stop:        
                BKS=1
                LogInfo(tcode,"多单止赢",true_lots,"手")
                tim_trigger_Exit(False,True,ovprice_tick,tcode,true_lots)  
            elif absolute_minutes-BK_time<Dynamic_loss_take_profit_mode_switching_minutes and BKProfitability-profit_diff>=Dynamic_loss_take_profit_1_Retracement_tick:
                BKS=1
                LogInfo(tcode,"多单动态止损止盈1",true_lots,"手")
                tim_trigger_Exit(False,True,ovprice_tick,tcode,true_lots)
            elif absolute_minutes-BK_time>=Dynamic_loss_take_profit_mode_switching_minutes and C[-1]<_2_Flag_K_LL:
                BKS=1
                LogInfo(tcode,"多单动态止损止盈2",true_lots,"手")
                tim_trigger_Exit(False,True,ovprice_tick,tcode,true_lots)    
            elif BKStatus==0 and absolute_minutes-BK_time>=lots_add_1_judgmental_minute and C[-1]>_AVP:
                BKStatus=1
                BK_time=absolute_minutes
                tick_diff=(C[-1]-_AVP)/MINPRICE
                BK_lot=int(tick_diff*lots_add_1_order_multiplier)
                LogInfo(tcode, "多单加仓1", BK_lot, "手")
                tim_trigger(True,False,BK_lot,ovprice_tick,tcode)
            elif BKStatus==1 and absolute_minutes-BK_time>=lots_add_2_judgmental_minute and C[-1]>_AVP:
                BKStatus=2
                BK_time=absolute_minutes
                tick_diff=(C[-1]-_AVP)/MINPRICE
                BK_lot=int(tick_diff*lots_add_2_order_multiplier)
                LogInfo(tcode, "多单加仓2", BK_lot, "手")
                tim_trigger(True,False,BK_lot,ovprice_tick,tcode)    
            elif  TradeOffTime:
                BKS=1
                LogInfo(tcode,"收盘前平多仓",true_lots,"手")
                tim_trigger_Exit(False,True,ovprice_tick,tcode,true_lots)    
        if A_SellPosition(tcode)>0: 
            K_status=0
            _AVP=A_SellAvgPrice(tcode)
            profit_diff=(_AVP-C[-1])/MINPRICE
            SKProfitability=max(SKProfitability,profit_diff)
            if (O[-1]-C[-1])/MINPRICE>=Dynamic_loss_take_profit_2_Flag_K_tick:
                _2_Flag_K_HH=H[-1]
            true_lots= A_SellPosition(tcode)
            hh_stop = _AVP - fixed_take_profit_tick*MINPRICE
            ll_stop = _AVP + fixed_stop_loss_tick*MINPRICE
            PlotPartLine("固定止损线",CurrentBar(),ll_stop,2,ll_stop,0x00ff00)
            PlotPartLine("固定止盈线", CurrentBar(), hh_stop, 2, hh_stop, 0xff0000)
            if C[-1]>ll_stop:
                SKS=1
                LogInfo(tcode,"空单止损",true_lots,"手")
                tim_trigger_Exit(True,False,ovprice_tick,tcode,true_lots)
            elif C[-1]<hh_stop:
                SKS=1
                LogInfo(tcode,"空单止赢",true_lots,"手")
                tim_trigger_Exit(True,False,ovprice_tick,tcode,true_lots)
            elif absolute_minutes-SK_time<Dynamic_loss_take_profit_mode_switching_minutes and SKProfitability-profit_diff>=Dynamic_loss_take_profit_1_Retracement_tick: 
                SKS=1
                LogInfo(tcode,"空单动态止损止盈1",true_lots,"手")
                tim_trigger_Exit(True,False,ovprice_tick,tcode,true_lots)
            elif absolute_minutes-SK_time>=Dynamic_loss_take_profit_mode_switching_minutes and C[-1]>_2_Flag_K_HH:
                SKS=1
                LogInfo(tcode,"空单动态止损止盈2",true_lots,"手")
                tim_trigger_Exit(True,False,ovprice_tick,tcode,true_lots)
            elif SKStatus==0 and absolute_minutes-SK_time>=lots_add_1_judgmental_minute and C[-1]<_AVP:
                SKStatus=1
                SK_time=absolute_minutes
                tick_diff=(_AVP-C[-1])/MINPRICE
                SK_lot=int(tick_diff*lots_add_1_order_multiplier)
                LogInfo(tcode, "空单加仓1", SK_lot, "手")
                tim_trigger(False, True, SK_lot, ovprice_tick, tcode)
            elif SKStatus==1 and absolute_minutes-SK_time>=lots_add_2_judgmental_minute and C[-1]<_AVP:
                SKStatus=2
                SK_time=absolute_minutes
                tick_diff=(_AVP-C[-1])/MINPRICE
                SK_lot=int(tick_diff*lots_add_2_order_multiplier)
                LogInfo(tcode, "空单加仓2", SK_lot, "手")
                tim_trigger(False, True, SK_lot, ovprice_tick, tcode)
            elif  TradeOffTime:
                SKS=1
                LogInfo(tcode,"收盘前平空仓",true_lots,"手")
                tim_trigger_Exit(True,False,ovprice_tick,tcode,true_lots)

def tim_trigger(BK,SK,qty,itk,tcode):#盘中实时开仓
    global BKS,SKS
    if BK and BKS==0 and (A_BuyPosition(tcode) == 0) :
        iprc = min(Q_AskPrice(tcode) +itk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        A_SendOrder(Enum_Buy(), Enum_Entry(), qty, iprc,tcode) 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单买入开仓价==>",iprc,"买入数量==>",qty)
        BKS=1    
    elif SK and SKS==0 and (A_SellPosition(tcode) == 0):    
        iprc = max(Q_BidPrice(tcode) - itk*PriceTick(tcode), Q_LowLimit(tcode))  # 对盘超价                         
        A_SendOrder(Enum_Sell(), Enum_Entry(), qty, iprc,tcode)   
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单卖出开仓价==>",iprc,"卖出数量==>",qty)
        SKS=1    

def tim_trigger_Exit(BP,SP,otk,tcode,clots):#盘中实时平仓
    global BKS,SKS,BPS,SPS
    if BP and BPS==0 and A_SellPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_SellPosition(tcode))
        prc = min(Q_AskPrice(tcode) +otk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        if ExchangeName(tcode) not in ['SHFE','INE']:    
            retExit, ExitOrderId=A_SendOrder(Enum_Buy(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodaySellPosition(tcode)
            dlots=lots-tlots            
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),lots, prc,tcode) #今仓足够平仓,上期所能交所优先超价全部平今仓    
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Buy(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Buy(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单买入平仓价==>",prc,"买入平仓数量==>",_lots)
        BPS=1  
        if SKS==1:SKS=2      
    elif SP and SPS==0 and A_BuyPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_BuyPosition(tcode))
        prc = max(Q_BidPrice(tcode) - otk*PriceTick(tcode), Q_LowLimit(tcode))
        if ExchangeName(tcode) not in ['SHFE','INE']:
            retExit, ExitOrderId=A_SendOrder(Enum_Sell(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodayBuyPosition(tcode)
            dlots=lots-tlots
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),lots, prc,tcode)   #今仓足够平仓,上期所能交所优先超价全部平今仓  
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Sell(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Sell(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单卖出平仓价==>",prc,"卖出平仓数量==>",_lots)
        SPS=1    
        if BKS==1:BKS=2 

def floattime_sum(floatin1, floatin2, len_set=12):  # 高精度浮点时间求和（精确到毫秒）
    # 设置浮点数格式，保留len_set位小数
    lensave = f"%0.{len_set}f"
    
    # 格式化浮点数并提取各时间部分
    def extract_time_parts(floatin):
        strfloat = lensave % floatin
        return int(strfloat[2:4]), int(strfloat[4:6]), int(strfloat[6:8]), int(strfloat[8:11])
    
    h1, m1, s1, ms1 = extract_time_parts(floatin1)
    h2, m2, s2, ms2 = extract_time_parts(floatin2)
    
    # 计算总和并处理进位
    total_ms = ms1 + ms2
    ms_carry = total_ms // 1000
    new_ms = total_ms % 1000
    
    total_s = s1 + s2 + ms_carry
    s_carry = total_s // 60
    new_s = total_s % 60
    
    total_m = m1 + m2 + s_carry
    m_carry = total_m // 60
    new_m = total_m % 60
    
    new_h = h1 + h2 + m_carry
    new_h = min(new_h, 99)  # 限制小时数不超过99
    
    # 组合新的浮点时间字符串并转换回浮点数
    new_str_time = f"0.{new_h:02}{new_m:02}{new_s:02}{new_ms:03}"
    return float(new_str_time)

def TimeTo_Minutes(time_in):
    timestr='%0.6f'%time_in
    hsave=int(timestr[2:4])
    msave=int(timestr[4:6])
    tcout=hsave*60+msave
    return tcout
def SessionOpenTime(contractId=''):  # 获取交易时段开盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler:continue
        tlout.append(GetSessionStartTime(contractId, i))  # 获取每个交易时段的开盘时间并加入列表
    return tlout

def SessionCloseTime(contractId=''):  # 获取交易时段收盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler-1:continue
        tlout.append(GetSessionEndTime(contractId, i))  # 获取每个交易时段的收盘时间并加入列表
    return tlout

def VTS(time_in, contractId=''):  # 根据输入时间和合约ID计算交易时段
    RTS, CTS, TSession = [], [], []  # 初始化三个列表，用于存储修正后的时间、收盘时间和交易时段
    opentimet = SessionOpenTime(contractId)  # 获取所有交易时段的开盘时间
    Closetimet = SessionCloseTime(contractId)  # 获取所有交易时段的收盘时间
    
    for open_time, close_time in zip(opentimet, Closetimet):
        if time_in >= open_time:  # 判断输入时间是否在开盘时间之后
            RTS.append(time_in)  # 如果是，加入RTS列表
        else:
            RTS.append(floattime_sum(time_in, 0.24))  # 如果不是，修正时间后加入RTS列表
        
        if close_time >= open_time:  # 判断收盘时间是否在开盘时间之后
            CTS.append(close_time)  # 如果是，加入CTS列表
        else:
            CTS.append(floattime_sum(close_time, 0.24))  # 如果不是，修正时间后加入CTS列表
        
        if open_time <= RTS[-1] <= CTS[-1]:  # 判断修正后的时间是否在交易时段内
            TSession.append(len(RTS) - 1)  # 如果是，加入TSession列表

    if len(TSession) == 1:  # 如果只有一个交易时段
        idx = TSession[0]
        return idx, opentimet[idx], RTS[idx], CTS[idx]  # 返回交易时段和相关时间
    else:
        return -1, time_in, time_in, time_in  # 否则返回默认值
