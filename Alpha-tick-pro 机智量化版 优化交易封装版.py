# -*- coding: utf-8 -*-
"""
Alpha-Tick Pro RB 螺纹钢高频策略 - 机智量化版优化交易封装版
使用封装的交易函数，支持实时和历史阶段的差异化处理
"""

import numpy as np
import pandas as pd
import time

# ==================== 策略参数配置 ====================
g_params['大单标准差倍数'] = 3.2
g_params['订单流分析周期'] = 15
g_params['基础突破系数'] = 0.28
g_params['波动率调整系数'] = 0.12
g_params['ATR计算周期'] = 14
g_params['单笔最大风险'] = 0.02
g_params['单日止损线'] = -0.03
g_params['最低流动性阈值'] = 80
g_params['合约乘数'] = 10
g_params['最小价格变动'] = 1
g_params['最大持仓时间'] = 180
g_params['交易间隔'] = 3000
g_params['K线基础时间'] = 'T'
g_params['K线基础周期'] = 1
g_params['订阅数据长度'] = 2000

# ==================== 全局变量 ====================
# 核心状态变量
big_vol = 0
trading_paused = False
daily_high = 0
daily_low = 0
pre_close = 0
observe_sell_price = 0
observe_buy_price = 0
cumulative_delta = 0
ema_delta = 0
buy_market_vol = 0
sell_market_vol = 0
position_size = 1
volatility_ratio = 0
dynamic_k = 0
last_trade_time = 0
loss_streak = 0
daily_max_profit = 0
account_equity = 0
trend_confidence = 0
flow_strength = 0

# 交易状态管理变量
BKS = 0  # 多头开仓状态 (0:未开仓, 1:已开仓, 2:已平仓)
SKS = 0  # 空头开仓状态 (0:未开仓, 1:已开仓, 2:已平仓)
BPS = 0  # 多头平仓状态 (0:未平仓, 1:已平仓)
SPS = 0  # 空头平仓状态 (0:未平仓, 1:已平仓)

# 交易参数
超价跳数 = 2
平仓超价跳数 = 1
基准合约 = 'SHFE|Z|RB|MAIN'

# ==================== 交易封装函数 ====================
def tim_trigger(BK, SK, qty, itk, tcode):
    """盘中实时开仓"""
    global BKS, SKS
    
    if BK and BKS == 0:
        iprc = min(Q_AskPrice(tcode) + itk * PriceTick(tcode), Q_UpperLimit(tcode))
        A_SendOrder(Enum_Buy(), Enum_Entry(), qty, iprc, tcode) 
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "多单买入开仓价==>", iprc, "买入数量==>", qty)
        BKS = 1    
    elif SK and SKS == 0:    
        iprc = max(Q_BidPrice(tcode) - itk * PriceTick(tcode), Q_LowLimit(tcode))
        A_SendOrder(Enum_Sell(), Enum_Entry(), qty, iprc, tcode)   
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "空单卖出开仓价==>", iprc, "卖出数量==>", qty)
        SKS = 1    

def tim_trigger_Exit(BP, SP, otk, tcode, clots):
    """盘中实时平仓"""
    global BKS, SKS, BPS, SPS
    
    if BP and BPS == 0 and A_SellPosition(tcode) > 0 and clots > 0:
        _lots = min(clots, A_SellPosition(tcode))
        prc = min(Q_AskPrice(tcode) + otk * PriceTick(tcode), Q_UpperLimit(tcode))
        
        if ExchangeName(tcode) not in ['SHFE', 'INE']:    
            retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), _lots, prc, tcode) 
        else:
            lots = _lots
            tlots = A_TodaySellPosition(tcode)
            dlots = lots - tlots            
            if tlots >= lots:       
                TretExit, TExitOrderId = A_SendOrder(Enum_Buy(), Enum_ExitToday(), lots, prc, tcode)
            elif tlots > 0:       
                TretExit, TExitOrderId = A_SendOrder(Enum_Buy(), Enum_ExitToday(), tlots, prc, tcode)
                TretExit2, TExitOrderId2 = A_SendOrder(Enum_Buy(), Enum_Exit(), int(dlots), prc, tcode)
            elif tlots == 0:  
                retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), lots, prc, tcode)
        
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "空单买入平仓价==>", prc, "买入平仓数量==>", _lots)
        BPS = 1  
        if SKS == 1: 
            SKS = 2      
            
    elif SP and SPS == 0 and A_BuyPosition(tcode) > 0 and clots > 0:
        _lots = min(clots, A_BuyPosition(tcode))
        prc = max(Q_BidPrice(tcode) - otk * PriceTick(tcode), Q_LowLimit(tcode))
        
        if ExchangeName(tcode) not in ['SHFE', 'INE']:
            retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), _lots, prc, tcode) 
        else:
            lots = _lots
            tlots = A_TodayBuyPosition(tcode)
            dlots = lots - tlots
            if tlots >= lots:       
                TretExit, TExitOrderId = A_SendOrder(Enum_Sell(), Enum_ExitToday(), lots, prc, tcode)
            elif tlots > 0:       
                TretExit, TExitOrderId = A_SendOrder(Enum_Sell(), Enum_ExitToday(), tlots, prc, tcode)
                TretExit2, TExitOrderId2 = A_SendOrder(Enum_Sell(), Enum_Exit(), int(dlots), prc, tcode)
            elif tlots == 0:  
                retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), lots, prc, tcode)
        
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "多单卖出平仓价==>", prc, "卖出平仓数量==>", _lots)
        SPS = 1    
        if BKS == 1: 
            BKS = 2 
        
def his_trigger(BK, SK, qty, itk):
    """历史开仓"""
    global BKS, SKS
    
    if BK and BKS == 0:
        iprc = Close()[-1] + itk * PriceTick()
        Buy(qty, iprc) 
        LogInfo(Time(), "->合约==>", Symbol(), "多单买入开仓价==>", iprc, "买入数量==>", qty)
        BKS = 1    
    elif SK and SKS == 0:
        iprc = Close()[-1] - itk * PriceTick()
        SellShort(qty, iprc) 
        LogInfo(Time(), "->合约==>", Symbol(), "空单卖出开仓价==>", iprc, "卖出数量==>", qty)
        SKS = 1    

def his_trigger_Exit(BP, SP, otk, clots):
    """历史平仓"""
    global BKS, SKS, BPS, SPS
    
    if BP and BPS == 0 and SellPosition() > 0 and clots > 0:
        _lots = min(clots, SellPosition())
        prc = Close()[-1] + otk * PriceTick()
        BuyToCover(_lots, prc)
        LogInfo(Time(), "->合约==>", Symbol(), "空单买入平仓价==>", prc, "买入平仓数量==>", _lots)
        BPS = 1  
        if SKS == 1: 
            SKS = 2   
    elif SP and SPS == 0 and BuyPosition() > 0 and clots > 0:
        _lots = min(clots, BuyPosition())
        prc = Close()[-1] - otk * PriceTick()
        Sell(_lots, prc)
        LogInfo(Time(), "->合约==>", Symbol(), "多单卖出平仓价==>", prc, "卖出平仓数量==>", _lots)
        SPS = 1  
        if BKS == 1: 
            BKS = 2   

def reset_trading_status():
    """重置交易状态"""
    global BKS, SKS, BPS, SPS
    BKS = 0
    SKS = 0
    BPS = 0
    SPS = 0

def is_real_time():
    """判断是否为实时阶段"""
    try:
        return Q_UpdateTime() is not None
    except:
        return False

# ==================== 策略初始化 ====================
def initialize(context):
    """策略初始化"""
    global account_equity
    
    LogInfo("=== Alpha-Tick Pro RB 螺纹钢高频策略初始化 ===")
    
    # 订阅数据
    SetBarInterval(基准合约, g_params['K线基础时间'], g_params['K线基础周期'], g_params['订阅数据长度'])
    
    # 初始化账户信息
    try:
        account_equity = A_TotalAssets()
    except:
        account_equity = 1000000
    
    # 初始化变量
    init_daily_variables()
    
    LogInfo(f"策略初始化完成，基准合约: {基准合约}")
    LogInfo(f"账户总资产: {account_equity:.2f}")

def init_daily_variables():
    """初始化日变量"""
    global daily_high, daily_low, pre_close, cumulative_delta
    global daily_max_profit, loss_streak, buy_market_vol, sell_market_vol
    
    try:
        close_prices = Close()
        if len(close_prices) > 1:
            pre_close = close_prices[-2]
            daily_high = High()[-1]
            daily_low = Low()[-1]
        else:
            pre_close = Close()[-1] if len(close_prices) > 0 else 0
            daily_high = High()[-1] if len(High()) > 0 else 0
            daily_low = Low()[-1] if len(Low()) > 0 else 0
    except:
        pre_close = 0
        daily_high = 0
        daily_low = 0
    
    cumulative_delta = 0
    daily_max_profit = 0
    loss_streak = 0
    buy_market_vol = 0
    sell_market_vol = 0
    
    # 重置交易状态
    reset_trading_status()
    
    LogInfo(f"日变量初始化完成 - 前收盘: {pre_close:.1f}, 当日高: {daily_high:.1f}, 当日低: {daily_low:.1f}")
    LogInfo("交易状态已重置")

def handle_data(context):
    """主策略逻辑"""
    try:
        # 检查日期变化
        check_daily_reset()
        
        # 更新账户信息
        update_account_info()
        
        # 核心计算
        calculate_big_volume_threshold()
        analyze_order_flow()
        calculate_rbreaker_levels()
        calculate_trend_strength()
        
        # 信号生成
        long_signal, short_signal = generate_trading_signals()
        
        # 仓位计算
        calculate_position_size()
        
        # 风险控制
        if risk_control_check():
            return
        
        # 交易执行
        if not trading_paused:
            execute_trading_signals(long_signal, short_signal)
            manage_existing_positions()
        
        # 收盘检查
        check_market_close()
        
        # 可视化
        update_visualization()
        
    except Exception as e:
        LogError(f"策略执行异常: {str(e)}")

def check_daily_reset():
    """检查日期重置"""
    try:
        current_date = Date()
        # 这里可以添加日期变化检测逻辑
        # 如果检测到新的交易日，调用init_daily_variables()
    except:
        pass

def update_account_info():
    """更新账户信息"""
    global account_equity, daily_max_profit

    try:
        account_equity = A_TotalAssets()
        current_profit = A_FloatProfit()
        if current_profit > daily_max_profit:
            daily_max_profit = current_profit
    except:
        pass

def calculate_big_volume_threshold():
    """计算大单阈值"""
    global big_vol

    try:
        volumes = Vol()
        if len(volumes) < 10:
            big_vol = 100
            return

        recent_volumes = volumes[-30:]
        mean_vol = np.mean(recent_volumes)
        std_vol = np.std(recent_volumes)

        big_vol = mean_vol + g_params['大单标准差倍数'] * std_vol

    except Exception as e:
        big_vol = 100

def analyze_order_flow():
    """订单流分析"""
    global cumulative_delta, ema_delta, flow_strength

    try:
        if is_real_time():
            bid_vol1 = Q_BidVolume(0)
            ask_vol1 = Q_AskVolume(0)
        else:
            bid_vol1 = 50  # 历史阶段使用默认值
            ask_vol1 = 50

        weighted_delta = bid_vol1 - ask_vol1
        cumulative_delta += weighted_delta

        alpha = 2.0 / (g_params['订单流分析周期'] + 1)
        ema_delta = alpha * weighted_delta + (1 - alpha) * ema_delta

        flow_strength = ema_delta

    except Exception as e:
        flow_strength = 0

def calculate_rbreaker_levels():
    """计算R-Breaker价位"""
    global observe_sell_price, observe_buy_price, dynamic_k, volatility_ratio

    try:
        if daily_high == 0 or daily_low == 0 or pre_close == 0:
            return

        close_prices = Close()
        if len(close_prices) >= 20:
            volatility_ratio = np.std(close_prices[-20:]) / np.mean(close_prices[-20:])
        else:
            volatility_ratio = 0.02

        dynamic_k = g_params['基础突破系数'] + g_params['波动率调整系数'] * volatility_ratio

        observe_sell_price = daily_high + dynamic_k * (pre_close - daily_low)
        observe_buy_price = daily_low - dynamic_k * (daily_high - pre_close)

    except Exception as e:
        pass

def calculate_trend_strength():
    """计算趋势强度"""
    global trend_confidence

    try:
        close_prices = Close()
        if len(close_prices) < 20:
            trend_confidence = 0
            return

        current_close = close_prices[-1]
        ma5 = np.mean(close_prices[-5:])
        ma20 = np.mean(close_prices[-20:])

        trend_confidence = 0
        if ma5 > ma20:
            trend_confidence += 30
        if current_close > ma20:
            trend_confidence += 20

        trend_confidence = min(100, max(-100, trend_confidence))

    except Exception as e:
        trend_confidence = 0

def generate_trading_signals():
    """生成交易信号"""
    try:
        current_close = Close()[-1]

        # 多头信号
        long_score = 0
        if current_close >= observe_sell_price and observe_sell_price > 0:
            long_score += 25
        if flow_strength < -20:
            long_score += 25
        if trend_confidence > 30:
            long_score += 20

        long_signal = long_score >= 50

        # 空头信号
        short_score = 0
        if current_close <= observe_buy_price and observe_buy_price > 0:
            short_score += 25
        if flow_strength > 20:
            short_score += 25
        if trend_confidence < -30:
            short_score += 20

        short_signal = short_score >= 50

        return long_signal, short_signal

    except Exception as e:
        return False, False

def calculate_position_size():
    """计算仓位大小"""
    global position_size

    try:
        if account_equity <= 0:
            position_size = 1
            return

        risk_unit = g_params['单笔最大风险'] * account_equity
        current_close = Close()[-1]

        position_size = max(1, min(10, int(risk_unit / (current_close * g_params['合约乘数'] * 0.02))))

    except Exception as e:
        position_size = 1

def risk_control_check():
    """风险控制检查"""
    global trading_paused

    try:
        current_profit_ratio = A_FloatProfit() / account_equity if account_equity > 0 else 0

        if current_profit_ratio < g_params['单日止损线']:
            close_all_positions()
            trading_paused = True
            LogWarn(f"【风控】单日最大损失触发: {current_profit_ratio:.2%}")
            return True

        return False

    except Exception as e:
        return False

def execute_trading_signals(long_signal, short_signal):
    """执行交易信号"""
    global last_trade_time

    try:
        current_time_ms = int(time.time() * 1000)

        if current_time_ms - last_trade_time < g_params['交易间隔']:
            return

        if is_real_time():
            # 实时阶段
            tim_trigger(long_signal, short_signal, position_size, 超价跳数, 基准合约)
        else:
            # 历史阶段
            his_trigger(long_signal, short_signal, position_size, 超价跳数)

        if long_signal or short_signal:
            last_trade_time = current_time_ms

    except Exception as e:
        LogError(f"交易执行异常: {str(e)}")

def manage_existing_positions():
    """管理现有持仓"""
    try:
        # 生成平仓信号
        exit_long, exit_short = generate_exit_signals()

        if is_real_time():
            # 实时阶段
            if exit_long:
                long_position = A_BuyPosition(基准合约)
                if long_position > 0:
                    tim_trigger_Exit(False, True, 平仓超价跳数, 基准合约, long_position)

            if exit_short:
                short_position = A_SellPosition(基准合约)
                if short_position > 0:
                    tim_trigger_Exit(True, False, 平仓超价跳数, 基准合约, short_position)
        else:
            # 历史阶段
            if exit_long:
                long_position = BuyPosition()
                if long_position > 0:
                    his_trigger_Exit(False, True, 平仓超价跳数, long_position)

            if exit_short:
                short_position = SellPosition()
                if short_position > 0:
                    his_trigger_Exit(True, False, 平仓超价跳数, short_position)

    except Exception as e:
        LogError(f"持仓管理异常: {str(e)}")

def generate_exit_signals():
    """生成平仓信号"""
    try:
        close_prices = Close()
        current_close = close_prices[-1]

        # 多头平仓信号
        exit_long = False
        if len(close_prices) >= 5:
            ma5 = np.mean(close_prices[-5:])
            if current_close < ma5 and flow_strength > 20:
                exit_long = True

        # 空头平仓信号
        exit_short = False
        if len(close_prices) >= 5:
            ma5 = np.mean(close_prices[-5:])
            if current_close > ma5 and flow_strength < -20:
                exit_short = True

        return exit_long, exit_short

    except Exception as e:
        return False, False

def check_market_close():
    """检查收盘时间"""
    try:
        current_time = Time()
        if current_time >= 0.1455:  # 14:55
            close_all_positions()
    except Exception as e:
        pass

def close_all_positions():
    """强制平仓所有持仓"""
    try:
        if is_real_time():
            long_position = A_BuyPosition(基准合约)
            short_position = A_SellPosition(基准合约)

            if long_position > 0:
                tim_trigger_Exit(False, True, 平仓超价跳数, 基准合约, long_position)

            if short_position > 0:
                tim_trigger_Exit(True, False, 平仓超价跳数, 基准合约, short_position)
        else:
            long_position = BuyPosition()
            short_position = SellPosition()

            if long_position > 0:
                his_trigger_Exit(False, True, 平仓超价跳数, long_position)

            if short_position > 0:
                his_trigger_Exit(True, False, 平仓超价跳数, short_position)

    except Exception as e:
        LogError(f"强制平仓异常: {str(e)}")

def update_visualization():
    """更新可视化"""
    try:
        if observe_sell_price > 0:
            PlotNumeric('观察卖出价', observe_sell_price, 0xFF0000, False)
        if observe_buy_price > 0:
            PlotNumeric('观察买入价', observe_buy_price, 0x0000FF, False)

        PlotBar('订单流强度', flow_strength, 0xFF0000 if flow_strength > 0 else 0x00FF00, False, True, 1)
        PlotBar('趋势置信度', trend_confidence, 0xFF0000 if trend_confidence > 0 else 0x0000FF, False, True, 2)

    except Exception as e:
        pass

# # 策略信息
# LogInfo("Alpha-Tick Pro RB 螺纹钢高频策略 - 机智量化版优化交易封装版")
# LogInfo("使用封装交易函数，支持实时和历史阶段差异化处理")
# LogInfo("交易状态管理：BKS/SKS/BPS/SPS 避免重复开仓")
