import talib
import math
import copy
import numpy as np
import pandas as pd
from collections import deque
from xtquant.xttrader import XtQuantTrader
from xtquant import xtconstant



from typing import Union
def stock_code_mapping(code: Union[int, str]) -> str:
    # 整数处理分支（进一步优化）
    if isinstance(code, int):
        if not (1 <= code <= 999999):
            raise ValueError("输入必须为6位以下正整数")
        
        # 格式化代码字符串（只做一次）
        code_str = f"{code:06d}"
        
        # 快速分类 - 使用整数除法和模运算
        first_digit = code // 100000
        first_two = code // 10000
        first_three = code // 1000
        
        # 沪市股票 (6开头)
        if first_digit == 6:
            if first_three == 688:
                return f"SSE|T|KSHARES|{code_str}"  # 科创板
            elif first_three in {600, 601, 603, 605}:
                return f"SSE|T|ASHARES|{code_str}"      # 沪主板
            
        # 深主板 (0,1,3开头或4-9开头)
        if first_three  in {0, 1, 3}:
            return f"SZSE|T|ASHARES|{code_str}"    # 深主板            
        # 中小板 (002开头)    
        if first_three == 2:
            return f"SZSE|T|SMESHARES|{code_str}"  # 中小板
        # 创业板 (30开头)
        if first_two == 30:
            return f"SZSE|T|CHSHARES|{code_str}"   # 创业板   
        # 深B股 (200开头)
        if first_three == 200:
            return f"SZSE|T|BSHARES|{code_str}"    # 深B股
        # ETF (159开头)
        if first_three == 159:
            return f"SZSE|T|FUNDS|{code_str}"      # ETF
             
        # 基金 (5开头)
        if first_digit == 5:
            return f"SSE|T|FUNDS|{code_str}"       # 沪基金
            
        # REITs (16-18开头)
        if first_two in {16, 18}:
            return f"SZSE|T|FUNDS|{code_str}"      # REITs
            
        # 沪B股 (9开头)
        if first_digit == 9:
            return f"SSE|T|BSHARES|{code_str}"     # 沪B股
            
        # 北交所和新三板
        if first_three in {830, 831, 832, 833, 834, 835, 836, 837, 838, 839, 
                          870, 871, 872, 873, 874, 875, 876, 877, 878, 879, 
                          920, 921, 922, 923, 924, 925, 926, 927, 928, 929}:
            return f"BJSE|T|STOCK|{code_str}"
        if first_three in {400, 430, 830}:
            return f"NEEQ|T|OTC|{code_str}"
            
        return f"UNKNOWN|{code_str}"
    
    # 字符串处理分支（原逻辑）
    elif isinstance(code, str):
        if not (code.isdigit() and len(code) == 6):
            raise ValueError("输入必须为6位数字字符串")

        if code.startswith('688'):
            return f"SSE|T|KSHARES|{code}"
        elif code.startswith(('600','601','603','605')):
            return f"SSE|T|ASHARES|{code}"
        elif code.startswith('5'):
            return f"SSE|T|FUNDS|{code}"
        elif code.startswith('900'):
            return f"SSE|T|BSHARES|{code}"
        elif code.startswith('159'):
            return f"SZSE|T|FUNDS|{code}"
        elif code.startswith(('000','001','003')):
            return f"SZSE|T|ASHARES|{code}"
        elif code.startswith('002'):
            return f"SZSE|T|SMESHARES|{code}"
        elif code.startswith('30'):
            return f"SZSE|T|CHSHARES|{code}"
        elif code.startswith('200'):
            return f"SZSE|T|BSHARES|{code}"
        elif code.startswith(('16','18')):
            return f"SZSE|T|FUNDS|{code}"
        elif code.startswith(('83','87','920')):
            return f"BJSE|T|STOCK|{code}"
        elif code.startswith(('400','430','830')):
            return f"NEEQ|T|OTC|{code}"
        else:
            return f"UNKNOWN|{code}"
    
    else:
        raise TypeError("输入类型必须为int或str")
    
def stock_index_code_mapping(code: Union[int, str]) -> str:
    if isinstance(code, int):
        code_str = f"{code:06d}"
        prefix_three = code // 1000
        if prefix_three == 399:
            return f"SZSE|T|INDEX|{code_str}"
        if prefix_three == 0:
            return f"SSE|T|INDEX|{code_str}"
    elif isinstance(code, str):
        if code.startswith('399'):
            return f"SZSE|T|INDEX|{code}"
        if code.startswith('000'):
            return f"SSE|T|INDEX|{code}"

def normalize_stock_code(code):
    """
    将各种格式的股票代码转换为标准的QMT API格式
    沪市：数字代码.SH（例如：600000.SH）
    深市：数字代码.SZ（例如：000001.SZ）
    北交所：数字代码.BJ（例如：430047.BJ）
    
    参数:
        code: 字符串型股票代码，可以是多种格式（sh600000, SH600000, 600000, 600000.SH, 430047.BJ等）
    
    返回:
        标准化后的股票代码字符串
    """
    # 去除所有空格并转换为大写
    code = str(code).strip().upper()
    
    # 去除任何前缀和后缀，只保留数字部分
    numeric_part = ''.join(filter(str.isdigit, code))
    
    # 判断市场类型
    if any(prefix in code for prefix in ['SH', 'SHSE', 'SSE', 'SH.', '.SH']):
        return f"{numeric_part}.SH"
    elif any(prefix in code for prefix in ['SZ', 'SZSE', 'SZ.', '.SZ']):
        return f"{numeric_part}.SZ"
    elif any(prefix in code for prefix in ['BJ', 'BSE', 'BJSE', 'BJ.', '.BJ']):
        return f"{numeric_part}.BJ"
    else:
        # 根据股票代码规则判断市场类型
        # 沪市：以6开头（主板）、5开头（基金）、7开头（衍生品）
        # 深市：以0开头（主板）、1开头（SME）、2开头（中小板）、3开头（创业板）
        # 北交所：以4、8开头，68开头或82、83、87、88开头的股票代码
        if numeric_part.startswith('6'):
            return f"{numeric_part}.SH"
        elif numeric_part.startswith(('0', '1', '2', '3')):
            return f"{numeric_part}.SZ"
        elif numeric_part.startswith('4') or numeric_part.startswith('8'):
            # 北交所代码通常以4开头（如43开头的新三板精选层挂牌公司）
            # 或8开头（部分北交所特定代码）
            return f"{numeric_part}.BJ"
        elif numeric_part.startswith('68') or numeric_part.startswith(('82', '83', '87', '88')):
            # 特定的北交所其他代码规则
            return f"{numeric_part}.BJ"
        elif numeric_part.startswith('5') or numeric_part.startswith('7'):
            # 上交所基金和衍生品
            return f"{numeric_part}.SH"
        else:
            # 无法确定市场类型，保持原样返回
            return code


class SmartOrderManager:
    """
    智能拆单交易管理类，支持买入和卖出的自动拆单执行。
    根据市场挂单量动态调整每笔订单大小，异步跟踪订单状态直到全部完成。
    
    特点:
    - 自动读取盘口挂单量，按比例拆单
    - 分批执行，成交后自动发送后续订单
    - 支持价格偏离和时间窗口限制
    - 完整的订单生命周期跟踪
    """
    
    def __init__(self, price_limit_percent=0, time_limit_seconds=0, order_size_ratio=1/3, 
                 max_retry=3, price_step_percent=0.02, status_check_interval=1, log_type=0):
        """
        初始化智能拆单管理器
        
        参数:
            price_limit_percent: 价格偏离限制百分比，0表示不限制
            time_limit_seconds: 订单超时时间(秒)，0表示不限制
            order_size_ratio: 相对于盘口挂单量的下单比例，默认1/3
            max_retry: 单笔订单最大重试次数
            price_step_percent: 价格调整步长百分比
            status_check_interval: 订单状态检查间隔(秒)
            log_type: 日志输出类型，0为print输出(通用环境)，1为LogInfo输出(极星量化环境)
        """
        self.price_limit_percent = price_limit_percent
        self.time_limit_seconds = time_limit_seconds
        self.order_size_ratio = order_size_ratio
        self.max_retry = max_retry
        self.price_step_percent = price_step_percent
        self.status_check_interval = status_check_interval
        self.log_type = log_type
        
        # 订单跟踪相关变量
        self.active_orders = {}      # 活跃订单字典
        self.completed_orders = {}   # 已完成订单字典
        self.failed_orders = {}      # 失败订单字典
        self.order_tasks = {}        # 订单任务跟踪
        
        # 订单状态常量
        self.ORDER_SUBMITTED = "已提交"
        self.ORDER_ACCEPTED = "已接受"
        self.ORDER_FILLED = "已成交"
        self.ORDER_PARTIALLY_FILLED = "部分成交"
        self.ORDER_CANCELLED = "已撤销"
        self.ORDER_REJECTED = "已拒绝"
        self.ORDER_EXPIRED = "已过期"
        
        # 日志相关
        self.log_prefix = "[智能拆单] "
        
        # 导入必要的库
        import threading
        import time
        import datetime
        self.threading = threading
        self.time = time
        self.datetime = datetime
        
        # 创建线程锁
        self.lock = threading.Lock()
        
        # 启动订单状态监控线程
        self.stop_monitor = False
        self.monitor_thread = threading.Thread(target=self._monitor_orders)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        self._log(f"{self.log_prefix}智能拆单交易管理器已初始化")
    
    def _log(self, message):
        """
        根据日志类型输出日志
        
        参数:
            message: 日志信息
        """
        if self.log_type == 0:
            # 使用print输出日志(通用环境)
            print(message)
        else:
            # 使用LogInfo输出日志(极星量化环境)
            try:
                LogInfo(message)
            except:
                # 如果LogInfo未定义，回退到print
                print(message)
    
    def _get_upper_limit(self, symbol):
        """
        获取涨停价
        
        参数:
            symbol: 股票代码
            
        返回:
            涨停价，获取失败返回0
        """
        try:
            if self.log_type == 0:
                # 通用环境使用QMT的API
                from xtquant import xtdata
                # 获取涨停价
                tick_data = xtdata.get_full_tick([symbol])
                if symbol in tick_data and "upperLimit" in tick_data[symbol]:
                    return tick_data[symbol]["upperLimit"]
                return 0
            else:
                # 极星平台环境使用平台API
                return Q_UpperLimit(symbol)
        except Exception as e:
            self._log(f"{self.log_prefix}获取 {symbol} 涨停价异常: {str(e)}")
            return 0
    
    def _get_lower_limit(self, symbol):
        """
        获取跌停价
        
        参数:
            symbol: 股票代码
            
        返回:
            跌停价，获取失败返回0
        """
        try:
            if self.log_type == 0:
                # 通用环境使用QMT的API
                from xtquant import xtdata
                # 获取跌停价
                tick_data = xtdata.get_full_tick([symbol])
                if symbol in tick_data and "lowerLimit" in tick_data[symbol]:
                    return tick_data[symbol]["lowerLimit"]
                return 0
            else:
                # 极星平台环境使用平台API
                return Q_LowLimit(symbol)
        except Exception as e:
            self._log(f"{self.log_prefix}获取 {symbol} 跌停价异常: {str(e)}")
            return 0
    
    def _get_last_price(self, symbol):
        """
        获取最新价
        
        参数:
            symbol: 股票代码
            
        返回:
            最新价，获取失败返回0
        """
        try:
            if self.log_type == 0:
                # 通用环境使用QMT的API
                from xtquant import xtdata
                # 获取最新价
                tick_data = xtdata.get_full_tick([symbol])
                if symbol in tick_data and "lastPrice" in tick_data[symbol]:
                    return tick_data[symbol]["lastPrice"]
                return 0
            else:
                # 极星平台环境使用平台API
                return Q_Last(symbol)
        except Exception as e:
            self._log(f"{self.log_prefix}获取 {symbol} 最新价异常: {str(e)}")
            return 0
    
    def _get_ask1_info(self, symbol):
        """
        获取卖一档价格和量
        
        参数:
            symbol: 股票代码
            
        返回:
            (卖一价, 卖一量)元组，获取失败返回(0, 0)
        """
        try:
            if self.log_type == 0:
                # 通用环境使用QMT的API
                from xtquant import xtdata
                # 获取卖一价和卖一量
                tick_data = xtdata.get_full_tick([symbol])
                if symbol in tick_data:
                    ask1_price = tick_data[symbol]["askPrice"][0] if "askPrice" in tick_data[symbol] and len(tick_data[symbol]["askPrice"]) > 0 else 0
                    ask1_volume = tick_data[symbol]["askVol"][0] if "askVol" in tick_data[symbol] and len(tick_data[symbol]["askVol"]) > 0 else 0
                    return ask1_price, ask1_volume
                return 0, 0
            else:
                # 极星平台环境使用平台API
                ask1_price = Q_AskPrice(symbol, 0)  # 卖一价
                ask1_volume = Q_AskVolume(symbol, 0)  # 卖一量
                return ask1_price, ask1_volume
        except Exception as e:
            self._log(f"{self.log_prefix}获取 {symbol} 卖一档信息异常: {str(e)}")
            return 0, 0
    
    def _get_bid1_info(self, symbol):
        """
        获取买一档价格和量
        
        参数:
            symbol: 股票代码
            
        返回:
            (买一价, 买一量)元组，获取失败返回(0, 0)
        """
        try:
            if self.log_type == 0:
                # 通用环境使用QMT的API
                from xtquant import xtdata
                # 获取买一价和买一量
                tick_data = xtdata.get_full_tick([symbol])
                if symbol in tick_data:
                    bid1_price = tick_data[symbol]["bidPrice"][0] if "bidPrice" in tick_data[symbol] and len(tick_data[symbol]["bidPrice"]) > 0 else 0
                    bid1_volume = tick_data[symbol]["bidVol"][0] if "bidVol" in tick_data[symbol] and len(tick_data[symbol]["bidVol"]) > 0 else 0
                    return bid1_price, bid1_volume
                return 0, 0
            else:
                # 极星平台环境使用平台API
                bid1_price = Q_BidPrice(symbol, 0)  # 买一价
                bid1_volume = Q_BidVolume(symbol, 0)  # 买一量
                return bid1_price, bid1_volume
        except Exception as e:
            self._log(f"{self.log_prefix}获取 {symbol} 买一档信息异常: {str(e)}")
            return 0, 0
    
    def place_buy_order(self, symbol, total_quantity, limit_price=None, 
                        min_quantity=100, callback=None, **kwargs):
        """
        执行买入订单，自动拆分为多笔小订单
        
        参数:
            symbol: 股票代码
            total_quantity: 总买入数量
            limit_price: 限价，None表示市价
            min_quantity: 最小下单数量，低于此数量将直接一次性下单
            callback: 订单完成后的回调函数
            **kwargs: 其他参数传递给下单API
            
        返回:
            订单任务ID
        """
        # 标准化股票代码
        symbol = normalize_stock_code(symbol)
        
        # 创建订单任务ID
        task_id = f"BUY_{symbol}_{self.datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')}"
        
        # 获取当前市场价格和涨停价
        current_price = limit_price if limit_price else self._get_last_price(symbol)
        upper_limit = self._get_upper_limit(symbol)
        
        # 如果指定了限价并超过涨停，提前调整限价
        if limit_price and upper_limit > 0 and limit_price > upper_limit:
            self._log(f"{self.log_prefix}指定买入价格 {limit_price} 超过涨停价 {upper_limit}，自动调整为涨停价")
            limit_price = upper_limit
        
        # 计算价格限制
        max_price = current_price * (1 + self.price_limit_percent/100) if self.price_limit_percent else float('inf')
        
        # 计算时间限制
        end_time = self.time.time() + self.time_limit_seconds if self.time_limit_seconds else float('inf')
        
        # 创建任务数据结构
        task_data = {
            "task_id": task_id,
            "symbol": symbol,
            "action": "BUY",
            "total_quantity": total_quantity,
            "remaining_quantity": total_quantity,
            "filled_quantity": 0,
            "start_price": current_price,
            "limit_price": limit_price,
            "max_price": max_price,
            "end_time": end_time,
            "min_quantity": min_quantity,
            "status": "ACTIVE",
            "start_time": self.time.time(),
            "orders": [],
            "callback": callback,
            "kwargs": kwargs
        }
        
        # 添加到任务列表
        with self.lock:
            self.order_tasks[task_id] = task_data
        
        # 启动执行线程
        execution_thread = self.threading.Thread(
            target=self._execute_buy_task,
            args=(task_id,)
        )
        execution_thread.daemon = True
        execution_thread.start()
        
        self._log(f"{self.log_prefix}已创建买入任务 {task_id}，总数量: {total_quantity}，初始价格: {current_price}")
        return task_id
    
    def place_sell_order(self, symbol, total_quantity, limit_price=None, 
                         min_quantity=100, callback=None, **kwargs):
        """
        执行卖出订单，自动拆分为多笔小订单
        
        参数:
            symbol: 股票代码
            total_quantity: 总卖出数量
            limit_price: 限价，None表示以市价卖出
            min_quantity: 最小下单数量，低于此数量将直接一次性下单
            callback: 订单完成后的回调函数
            **kwargs: 其他参数传递给下单API
            
        返回:
            订单任务ID
        """
        # 标准化股票代码
        symbol = normalize_stock_code(symbol)
        
        # 创建订单任务ID
        task_id = f"SELL_{symbol}_{self.datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')}"
        
        # 获取当前市场价格和跌停价
        current_price = limit_price if limit_price else self._get_last_price(symbol)
        lower_limit = self._get_lower_limit(symbol)
        
        # 如果指定了限价并低于跌停，提前调整限价
        if limit_price and lower_limit > 0 and limit_price < lower_limit:
            self._log(f"{self.log_prefix}指定卖出价格 {limit_price} 低于跌停价 {lower_limit}，自动调整为跌停价")
            limit_price = lower_limit
        
        # 计算价格限制
        min_price = current_price * (1 - self.price_limit_percent/100) if self.price_limit_percent else float('-inf')
        
        # 计算时间限制
        end_time = self.time.time() + self.time_limit_seconds if self.time_limit_seconds else float('inf')
        
        # 创建任务数据结构
        task_data = {
            "task_id": task_id,
            "symbol": symbol,
            "action": "SELL",
            "total_quantity": total_quantity,
            "remaining_quantity": total_quantity,
            "filled_quantity": 0,
            "start_price": current_price,
            "limit_price": limit_price,
            "min_price": min_price,
            "end_time": end_time,
            "min_quantity": min_quantity,
            "status": "ACTIVE",
            "start_time": self.time.time(),
            "orders": [],
            "callback": callback,
            "kwargs": kwargs
        }
        
        # 添加到任务列表
        with self.lock:
            self.order_tasks[task_id] = task_data
        
        # 启动执行线程
        execution_thread = self.threading.Thread(
            target=self._execute_sell_task,
            args=(task_id,)
        )
        execution_thread.daemon = True
        execution_thread.start()
        
        self._log(f"{self.log_prefix}已创建卖出任务 {task_id}，总数量: {total_quantity}，初始价格: {current_price}")
        return task_id
    
    def _execute_buy_task(self, task_id):
        """执行买入任务的内部方法"""
        task = self.order_tasks.get(task_id)
        if not task:
            self._log(f"{self.log_prefix}找不到任务 {task_id}")
            return
        
        # 导入随机数模块
        import random
        
        symbol = task["symbol"]
        retry_count = 0
        
        while (task["remaining_quantity"] > 0 and 
               task["status"] == "ACTIVE" and 
               self.time.time() < task["end_time"] and
               retry_count < self.max_retry):
            
            # 检查价格是否超出限制
            current_price = self._get_last_price(symbol)
            if current_price > task["max_price"]:
                self._log(f"{self.log_prefix}任务 {task_id} 价格 {current_price} 超出限制 {task['max_price']}，停止执行")
                with self.lock:
                    task["status"] = "PRICE_LIMIT_EXCEEDED"
                break
            
            # 获取涨停价格
            upper_limit = self._get_upper_limit(symbol)
            if upper_limit <= 0:
                self._log(f"{self.log_prefix}无法获取 {symbol} 的涨停价，使用当前价格")
                upper_limit = None
            
            # 获取卖一档挂单量
            ask1_price, ask1_volume = self._get_ask1_info(symbol)
            if not ask1_volume or ask1_volume <= 0:
                self._log(f"{self.log_prefix}无法获取 {symbol} 的卖一档信息，等待重试")
                self.time.sleep(self.status_check_interval)
                retry_count += 1
                continue
            
            # 使用更复杂的随机因子，让下单量在50%-100%之间随机浮动
            random_factor = random.uniform(0.5, 1.0)
            
            # 也随机调整订单比例参数，使每次比例不同
            order_ratio_factor = random.uniform(0.8, 1.2) * self.order_size_ratio
            
            # 计算本次下单量，使用随机因子调整
            base_size = int(ask1_volume * order_ratio_factor)
            batch_size = min(
                int(base_size * random_factor),  # 加入随机因素的卖一档挂单量的比例
                task["remaining_quantity"]       # 剩余需要买入的数量
            )
            
            # 随机调整批量，增加随机扰动
            batch_random_adjustment = random.choice([0, 100, 200, 300, 400, 500])
            batch_size = (batch_size // 100) * 100 + batch_random_adjustment
            batch_size = min(batch_size, task["remaining_quantity"])
            
            # 随机调整最小下单量，在80%-120%之间浮动
            min_qty_factor = random.uniform(0.8, 1.2)
            adjusted_min_qty = int(task["min_quantity"] * min_qty_factor)
            adjusted_min_qty = (adjusted_min_qty // 100) * 100  # 确保是100的整数倍
            if adjusted_min_qty < 100:
                adjusted_min_qty = 100
            
            # 确保批量符合最小交易单位(通常是100股)
            batch_size = (batch_size // 100) * 100
            if batch_size < adjusted_min_qty:
                batch_size = min(adjusted_min_qty, task["remaining_quantity"])
            
            # 如果剩余很少，一次性买入
            if task["remaining_quantity"] < adjusted_min_qty:
                batch_size = task["remaining_quantity"]
            
            # 如果批量合法，执行买入
            if batch_size > 0:
                # 计算下单价格（限价或卖一价），同时确保不超过涨停价
                if task["limit_price"]:
                    order_price = task["limit_price"]
                else:
                    order_price = ask1_price
                    
                # 检查是否超过涨停价
                if upper_limit and order_price > upper_limit:
                    self._log(f"{self.log_prefix}买入价格 {order_price} 超过涨停价 {upper_limit}，已调整为涨停价")
                    order_price = upper_limit
                
                # 调用API买入
                try:
                    self._log(f"{self.log_prefix}任务 {task_id} 准备买入 {batch_size} 股 {symbol}，价格 {order_price}")
                    # 使用QMT的API执行买入
                    order_id = self._place_order(
                        symbol=symbol,
                        direction="BUY",
                        quantity=batch_size,
                        price=order_price,
                        **task["kwargs"]
                    )
                    
                    if order_id:
                        self._log(f"{self.log_prefix}任务 {task_id} 发送买入订单 {order_id}，数量 {batch_size}，价格 {order_price}")
                        with self.lock:
                            task["orders"].append({
                                "order_id": order_id,
                                "quantity": batch_size,
                                "price": order_price,
                                "status": "SUBMITTED",
                                "filled_quantity": 0,
                                "submit_time": self.time.time()
                            })
                            self.active_orders[order_id] = {
                                "task_id": task_id,
                                "symbol": symbol,
                                "action": "BUY",
                                "quantity": batch_size,
                                "price": order_price,
                                "status": "SUBMITTED"
                            }
                        
                        # 随机等待时间，2-5秒不等
                        wait_time = random.uniform(2, 5) * self.status_check_interval
                        self.time.sleep(wait_time)
                    else:
                        self._log(f"{self.log_prefix}任务 {task_id} 买入订单发送失败")
                        retry_count += 1
                except Exception as e:
                    self._log(f"{self.log_prefix}任务 {task_id} 买入异常: {str(e)}")
                    retry_count += 1
            
            # 检查任务状态和剩余数量
            with self.lock:
                task = self.order_tasks.get(task_id)
                if not task or task["status"] != "ACTIVE" or task["remaining_quantity"] <= 0:
                    break
            
            # 随机设置下一次尝试的等待时间，0.5-2倍的标准间隔
            wait_factor = random.uniform(0.5, 2.0)
            self.time.sleep(self.status_check_interval * wait_factor)
        
        # 最终检查任务状态
        with self.lock:
            task = self.order_tasks.get(task_id)
            if task and task["status"] == "ACTIVE":
                if task["remaining_quantity"] <= 0:
                    task["status"] = "COMPLETED"
                    self._log(f"{self.log_prefix}任务 {task_id} 已完成，总成交: {task['filled_quantity']}")
                elif self.time.time() >= task["end_time"]:
                    task["status"] = "TIMEOUT"
                    self._log(f"{self.log_prefix}任务 {task_id} 超时，剩余: {task['remaining_quantity']}")
                else:
                    task["status"] = "FAILED"
                    self._log(f"{self.log_prefix}任务 {task_id} 失败，剩余: {task['remaining_quantity']}")
                
                # 执行回调
                if task["callback"]:
                    try:
                        task["callback"](task_id, task)
                    except Exception as e:
                        self._log(f"{self.log_prefix}任务 {task_id} 回调异常: {str(e)}")
    
    def _execute_sell_task(self, task_id):
        """执行卖出任务的内部方法"""
        task = self.order_tasks.get(task_id)
        if not task:
            self._log(f"{self.log_prefix}找不到任务 {task_id}")
            return
        
        # 导入随机数模块
        import random
        
        symbol = task["symbol"]
        retry_count = 0
        
        while (task["remaining_quantity"] > 0 and 
               task["status"] == "ACTIVE" and 
               self.time.time() < task["end_time"] and
               retry_count < self.max_retry):
            
            # 检查价格是否超出限制
            current_price = self._get_last_price(symbol)
            if current_price < task["min_price"]:
                self._log(f"{self.log_prefix}任务 {task_id} 价格 {current_price} 低于限制 {task['min_price']}，停止执行")
                with self.lock:
                    task["status"] = "PRICE_LIMIT_EXCEEDED"
                break
            
            # 获取跌停价格
            lower_limit = self._get_lower_limit(symbol)
            if lower_limit <= 0:
                self._log(f"{self.log_prefix}无法获取 {symbol} 的跌停价，使用当前价格")
                lower_limit = None
            
            # 获取买一档挂单量
            bid1_price, bid1_volume = self._get_bid1_info(symbol)
            if not bid1_volume or bid1_volume <= 0:
                self._log(f"{self.log_prefix}无法获取 {symbol} 的买一档信息，等待重试")
                self.time.sleep(self.status_check_interval)
                retry_count += 1
                continue
            
            # 使用更复杂的随机因子，让下单量在50%-100%之间随机浮动
            random_factor = random.uniform(0.5, 1.0)
            
            # 也随机调整订单比例参数，使每次比例不同
            order_ratio_factor = random.uniform(0.8, 1.2) * self.order_size_ratio
            
            # 计算本次下单量，使用随机因子调整
            base_size = int(bid1_volume * order_ratio_factor)
            batch_size = min(
                int(base_size * random_factor),  # 加入随机因素的买一档挂单量的比例
                task["remaining_quantity"]       # 剩余需要卖出的数量
            )
            
            # 随机调整批量，增加随机扰动
            batch_random_adjustment = random.choice([0, 100, 200, 300, 400, 500])
            batch_size = (batch_size // 100) * 100 + batch_random_adjustment
            batch_size = min(batch_size, task["remaining_quantity"])
            
            # 随机调整最小下单量，在80%-120%之间浮动
            min_qty_factor = random.uniform(0.8, 1.2)
            adjusted_min_qty = int(task["min_quantity"] * min_qty_factor)
            adjusted_min_qty = (adjusted_min_qty // 100) * 100  # 确保是100的整数倍
            if adjusted_min_qty < 100:
                adjusted_min_qty = 100
            
            # 确保批量符合最小交易单位(通常是100股)
            batch_size = (batch_size // 100) * 100
            if batch_size < adjusted_min_qty:
                batch_size = min(adjusted_min_qty, task["remaining_quantity"])
            
            # 如果剩余很少，一次性卖出
            if task["remaining_quantity"] < adjusted_min_qty:
                batch_size = task["remaining_quantity"]
            
            # 如果批量合法，执行卖出
            if batch_size > 0:
                # 计算下单价格（限价或买一价），同时确保不低于跌停价
                if task["limit_price"]:
                    order_price = task["limit_price"]
                else:
                    order_price = bid1_price
                    
                # 检查是否低于跌停价
                if lower_limit and order_price < lower_limit:
                    self._log(f"{self.log_prefix}卖出价格 {order_price} 低于跌停价 {lower_limit}，已调整为跌停价")
                    order_price = lower_limit
                
                # 调用API卖出
                try:
                    self._log(f"{self.log_prefix}任务 {task_id} 准备卖出 {batch_size} 股 {symbol}，价格 {order_price}")
                    # 使用QMT的API执行卖出
                    order_id = self._place_order(
                        symbol=symbol,
                        direction="SELL",
                        quantity=batch_size,
                        price=order_price,
                        **task["kwargs"]
                    )
                    
                    if order_id:
                        self._log(f"{self.log_prefix}任务 {task_id} 发送卖出订单 {order_id}，数量 {batch_size}，价格 {order_price}")
                        with self.lock:
                            task["orders"].append({
                                "order_id": order_id,
                                "quantity": batch_size,
                                "price": order_price,
                                "status": "SUBMITTED",
                                "filled_quantity": 0,
                                "submit_time": self.time.time()
                            })
                            self.active_orders[order_id] = {
                                "task_id": task_id,
                                "symbol": symbol,
                                "action": "SELL",
                                "quantity": batch_size,
                                "price": order_price,
                                "status": "SUBMITTED"
                            }
                        
                        # 随机等待时间，2-5秒不等
                        wait_time = random.uniform(2, 5) * self.status_check_interval
                        self.time.sleep(wait_time)
                    else:
                        self._log(f"{self.log_prefix}任务 {task_id} 卖出订单发送失败")
                        retry_count += 1
                except Exception as e:
                    self._log(f"{self.log_prefix}任务 {task_id} 卖出异常: {str(e)}")
                    retry_count += 1
            
            # 检查任务状态和剩余数量
            with self.lock:
                task = self.order_tasks.get(task_id)
                if not task or task["status"] != "ACTIVE" or task["remaining_quantity"] <= 0:
                    break
            
            # 随机设置下一次尝试的等待时间，0.5-2倍的标准间隔
            wait_factor = random.uniform(0.5, 2.0)
            self.time.sleep(self.status_check_interval * wait_factor)
        
        # 最终检查任务状态
        with self.lock:
            task = self.order_tasks.get(task_id)
            if task and task["status"] == "ACTIVE":
                if task["remaining_quantity"] <= 0:
                    task["status"] = "COMPLETED"
                    self._log(f"{self.log_prefix}任务 {task_id} 已完成，总成交: {task['filled_quantity']}")
                elif self.time.time() >= task["end_time"]:
                    task["status"] = "TIMEOUT"
                    self._log(f"{self.log_prefix}任务 {task_id} 超时，剩余: {task['remaining_quantity']}")
                else:
                    task["status"] = "FAILED"
                    self._log(f"{self.log_prefix}任务 {task_id} 失败，剩余: {task['remaining_quantity']}")
                
                # 执行回调
                if task["callback"]:
                    try:
                        task["callback"](task_id, task)
                    except Exception as e:
                        self._log(f"{self.log_prefix}任务 {task_id} 回调异常: {str(e)}")
    
    def _place_order(self, symbol, direction, quantity, price, **kwargs):
        """
        实际下单操作，调用QMT API
        
        返回订单ID或None
        """
        try:
            # 使用全局的交易连接管理器
            global g_trade_connection
            
            if not g_trade_connection:
                self._log(f"{self.log_prefix}交易连接未初始化，无法下单")
                return None
                
            if direction == "BUY":
                order_id = g_trade_connection.order_stock(
                    symbol=symbol,
                    direction=xtconstant.STOCK_BUY, 
                    quantity=quantity,
                    price_type=xtconstant.FIX_PRICE,
                    price=price, 
                    strategy_name='智能拆单系统',
                    remark='买入'
                )
            else:  # SELL
                order_id = g_trade_connection.order_stock(
                    symbol=symbol,
                    direction=xtconstant.STOCK_SELL, 
                    quantity=quantity,
                    price_type=xtconstant.FIX_PRICE,
                    price=price, 
                    strategy_name='智能拆单系统',
                    remark='卖出'
                )
            
            return order_id
        except Exception as e:
            self._log(f"{self.log_prefix}下单异常: {str(e)}")
            return None
    
    def _monitor_orders(self):
        """
        订单状态监控线程
        持续监控活跃订单的状态变化
        """
        while not self.stop_monitor:
            try:
                # 复制活跃订单列表，避免遍历时修改
                with self.lock:
                    active_orders = list(self.active_orders.items())
                
                for order_id, order_info in active_orders:
                    try:
                        # 获取订单状态
                        order_status = self._get_order_status(order_id)
                        
                        if not order_status:
                            continue
                            
                        task_id = order_info["task_id"]
                        
                        # 更新订单状态
                        with self.lock:
                            if task_id in self.order_tasks and order_id in self.active_orders:
                                task = self.order_tasks[task_id]
                                
                                # 找到对应的订单记录
                                for order in task["orders"]:
                                    if order["order_id"] == order_id:
                                        order["status"] = order_status["status"]
                                        order["filled_quantity"] = order_status["filled_quantity"]
                                        
                                        # 如果订单状态改变
                                        if order_status["status"] != self.active_orders[order_id]["status"]:
                                            self.active_orders[order_id]["status"] = order_status["status"]
                                            
                                            # 处理已成交或部分成交
                                            if order_status["status"] in [self.ORDER_FILLED, self.ORDER_PARTIALLY_FILLED]:
                                                filled_qty = order_status["filled_quantity"] - order.get("reported_filled", 0)
                                                if filled_qty > 0:
                                                    order["reported_filled"] = order_status["filled_quantity"]
                                                    task["filled_quantity"] += filled_qty
                                                    task["remaining_quantity"] -= filled_qty
                                                    self._log(f"{self.log_prefix}任务 {task_id} 订单 {order_id} 成交 {filled_qty} 股，剩余 {task['remaining_quantity']} 股")
                                            
                                            # 处理已完成(成交/撤销/拒绝/过期)
                                            if order_status["status"] in [self.ORDER_FILLED, self.ORDER_CANCELLED, self.ORDER_REJECTED, self.ORDER_EXPIRED]:
                                                # 从活跃订单中移除
                                                self.completed_orders[order_id] = self.active_orders.pop(order_id)
                                                self.completed_orders[order_id]["completion_time"] = self.time.time()
                                                self._log(f"{self.log_prefix}订单 {order_id} 已完成，状态: {order_status['status']}")
                                        
                                        break
                    except Exception as e:
                        self._log(f"{self.log_prefix}监控订单 {order_id} 异常: {str(e)}")
            
            except Exception as e:
                self._log(f"{self.log_prefix}订单监控线程异常: {str(e)}")
            
            self.time.sleep(self.status_check_interval)
    
    def _get_order_status(self, order_id):
        """
        获取订单状态
        
        返回订单状态字典或None
        """
        try:
            # 使用全局的交易连接管理器查询订单状态
            global g_trade_connection
            
            if not g_trade_connection:
                self._log(f"{self.log_prefix}交易连接未初始化，无法查询订单状态")
                return None
                
            order_info = g_trade_connection.query_order(order_id)
            if not order_info:
                return None
            
            # 解析订单状态 - 对照xtconstant订单状态常量
            # 直接使用数字代替常量，避免属性不存在的问题
            # 0: 未报, 1: 待报, 2: 已报, 3: 已报待撤, 4: 部成待撤, 5: 部撤, 6: 已撤, 7: 已成, 8: 废单
            status_map = {
                0: self.ORDER_SUBMITTED,        # 未报
                1: self.ORDER_SUBMITTED,        # 待报
                2: self.ORDER_ACCEPTED,         # 已报
                3: self.ORDER_CANCELLED,        # 已报待撤
                4: self.ORDER_PARTIALLY_FILLED, # 部成待撤
                5: self.ORDER_PARTIALLY_FILLED, # 部撤
                6: self.ORDER_CANCELLED,        # 已撤
                7: self.ORDER_FILLED,           # 已成
                8: self.ORDER_REJECTED,         # 废单
            }
            
            # 根据提供的订单对象属性列表使用正确的属性名
            order_status = order_info.order_status if hasattr(order_info, 'order_status') else order_info.m_nOrderStatus
            status = status_map.get(order_status, "UNKNOWN")
            
            # 使用正确的成交数量属性
            filled_quantity = 0
            if hasattr(order_info, 'traded_volume'):
                filled_quantity = order_info.traded_volume
            elif hasattr(order_info, 'm_nTradedVolume'):
                filled_quantity = order_info.m_nTradedVolume
            
            # 使用正确的成交均价属性
            avg_price = 0
            if hasattr(order_info, 'traded_price'):
                avg_price = order_info.traded_price
            elif hasattr(order_info, 'm_dTradedPrice'):
                avg_price = order_info.m_dTradedPrice
            
            # 使用正确的订单时间属性
            order_time = ""
            if hasattr(order_info, 'order_time'):
                order_time = order_info.order_time
            elif hasattr(order_info, 'm_nOrderTime'):
                order_time = order_info.m_nOrderTime
            
            return {
                "status": status,
                "filled_quantity": filled_quantity,
                "avg_price": avg_price,
                "order_time": order_time
            }
        except Exception as e:
            self._log(f"{self.log_prefix}获取订单 {order_id} 状态异常: {str(e)}")
            return None
    
    def cancel_order(self, order_id):
        """
        取消指定订单
        
        参数:
            order_id: 订单ID
            
        返回:
            是否成功
        """
        try:
            global g_trade_connection
            
            if not g_trade_connection:
                self._log(f"{self.log_prefix}交易连接未初始化，无法撤单")
                return False
                
            success = g_trade_connection.cancel_order(order_id)
            if success:
                self._log(f"{self.log_prefix}订单 {order_id} 已发送撤单请求")
            else:
                self._log(f"{self.log_prefix}订单 {order_id} 撤单请求失败")
            return success
        except Exception as e:
            self._log(f"{self.log_prefix}撤销订单 {order_id} 异常: {str(e)}")
            return False
    
    def cancel_task(self, task_id):
        """
        取消整个任务
        
        参数:
            task_id: 任务ID
            
        返回:
            是否成功
        """
        try:
            with self.lock:
                if task_id not in self.order_tasks:
                    self._log(f"{self.log_prefix}找不到任务 {task_id}")
                    return False
                
                task = self.order_tasks[task_id]
                if task["status"] != "ACTIVE":
                    self._log(f"{self.log_prefix}任务 {task_id} 已不是活跃状态，当前状态: {task['status']}")
                    return False
                
                task["status"] = "CANCELLING"
            
            # 取消所有活跃订单
            cancel_success = True
            for order in task["orders"]:
                order_id = order["order_id"]
                if order_id in self.active_orders:
                    if not self.cancel_order(order_id):
                        cancel_success = False
            
            with self.lock:
                task = self.order_tasks[task_id]
                task["status"] = "CANCELLED"
            
            self._log(f"{self.log_prefix}任务 {task_id} 已取消，成交量: {task['filled_quantity']}, 剩余: {task['remaining_quantity']}")
            return cancel_success
        except Exception as e:
            self._log(f"{self.log_prefix}取消任务 {task_id} 异常: {str(e)}")
            return False
    
    def get_task_status(self, task_id):
        """
        获取任务状态
        
        参数:
            task_id: 任务ID
            
        返回:
            任务状态信息或None
        """
        with self.lock:
            return self.order_tasks.get(task_id, None)
    
    def get_all_tasks(self):
        """
        获取所有任务信息
        
        返回:
            任务字典的副本
        """
        with self.lock:
            return dict(self.order_tasks)
    
    def shutdown(self):
        """关闭管理器"""
        self._log(f"{self.log_prefix}正在关闭智能拆单交易管理器...")
        self.stop_monitor = True
        if hasattr(self, 'monitor_thread') and self.monitor_thread.is_alive():
            self.monitor_thread.join(5)  # 等待最多5秒
        self._log(f"{self.log_prefix}智能拆单交易管理器已关闭")

# 在策略关闭函数中添加清理代码
def on_strategy_stop():
    global g_order_manager, g_active_orders
    
    # 取消所有活跃订单
    for symbol, task_id in g_active_orders.items():
        if hasattr(g_order_manager, '_log'):
            g_order_manager._log(f"{g_order_manager.log_prefix}策略停止，取消 {symbol} 的订单 {task_id}")
        else:
            try:
                LogInfo(f"策略停止，取消 {symbol} 的订单 {task_id}")
            except:
                print(f"策略停止，取消 {symbol} 的订单 {task_id}")
        g_order_manager.cancel_task(task_id)
    
    # 关闭智能拆单管理器
    if g_order_manager:
        g_order_manager.shutdown()
        if hasattr(g_order_manager, '_log'):
            g_order_manager._log(f"{g_order_manager.log_prefix}智能拆单管理器已关闭")
        else:
            try:
                LogInfo("智能拆单管理器已关闭")
            except:
                print("智能拆单管理器已关闭")


class TradeConnectionManager:
    """
    交易连接管理类 - 专门负责交易连接和订单处理
    
    功能：
    1. 管理交易账户连接
    2. 处理交易连接中断与重连
    3. 提供下单接口
    """
    def __init__(self, client_path, session_id=None, account_id=None, log_type=0):
        """
        初始化交易连接管理器
        
        参数:
            client_path: 客户端路径，如"D:\\国金QMT交易端模拟\\userdata_mini"
            session_id: 会话编号，如果为None则自动生成
            account_id: 交易账号，如果为None则需要后续设置
            log_type: 日志输出类型，0为print输出(通用环境)，1为LogInfo输出(极星量化环境)
        """
        # 导入必要的库
        import threading
        import time
        import datetime
        from xtquant import xttrader, xtconstant, xttype
        
        self.threading = threading
        self.time = time
        self.datetime = datetime
        self.xttrader = xttrader
        self.xtconstant = xtconstant
        self.xttype = xttype  # 直接导入xttype模块
        
        # 日志类型
        self.log_type = log_type
        
        # 设置会话ID（如果未提供则生成）
        if session_id is None:
            # 使用当前时间戳作为会话ID
            session_id = int(datetime.datetime.now().timestamp())
        
        # 交易连接参数
        self.client_path = client_path
        self.session_id = session_id
        self.account_id = account_id
        
        # 交易对象
        self.trader = None
        self.account = None
        
        # 连接状态
        self.connected = False
        self.last_heartbeat = 0
        self.heartbeat_timeout = 30   # 心跳超时秒数
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 3  # 最大重连次数
        
        # 日志相关
        self.log_prefix = "[交易] "
        
        # 创建线程锁
        self.lock = threading.Lock()
        
        # 初始化交易对象
        self._init_trader()
        
        # 启动连接监控线程
        self.stop_monitor = False
        self.monitor_thread = threading.Thread(target=self._monitor_connection)
        self.monitor_thread.daemon = True
        
        self._log(f"{self.log_prefix}交易连接管理器初始化完成")
    
    def _log(self, message):
        """
        根据日志类型输出日志
        
        参数:
            message: 日志信息
        """
        if self.log_type == 0:
            # 使用print输出日志(通用环境)
            print(message)
        else:
            # 使用LogInfo输出日志(极星量化环境)
            try:
                LogInfo(message)
            except:
                # 如果LogInfo未定义，回退到print
                print(message)
    
    def _init_trader(self):
        """初始化交易对象"""
        try:
            # 创建XtQuantTrader实例
            self.trader = self.xttrader.XtQuantTrader(self.client_path, self.session_id)
            
            # 如果已提供账号，则创建账号对象
            if self.account_id:
                # 直接使用xttype模块
                self.account = self.xttype.StockAccount(self.account_id)
                
            self._log(f"{self.log_prefix}交易对象初始化完成")
        except Exception as e:
            self._log(f"{self.log_prefix}交易对象初始化异常: {str(e)}")
    
    def set_account(self, account_id, account_type="STOCK"):
        """
        设置交易账号
        
        参数:
            account_id: 交易账号
            account_type: 账号类型，默认为STOCK，可选项有STOCK, CREDIT, FUTURE等
        """
        try:
            self.account_id = account_id
            
            # 根据账号类型创建不同的账号对象
            if account_type == "CREDIT":
                self.account = self.xttype.StockAccount(account_id, self.xtconstant.CREDIT_ACCOUNT)
            elif account_type == "FUTURE":
                self.account = self.xttype.FutureAccount(account_id)
            else:  # 默认为股票账户
                self.account = self.xttype.StockAccount(account_id)
                
            self._log(f"{self.log_prefix}账号设置成功: {account_id}, 类型: {account_type}")
            return True
        except Exception as e:
            self._log(f"{self.log_prefix}账号设置异常: {str(e)}")
            return False
    
    def register_callback(self, callback_obj):
        """
        注册交易回调对象
        
        参数:
            callback_obj: 实现了XtQuantTraderCallback接口的回调对象
        """
        if not self.trader:
            self._log(f"{self.log_prefix}交易对象未初始化，无法注册回调")
            return False
            
        try:
            self.trader.register_callback(callback_obj)
            self._log(f"{self.log_prefix}交易回调注册成功")
            return True
        except Exception as e:
            self._log(f"{self.log_prefix}交易回调注册异常: {str(e)}")
            return False
    
    def start(self):
        """启动交易线程"""
        if not self.trader:
            self._log(f"{self.log_prefix}交易对象未初始化，无法启动")
            return False
            
        try:
            self.trader.start()
            
            # 启动监控线程
            if not self.monitor_thread.is_alive():
                self.monitor_thread.start()
                
            self._log(f"{self.log_prefix}交易线程启动成功")
            return True
        except Exception as e:
            self._log(f"{self.log_prefix}交易线程启动异常: {str(e)}")
            return False
    
    def connect(self):
        """建立交易连接"""
        if not self.trader:
            self._log(f"{self.log_prefix}交易对象未初始化，无法连接")
            return False
            
        try:
            # 连接交易服务器
            result = self.trader.connect()
            
            if result == 0:
                self.connected = True
                self.last_heartbeat = self.time.time()
                self.reconnect_attempts = 0
                self._log(f"{self.log_prefix}交易服务器连接成功")
                return True
            else:
                self.connected = False
                self._log(f"{self.log_prefix}交易服务器连接失败，错误码: {result}")
                return False
        except Exception as e:
            self.connected = False
            self._log(f"{self.log_prefix}交易服务器连接异常: {str(e)}")
            return False
    
    def subscribe(self):
        """订阅交易主推"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法订阅")
            return False
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法订阅")
            return False
            
        try:
            # 订阅交易主推
            result = self.trader.subscribe(self.account)
            
            if result == 0:
                self._log(f"{self.log_prefix}交易主推订阅成功")
                return True
            else:
                self._log(f"{self.log_prefix}交易主推订阅失败，错误码: {result}")
                return False
        except Exception as e:
            self._log(f"{self.log_prefix}交易主推订阅异常: {str(e)}")
            return False
    
    def check_connection_health(self):
        """检查连接健康状态"""
        if not self.connected:
            return False
        
        try:
            # 检查心跳
            current_time = self.time.time()
            if current_time - self.last_heartbeat > self.heartbeat_timeout:
                self._log(f"{self.log_prefix}连接心跳超时")
                return False
            
            # 尝试获取账户信息以验证连接
            try:
                if self.trader and self.account:
                    asset = self.trader.query_stock_asset(self.account)
                    if asset:
                        self.last_heartbeat = current_time
                        return True
                return False
            except:
                return False
                
        except Exception as e:
            self._log(f"{self.log_prefix}检查连接健康状态异常: {str(e)}")
            return False
    
    def _monitor_connection(self):
        """连接监控线程"""
        while not self.stop_monitor:
            try:
                if self.connected and not self.check_connection_health():
                    self._log(f"{self.log_prefix}检测到连接异常，尝试重连")
                    self._reconnect()
                
                self.time.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                self._log(f"{self.log_prefix}连接监控异常: {str(e)}")
                self.time.sleep(5)
    
    def _reconnect(self):
        """重新连接"""
        try:
            if self.reconnect_attempts >= self.max_reconnect_attempts:
                self._log(f"{self.log_prefix}达到最大重连次数，停止重连")
                return False
            
            self.reconnect_attempts += 1
            self._log(f"{self.log_prefix}开始第 {self.reconnect_attempts} 次重连")
            
            # 断开现有连接
            if self.trader:
                try:
                    self.trader.disconnect()
                except:
                    pass
            
            # 初始化交易对象
            self._init_trader()
            
            # 重新连接
            connect_success = self.connect()
            
            # 如果连接成功，则重新订阅
            if connect_success:
                subscribe_success = self.subscribe()
                self._log(f"{self.log_prefix}重连成功，订阅状态: {subscribe_success}")
                self.reconnect_attempts = 0
                return True
            else:
                self._log(f"{self.log_prefix}重连失败")
                return False
                
        except Exception as e:
            self._log(f"{self.log_prefix}重连异常: {str(e)}")
            return False
    
    def order_stock(self, symbol, direction, quantity, price_type, price, 
                   strategy_name="auto_trading", remark=""):
        """
        下单
        
        参数:
            symbol: 证券代码，如"600000.SH"
            direction: 交易方向，使用xtconstant.STOCK_BUY或xtconstant.STOCK_SELL
            quantity: 数量
            price_type: 价格类型，使用xtconstant.FIX_PRICE(限价)或xtconstant.LATEST_PRICE(市价)
            price: 价格，市价单可传0
            strategy_name: 策略名称
            remark: 备注
            
        返回:
            下单成功返回订单ID，失败返回None
        """
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法下单")
            return None
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法下单")
            return None
            
        try:
            # 下单
            self._log(f"{self.log_prefix}准备下单: {symbol}, 方向={direction}, 数量={quantity}, 价格={price}, 类型={price_type}")
            
            order_id = self.trader.order_stock(
                self.account, 
                symbol, 
                direction, 
                quantity, 
                price_type, 
                price, 
                strategy_name, 
                remark
            )
            
            if order_id:
                self._log(f"{self.log_prefix}下单成功，订单ID: {order_id}")
                return order_id
            else:
                self._log(f"{self.log_prefix}下单失败")
                return None
                
        except Exception as e:
            self._log(f"{self.log_prefix}下单异常: {str(e)}")
            return None
    
    def cancel_order(self, order_id):
        """
        撤单
        
        参数:
            order_id: 订单ID
            
        返回:
            撤单成功返回True，失败返回False
        """
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法撤单")
            return False
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法撤单")
            return False
            
        try:
            # 撤单
            self._log(f"{self.log_prefix}准备撤单: {order_id}")
            
            result = self.trader.cancel_order_stock(self.account, order_id)
            
            if result:
                self._log(f"{self.log_prefix}撤单成功，订单ID: {order_id}")
                return True
            else:
                self._log(f"{self.log_prefix}撤单失败，订单ID: {order_id}")
                return False
                
        except Exception as e:
            self._log(f"{self.log_prefix}撤单异常: {str(e)}")
            return False
    
    def query_asset(self):
        """查询资产"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询资产")
            return None
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询资产")
            return None
            
        try:
            return self.trader.query_stock_asset(self.account)
        except Exception as e:
            self._log(f"{self.log_prefix}查询资产异常: {str(e)}")
            return None
    
    def query_orders(self):
        """查询当日委托"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询委托")
            return []
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询委托")
            return []
            
        try:
            return self.trader.query_stock_orders(self.account)
        except Exception as e:
            self._log(f"{self.log_prefix}查询委托异常: {str(e)}")
            return []
    
    def query_trades(self):
        """查询当日成交"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询成交")
            return []
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询成交")
            return []
            
        try:
            return self.trader.query_stock_trades(self.account)
        except Exception as e:
            self._log(f"{self.log_prefix}查询成交异常: {str(e)}")
            return []
    
    def query_positions(self):
        """查询持仓"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询持仓")
            return []
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询持仓")
            return []
            
        try:
            return self.trader.query_stock_positions(self.account)
        except Exception as e:
            self._log(f"{self.log_prefix}查询持仓异常: {str(e)}")
            return []
    
    def query_position(self, symbol):
        """查询单个持仓"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询持仓")
            return None
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询持仓")
            return None
            
        try:
            return self.trader.query_stock_position(self.account, symbol)
        except Exception as e:
            self._log(f"{self.log_prefix}查询持仓异常: {str(e)}")
            return None
    
    def query_order(self, order_id):
        """
        查询订单
        
        参数:
            order_id: 订单ID
            
        返回:
            订单对象，失败返回None
        """
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询订单")
            return None
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询订单")
            return None
            
        try:
            # 使用正确的方法名：query_stock_order，而不是query_order_by_code
            return self.trader.query_stock_order(self.account, order_id)
        except Exception as e:
            self._log(f"{self.log_prefix}查询订单异常: {str(e)}")
            return None
    
    def run_forever(self):
        """阻塞线程，接收交易推送"""
        if not self.trader:
            self._log(f"{self.log_prefix}交易对象未初始化，无法等待推送")
            return
            
        try:
            self.trader.run_forever()
        except Exception as e:
            self._log(f"{self.log_prefix}等待推送异常: {str(e)}")
    
    def shutdown(self):
        """关闭连接管理器"""
        self._log(f"{self.log_prefix}正在关闭交易连接管理器...")
        self.stop_monitor = True
        
        if hasattr(self, 'monitor_thread') and self.monitor_thread.is_alive():
            self.monitor_thread.join(5)
        
        if self.trader:
            try:
                self.trader.disconnect()
            except:
                pass
        
        self._log(f"{self.log_prefix}交易连接管理器已关闭")

    def setup_email(self, sender, receivers, smtp_server, smtp_port, username, password):
        """
        设置邮件发送参数
        
        参数:
            sender: 发件人邮箱地址
            receivers: 收件人邮箱地址列表
            smtp_server: SMTP服务器地址
            smtp_port: SMTP服务器端口
            username: 邮箱账号
            password: 邮箱密码
            
        返回:
            设置成功返回True，失败返回False
        """
        try:
            self.email_sender = sender
            self.email_receivers = receivers if isinstance(receivers, list) else [receivers]
            self.email_smtp_server = smtp_server
            self.email_smtp_port = smtp_port
            self.email_username = username
            self.email_password = password
            self.email_enabled = True
            
            self._log(f"{self.log_prefix}邮件设置成功")
            return True
        except Exception as e:
            self._log(f"{self.log_prefix}邮件设置异常: {str(e)}")
            self.email_enabled = False
            return False
    
    def send_email(self, subject, content):
        """
        发送邮件
        
        参数:
            subject: 邮件主题
            content: 邮件内容
            
        返回:
            发送成功返回True，失败返回False
        """
        if not self.email_enabled:
            self._log(f"{self.log_prefix}邮件功能未启用，无法发送邮件")
            return False
            
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            from email.header import Header
            
            # 创建邮件对象
            message = MIMEMultipart()
            message['From'] = self.email_sender
            message['To'] = ','.join(self.email_receivers)
            message['Subject'] = Header(subject, 'utf-8')
            
            # 添加邮件内容
            message.attach(MIMEText(content, 'plain', 'utf-8'))
            
            # 发送邮件
            if self.email_smtp_port == 465:
                # SSL加密方式
                smtp = smtplib.SMTP_SSL(self.email_smtp_server, self.email_smtp_port)
            else:
                # 普通方式
                smtp = smtplib.SMTP(self.email_smtp_server, self.email_smtp_port)
                
            # 登录邮箱
            smtp.login(self.email_username, self.email_password)
            
            # 发送邮件
            smtp.sendmail(self.email_sender, self.email_receivers, message.as_string())
            
            # 关闭连接
            smtp.quit()
            
            self._log(f"{self.log_prefix}邮件发送成功")
            return True
        except Exception as e:
            self._log(f"{self.log_prefix}邮件发送异常: {str(e)}")
            return False


import datetime
from xtquant import xtconstant
g_trade_connection = None
# 初始化参数
client_path = "D:\\国金QMT交易端模拟\\userdata_mini"  # 请替换为实际路径
session_id = int(datetime.datetime.now().timestamp())  # 使用当前时间戳作为会话ID
account_id = "********"  # 请替换为实际账号

# 创建交易连接管理器，使用print输出日志(通用环境)
g_trade_connection = TradeConnectionManager(
    client_path=client_path,
    session_id=session_id,
    account_id=account_id,
    log_type=0  # 0表示print输出，1表示LogInfo输出
)

# 启动交易线程
g_trade_connection.start()

# 连接交易服务器
connect_result = g_trade_connection.connect()
print(f"交易连接结果: {connect_result}")

# 订阅交易推送
subscribe_result = g_trade_connection.subscribe()
print(f"交易订阅结果: {subscribe_result}")

# 查询资产
asset = g_trade_connection.query_asset()
if asset:
    print(f"可用资金: {asset.cash}")



    
# 使用示例# 使用示例


# 使用示例
def smart_order_manager_example():
    import time
    import datetime
    
    # 初始化智能拆单管理器，使用print输出日志(通用环境)
    client_path = "D:\\国金QMT交易端模拟\\userdata_mini"  # 请替换为实际路径
    session_id = int(datetime.datetime.now().timestamp())  # 使用当前时间戳作为会话ID
    account_id = "********"  # 请替换为实际账号
    order_manager = SmartOrderManager(
        # client_path=client_path,
        # session_id=session_id,
        # account_id=account_id,
        price_limit_percent=1,       # 价格偏离限制1%
        time_limit_seconds=600,      # 10分钟超时
        order_size_ratio=0.3,        # 相对盘口挂单量的30%
        max_retry=3,                 # 最大重试3次
        price_step_percent=0.02,     # 价格调整步长0.02%
        status_check_interval=1,     # 每秒检查一次订单状态
        log_type=0                   # 使用print输出日志
    )
    
    # 模拟买入操作
    symbol = "510050.SH"  # 以上证50ETF为例
    buy_task_id = order_manager.place_buy_order(
        symbol=symbol,
        total_quantity=100000,       # 买入100000股
        limit_price=None,            # 市价买入
        min_quantity=100,            # 最小下单量100股
        callback=lambda task_id, task: print(f"买入任务 {task_id} 完成，状态: {task['status']}")
    )
    
    # 等待一段时间，让订单有机会执行
    time.sleep(10)
    
    # 查询任务状态
    buy_task = order_manager.get_task_status(buy_task_id)
    if buy_task:
        print(f"买入任务状态: {buy_task['status']}, 已成交: {buy_task['filled_quantity']}, 剩余: {buy_task['remaining_quantity']}")
    
    # # 模拟卖出操作
    # sell_task_id = order_manager.place_sell_order(
    #     symbol=symbol,
    #     total_quantity=500,          # 卖出500股
    #     limit_price=None,            # 市价卖出
    #     min_quantity=100,            # 最小下单量100股
    #     callback=lambda task_id, task: print(f"卖出任务 {task_id} 完成，状态: {task['status']}")
    # )
    
    # # 等待一段时间，让订单有机会执行
    # time.sleep(10)
    
    # # 查询任务状态
    # sell_task = order_manager.get_task_status(sell_task_id)
    # if sell_task:
    #     print(f"卖出任务状态: {sell_task['status']}, 已成交: {sell_task['filled_quantity']}, 剩余: {sell_task['remaining_quantity']}")
    
    # 取消剩余的任务
    # order_manager.cancel_task(buy_task_id)
    # order_manager.cancel_task(sell_task_id)
    
    # 查询所有任务
    all_tasks = order_manager.get_all_tasks()
    print(f"共有 {len(all_tasks)} 个任务")
    
    # 关闭管理器
    order_manager.shutdown()
    
    # print("智能拆单示例运行完成")


# smart_order_manager_example()






# 使用示例
def trade_connection_example():
    import time
    import datetime
    from xtquant import xtconstant
    
    # 初始化参数
    client_path = "D:\\国金QMT交易端模拟\\userdata_mini"  # 请替换为实际路径
    session_id = int(datetime.datetime.now().timestamp())  # 使用当前时间戳作为会话ID
    account_id = "********"  # 请替换为实际账号
    
    # 创建交易连接管理器，使用print输出日志(通用环境)
    trade_manager = TradeConnectionManager(
        client_path=client_path,
        session_id=session_id,
        account_id=account_id,
        log_type=0  # 0表示print输出，1表示LogInfo输出
    )
    
    # 配置邮件设置
    trade_manager.setup_email(
        sender="<EMAIL>",
        receivers=["<EMAIL>"],
        smtp_server="smtp.qq.com",
        smtp_port=465,
        username="<EMAIL>",  
        password="suvnyawlugejbgeg"
    )
    
    # 启动交易线程
    trade_manager.start()
    
    # 连接交易服务器
    connect_result = trade_manager.connect()
    print(f"交易连接结果: {connect_result}")
    
    # 订阅交易推送
    subscribe_result = trade_manager.subscribe()
    print(f"交易订阅结果: {subscribe_result}")
    
    # 查询资产
    asset = trade_manager.query_asset()
    if asset:
        print(f"可用资金: {asset.cash}")
    
    # 下单示例
    symbol = "600000.SH"
    order_id = trade_manager.order_stock(
        symbol=symbol,
        direction=xtconstant.STOCK_BUY,
        quantity=100,
        price_type=xtconstant.FIX_PRICE,
        price=10.5,
        strategy_name="测试策略",
        remark="测试买入"
    )
    
    if order_id:
        print(f"下单成功，订单ID: {order_id}")
        
        # # 发送邮件通知
        # trade_manager.send_email(
        #     subject="下单通知",
        #     content=f"已成功下单买入 {symbol} 100股，价格10.5，订单ID: {order_id}"
        # )
        
        # 等待一段时间
        time.sleep(2)
        
        # 查询订单状态
        order = trade_manager.query_order(order_id)
        if order:
            print(f"订单状态: {order.order_status}")
    
    # 关闭连接
    trade_manager.shutdown()
trade_connection_example()