class TradingStrategy:
    def __init__(self):
        # =========================
        # 参数设定
        # =========================
        # 开盘追单相关参数
        self.n_open_intervals = 5           # 开盘后5个3分钟周期内（例如09:30~09:45）
        self.extreme_speed_threshold = 10   # 极端行情下1分钟内价格变动点数门槛（示例值）
        self.sp_slope_threshold = 70        # SP斜率门槛（例如70度）
        self.reversal_angle_threshold = 5   # 1分钟K线反转拐角小于5度（示例）
        self.profit_drawdown_pct = 0.01     # 利润回撤比例（1%），作为平仓止盈条件
        
        # V反和SP击穿相关参数
        self.M_threshold = 1.0              # 用于计算左侧点差M的阈值（示例值）
        self.required_left_speed = 10       # 左侧速度门槛，M值/3分钟必须大于此值
        
        # 止盈止损相关参数
        self.boll_stable_duration = 3       # BOLL中轨站稳时长（分钟）
        self.trailing_stop_loss_pct = 0.01  # 最大利润回撤1%触发移动止盈
        self.stoploss_duration = 3          # SP跌破后超过N分钟止损（示例3分钟）
    
    def process_market_data(self, market_data):
        """
        主策略执行函数，根据时间序列数据对接收的数据进行处理：
          - 根据当前时间判断：是否处于开盘后N个3分钟周期内
          - 开盘阶段使用1分钟数据判断极端行情；否则不计开仓（常规开盘追单不开单）
          - 盘中阶段（例如从第16分钟开始）采用3分钟K线判断
        参数 market_data 为一个时间序列数据，每个元素包含以下信息：
          {
              'time': <当前时间>,
              'kline_1min': {...},       # 1分钟K线数据（在必要时使用）
              'kline_3min': [ {...}, ... ], # 3分钟K线序列（包括历史数据窗口）
              'sp_line': { 'value': ..., 'slope': ... },   # SP均价线当前值和斜率
              'boll_mid': { 'value': ... } # BOLL中轨数据
          }
        """
        for data in market_data:
            current_time = data['time']
            kline_1min = data.get('kline_1min', None)
            kline_3min = data.get('kline_3min', None)
            sp_line = data['sp_line']  # SP日内均价线及其他信息
            boll_mid = data.get('boll_mid', None)
            
            # ① 开盘追单阶段：在开盘后N个3分钟周期内（常规不开仓）
            if self.in_opening_period(current_time):
                self.process_opening_trade(current_time, kline_1min, kline_3min, sp_line)
            else:
                # ② 盘中阶段（从第16分钟开始）的逻辑，包含 V反、SP击穿与止盈止损判断
                self.process_intraday_trade(current_time, kline_3min, sp_line, boll_mid)
    
    def in_opening_period(self, current_time):
        """
        判断当前时间是否处于开盘后的N个3分钟周期内
        （例如：假设开盘时间为09:30，结束时间为09:30 + 5×3分钟 = 09:45）
        """
        open_time = ...  # 设定具体开盘时间，如 datetime 对象
        end_time = open_time + self.n_open_intervals * 3  # 伪代码：单位为分钟
        return current_time <= end_time
    
    def process_opening_trade(self, current_time, kline_1min, kline_3min, sp_line):
        """
        开盘追单逻辑：
         1. 在开盘后5个3分钟周期内，常规逻辑不开单。
         2. 如遇到极端行情（价格在1分钟内快速移动，满足：
              (a) 1分钟内价格变动速度大于阈值；
              (b) SP斜率大于阈值）；
             且1分钟K线无明显拐角反向（例如反转角度小于5度）则立刻市价下单，
             同时设置利润回撤比例一键止损、以及特定的平仓条件（例如开盘后最高/最低价维持20秒不回撤）。
        """
        # 判断是否满足极端行情条件
        if self.is_extreme_market(kline_1min, sp_line):
            # 进一步判断1分钟级别是否无反向信号
            if not self.has_1min_reversal(kline_1min, self.reversal_angle_threshold):
                # 市价开单并记录订单
                self.open_position("市价开单", current_time, "极端行情触发")
                # 设置对应的止盈止损条件（如：利润回撤、20秒内价格条件等）
                self.set_profit_stop_condition(current_time)
            else:
                # 如果检测到明显反向信号，则不下单
                pass
        else:
            # 正常情况：开盘后5个3分钟周期内不主动开仓
            pass

    def is_extreme_market(self, kline_1min, sp_line):
        """
        判断是否为极端行情：
          - 计算1分钟内价格的移动速度，若大于 self.extreme_speed_threshold
          - 计算SP斜率（可利用前面30个3分钟K线或者根据最高/最低值计算），若大于 self.sp_slope_threshold
        返回 True 表示符合极端行情条件，否则 False。
        """
        speed = self.calculate_speed(kline_1min)
        sp_slope = self.calculate_sp_slope(sp_line)
        if speed > self.extreme_speed_threshold and sp_slope > self.sp_slope_threshold:
            return True
        return False

    def calculate_speed(self, kline):
        """
        计算1分钟内的价格变动速度：
          速度 = \( (收盘价 - 开盘价) / 1 \) 分钟
        """
        if kline:
            return (kline['close'] - kline['open'])  # 假设时间间隔为1分钟
        return 0

    def calculate_sp_slope(self, sp_line):
        """
        返回 SP 斜率，目前假设 sp_line 中已有 'slope' 字段，单位为度数
        """
        return sp_line.get('slope', 0)

    def has_1min_reversal(self, kline_1min, angle_threshold):
        """
        判断1分钟K线是否出现反向拐角信号，
        例如通过对比当分钟K线的角度与 angle_threshold。
        返回 True 表示存在反向信号，否则 False。
        """
        angle = kline_1min.get('angle', 0) if kline_1min else 0
        if angle < angle_threshold:
            return True
        return False

    def open_position(self, order_type, current_time, reason):
        """
        模拟开仓操作，此处仅做记录输出，实际系统应调用下单接口。
        """
        print(f"{current_time}: 开仓 - {order_type} (原因: {reason})")

    def set_profit_stop_condition(self, current_time):
        """
        设置止盈/止损条件：
         - 当价格达到一定利润水平后，一旦回撤达到 self.profit_drawdown_pct 即止盈平仓
         - 同时可设置例如：“开盘后的最高价、最低价不回撤超过一半且维持20秒”作为平仓条件
        """
        print(f"{current_time}: 设置利润止盈/止损条件")

    def process_intraday_trade(self, current_time, kline_3min, sp_line, boll_mid):
        """
        盘中阶段逻辑：
         1. 从第16分钟开始使用3分钟K线数据判断行情（例如取前5根K线+后2分钟数据构成观察窗口）
         2. 判断是否出现 V反 信号（见下述 check_v_reversal 方法）
         3. 判断是否出现 SP击穿 信号（见下述 check_sp_breakthrough 方法）
         4. 根据多个止盈平仓法则判断是否需要平仓（BOLL中轨、极远值、移动止盈）
        """
        # 检查 V反 开仓条件
        if self.check_v_reversal(kline_3min, sp_line):
            self.open_position("V反开仓", current_time, "V反条件满足")
        
        # 检查 SP 击穿 开仓条件
        if self.check_sp_breakthrough(kline_3min, sp_line):
            self.open_position("SP击穿开仓", current_time, "SP击穿条件满足")
        
        # 检查是否触发平仓止盈条件
        if self.check_profit_take(current_time, boll_mid, kline_3min):
            self.close_all_positions(current_time, "达到平仓止盈条件")

    def check_v_reversal(self, kline_3min, sp_line):
        """
        V反开仓逻辑：
         ① 找出3分钟K线中最接近SP值的那一根作为中轴
         ② 左侧取3根K线，计算最高价与SP差值 M
         ③ 计算左侧速度 \(= M / 3 \text{分钟}\)，必须大于一定门槛
         ④ 右侧观察5根K线（约15分钟），若价格反弹突破 \(M/2\) 并能站稳N分钟（如1分钟）则认为V反成功
         ⑤ 如SP斜率满足条件，可给予加权（例如：3分钟BOLL中轨处于SP近带内时乘1.2，否则降权）
        """
        mid_index = self.find_mid_axis(kline_3min, sp_line)
        if mid_index is None:
            return False

        left_k_lines = kline_3min[max(0, mid_index-3):mid_index]
        right_k_lines = kline_3min[mid_index+1:mid_index+6]

        # 计算左侧最高价与SP值的差值 M
        M = self.calculate_M(left_k_lines, sp_line)
        left_speed = M / 3.0  # 假设3分钟内

        if left_speed < self.required_left_speed:
            return False

        # 检查右侧反弹：判断最后一根K线收盘价是否超过 M/2 且是否站稳（例如持续1分钟）
        if right_k_lines:
            rebound = right_k_lines[-1]['close'] - sp_line['value']
            if rebound > M / 2:
                if self.is_stable(right_k_lines, duration=1):
                    return True

        return False

    def find_mid_axis(self, kline_list, sp_line):
        """
        找出3分钟K线中与SP价格最接近的那一根，返回其索引作为中轴
        """
        mid_index = None
        min_diff = float('inf')
        for i, k in enumerate(kline_list):
            diff = abs(k['close'] - sp_line['value'])
            if diff < min_diff:
                min_diff = diff
                mid_index = i
        return mid_index

    def calculate_M(self, kline_list, sp_line):
        """
        计算左侧3根K线中最高价与SP之间的价差 M
        """
        if not kline_list:
            return 0
        highest_price = max(k['high'] for k in kline_list)
        M = highest_price - sp_line['value']
        return M

    def is_stable(self, kline_list, duration):
        """
        简单判断在给定的时间窗口（duration 分钟内）价格是否站稳
        伪代码示例：可检查是否有连续K线未出现明显反转
        """
        # 这里简单返回 True，实际应对连续数据进行判断
        return True

    def check_sp_breakthrough(self, kline_3min, sp_line):
        """
        SP击穿开仓逻辑：
         ① 以满足SP斜率或拐角加权条件的K线作为中轴
         ② 左侧取3根K线，计算最高价与SP之间的差值 M
         ③ 右侧观察5个K线（15分钟窗口）：若当前K线价格突破 M 的一半值且站稳N分钟，则视为有效击穿
         ④ 或者直接突破 M（不计时长）也触发市价单开仓
        """
        mid_index = self.find_mid_axis(kline_3min, sp_line)
        if mid_index is None or mid_index < 3:
            return False

        left_k_lines = kline_3min[mid_index-3:mid_index]
        M = self.calculate_M(left_k_lines, sp_line)

        current_k = kline_3min[mid_index]
        # 判断当前价格突破情况
        if (current_k['close'] - sp_line['value']) > M / 2:
            if self.is_stable(kline_3min[mid_index:mid_index+5], duration=1):
                return True
        # 或直接突破 M 值
        if (current_k['close'] - sp_line['value']) > M:
            return True

        return False

    def check_profit_take(self, current_time, boll_mid, kline_3min):
        """
        平仓止盈逻辑——多法则关系：
         ① BOLL中轨法则：价格未回撤破3分钟BOLL中轨且站稳超过一定时长，否则全平
         ② 极远值法则：当价格进入远带区域，参考历史经验值计算极远空间点差，达到条件则逐步平仓或一次平仓
         ③ 移动止盈法则：当持仓期间价格达到最大利润后回撤达到 self.trailing_stop_loss_pct，平仓止盈
         ④ 多法则：如果触发极远值止盈，则无视其余指示；若移动止盈条件满足但BOLL中轨法则未触发则不平
        """
        # 示例：检查 BOLL 中轨突破情况（注意：此处仅为简单示例）
        if boll_mid is None:
            return False

        current_price = kline_3min[-1]['close'] if kline_3min else None
        if current_price is None:
            return False

        # 判断与BOLL中轨的差距是否超过设定点位（例如正负 n 点）
        if abs(current_price - boll_mid['value']) > self.reversal_angle_threshold:
            # 检查价格是否站稳超过设定时长
            if self.is_stable(kline_3min[-5:], duration=self.boll_stable_duration):
                return True

        # 判断移动止盈条件：如果当前价格与持仓期间最高价的回撤超过 self.trailing_stop_loss_pct
        if self.check_trailing_stop(current_price):
            return True

        # 判断是否进入极远值区域（根据历史经验判断，这里以示例数值判断）
        if self.is_in_extreme_zone(current_price):
            return True

        return False

    def check_trailing_stop(self, current_price):
        """
        判断是否触发移动止盈平仓条件
        """
        max_price = self.get_max_hold_price()
        if max_price is None:
            return False
        if (max_price - current_price) / max_price >= self.trailing_stop_loss_pct:
            return True
        return False

    def get_max_hold_price(self):
        """
        获取持仓期间的最高价格
        实际应根据订单持仓实时更新，此处返回一个示例值
        """
        return 100.0

    def is_in_extreme_zone(self, current_price):
        """
        判断价格是否处于极远值区域
        例如：参考过去N天的经验值，若价格进入某一高位区域则认为已到达极远区
        """
        # 示例：若当前价格大于102则视为极远区
        if current_price >= 102:
            return True
        return False

    def close_all_positions(self, current_time, reason):
        """
        平仓操作：一键平仓所有持仓
        """
        print(f"{current_time}: 平仓所有仓位 (原因: {reason})")


# 示例：使用策略伪代码
if __name__ == "__main__":
    strategy = TradingStrategy()
    
    # market_data 为预先构造的市场数据序列，包含每个时刻的K线、SP线、BOLL数据等
    market_data = [
        # 示例数据，实际数据应包含详细字段：
        {
            'time': "09:32",
            'kline_1min': {'open': 100, 'close': 110, 'angle': 8},
            'kline_3min': [
                {'open': 100, 'close': 108, 'high': 111},
                {'open': 108, 'close': 112, 'high': 113},
                # ……更多3分钟K线数据
            ],
            'sp_line': {'value': 107, 'slope': 75},
            'boll_mid': {'value': 106}
        },
        # ……更多市场数据点
    ]
    
    strategy.process_market_data(market_data)