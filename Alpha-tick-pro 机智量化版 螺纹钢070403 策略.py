# -*- coding: utf-8 -*-
"""
Alpha-Tick Pro RB 螺纹钢高频策略 - 机智量化版070403
基于文华财经版本转换而来，适配机智量化平台语法和API

策略特点：
1. 动态大单阈值计算 (螺纹钢优化)
2. 增强版订单流分析
3. R-Breaker价位计算 (螺纹钢特化)
4. 多条件加权信号生成
5. 动态仓位与风险管理
6. 智能止盈止损机制
"""

import numpy as np
import pandas as pd
import talib as ta
from datetime import datetime, timedelta
import time

# ==================== 策略参数配置 ====================
# 大单参数
g_params['大单标准差倍数'] = 3.2  # 螺纹钢优化值，范围2.5-4.0
g_params['订单流分析周期'] = 15   # 订单流分析周期，范围10-30

# R-Breaker参数  
g_params['基础突破系数'] = 0.28   # 螺纹钢优化值，范围0.1-0.4
g_params['波动率调整系数'] = 0.12  # 范围0.05-0.2
g_params['ATR计算周期'] = 14      # ATR计算周期，范围5-30

# 风险管理参数
g_params['单笔最大风险'] = 0.02   # 单笔最大风险比例，范围0.005-0.05
g_params['单日止损线'] = -0.03    # 单日止损线(-3%)
g_params['最低流动性阈值'] = 80   # 最低流动性阈值(手)，范围50-150

# 交易控制参数
g_params['合约乘数'] = 10         # 螺纹钢合约乘数
g_params['最小价格变动'] = 1      # 最小价格变动单位
g_params['最大持仓时间'] = 180    # 最大持仓时间(秒)
g_params['交易间隔'] = 3000       # 交易间隔(毫秒)

# 数据订阅参数
g_params['K线基础时间'] = 'T'     # Tick数据
g_params['K线基础周期'] = 1       # 1分钟
g_params['订阅数据长度'] = 2000   # 订阅数据长度

# ==================== 全局变量声明 ====================
# 核心状态变量
big_vol = 0                    # 动态大单阈值
first_run = True               # 首次运行标志
trading_paused = False         # 交易暂停标志
daily_high = 0                 # 当日最高价
daily_low = 0                  # 当日最低价
pre_close = 0                  # 前收盘价

# R-Breaker关键价位
observe_sell_price = 0         # 观察卖出价
observe_buy_price = 0          # 观察买入价

# 订单流分析变量
cumulative_delta = 0           # 累积订单流差值
ema_delta = 0                  # 订单流EMA
buy_market_vol = 0             # 买方大单成交量
sell_market_vol = 0            # 卖方大单成交量

# 交易管理变量
position_size = 1              # 动态仓位大小
volatility_ratio = 0           # 波动率比率
dynamic_k = 0                  # 动态调整系数
last_trade_time = 0            # 上次交易时间
current_time = 0               # 当前时间

# 风险控制变量
loss_streak = 0                # 连续亏损次数
daily_max_profit = 0           # 当日最大盈利
account_equity = 0             # 账户净值

# 信号评分变量
trend_confidence = 0           # 趋势置信度
flow_strength = 0              # 订单流强度

# 订单ID管理
buy_order_id = 0               # 买入订单ID
sell_order_id = 0              # 卖出订单ID
cover_order_id = 0             # 平仓订单ID

# 交易状态管理变量
BKS = 0                        # 多头开仓状态 (0:未开仓, 1:已开仓, 2:已平仓)
SKS = 0                        # 空头开仓状态 (0:未开仓, 1:已开仓, 2:已平仓)
BPS = 0                        # 多头平仓状态 (0:未平仓, 1:已平仓)
SPS = 0                        # 空头平仓状态 (0:未平仓, 1:已平仓)

# 历史数据缓存
price_history = []             # 价格历史数据
volume_history = []            # 成交量历史数据
time_history = []              # 时间历史数据

# 交易参数
超价跳数 = 2                   # 开仓超价跳数
平仓超价跳数 = 1               # 平仓超价跳数
基准合约 = 'SHFE|F|RB|MAIN'   # 基准合约代码

# ==================== 交易封装函数 ====================

def tim_trigger(BK, SK, qty, itk, tcode):
    """盘中实时开仓"""
    global BKS, SKS

    if BK and BKS == 0:
        iprc = min(Q_AskPrice(tcode) + itk * PriceTick(tcode), Q_UpperLimit(tcode))  # 对盘超价
        A_SendOrder(Enum_Buy(), Enum_Entry(), qty, iprc, tcode)
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "多单买入开仓价==>", iprc, "买入数量==>", qty)
        BKS = 1
    elif SK and SKS == 0:
        iprc = max(Q_BidPrice(tcode) - itk * PriceTick(tcode), Q_LowLimit(tcode))  # 对盘超价
        A_SendOrder(Enum_Sell(), Enum_Entry(), qty, iprc, tcode)
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "空单卖出开仓价==>", iprc, "卖出数量==>", qty)
        SKS = 1

def tim_trigger_Exit(BP, SP, otk, tcode, clots):
    """盘中实时平仓"""
    global BKS, SKS, BPS, SPS

    if BP and BPS == 0 and A_SellPosition(tcode) > 0 and clots > 0:
        _lots = min(clots, A_SellPosition(tcode))
        prc = min(Q_AskPrice(tcode) + otk * PriceTick(tcode), Q_UpperLimit(tcode))  # 对盘超价

        if ExchangeName(tcode) not in ['SHFE', 'INE']:
            retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), _lots, prc, tcode)
        else:
            lots = _lots
            tlots = A_TodaySellPosition(tcode)
            dlots = lots - tlots
            if tlots >= lots:
                TretExit, TExitOrderId = A_SendOrder(Enum_Buy(), Enum_ExitToday(), lots, prc, tcode)  # 今仓足够平仓,上期所能交所优先超价全部平今仓
            elif tlots > 0:
                TretExit, TExitOrderId = A_SendOrder(Enum_Buy(), Enum_ExitToday(), tlots, prc, tcode)  # 今仓不够，上期所能交所优先超价部分平今仓
                TretExit2, TExitOrderId2 = A_SendOrder(Enum_Buy(), Enum_Exit(), int(dlots), prc, tcode)  # 今仓不够，上期所能交所优先超价剩余部分平昨仓
            elif tlots == 0:
                retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), lots, prc, tcode)  # 上期所能交所超价平昨仓

        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "空单买入平仓价==>", prc, "买入平仓数量==>", _lots)
        BPS = 1
        if SKS == 1:
            SKS = 2

    elif SP and SPS == 0 and A_BuyPosition(tcode) > 0 and clots > 0:
        _lots = min(clots, A_BuyPosition(tcode))
        prc = max(Q_BidPrice(tcode) - otk * PriceTick(tcode), Q_LowLimit(tcode))

        if ExchangeName(tcode) not in ['SHFE', 'INE']:
            retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), _lots, prc, tcode)
        else:
            lots = _lots
            tlots = A_TodayBuyPosition(tcode)
            dlots = lots - tlots
            if tlots >= lots:
                TretExit, TExitOrderId = A_SendOrder(Enum_Sell(), Enum_ExitToday(), lots, prc, tcode)  # 今仓足够平仓,上期所能交所优先超价全部平今仓
            elif tlots > 0:
                TretExit, TExitOrderId = A_SendOrder(Enum_Sell(), Enum_ExitToday(), tlots, prc, tcode)  # 今仓不够，上期所能交所优先超价部分平今仓
                TretExit2, TExitOrderId2 = A_SendOrder(Enum_Sell(), Enum_Exit(), int(dlots), prc, tcode)  # 今仓不够，上期所能交所优先超价剩余部分平昨仓
            elif tlots == 0:
                retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), lots, prc, tcode)  # 上期所能交所超价平昨仓

        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "多单卖出平仓价==>", prc, "卖出平仓数量==>", _lots)
        SPS = 1
        if BKS == 1:
            BKS = 2

def his_trigger(BK, SK, qty, itk):
    """历史开仓"""
    global BKS, SKS

    if BK and BKS == 0:
        iprc = Close()[-1] + itk * PriceTick()
        Buy(qty, iprc)
        LogInfo(Time(), "->合约==>", Symbol(), "多单买入开仓价==>", iprc, "买入数量==>", qty)
        BKS = 1
    elif SK and SKS == 0:
        iprc = Close()[-1] - itk * PriceTick()
        SellShort(qty, iprc)
        LogInfo(Time(), "->合约==>", Symbol(), "空单卖出开仓价==>", iprc, "卖出数量==>", qty)
        SKS = 1

def his_trigger_Exit(BP, SP, otk, clots):
    """历史平仓"""
    global BKS, SKS, BPS, SPS

    if BP and BPS == 0 and SellPosition() > 0 and clots > 0:
        _lots = min(clots, SellPosition())
        prc = Close()[-1] + otk * PriceTick()
        BuyToCover(_lots, prc)
        LogInfo(Time(), "->合约==>", Symbol(), "空单买入平仓价==>", prc, "买入平仓数量==>", _lots)
        BPS = 1
        if SKS == 1:
            SKS = 2
    elif SP and SPS == 0 and BuyPosition() > 0 and clots > 0:
        _lots = min(clots, BuyPosition())
        prc = Close()[-1] - otk * PriceTick()
        Sell(_lots, prc)
        LogInfo(Time(), "->合约==>", Symbol(), "多单卖出平仓价==>", prc, "卖出平仓数量==>", _lots)
        SPS = 1
        if BKS == 1:
            BKS = 2

def reset_trading_status():
    """重置交易状态"""
    global BKS, SKS, BPS, SPS
    BKS = 0
    SKS = 0
    BPS = 0
    SPS = 0

def is_real_time():
    """判断是否为实时阶段"""
    try:
        # 通过检查是否有实时行情数据来判断
        return Q_UpdateTime() is not None
    except:
        return False

# ==================== 策略初始化 ====================

def initialize(context):
    """
    策略初始化函数
    """
    global first_run, account_equity
    
    LogInfo("=== Alpha-Tick Pro RB 螺纹钢高频策略初始化 ===")
    
    # 设置基准合约为螺纹钢主力合约
    base_contract = 'SHFE|F|RB|MAIN'
    
    # 订阅K线数据
    SetBarInterval(base_contract, g_params['K线基础时间'], g_params['K线基础周期'], g_params['订阅数据长度'])
    
    # 订阅实时行情数据
    SubscribeQuote([base_contract])
    
    # 初始化账户信息
    account_equity = A_TotalAssets()
    
    # 初始化日期相关变量
    current_date = Date()
    init_daily_variables()
    
    # 设置首次运行标志
    first_run = True
    
    LogInfo(f"策略初始化完成，基准合约: {base_contract}")
    LogInfo(f"账户总资产: {account_equity:.2f}")
    LogInfo(f"大单标准差倍数: {g_params['大单标准差倍数']}")
    LogInfo(f"基础突破系数: {g_params['基础突破系数']}")
    LogInfo(f"单笔最大风险: {g_params['单笔最大风险']*100:.1f}%")

def init_daily_variables():
    """
    初始化日变量
    """
    global daily_high, daily_low, pre_close, cumulative_delta
    global daily_max_profit, loss_streak, buy_market_vol, sell_market_vol

    try:
        # 获取前一日收盘价
        close_prices = Close()
        if len(close_prices) > 1:
            pre_close = close_prices[-2]
            daily_high = High()[-1]
            daily_low = Low()[-1]
        else:
            pre_close = Close()[-1] if len(close_prices) > 0 else 0
            daily_high = High()[-1] if len(High()) > 0 else 0
            daily_low = Low()[-1] if len(Low()) > 0 else 0
    except:
        pre_close = 0
        daily_high = 0
        daily_low = 0

    # 重置日变量
    cumulative_delta = 0
    daily_max_profit = 0
    loss_streak = 0
    buy_market_vol = 0
    sell_market_vol = 0

    # 重置交易状态
    reset_trading_status()

    LogInfo(f"日变量初始化完成 - 前收盘: {pre_close:.1f}, 当日高: {daily_high:.1f}, 当日低: {daily_low:.1f}")
    LogInfo("交易状态已重置")

def handle_data(context):
    """
    主策略逻辑函数 - 每个数据周期调用
    """
    global first_run
    
    try:
        # 检查是否需要初始化日变量
        check_daily_reset()
        
        # 更新账户信息
        update_account_info()
        
        # 核心计算模块
        calculate_big_volume_threshold()    # 动态大单阈值计算
        analyze_order_flow()               # 订单流分析
        calculate_rbreaker_levels()        # R-Breaker价位计算
        calculate_trend_strength()         # 趋势强度判断
        
        # 信号生成模块
        long_signal, short_signal = generate_trading_signals()
        
        # 仓位与风险管理
        calculate_position_size()
        risk_control_check()
        
        # 交易执行模块
        if not trading_paused:
            execute_trading_signals(long_signal, short_signal)
            manage_existing_positions()
        
        # 收盘平仓检查
        check_market_close()
        
        # 可视化更新
        update_visualization()
        
        # 重置首次运行标志
        if first_run:
            first_run = False
            
    except Exception as e:
        LogError(f"策略执行异常: {str(e)}")

def check_daily_reset():
    """
    检查是否需要重置日变量
    """
    global daily_high, daily_low
    
    current_date = Date()
    
    # 检查是否是新的交易日
    if len(time_history) > 0:
        last_date = time_history[-1] if time_history else 0
        if current_date != last_date:
            LogInfo(f"检测到新交易日: {current_date}, 重置日变量")
            init_daily_variables()
    
    # 更新当日高低价
    current_high = High()[-1] if len(High()) > 0 else 0
    current_low = Low()[-1] if len(Low()) > 0 else 0
    
    if current_high > daily_high:
        daily_high = current_high
    if current_low < daily_low or daily_low == 0:
        daily_low = current_low

def update_account_info():
    """
    更新账户信息
    """
    global account_equity, daily_max_profit
    
    account_equity = A_TotalAssets()
    current_profit = A_FloatProfit()
    
    # 更新当日最大盈利
    if current_profit > daily_max_profit:
        daily_max_profit = current_profit

def calculate_big_volume_threshold():
    """
    动态大单阈值计算 (螺纹钢优化版)
    """
    global big_vol
    
    try:
        volumes = Vol()
        if len(volumes) < 10:
            big_vol = 100  # 默认阈值
            return
        
        # 动态调整分析窗口(最近2小时数据)
        current_time_float = Time()
        lookback_period = 30 if current_time_float < 0.1130 else 120  # 早盘用30分钟，其他时段用2小时
        
        # 计算样本数据
        sample_size = min(len(volumes), lookback_period)
        if sample_size < 5:
            return
        
        recent_volumes = volumes[-sample_size:]
        
        # 计算均值和标准差
        mean_vol = np.mean(recent_volumes)
        std_vol = np.std(recent_volumes)
        
        # 动态调整大单系数(根据市场波动率)
        dynamic_ratio = g_params['大单标准差倍数'] * (1 + 0.5 * volatility_ratio)
        
        # 计算大单阈值
        big_vol = mean_vol + dynamic_ratio * std_vol
        
        LogInfo(f"【动态大单】最新阈值: {big_vol:.0f}, 动态系数: {dynamic_ratio:.2f}")
        
    except Exception as e:
        LogError(f"计算大单阈值异常: {str(e)}")
        big_vol = 100  # 使用默认值

def analyze_order_flow():
    """
    增强版订单流分析
    """
    global cumulative_delta, ema_delta, buy_market_vol, sell_market_vol, flow_strength
    
    try:
        # 获取买卖盘数据
        bid_vol1 = Q_BidVolume(0)  # 买一量
        ask_vol1 = Q_AskVolume(0)  # 卖一量
        bid_vol2 = Q_BidVolume(1)  # 买二量  
        ask_vol2 = Q_AskVolume(1)  # 卖二量
        bid_vol3 = Q_BidVolume(2)  # 买三量
        ask_vol3 = Q_AskVolume(2)  # 卖三量
        
        # 使用三档数据加权计算
        depth_factor = 0.7
        weighted_delta = ((bid_vol1 - ask_vol1) * 0.5 + 
                         (bid_vol2 - ask_vol2) * 0.3 * depth_factor +
                         (bid_vol3 - ask_vol3) * 0.2 * depth_factor)
        
        # 动态平滑周期(市场活跃时缩短周期)
        total_liquidity = bid_vol1 + ask_vol1
        dynamic_period = max(5, g_params['订单流分析周期'] - 5) if total_liquidity > 2 * g_params['最低流动性阈值'] else g_params['订单流分析周期']
        
        # 更新累积订单流差值
        cumulative_delta += weighted_delta
        
        # 计算EMA
        alpha = 2.0 / (dynamic_period + 1)
        ema_delta = alpha * weighted_delta + (1 - alpha) * ema_delta
        
        # 增加大单净流向指标
        current_volume = Vol()[-1] if len(Vol()) > 0 else 0
        current_close = Close()[-1] if len(Close()) > 0 else 0
        current_open = Open()[-1] if len(Open()) > 0 else 0
        current_high = High()[-1] if len(High()) > 0 else 0
        current_low = Low()[-1] if len(Low()) > 0 else 0
        
        if current_volume > big_vol:
            typical_price = (current_open + current_high + current_low) / 3
            if current_close > typical_price:
                buy_market_vol += current_volume
            elif current_close < typical_price:
                sell_market_vol += current_volume
        
        # 计算订单流强度
        flow_strength = ema_delta * (1.2 if abs(ema_delta) > 50 else 1.0)
        if big_vol > 0:
            flow_strength += 0.3 * (buy_market_vol - sell_market_vol) / big_vol
        
    except Exception as e:
        LogError(f"订单流分析异常: {str(e)}")

def calculate_rbreaker_levels():
    """
    R-Breaker价位计算 (螺纹钢优化)
    """
    global observe_sell_price, observe_buy_price, dynamic_k, volatility_ratio
    
    try:
        if daily_high == 0 or daily_low == 0 or pre_close == 0:
            return
        
        # 时段调整系数
        current_time_float = Time()
        if current_time_float < 0.1030:
            session_factor = 1.2    # 早盘活跃
        elif current_time_float > 0.1430:
            session_factor = 1.1    # 尾盘
        else:
            session_factor = 1.0    # 其他时段
        
        # 计算波动率比率
        close_prices = Close()
        if len(close_prices) >= g_params['ATR计算周期']:
            atr_value = ATR(High(), Low(), Close(), g_params['ATR计算周期'])[-1]
            ma20_close = MA(close_prices, 20)[-1] if len(close_prices) >= 20 else close_prices[-1]
            volatility_ratio = atr_value / ma20_close if ma20_close > 0 else 0.012
        else:
            volatility_ratio = 0.012
        
        # 计算成交量比率
        volumes = Vol()
        if len(volumes) >= 20:
            current_volume = volumes[-1]
            ma20_volume = MA(volumes, 20)[-1]
            volume_ratio = current_volume / ma20_volume if ma20_volume > 0 else 1.0
        else:
            volume_ratio = 1.0
        
        # 动态调整系数
        dynamic_k = ((g_params['基础突破系数'] + g_params['波动率调整系数'] * (volatility_ratio - 0.012)) *
                    (0.8 + 0.2 * volume_ratio) *     # 成交量影响
                    session_factor)                   # 时段影响
        
        # 计算关键价位(增加平滑处理)
        raw_sell_price = daily_high + dynamic_k * (pre_close - daily_low)
        raw_buy_price = daily_low - dynamic_k * (daily_high - pre_close)
        
        # EMA平滑处理
        alpha = 2.0 / 4  # 3周期EMA
        observe_sell_price = alpha * raw_sell_price + (1 - alpha) * observe_sell_price if observe_sell_price > 0 else raw_sell_price
        observe_buy_price = alpha * raw_buy_price + (1 - alpha) * observe_buy_price if observe_buy_price > 0 else raw_buy_price
        
    except Exception as e:
        LogError(f"R-Breaker价位计算异常: {str(e)}")

def calculate_trend_strength():
    """
    趋势强度判断
    """
    global trend_confidence
    
    try:
        close_prices = Close()
        if len(close_prices) < 50:
            trend_confidence = 0
            return
        
        # 计算各种技术指标
        ma5 = MA(close_prices, 5)[-1]
        ma20 = MA(close_prices, 20)[-1]
        ma50 = MA(close_prices, 50)[-1]
        
        # ADX指标
        adx_value = ADX(High(), Low(), Close(), 14)[-1] if len(close_prices) >= 14 else 20
        
        # MACD指标
        macd_line, macd_signal, macd_hist = MACD(close_prices)
        macd_hist_current = macd_hist[-1] if len(macd_hist) > 0 else 0
        
        # RSI指标
        rsi_value = RSI(close_prices, 14)[-1] if len(close_prices) >= 14 else 50
        
        current_close = close_prices[-1]
        
        # 趋势置信度计算(多指标组合)
        trend_confidence = min(100, max(-100,
            30 * (1 if ma5 > ma20 else -1) +
            25 * (1 if adx_value > 22 else 0) +
            20 * (1 if current_close > ma50 else -1) +
            15 * (1 if macd_hist_current > 0 else -1) +
            10 * (1 if rsi_value > 50 else -1)
        ))
        
    except Exception as e:
        LogError(f"趋势强度计算异常: {str(e)}")
        trend_confidence = 0

def generate_trading_signals():
    """
    信号生成模块 - 多条件加权评分
    """
    try:
        current_close = Close()[-1] if len(Close()) > 0 else 0
        bid_vol1 = Q_BidVolume(0)
        ask_vol1 = Q_AskVolume(0)
        total_liquidity = bid_vol1 + ask_vol1

        # 多头信号(多条件加权评分)
        long_score = 0
        if current_close >= observe_sell_price:
            long_score += 25
        if ask_vol1 > big_vol * 0.7:
            long_score += 20
        if flow_strength < -45:
            long_score += 25
        if trend_confidence > 60:
            long_score += 20
        if total_liquidity > g_params['最低流动性阈值']:
            long_score += 10

        long_signal = long_score >= 70  # 总分阈值

        # 空头信号
        short_score = 0
        if current_close <= observe_buy_price:
            short_score += 25
        if bid_vol1 > big_vol * 0.7:
            short_score += 20
        if flow_strength > 45:
            short_score += 25
        if trend_confidence < -60:
            short_score += 20
        if total_liquidity > g_params['最低流动性阈值']:
            short_score += 10

        short_signal = short_score >= 70

        if long_signal:
            LogInfo(f"【多头信号】评分: {long_score}, 价格: {current_close:.1f}, 观察卖出价: {observe_sell_price:.1f}")
        if short_signal:
            LogInfo(f"【空头信号】评分: {short_score}, 价格: {current_close:.1f}, 观察买入价: {observe_buy_price:.1f}")

        return long_signal, short_signal

    except Exception as e:
        LogError(f"信号生成异常: {str(e)}")
        return False, False

def calculate_position_size():
    """
    动态仓位计算 (螺纹钢特化)
    """
    global position_size

    try:
        if account_equity <= 0:
            position_size = 1
            return

        # 基础计算
        risk_unit = g_params['单笔最大风险'] * account_equity
        current_close = Close()[-1] if len(Close()) > 0 else 0
        contract_value = current_close * g_params['合约乘数']

        # 计算ATR
        if len(Close()) >= g_params['ATR计算周期']:
            atr_value = ATR(High(), Low(), Close(), g_params['ATR计算周期'])[-1]
        else:
            atr_value = current_close * 0.02  # 默认2%波动

        base_size = int(risk_unit / (atr_value * contract_value)) if atr_value > 0 and contract_value > 0 else 1

        # 流动性评分(0-1)
        bid_vol1 = Q_BidVolume(0)
        ask_vol1 = Q_AskVolume(0)
        total_liquidity = bid_vol1 + ask_vol1
        liquidity_score = min(1.0, total_liquidity / (2 * g_params['最低流动性阈值']))

        # 波动率评分(0-1.5)
        volatility_score = 0.5 + volatility_ratio / 0.02

        # 趋势评分(基于趋势置信度)
        trend_score = 0.5 + abs(trend_confidence) / 200

        # 预估滑点成本(单位:手)
        slippage_cost = max(1, int(volatility_score * 2))

        # 综合仓位计算
        position_size = max(1, int(
            base_size *
            (0.4 + 0.6 * liquidity_score) *      # 流动性影响40-100%
            (0.7 + 0.3 * volatility_score) *     # 波动率影响70-115%
            (0.8 + 0.2 * trend_score) -          # 趋势影响80-110%
            slippage_cost                         # 扣除滑点成本
        ))

        LogInfo(f"【仓位】基础: {base_size}, 最终: {position_size}, 滑点: {slippage_cost}")

    except Exception as e:
        LogError(f"仓位计算异常: {str(e)}")
        position_size = 1

def risk_control_check():
    """
    风险控制检查
    """
    global trading_paused, loss_streak, position_size

    try:
        # 单日最大亏损检查
        current_profit_ratio = A_FloatProfit() / account_equity if account_equity > 0 else 0
        if current_profit_ratio < g_params['单日止损线']:
            # 强制平仓
            close_all_positions()
            trading_paused = True
            LogWarn(f"【风控】单日最大损失触发: {current_profit_ratio:.2%}")
            return

        # 连续亏损控制
        total_position = A_BuyPosition() + A_SellPosition()
        if total_position == 0:  # 无持仓时检查上次交易结果
            last_profit = A_LastProfit()
            if last_profit < 0:
                loss_streak += 1
                if loss_streak >= 3:
                    position_size = max(1, int(position_size * 0.5))
                    LogWarn(f"【风控】连续亏损降仓至: {position_size}")
            else:
                loss_streak = 0

        # 盈利保护
        if daily_max_profit > 0 and A_FloatProfit() < daily_max_profit * 0.7:
            LogWarn("【保护】回撤超过30%，部分平仓")
            partial_close_positions(0.5)

    except Exception as e:
        LogError(f"风险控制检查异常: {str(e)}")

def execute_trading_signals(long_signal, short_signal):
    """
    交易执行模块 - 使用封装的交易函数
    """
    global last_trade_time

    try:
        current_time_ms = int(time.time() * 1000)

        # 检查交易间隔
        if current_time_ms - last_trade_time < g_params['交易间隔']:
            return

        # 判断是否为实时阶段
        if is_real_time():
            # 实时阶段使用tim_trigger函数
            tim_trigger(long_signal, short_signal, position_size, 超价跳数, 基准合约)

            # 检查是否需要平仓
            check_exit_signals()
        else:
            # 历史阶段使用his_trigger函数
            his_trigger(long_signal, short_signal, position_size, 超价跳数)

            # 检查是否需要平仓
            check_exit_signals_historical()

        if long_signal or short_signal:
            last_trade_time = current_time_ms

    except Exception as e:
        LogError(f"交易执行异常: {str(e)}")

def check_exit_signals():
    """
    检查平仓信号 - 实时阶段
    """
    try:
        # 获取当前持仓
        long_position = A_BuyPosition(基准合约) if 'A_BuyPosition' in globals() else 0
        short_position = A_SellPosition(基准合约) if 'A_SellPosition' in globals() else 0

        # 生成平仓信号
        exit_long, exit_short = generate_exit_signals()

        # 执行平仓
        if exit_long and long_position > 0:
            tim_trigger_Exit(False, True, 平仓超价跳数, 基准合约, long_position)

        if exit_short and short_position > 0:
            tim_trigger_Exit(True, False, 平仓超价跳数, 基准合约, short_position)

    except Exception as e:
        LogError(f"实时平仓检查异常: {str(e)}")

def check_exit_signals_historical():
    """
    检查平仓信号 - 历史阶段
    """
    try:
        # 获取当前持仓
        long_position = BuyPosition() if 'BuyPosition' in globals() else 0
        short_position = SellPosition() if 'SellPosition' in globals() else 0

        # 生成平仓信号
        exit_long, exit_short = generate_exit_signals()

        # 执行平仓
        if exit_long and long_position > 0:
            his_trigger_Exit(False, True, 平仓超价跳数, long_position)

        if exit_short and short_position > 0:
            his_trigger_Exit(True, False, 平仓超价跳数, short_position)

    except Exception as e:
        LogError(f"历史平仓检查异常: {str(e)}")

def generate_exit_signals():
    """
    生成平仓信号
    """
    try:
        current_close = Close()[-1] if len(Close()) > 0 else 0
        current_time_ms = int(time.time() * 1000)

        # 多头平仓信号
        exit_long = False
        exit_score_long = 0

        # 短期趋势反转
        close_prices = Close()
        if len(close_prices) >= 5:
            ma5 = np.mean(close_prices[-5:])
            if current_close < ma5:
                exit_score_long += 30

        # 订单流反转
        if flow_strength > 20:
            exit_score_long += 25

        # 价格新低
        if len(close_prices) >= 3:
            lowest_3 = min(close_prices[-3:])
            if current_close < lowest_3:
                exit_score_long += 20

        # 超时检查
        if BKS == 1:  # 有多头持仓
            # 这里可以添加持仓时间检查逻辑
            pass

        exit_long = exit_score_long >= 50

        # 空头平仓信号
        exit_short = False
        exit_score_short = 0

        # 短期趋势反转
        if len(close_prices) >= 5:
            ma5 = np.mean(close_prices[-5:])
            if current_close > ma5:
                exit_score_short += 30

        # 订单流反转
        if flow_strength < -20:
            exit_score_short += 25

        # 价格新高
        if len(close_prices) >= 3:
            highest_3 = max(close_prices[-3:])
            if current_close > highest_3:
                exit_score_short += 20

        exit_short = exit_score_short >= 50

        return exit_long, exit_short

    except Exception as e:
        LogError(f"平仓信号生成异常: {str(e)}")
        return False, False

def manage_existing_positions():
    """
    管理现有持仓 - 动态止盈止损
    """
    try:
        current_close = Close()[-1] if len(Close()) > 0 else 0
        current_time_ms = int(time.time() * 1000)
        bid_price = Q_BidPrice(0)
        ask_price = Q_AskPrice(0)
        min_price = g_params['最小价格变动']

        # 多头持仓处理
        long_position = A_BuyPosition()
        if long_position > 0:
            avg_price = A_BuyAvgPrice()
            current_profit = (current_close - avg_price) * g_params['合约乘数'] * long_position
            profit_ratio = current_profit / (account_equity * g_params['单笔最大风险']) if account_equity > 0 else 0

            # 动态退出机制 - 多条件组合
            exit_score = 0

            # 短期趋势反转
            close_prices = Close()
            if len(close_prices) >= 5:
                ma5 = MA(close_prices, 5)[-1]
                if current_close < ma5:
                    exit_score += 30

            # 订单流反转
            if flow_strength > 20:
                exit_score += 25

            # 价格新低
            if len(close_prices) >= 3:
                lowest_3 = min(close_prices[-3:])
                if current_close < lowest_3:
                    exit_score += 20

            # 超时
            entry_time = A_LastEntryTime()
            if current_time_ms - entry_time > g_params['最大持仓时间'] * 1000:
                exit_score += 15

            # 亏损扩大
            if profit_ratio < -1:
                exit_score += 10

            # 根据评分确定退出
            if exit_score >= 50:
                exit_price = bid_price - min_price * (3 if exit_score > 70 else 1)
                Sell(long_position, exit_price)
                LogInfo(f"【多头平仓】评分: {exit_score}, 价格: {exit_price:.1f}")

        # 空头持仓处理
        short_position = A_SellPosition()
        if short_position > 0:
            avg_price = A_SellAvgPrice()
            current_profit = (avg_price - current_close) * g_params['合约乘数'] * short_position
            profit_ratio = current_profit / (account_equity * g_params['单笔最大风险']) if account_equity > 0 else 0

            # 动态退出机制 - 多条件组合
            exit_score = 0

            # 短期趋势反转
            close_prices = Close()
            if len(close_prices) >= 5:
                ma5 = MA(close_prices, 5)[-1]
                if current_close > ma5:
                    exit_score += 30

            # 订单流反转
            if flow_strength < -20:
                exit_score += 25

            # 价格新高
            if len(close_prices) >= 3:
                highest_3 = max(close_prices[-3:])
                if current_close > highest_3:
                    exit_score += 20

            # 超时
            entry_time = A_LastEntryTime()
            if current_time_ms - entry_time > g_params['最大持仓时间'] * 1000:
                exit_score += 15

            # 亏损扩大
            if profit_ratio < -1:
                exit_score += 10

            # 根据评分确定退出
            if exit_score >= 50:
                exit_price = ask_price + min_price * (3 if exit_score > 70 else 1)
                BuyToCover(short_position, exit_price)
                LogInfo(f"【空头平仓】评分: {exit_score}, 价格: {exit_price:.1f}")

    except Exception as e:
        LogError(f"持仓管理异常: {str(e)}")

def check_market_close():
    """
    收盘平仓检查
    """
    try:
        current_time_float = Time()

        # 检查是否接近收盘时间 (14:55:00)
        if current_time_float >= 0.1455:
            close_all_positions()
            LogInfo("【收盘平仓】强制平仓所有持仓")

    except Exception as e:
        LogError(f"收盘检查异常: {str(e)}")

def close_all_positions():
    """
    强制平仓所有持仓
    """
    try:
        if is_real_time():
            # 实时阶段
            long_position = A_BuyPosition(基准合约) if 'A_BuyPosition' in globals() else 0
            short_position = A_SellPosition(基准合约) if 'A_SellPosition' in globals() else 0

            if long_position > 0:
                tim_trigger_Exit(False, True, 平仓超价跳数, 基准合约, long_position)
                LogInfo(f"【强制平多】手数: {long_position}")

            if short_position > 0:
                tim_trigger_Exit(True, False, 平仓超价跳数, 基准合约, short_position)
                LogInfo(f"【强制平空】手数: {short_position}")
        else:
            # 历史阶段
            long_position = BuyPosition() if 'BuyPosition' in globals() else 0
            short_position = SellPosition() if 'SellPosition' in globals() else 0

            if long_position > 0:
                his_trigger_Exit(False, True, 平仓超价跳数, long_position)
                LogInfo(f"【强制平多】手数: {long_position}")

            if short_position > 0:
                his_trigger_Exit(True, False, 平仓超价跳数, short_position)
                LogInfo(f"【强制平空】手数: {short_position}")

    except Exception as e:
        LogError(f"强制平仓异常: {str(e)}")

def partial_close_positions(ratio):
    """
    部分平仓
    """
    try:
        if is_real_time():
            # 实时阶段
            long_position = A_BuyPosition(基准合约) if 'A_BuyPosition' in globals() else 0
            short_position = A_SellPosition(基准合约) if 'A_SellPosition' in globals() else 0

            if long_position > 0:
                close_size = max(1, int(long_position * ratio))
                tim_trigger_Exit(False, True, 平仓超价跳数, 基准合约, close_size)
                LogInfo(f"【部分平多】手数: {close_size}/{long_position}")

            if short_position > 0:
                close_size = max(1, int(short_position * ratio))
                tim_trigger_Exit(True, False, 平仓超价跳数, 基准合约, close_size)
                LogInfo(f"【部分平空】手数: {close_size}/{short_position}")
        else:
            # 历史阶段
            long_position = BuyPosition() if 'BuyPosition' in globals() else 0
            short_position = SellPosition() if 'SellPosition' in globals() else 0

            if long_position > 0:
                close_size = max(1, int(long_position * ratio))
                his_trigger_Exit(False, True, 平仓超价跳数, close_size)
                LogInfo(f"【部分平多】手数: {close_size}/{long_position}")

            if short_position > 0:
                close_size = max(1, int(short_position * ratio))
                his_trigger_Exit(True, False, 平仓超价跳数, close_size)
                LogInfo(f"【部分平空】手数: {close_size}/{short_position}")

    except Exception as e:
        LogError(f"部分平仓异常: {str(e)}")

def cancel_pending_orders():
    """
    取消未成交订单
    """
    try:
        # 获取所有未成交订单并取消
        pending_orders = A_AllQueueOrderNo()
        for order_id in pending_orders:
            A_DeleteOrder(order_id)

    except Exception as e:
        LogError(f"取消订单异常: {str(e)}")

def update_visualization():
    """
    可视化更新
    """
    try:
        current_close = Close()[-1] if len(Close()) > 0 else 0

        # 绘制关键价位线
        PlotNumeric('观察卖出价', observe_sell_price, 0xFF0000, False)  # 红色
        PlotNumeric('观察买入价', observe_buy_price, 0x0000FF, False)   # 蓝色

        # 绘制移动平均线
        close_prices = Close()
        if len(close_prices) >= 20:
            ma5 = MA(close_prices, 5)[-1]
            ma20 = MA(close_prices, 20)[-1]
            PlotNumeric('MA5', ma5, 0x00FFFF, False)      # 青色
            PlotNumeric('MA20', ma20, 0xFF00FF, False)    # 紫色

        # 绘制订单流强度
        PlotBar('订单流强度', flow_strength, 0xFF0000 if flow_strength > 0 else 0x00FF00, False, True, 1)

        # 绘制趋势置信度
        PlotBar('趋势置信度', trend_confidence, 0xFF0000 if trend_confidence > 0 else 0x0000FF, False, True, 2)

        # 绘制大单阈值信息
        PlotNumeric('大单阈值', big_vol, 0x808080, False)

        # 持仓信息显示
        long_position = A_BuyPosition()
        short_position = A_SellPosition()

        if long_position > 0:
            avg_price = A_BuyAvgPrice()
            profit = (current_close - avg_price) * g_params['合约乘数'] * long_position
            LogInfo(f"【持多】{long_position}手, 均价: {avg_price:.1f}, 盈亏: {profit:.2f}")

        if short_position > 0:
            avg_price = A_SellAvgPrice()
            profit = (avg_price - current_close) * g_params['合约乘数'] * short_position
            LogInfo(f"【持空】{short_position}手, 均价: {avg_price:.1f}, 盈亏: {profit:.2f}")

    except Exception as e:
        LogError(f"可视化更新异常: {str(e)}")

# ==================== 机智量化特有功能 ====================

def on_order_filled(context, order):
    """
    订单成交回调函数
    """
    try:
        LogInfo(f"【订单成交】订单ID: {order.order_id}, 方向: {order.direction}, 数量: {order.filled_qty}, 价格: {order.filled_price:.1f}")

        # 更新交易统计
        update_trade_statistics(order)

    except Exception as e:
        LogError(f"订单成交回调异常: {str(e)}")

def on_order_cancelled(context, order):
    """
    订单取消回调函数
    """
    try:
        LogWarn(f"【订单取消】订单ID: {order.order_id}, 方向: {order.direction}, 数量: {order.order_qty}")

    except Exception as e:
        LogError(f"订单取消回调异常: {str(e)}")

def update_trade_statistics(order):
    """
    更新交易统计信息
    """
    try:
        # 这里可以添加交易统计逻辑
        # 比如胜率、盈亏比、最大回撤等
        pass

    except Exception as e:
        LogError(f"更新交易统计异常: {str(e)}")

def performance_monitor():
    """
    性能监控函数
    """
    try:
        # 监控策略性能指标
        total_assets = A_TotalAssets()
        available_funds = A_Available()
        float_profit = A_FloatProfit()

        # 计算资金使用率
        fund_usage_ratio = (total_assets - available_funds) / total_assets if total_assets > 0 else 0

        # 记录关键性能指标
        if CurrentBar() % 100 == 0:  # 每100个Bar记录一次
            LogInfo(f"【性能监控】总资产: {total_assets:.2f}, 可用资金: {available_funds:.2f}, 浮动盈亏: {float_profit:.2f}, 资金使用率: {fund_usage_ratio:.2%}")

    except Exception as e:
        LogError(f"性能监控异常: {str(e)}")

def data_quality_check():
    """
    数据质量检查
    """
    try:
        # 检查K线数据完整性
        open_data = Open()
        high_data = High()
        low_data = Low()
        close_data = Close()
        volume_data = Vol()

        if len(open_data) == 0 or len(high_data) == 0 or len(low_data) == 0 or len(close_data) == 0:
            LogWarn("【数据质量】K线数据不完整")
            return False

        # 检查行情数据有效性
        bid_price = Q_BidPrice(0)
        ask_price = Q_AskPrice(0)

        if bid_price <= 0 or ask_price <= 0 or ask_price <= bid_price:
            LogWarn(f"【数据质量】行情数据异常 - 买一: {bid_price:.1f}, 卖一: {ask_price:.1f}")
            return False

        return True

    except Exception as e:
        LogError(f"数据质量检查异常: {str(e)}")
        return False

def emergency_stop():
    """
    紧急停止函数
    """
    global trading_paused

    try:
        LogError("【紧急停止】触发紧急停止机制")

        # 取消所有未成交订单
        cancel_pending_orders()

        # 强制平仓所有持仓
        close_all_positions()

        # 暂停交易
        trading_paused = True

        # 发送紧急通知
        Alert("Alpha-Tick Pro策略触发紧急停止", True, 'Error')

    except Exception as e:
        LogError(f"紧急停止异常: {str(e)}")

def strategy_health_check():
    """
    策略健康检查
    """
    try:
        # 检查关键变量是否正常
        if big_vol <= 0:
            LogWarn("【健康检查】大单阈值异常")
            return False

        if observe_sell_price <= 0 or observe_buy_price <= 0:
            LogWarn("【健康检查】R-Breaker价位异常")
            return False

        if account_equity <= 0:
            LogWarn("【健康检查】账户净值异常")
            return False

        # 检查数据质量
        if not data_quality_check():
            return False

        return True

    except Exception as e:
        LogError(f"策略健康检查异常: {str(e)}")
        return False

def adaptive_parameter_adjustment():
    """
    自适应参数调整
    """
    try:
        # 根据市场状况动态调整参数
        # 这里可以添加机器学习或统计学习算法

        # 示例：根据波动率调整大单阈值倍数
        if volatility_ratio > 0.025:  # 高波动市场
            adjusted_ratio = g_params['大单标准差倍数'] * 1.2
        elif volatility_ratio < 0.01:  # 低波动市场
            adjusted_ratio = g_params['大单标准差倍数'] * 0.8
        else:
            adjusted_ratio = g_params['大单标准差倍数']

        # 记录参数调整
        if abs(adjusted_ratio - g_params['大单标准差倍数']) > 0.1:
            LogInfo(f"【参数调整】大单标准差倍数: {g_params['大单标准差倍数']:.2f} -> {adjusted_ratio:.2f}")

    except Exception as e:
        LogError(f"自适应参数调整异常: {str(e)}")

# ==================== 主策略入口优化 ====================

def handle_data_enhanced(context):
    """
    增强版主策略逻辑函数
    """
    global first_run

    try:
        # 策略健康检查
        if not strategy_health_check():
            LogWarn("【策略状态】健康检查失败，跳过本次执行")
            return

        # 性能监控
        performance_monitor()

        # 自适应参数调整
        adaptive_parameter_adjustment()

        # 执行原有策略逻辑
        handle_data(context)

    except Exception as e:
        LogError(f"增强版策略执行异常: {str(e)}")
        # 触发紧急停止
        emergency_stop()

# ==================== 策略配置信息 ====================

def get_strategy_info():
    """
    获取策略配置信息
    """
    strategy_info = {
        "策略名称": "Alpha-Tick Pro RB 螺纹钢高频策略",
        "版本": "机智量化版070403",
        "适用品种": "螺纹钢期货(RB)",
        "策略类型": "高频交易",
        "风险等级": "中高风险",
        "建议资金": "50万以上",
        "参数配置": {
            "大单标准差倍数": g_params['大单标准差倍数'],
            "基础突破系数": g_params['基础突破系数'],
            "单笔最大风险": f"{g_params['单笔最大风险']*100:.1f}%",
            "单日止损线": f"{g_params['单日止损线']*100:.1f}%"
        }
    }
    return strategy_info

def print_strategy_info():
    """
    打印策略信息
    """
    info = get_strategy_info()
    LogInfo("=" * 60)
    LogInfo(f"策略名称: {info['策略名称']}")
    LogInfo(f"版本: {info['版本']}")
    LogInfo(f"适用品种: {info['适用品种']}")
    LogInfo(f"策略类型: {info['策略类型']}")
    LogInfo(f"风险等级: {info['风险等级']}")
    LogInfo(f"建议资金: {info['建议资金']}")
    LogInfo("参数配置:")
    for key, value in info['参数配置'].items():
        LogInfo(f"  {key}: {value}")
    LogInfo("=" * 60)

# ==================== 策略启动入口 ====================

# 在策略启动时打印配置信息
if __name__ == "__main__":
    print_strategy_info()

# 注意：实际使用时，请将 handle_data_enhanced 作为主入口函数
# 或者在 handle_data 函数开头添加健康检查和性能监控
