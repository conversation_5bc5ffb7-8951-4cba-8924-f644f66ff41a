import os
import time
import json
import pickle
import threading
import datetime
import random
import mmap
import struct
import logging
import psutil
import traceback
import numpy as np
from threading import Lock
from logging.handlers import TimedRotatingFileHandler
from xtquant.xttype import StockAccount
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
import tempfile

# 共享内存区域常量定义
MEM_SIZE = 1024 * 1024  # 1MB共享内存区
HEADER_SIZE = 64        # 头部信息大小
COMMAND_SIZE = 256 * 1024  # 命令区域大小
RESPONSE_SIZE = 256 * 1024  # 响应区域大小
DATA_SIZE = 512 * 1024  # 数据区域大小

# 共享内存区域名称
SHMEM_NAME = "QMT_TRADING_GATEWAY"
SHMEM_FILE = os.path.join(os.path.expanduser("~"), "QMT_TRADING_GATEWAY.dat")

# 状态和命令常量
CMD_NONE = 0
CMD_CONNECT = 1
CMD_PLACE_ORDER = 2
CMD_CANCEL_ORDER = 3
CMD_QUERY = 4
CMD_SHUTDOWN = 99

RESP_NONE = 0
RESP_SUCCESS = 1
RESP_FAILURE = 2
RESP_DATA = 3

# 数据区域索引
DATA_ACCOUNT = 0
DATA_POSITIONS = 1
DATA_ORDERS = 2
DATA_TRADES = 3
DATA_STATUS = 4

class SharedMemory:
    """共享内存类，用于交易网关与策略之间的通信"""
    
    def __init__(self, mem_name="trading_gateway", create=True):
        """初始化共享内存
        Args:
            mem_name: 共享内存名称
            create: 是否创建新的共享内存
        """
        self.mem_name = mem_name
        self.is_initialized = False
        self.mm = None
        self.file = None
        self.lock = threading.RLock()
        
        try:
            file_path = os.path.join(tempfile.gettempdir(), f"{mem_name}.dat")
            
            if create:
                if os.path.exists(file_path):
                    try:
                        # 尝试打开已存在的文件
                        self.file = open(file_path, "r+b")
                        # 验证文件大小
                        if os.path.getsize(file_path) != MEM_SIZE:
                            self.file.close()
                            os.remove(file_path)
                            self.file = open(file_path, "w+b")
                            self.file.write(b'\x00' * MEM_SIZE)
                            self.file.flush()
                    except Exception as e:
                        logging.error(f"打开已存在的共享内存文件失败: {str(e)}")
                        # 重新创建文件
                        if self.file:
                            self.file.close()
                        try:
                            os.remove(file_path)
                        except:
                            pass
                        self.file = open(file_path, "w+b")
                        self.file.write(b'\x00' * MEM_SIZE)
                        self.file.flush()
                else:
                    # 创建新文件
                    self.file = open(file_path, "w+b")
                    self.file.write(b'\x00' * MEM_SIZE)
                    self.file.flush()
            else:
                # 仅打开现有文件
                if os.path.exists(file_path):
                    self.file = open(file_path, "r+b")
                else:
                    logging.warning(f"共享内存文件不存在: {file_path}")
                    return
            
            # 创建内存映射
            try:
                self.mm = mmap.mmap(self.file.fileno(), MEM_SIZE)
                self.is_initialized = True
                logging.info(f"共享内存初始化成功: {file_path}")
            except Exception as e:
                logging.error(f"创建内存映射失败: {str(e)}")
                if self.file:
                    self.file.close()
                    self.file = None
        except Exception as e:
            logging.error(f"初始化共享内存失败: {str(e)}")
            if self.file:
                self.file.close()
                self.file = None
    
    def _check_initialized(self):
        """检查共享内存是否已初始化"""
        if not self.is_initialized or self.mm is None:
            logging.error("共享内存未初始化")
            return False
        return True
    
    def set_command(self, cmd_type, data):
        """设置命令
        Args:
            cmd_type: 命令类型
            data: 命令数据
        """
        if not self._check_initialized():
            return False
        
        with self.lock:
            try:
                cmd_data = {
                    "type": cmd_type,
                    "data": data,
                    "timestamp": time.time()
                }
                json_data = json.dumps(cmd_data)
                
                # 计算数据长度和偏移量
                data_len = len(json_data)
                if data_len > COMMAND_SIZE:
                    logging.error(f"命令数据过大: {data_len} > {COMMAND_SIZE}")
                    return False
                
                # 写入命令长度
                self.mm.seek(0)
                self.mm.write(struct.pack("I", data_len))
                
                # 写入命令数据
                self.mm.seek(HEADER_SIZE)
                self.mm.write(json_data.encode())
                
                # 更新命令状态
                self.mm.seek(HEADER_SIZE + COMMAND_SIZE)
                self.mm.write(struct.pack("I", 1))  # 1表示有新命令
                
                return True
            except Exception as e:
                logging.error(f"设置命令失败: {str(e)}")
                return False
    
    def get_command(self):
        """获取命令"""
        if not self._check_initialized():
            return None
        
        with self.lock:
            try:
                # 检查命令状态
                self.mm.seek(HEADER_SIZE + COMMAND_SIZE)
                cmd_status = struct.unpack("I", self.mm.read(4))[0]
                
                if cmd_status != 1:
                    return None
                
                # 读取命令长度
                self.mm.seek(0)
                cmd_len = struct.unpack("I", self.mm.read(4))[0]
                
                # 读取命令数据
                self.mm.seek(HEADER_SIZE)
                cmd_data = self.mm.read(cmd_len).decode()
                
                return json.loads(cmd_data)
            except Exception as e:
                logging.error(f"获取命令失败: {str(e)}")
                return None
    
    def clear_command(self):
        """清除命令"""
        if not self._check_initialized():
            return False
        
        with self.lock:
            try:
                # 更新命令状态
                self.mm.seek(HEADER_SIZE + COMMAND_SIZE)
                self.mm.write(struct.pack("I", 0))  # 0表示无命令
                return True
            except Exception as e:
                logging.error(f"清除命令失败: {str(e)}")
                return False
    
    def set_response(self, resp, data=None):
        """设置响应"""
        if not self._check_initialized():
            logging.error(f"无法设置响应{resp}：共享内存未初始化")
            return False
            
        try:
            with self.lock:
                # 写入响应区域
                self.mm.seek(4)
                self.mm.write(struct.pack('I', resp))  # 响应标志
                
                # 写入响应数据
                if data:
                    self.mm.seek(HEADER_SIZE + COMMAND_SIZE)
                    serialized = pickle.dumps(data)
                    self.mm.write(serialized[:RESPONSE_SIZE])
            return True
        except Exception as e:
            logging.error(f"设置响应失败: {str(e)}")
            return False
    
    def get_response(self):
        """获取响应"""
        if not self._check_initialized():
            logging.error("无法获取响应：共享内存未初始化")
            return RESP_NONE, None
            
        try:
            with self.lock:
                self.mm.seek(4)
                resp = struct.unpack('I', self.mm.read(4))[0]
                
                if resp != RESP_NONE:
                    # 读取响应数据
                    self.mm.seek(HEADER_SIZE + COMMAND_SIZE)
                    data = self.mm.read(RESPONSE_SIZE)
                    try:
                        # 尝试反序列化直到遇到EOF
                        data = pickle.loads(data)
                    except:
                        data = None
                    
                    return resp, data
                
                return RESP_NONE, None
        except Exception as e:
            logging.error(f"获取响应失败: {str(e)}")
            return RESP_NONE, None
    
    def clear_response(self):
        """清除响应"""
        if not self._check_initialized():
            logging.error("无法清除响应：共享内存未初始化")
            return False
            
        try:
            with self.lock:
                self.mm.seek(4)
                self.mm.write(struct.pack('I', RESP_NONE))
            return True
        except Exception as e:
            logging.error(f"清除响应失败: {str(e)}")
            return False
    
    def set_data(self, key, data):
        """设置数据
        Args:
            key: 数据键名
            data: 数据内容
        """
        if not self._check_initialized():
            return False
        
        with self.lock:
            try:
                # 首先检查是否已有数据
                existing_data = self.get_data(key) or {}
                
                # 更新数据而不是完全替换
                if isinstance(data, dict) and isinstance(existing_data, dict):
                    # 合并现有数据和新数据
                    existing_data.update(data)
                    data_to_write = existing_data
                else:
                    # 如果数据不是字典，或者现有数据不是字典，则直接替换
                    data_to_write = data
                
                # 确保健康状态被保留（如果之前已经设置）
                if isinstance(data_to_write, dict) and key == DATA_STATUS:
                    if "is_healthy" in existing_data and "is_healthy" not in data_to_write:
                        data_to_write["is_healthy"] = existing_data["is_healthy"]
                
                json_data = json.dumps(data_to_write)
                
                # 计算数据长度和偏移量
                data_len = len(json_data)
                
                if key == DATA_STATUS:
                    offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE
                    max_size = DATA_SIZE
                elif key == DATA_ACCOUNT:
                    offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + DATA_SIZE // 5
                    max_size = DATA_SIZE // 5
                elif key == DATA_POSITIONS:
                    offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + DATA_SIZE // 5 * 2
                    max_size = DATA_SIZE // 5
                elif key == DATA_ORDERS:
                    offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + DATA_SIZE // 5 * 3
                    max_size = DATA_SIZE // 5
                elif key == DATA_TRADES:
                    offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + DATA_SIZE // 5 * 4
                    max_size = DATA_SIZE // 5
                else:
                    logging.error(f"未知数据键名: {key}")
                    return False
                
                if data_len > max_size:
                    logging.error(f"数据过大: {data_len} > {max_size}")
                    return False
                
                # 写入数据长度
                self.mm.seek(offset)
                self.mm.write(struct.pack("I", data_len))
                
                # 写入数据
                self.mm.seek(offset + 4)
                self.mm.write(json_data.encode())
                
                return True
            except Exception as e:
                logging.error(f"设置数据失败: {str(e)}")
                import traceback
                logging.error(traceback.format_exc())
                return False
    
    def get_data(self, key):
        """获取数据
        Args:
            key: 数据键名
        """
        if not self._check_initialized():
            return None
        
        with self.lock:
            try:
                # 计算偏移量
                if key == DATA_STATUS:
                    offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE
                elif key == DATA_ACCOUNT:
                    offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + DATA_SIZE // 5
                elif key == DATA_POSITIONS:
                    offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + DATA_SIZE // 5 * 2
                elif key == DATA_ORDERS:
                    offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + DATA_SIZE // 5 * 3
                elif key == DATA_TRADES:
                    offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + DATA_SIZE // 5 * 4
                else:
                    logging.error(f"未知数据键名: {key}")
                    return None
                
                # 读取数据长度
                self.mm.seek(offset)
                data_len = struct.unpack("I", self.mm.read(4))[0]
                
                if data_len == 0:
                    return None
                
                # 读取数据
                self.mm.seek(offset + 4)
                data = self.mm.read(data_len).decode()
                
                try:
                    return json.loads(data)
                except:
                    return None
            except Exception as e:
                logging.error(f"获取数据失败: {str(e)}")
                return None
    
    def close(self):
        """关闭共享内存"""
        with self.lock:
            try:
                if self.mm:
                    self.mm.close()
                    self.mm = None
                
                if self.file:
                    self.file.close()
                    self.file = None
                
                self.is_initialized = False
            except Exception as e:
                logging.error(f"关闭共享内存失败: {str(e)}")
    
    def __del__(self):
        """析构函数"""
        self.close()

# 交易回调对象
class GatewayCallback(XtQuantTraderCallback):
    def __init__(self, gateway):
        self.gateway = gateway
    
    def on_disconnected(self):
        self.logger.warning("[网关] 连接断开")
        self.gateway.connected = False
        self.gateway.status["connected"] = False
        self.gateway.status["last_disconnect"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    def on_order_stock_async_response(self, response):
        self.logger.info(f"[网关] 异步下单响应: {response.order_id}, 备注: {response.order_remark}")
    def on_stock_order(self, order):
        self.logger.info(f"[网关] 委托回报: {order.order_id}, 状态: {order.order_status}")
        with self.gateway.lock:
            self.gateway.orders[order.order_id] = {
                "order_id": order.order_id,
                "stock_code": order.stock_code,
                "order_status": order.order_status,
                "order_time": order.order_time,
                "traded_volume": order.traded_volume,
                "traded_price": order.traded_price,
                "order_volume": order.order_volume,
                "price": order.price,
                "order_remark": order.order_remark
            }
    
    def on_stock_trade(self, trade):
        self.logger.info(f"[网关] 成交回报: {trade.order_id}, 数量: {trade.traded_volume}")
        with self.gateway.lock:
            # 保存成交记录
            trade_key = f"{trade.order_id}_{trade.traded_id}"
            self.gateway.trades[trade_key] = {
                "order_id": trade.order_id,
                "stock_code": trade.stock_code,
                "traded_id": trade.traded_id,
                "traded_time": trade.traded_time,
                "traded_volume": trade.traded_volume,
                "traded_price": trade.traded_price
            }
            
            # 更新订单信息
            if trade.order_id in self.gateway.orders:
                self.gateway.orders[trade.order_id]["traded_volume"] = trade.traded_volume
                self.gateway.orders[trade.order_id]["traded_price"] = trade.traded_price
    
    def on_order_error(self, error):
        self.logger.error(f"[网关] 委托错误: {error.error_msg}")
        self.gateway.status["last_error"] = {
            "time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "message": error.error_msg,
            "order_id": error.order_id
        }

class TradingGatewayService:
    """交易网关服务类"""
    def __init__(self):
        """初始化网关服务"""
        # 配置信息
        self.config = {}
        self._load_config()
        
        # 初始化日志
        self._init_logger()
        
        # 状态字典
        self.status = {
            "running": False,
            "connected": False,
            "last_heartbeat": time.time(),
            "is_healthy": False,
            "start_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 初始化共享内存
        try:
            self.shmem = SharedMemory(create=True)
        except Exception as e:
            logging.error(f"初始化共享内存失败: {str(e)}")
            self.shmem = None
        
        # 交易相关的实例变量
        self.trader = None
        self.account = None
        self.running = False
        self.service_thread = None
        self.monitor_thread = None
        
        # 控制变量
        self.is_initialized = False
        self.last_health_check = time.time()
        self.client_path = self.config.get("client_path", "")
        self.account_id = self.config.get("account_id", "")
        
        # 数据缓存
        self.account_data = {}
        self.positions = {}
        self.orders = {}
        self.trades = {}
        
        # 同步锁
        self.lock = Lock()
        
        # 创建回调对象
        self.callback = GatewayCallback(self)

    def _load_config(self):
        """从同目录加载配置文件"""
        config_path = os.path.join(os.path.dirname(__file__), "gateway_config.json")
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {str(e)}")
            raise SystemExit("无法启动服务：缺少配置文件")

    def _init_logger(self):
        """初始化日志记录器"""
        logger = logging.getLogger("TradingGateway")
        logger.setLevel(logging.INFO)
        
        # 创建每天轮转的日志文件
        handler = TimedRotatingFileHandler(
            filename="gateway_service.log",
            when="midnight",
            backupCount=7,
            encoding='utf-8'
        )
        formatter = logging.Formatter(
            '[%(asctime)s] [%(levelname)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        self.logger = logger
    
    def start(self):
        """启动服务"""
        try:
            logging.info("正在启动交易网关服务...")
            
            # 初始化共享内存
            self.shmem = SharedMemory(create=True)
            if not self.shmem.is_initialized:
                logging.error("共享内存初始化失败")
                return False
            
            # 初始化状态
            self.status = {
                "running": True,
                "connected": False,
                "last_heartbeat": time.time(),
                "is_healthy": False  # 初始化为不健康状态
            }
            self.shmem.set_data(DATA_STATUS, self.status)
            
            # 启动服务线程
            self.running = True
            self.service_thread = threading.Thread(target=self._service_loop)
            self.service_thread.daemon = True
            self.service_thread.start()
            
            # 启动监控线程
            self._start_monitor_thread()
            
            # 记录启动信息
            logging.info(f"服务启动时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            logging.info(f"客户端路径: {self.config['client_path']}")
            logging.info(f"账号ID: {self.config['account_id']}")
            
            # 服务初始化完成标志
            self.is_initialized = True
            
            # 初始化最后健康检查时间
            self.last_health_check = time.time()
            
            return True
        except Exception as e:
            logging.error(f"启动服务失败: {str(e)}")
            traceback.print_exc()
            return False
    
    def _service_loop(self):
        """服务主循环"""
        try:
            while self.running:
                try:
                    # 获取命令
                    command_data = self.shmem.get_command()
                    
                    if command_data:
                        # 从JSON对象中提取命令类型和数据
                        cmd_type = command_data.get("type")
                        data = command_data.get("data")
                        
                        # 处理命令
                        if cmd_type is not None:
                            self._handle_command(cmd_type, data)
                        
                        # 清除命令
                        self.shmem.clear_command()
                    
                    # 更新数据
                    if self.trader and self.trader.is_connected():
                        self._update_data()
                    
                    # 检查健康状态
                    if time.time() - self.last_health_check > 10:  # 每10秒检查一次
                        self._health_check()
                        self.last_health_check = time.time()
                    
                    time.sleep(0.1)  # 避免CPU占用过高
                
                except KeyboardInterrupt:
                    logging.info("[网关] 收到中断信号，关闭服务...")
                    self.running = False
                    break
                except Exception as e:
                    error_message = str(e)
                    tb = traceback.format_exc()
                    logging.error(f"服务循环异常: {error_message}")
                    logging.debug(tb)
                    
                    # 更新状态数据，标记为不健康
                    self.status["last_error"] = error_message
                    self.status["error_time"] = time.time()
                    self.status["is_healthy"] = False
                    self.shmem.set_data(DATA_STATUS, self.status)
                    
                    # 尝试恢复连接
                    if "connect" in self.status and self.status["connected"]:
                        if hasattr(self, "client_path") and hasattr(self, "account_id"):
                            self._auto_reconnect(self.client_path, self.account_id)
                    
                    time.sleep(1)  # 出错后稍微等待一下再继续
        
        except Exception as e:
            logging.error(f"服务主循环致命错误: {str(e)}")
            traceback.print_exc()
        finally:
            # 确保释放资源
            if self.trader:
                logging.info("关闭交易接口连接")
                self.trader.stop()
            
            logging.info("服务循环结束")
    
    def _handle_command(self, cmd, data):
        """处理命令"""
        if cmd == CMD_CONNECT:
            # 连接命令
            result = self._connect(data.get("client_path"), data.get("account_id"))
            self.shmem.set_response(RESP_SUCCESS if result else RESP_FAILURE, 
                                   {"success": result, "message": "连接成功" if result else "连接失败"})
        
        elif cmd == CMD_PLACE_ORDER:
            # 下单命令
            order_id = self._place_order(
                data.get("symbol"),
                data.get("direction"),
                data.get("quantity"),
                data.get("price_type"),
                data.get("price"),
                data.get("strategy_name", "auto_trading"),
                data.get("remark", "")
            )
            self.shmem.set_response(RESP_SUCCESS if order_id else RESP_FAILURE, 
                                   {"success": bool(order_id), "order_id": order_id})
        
        elif cmd == CMD_CANCEL_ORDER:
            # 撤单命令
            result = self._cancel_order(data.get("order_id"))
            self.shmem.set_response(RESP_SUCCESS if result else RESP_FAILURE, 
                                   {"success": result})
        
        elif cmd == CMD_QUERY:
            # 查询命令
            query_type = data.get("query_type")
            result = None
            
            if query_type == "account":
                result = self._query_account()
            elif query_type == "positions":
                result = self._query_positions()
            elif query_type == "orders":
                result = self._query_orders()
            elif query_type == "trades":
                result = self._query_trades()
            elif query_type == "order":
                result = self._query_order(data.get("order_id"))
            
            self.shmem.set_response(RESP_DATA, {"data": result})
        
        elif cmd == CMD_SHUTDOWN:
            # 关闭命令
            self._shutdown()
            self.shmem.set_response(RESP_SUCCESS, {"message": "服务已关闭"})
    
    def _connect(self, client_path, account_id):
        """连接交易接口
        
        Args:
            client_path: 客户端路径
            account_id: 账户ID
            
        Returns:
            bool: 连接是否成功
        """
        try:
            logging.info(f"尝试连接交易接口: {client_path}, 账户: {account_id}")
            
            # 保存连接信息
            self.client_path = client_path
            self.account_id = account_id
            
            # 检查客户端路径
            if not os.path.exists(client_path):
                logging.error(f"客户端路径不存在: {client_path}")
                return False
            
            # 创建会话ID
            session_id = int(time.time())
            
            # 创建交易接口
            self.trader = XtQuantTrader(client_path, session_id)
            self.trader.register_callback(self.callback)
            
            # 启动交易接口
            self.trader.start()
            
            # 连接交易接口
            if self.trader.connect() != 0:
                logging.error("连接交易接口失败")
                return False
                
            # 创建账户对象
            self.account = StockAccount(account_id, 'STOCK')
            
            # 订阅账户消息
            if self.trader.subscribe(self.account) != 0:
                logging.error("订阅账户失败")
                return False
                
            # 更新状态
            self.status["connected"] = True
            self.status["last_connect_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.status["is_healthy"] = True  # 连接成功即设为健康状态
            self.shmem.set_data(DATA_STATUS, self.status)
            
            logging.info(f"成功连接交易接口: {client_path}, 账户: {account_id}")
            
            # 连接成功后执行的操作
            self._after_successful_connection(self.account)
            
            return True
            
        except Exception as e:
            logging.error(f"连接交易接口时发生异常: {str(e)}")
            traceback.print_exc()
            
            # 更新状态
            self.status["connected"] = False
            self.status["last_error"] = str(e)
            self.status["error_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.status["is_healthy"] = False
            self.shmem.set_data(DATA_STATUS, self.status)
            
            return False
    
    def _place_order(self, symbol, direction, quantity, price_type, price, strategy_name="auto_trading", remark=""):
        """下单"""
        if not self.trader or not self.account or not self.connected:
            self.logger.error("[网关] 交易对象或账号未初始化，或连接未建立")
            return None
        
        try:
            order_id = self.trader.order_stock(
                self.account, 
                symbol, 
                direction, 
                quantity, 
                price_type, 
                price, 
                strategy_name, 
                remark
            )
            
            if order_id:
                self.logger.info(f"[网关] 下单成功，订单ID: {order_id}")
                return order_id
            else:
                self.logger.info("[网关] 下单失败")
                return None
                
        except Exception as e:
            self.logger.error(f"[网关] 下单异常: {str(e)}")
            return None
    
    def _cancel_order(self, order_id):
        """撤单"""
        if not self.trader or not self.account or not self.connected:
            self.logger.error("[网关] 交易对象或账号未初始化，或连接未建立")
            return False
        
        try:
            result = self.trader.cancel_order_stock(self.account, order_id)
            if result == 0:
                self.logger.info(f"[网关] 撤单成功，订单ID: {order_id}")
                return True
            else:
                self.logger.error(f"[网关] 撤单失败，订单ID: {order_id}, 错误码: {result}")
                return False
                
        except Exception as e:
            self.logger.error(f"[网关] 撤单异常: {str(e)}")
            return False
    
    def _query_account(self):
        """查询账户资金信息"""
        try:
            if not self.trader or not self.trader.is_connected():
                logging.warning("交易接口未连接，无法查询账户信息")
                return None
                
            # 查询账户资金
            account_data = {}
            account_info = self.trader.query_stock_asset(self.account)
            
            if account_info:
                # 转换为字典数据
                account_data = {
                    "total_asset": account_info.total_asset,  # 总资产
                    "cash": account_info.cash,  # 现金
                    "market_value": account_info.market_value,  # 市值
                    "frozen_cash": account_info.frozen_cash,  # 冻结资金
                    "avail_cash": account_info.avail_cash,  # 可用资金
                    "pnl": account_info.pnl,  # 盈亏
                    "update_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
                logging.debug(f"账户信息查询成功: {account_data}")
                return account_data
            else:
                logging.warning("账户信息查询返回空数据")
                return None
                
        except Exception as e:
            logging.error(f"查询账户资金出错: {str(e)}")
            return None
    
    def _query_positions(self):
        """查询持仓数据"""
        try:
            if not self.trader or not self.trader.is_connected():
                logging.warning("交易接口未连接，无法查询持仓信息")
                return None
                
            # 查询持仓
            positions_data = {}
            positions = self.trader.query_stock_positions(self.account)
            
            if positions:
                # 转换为字典数据
                for pos in positions:
                    symbol = pos.stock_code  # 股票代码
                    positions_data[symbol] = {
                        "symbol": symbol,
                        "volume": pos.volume,  # 总持仓
                        "available": pos.can_use_volume,  # 可用持仓
                        "frozen": pos.volume - pos.can_use_volume,  # 冻结持仓
                        "avg_price": pos.avg_price,  # 成本价
                        "market_value": pos.market_value,  # 市值
                        "pnl": pos.pnl,  # 盈亏
                        "update_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                
                logging.debug(f"持仓查询成功，共{len(positions_data)}个持仓")
                return positions_data
            else:
                logging.debug("持仓查询结果为空")
                return {}
                
        except Exception as e:
            logging.error(f"查询持仓出错: {str(e)}")
            return None
    
    def _query_orders(self):
        """查询委托订单"""
        try:
            if not self.trader or not self.trader.is_connected():
                logging.warning("交易接口未连接，无法查询委托信息")
                return None
                
            # 查询当日委托
            orders_data = {}
            orders = self.trader.query_stock_orders(self.account)
            
            if orders:
                # 转换为字典数据
                for order in orders:
                    order_id = order.order_id  # 订单编号
                    orders_data[order_id] = {
                        "order_id": order_id,
                        "symbol": order.stock_code,  # 股票代码
                        "direction": "买入" if order.order_type == 48 else "卖出",  # 买卖方向
                        "price": order.order_price,  # 委托价格
                        "total_volume": order.order_volume,  # 委托数量
                        "traded_volume": order.traded_volume,  # 成交数量
                        "status": order.order_status,  # 委托状态
                        "submit_time": order.order_time,  # 委托时间
                        "strategy_name": order.strategy_name if hasattr(order, "strategy_name") else "",
                        "order_remark": order.order_remark if hasattr(order, "order_remark") else "",
                        "update_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                
                logging.debug(f"委托查询成功，共{len(orders_data)}个委托")
                return orders_data
            else:
                logging.debug("委托查询结果为空")
                return {}
                
        except Exception as e:
            logging.error(f"查询委托出错: {str(e)}")
            return None
            
    def _query_trades(self):
        """查询成交记录"""
        try:
            if not self.trader or not self.trader.is_connected():
                logging.warning("交易接口未连接，无法查询成交信息")
                return None
                
            # 查询当日成交
            trades_data = {}
            trades = self.trader.query_stock_trades(self.account)
            
            if trades:
                # 转换为字典数据
                for trade in trades:
                    trade_id = trade.traded_id  # 成交编号
                    trades_data[trade_id] = {
                        "trade_id": trade_id,
                        "order_id": trade.order_id,  # 委托编号
                        "symbol": trade.stock_code,  # 股票代码
                        "direction": "买入" if trade.order_type == 48 else "卖出",  # 买卖方向
                        "price": trade.traded_price,  # 成交价格
                        "volume": trade.traded_volume,  # 成交数量
                        "amount": trade.traded_price * trade.traded_volume,  # 成交金额
                        "trade_time": trade.traded_time,  # 成交时间
                        "update_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                
                logging.debug(f"成交查询成功，共{len(trades_data)}个成交")
                return trades_data
            else:
                logging.debug("成交查询结果为空")
                return {}
                
        except Exception as e:
            logging.error(f"查询成交出错: {str(e)}")
            return None
    
    def _query_order(self, order_id):
        """查询单个订单"""
        if not self.trader or not self.account or not self.connected:
            return None
        
        try:
            order = self.trader.query_stock_order(self.account, order_id)
            if order:
                return {
                    "order_id": order.order_id,
                    "stock_code": order.stock_code,
                    "order_status": order.order_status,
                    "order_time": order.order_time,
                    "traded_volume": order.traded_volume,
                    "traded_price": order.traded_price,
                    "order_volume": order.order_volume,
                    "price": order.price,
                    "order_remark": order.order_remark
                }
            return None
        except Exception as e:
            self.logger.error(f"[网关] 查询订单异常: {str(e)}")
            return None
    
    def _update_data(self):
        """更新共享内存中的数据"""
        try:
            # 更新心跳和状态
            current_time = time.time()
            
            # 更新状态包括心跳时间
            self.status.update({
                "last_heartbeat": datetime.datetime.fromtimestamp(current_time).strftime("%Y-%m-%d %H:%M:%S"),
                "last_heartbeat_timestamp": current_time,
                "connected": self.trader is not None and self.trader.is_connected()
            })
            
            # 写入状态
            self.shmem.set_data(DATA_STATUS, self.status)
            
            # 只有在连接正常时才更新其他数据
            if self.trader and self.trader.is_connected():
                try:
                    # 查询并更新账户数据
                    account_info = self._query_account()
                    if account_info:
                        self.shmem.set_data(DATA_ACCOUNT, account_info)
                    
                    # 查询并更新持仓数据
                    positions = self._query_positions()
                    if positions:
                        self.shmem.set_data(DATA_POSITIONS, positions)
                    
                    # 查询并更新订单数据
                    orders = self._query_orders() 
                    if orders:
                        self.shmem.set_data(DATA_ORDERS, orders)
                    
                    # 查询并更新成交数据
                    trades = self._query_trades()
                    if trades:
                        self.shmem.set_data(DATA_TRADES, trades)
                    
                    # 更新成功后记录
                    logging.debug("数据更新成功")
                except Exception as e:
                    logging.error(f"更新交易数据发生错误: {str(e)}")
                    # 不要影响状态更新，只记录错误
            
        except Exception as e:
            logging.error(f"更新数据错误: {str(e)}")
            # 出现异常也要尝试更新状态
            try:
                self.status["is_healthy"] = False
                self.status["last_error"] = str(e)
                self.status["error_time"] = time.time()
                self.shmem.set_data(DATA_STATUS, self.status)
            except:
                pass
    
    def _shutdown(self):
        """关闭服务"""
        self.running = False  # 先停止服务循环
        self.connected = False  # 停止监控线程
        
        if self.trader:
            try:
                self.trader.stop()
            except Exception as e:
                self.logger.error(f"停止交易接口异常: {str(e)}")
            finally:
                self.trader = None
        
        self.logger.info("[网关] 服务已关闭")
    
    def run_forever(self):
        """阻塞运行"""
        self.start()
        
        try:
            while self.running and self.service_thread.is_alive():
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("[网关] 收到中断信号，关闭服务...")
            self._shutdown()

    def _after_successful_connection(self, account):
        """连接成功后的操作"""
        try:
            # 初始查询数据
            account_info = self._query_account()
            if account_info:
                self.shmem.set_data(DATA_ACCOUNT, account_info)
                
            positions = self._query_positions()
            if positions:
                self.shmem.set_data(DATA_POSITIONS, positions)
                
            orders = self._query_orders()
            if orders:
                self.shmem.set_data(DATA_ORDERS, orders)
                
            trades = self._query_trades()
            if trades:
                self.shmem.set_data(DATA_TRADES, trades)
                
            # 记录成功
            logging.info("初始数据查询完成")
            
        except Exception as e:
            logging.error(f"初始数据查询失败: {str(e)}")
            # 不影响连接状态
            
    def _auto_reconnect(self, client_path, account_id):
        """自动重连机制"""
        if not client_path or not account_id:
            logging.error("自动重连失败：客户端路径或账户ID为空")
            return False
            
        # 检查是否已连接
        if self.trader and self.trader.is_connected():
            logging.info("交易接口已经连接，无需重连")
            return True
            
        max_retry = self.config.get("max_reconnect", 3)
        current_retry = 0
        retry_interval = self.config.get("reconnect_interval", 5)
        
        logging.info(f"开始自动重连，最大尝试次数: {max_retry}")
        
        while current_retry < max_retry:
            current_retry += 1
            logging.info(f"第{current_retry}次尝试重连...")
            
            # 关闭旧连接
            if self.trader:
                try:
                    self.trader.stop()
                    self.trader = None
                except Exception as e:
                    logging.error(f"关闭旧连接异常: {str(e)}")
            
            # 创建新的会话ID
            session_id = int(time.time()) + current_retry
            
            try:
                # 创建新的交易接口
                self.trader = XtQuantTrader(client_path, session_id)
                self.trader.register_callback(self.callback)
                
                # 启动交易接口
                self.trader.start()
                
                # 连接交易接口
                connect_result = self.trader.connect()
                if connect_result == 0:
                    # 创建账户并订阅
                    self.account = StockAccount(account_id, 'STOCK')
                    subscribe_result = self.trader.subscribe(self.account)
                    
                    if subscribe_result == 0:
                        # 连接成功，更新状态
                        self.status["connected"] = True
                        self.status["last_connect_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        self.status["reconnect_count"] = current_retry
                        self.status["is_healthy"] = True
                        self.shmem.set_data(DATA_STATUS, self.status)
                        
                        logging.info(f"第{current_retry}次重连成功")
                        
                        # 执行连接成功后的操作
                        self._after_successful_connection(self.account)
                        
                        return True
                    else:
                        logging.error(f"订阅失败，错误码: {subscribe_result}")
                else:
                    logging.error(f"连接失败，错误码: {connect_result}")
            
            except Exception as e:
                logging.error(f"重连异常: {str(e)}")
                traceback.print_exc()
            
            # 等待一段时间后重试
            time.sleep(retry_interval)
            
        # 所有重试都失败
        logging.error(f"重连失败，已尝试{max_retry}次")
        
        # 更新状态
        self.status["connected"] = False
        self.status["reconnect_failed"] = True
        self.status["last_error"] = "重连失败，达到最大重试次数"
        self.status["is_healthy"] = False
        self.shmem.set_data(DATA_STATUS, self.status)
        
        return False

    def _start_monitor_thread(self):
        """启动监控线程"""
        def monitor_task():
            # 增加运行状态检查
            while self.running and self.connected:  # 增加self.running判断
                try:
                    # 检查客户端进程
                    if not self._check_qmt_process():
                        self.logger.error("检测到QMT客户端进程异常退出")
                        self.connected = False
                        break
                    
                    # 检查网络连接（增加返回值处理）
                    network_ok = self._check_network()
                    if not network_ok:
                        self.logger.warning("网络连接异常，尝试重新连接...")
                        self.connected = False
                        break
                    
                    time.sleep(30)
                except Exception as e:
                    self.logger.error(f"监控线程异常: {str(e)}")
                    break
        
        threading.Thread(target=monitor_task, daemon=True).start()

    def _check_qmt_process(self):
        """检查QMT客户端进程"""
        try:
            for proc in psutil.process_iter(['name']):
                if proc.info['name'] in ('miniquote.exe', 'XtMiniQmt.exe'):
                    return True
            return False
        except Exception as e:
            self.logger.error(f"进程检查失败: {str(e)}")
            return False

    def _check_network(self):
        """网络连接检查"""
        import socket
        try:
            # 测试连接QMT服务器（示例地址，需替换实际地址）
            sock = socket.create_connection(("127.0.0.1", 80), timeout=3)
            sock.close()
            return True
        except Exception as e:
            self.logger.warning(f"网络连接检查失败: {str(e)}")
            return False

    def _health_check(self):
        """系统健康检查，设置is_healthy状态标志"""
        try:
            # 初始默认为健康状态
            is_healthy = True
            check_details = {}
            
            # 检查trader对象
            if not self.trader:
                is_healthy = False
                check_details["trader"] = "交易接口未创建"
            else:
                # 检查连接状态
                if not self.trader.is_connected():
                    is_healthy = False
                    check_details["connection"] = "交易接口未连接"
                else:
                    check_details["connection"] = "正常"
            
            # 检查心跳
            current_time = time.time()
            last_heartbeat = self.status.get("last_heartbeat_timestamp", 0)
            
            # 心跳超时检查
            heartbeat_timeout = self.config.get("heartbeat_timeout", 10)
            if current_time - last_heartbeat > heartbeat_timeout:
                is_healthy = False
                check_details["heartbeat"] = f"心跳超时 ({int(current_time - last_heartbeat)}秒)"
            else:
                check_details["heartbeat"] = "正常"
            
            # 检查账户数据
            try:
                if not self._query_account():
                    is_healthy = False
                    check_details["account"] = "账户数据查询失败"
                else:
                    check_details["account"] = "正常"
            except Exception as e:
                is_healthy = False
                check_details["account"] = f"查询异常: {str(e)}"
            
            # 更新健康状态
            self.status["is_healthy"] = is_healthy
            self.status["health_check_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.status["health_check_details"] = check_details
            
            # 记录健康状态
            status_text = "健康" if is_healthy else "不健康"
            logging.info(f"健康检查结果: {status_text} - {check_details}")
            
            # 更新共享内存中的状态
            self.shmem.set_data(DATA_STATUS, self.status)
            
            return is_healthy
            
        except Exception as e:
            # 出现异常，标记为不健康
            logging.error(f"健康检查发生异常: {str(e)}")
            self.status["is_healthy"] = False
            self.status["health_check_error"] = str(e)
            self.shmem.set_data(DATA_STATUS, self.status)
            return False

    def is_gateway_running(self):
        """对外提供的运行状态检查接口"""
        status = self.shmem.get_data(DATA_STATUS) or {}
        return status.get("is_healthy", False)

# 启动网关服务的脚本示例
def start_gateway_service():
    """启动交易网关服务"""
    try:
        # 检查配置文件
        config_path = os.path.join(os.path.dirname(__file__), "gateway_config.json")
        if not os.path.exists(config_path):
            # 尝试创建默认配置文件
            try:
                default_config = {
                    "client_path": os.path.expanduser("~/userdata_mini"),
                    "account_id": "请填写您的账号ID",
                    "max_reconnect": 3,
                    "heartbeat_timeout": 10
                }
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=4, ensure_ascii=False)
                logging.warning(f"未找到配置文件，已创建默认配置: {config_path}")
                print(f"未找到配置文件，已创建默认配置: {config_path}")
                print("请编辑配置文件填写正确的账户信息后重新启动服务。")
                return
            except Exception as e:
                logging.error(f"创建默认配置文件失败: {str(e)}")
                print(f"创建默认配置文件失败: {str(e)}")
                return
        
        # # 检查是否已有网关服务在运行
        # try:
        #     existing_shmem = SharedMemory(create=False)
        #     if existing_shmem.is_initialized:
        #         status = existing_shmem.get_data(DATA_STATUS) or {}
                
        #         if status.get("is_healthy", False) and status.get("running", False):
        #             logging.warning("检测到已有正常运行的网关服务，拒绝启动新实例")
        #             print("检测到已有正常运行的网关服务，拒绝启动新实例")
        #             return
        # except Exception as e:
        #     logging.info(f"没有检测到现有服务或现有服务异常: {str(e)}")
        
        # 正常启动逻辑
        gateway = TradingGatewayService()
        
        # 检查共享内存是否初始化成功
        if not gateway.shmem.is_initialized:
            logging.error("共享内存初始化失败，无法启动服务")
            print("共享内存初始化失败，无法启动服务。请检查日志文件获取详细信息。")
            return
        
        gateway.start()
        
        # 检查是否已连接，若未连接则尝试连接
        if not gateway.connected:
            client_path = gateway.config.get("client_path")
            account_id = gateway.config.get("account_id")
            if client_path and account_id:
                gateway.logger.info("尝试自动连接交易账户...")
                if gateway._connect(client_path, account_id):
                    gateway.logger.info("交易账户连接成功")
                else:
                    gateway.logger.error("交易账户连接失败")
            else:
                gateway.logger.error("配置文件中缺少客户端路径或账户ID")
        
        gateway.logger.info("启动交易网关服务")
        gateway.run_forever()
    except KeyboardInterrupt:
        logging.info("用户中断，程序退出")
        print("用户中断，程序退出")
    except Exception as e:
        logging.error(f"启动交易网关服务出错: {str(e)}")
        print(f"启动交易网关服务出错: {str(e)}")
        # 打印详细堆栈信息到日志
        import traceback
        logging.error(traceback.format_exc())

if __name__ == "__main__":
    start_gateway_service()