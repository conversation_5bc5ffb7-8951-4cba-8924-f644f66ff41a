# MCP路径问题修复指南

## 🔧 问题描述

如果您遇到以下错误：
```
Failed to start the MCP server. 
{"command":"python mcp_servers/ragie_server.py","args":[],"error":"MCP error -32000: Connection closed","stderr":"python: can't open file 'D:\\\\Microsoft VS Code\\\\mcp_servers\\\\ragie_server.py': [Errno 2] No such file or directory\r\n"}
```

这是因为MCP配置中使用了相对路径，而Augment插件在不同的工作目录下运行。

## ✅ 解决方案

### 方法1: 自动修复 (推荐)

运行修复脚本：
```bash
python fix_mcp_paths.py
```

这个脚本会：
1. 自动找到Augment配置文件
2. 更新Python可执行文件路径为绝对路径
3. 确保MCP服务器脚本路径正确
4. 创建配置备份

### 方法2: 手动修复

1. **找到Augment配置文件**
   - 通常位于: `C:\Users\<USER>\.codegeex\agent\configs\user_mcp_config.json`

2. **更新配置文件**
   将配置中的：
   ```json
   {
     "command": "python",
     "args": ["mcp_servers/graphiti_server.py"]
   }
   ```
   
   改为：
   ```json
   {
     "command": "E:\\veighna_studio\\python.exe",
     "args": ["D:\\Quant000150v9.5\\Quant\\Strategy\\2025project01\\mcp_servers\\graphiti_server.py"]
   }
   ```

## 🔍 验证修复

### 检查配置文件
```bash
type "C:\Users\<USER>\.codegeex\agent\configs\user_mcp_config.json"
```

应该看到类似这样的配置：
```json
{
  "mcpServers": {
    "graphiti": {
      "command": "E:\\veighna_studio\\python.exe",
      "args": [
        "D:\\Quant000150v9.5\\Quant\\Strategy\\2025project01\\mcp_servers\\graphiti_server.py"
      ]
    }
  }
}
```

### 测试服务器启动
```bash
"E:\veighna_studio\python.exe" "D:\Quant000150v9.5\Quant\Strategy\2025project01\mcp_servers\graphiti_server.py"
```

如果没有错误输出，说明服务器可以正常启动。

## 📋 修复后的配置

修复后的完整配置应该是：

```json
{
  "mcpServers": {
    "graphiti": {
      "command": "E:\\veighna_studio\\python.exe",
      "args": [
        "D:\\Quant000150v9.5\\Quant\\Strategy\\2025project01\\mcp_servers\\graphiti_server.py"
      ],
      "description": "Graphiti knowledge graph and memory management service",
      "env": {
        "GRAPHITI_LOG_LEVEL": "INFO"
      }
    },
    "opik": {
      "command": "E:\\veighna_studio\\python.exe",
      "args": [
        "D:\\Quant000150v9.5\\Quant\\Strategy\\2025project01\\mcp_servers\\opik_server.py"
      ],
      "description": "Opik ML experiment tracking and monitoring service",
      "env": {
        "OPIK_LOG_LEVEL": "INFO"
      }
    },
    "ragie": {
      "command": "E:\\veighna_studio\\python.exe",
      "args": [
        "D:\\Quant000150v9.5\\Quant\\Strategy\\2025project01\\mcp_servers\\ragie_server.py"
      ],
      "description": "Ragie RAG (Retrieval Augmented Generation) service",
      "env": {
        "RAGIE_LOG_LEVEL": "INFO"
      }
    },
    "jupyter_mcp": {
      "command": "jupyter-mcp-server",
      "args": [
        "start",
        "--transport",
        "stdio"
      ],
      "description": "Jupyter MCP Server for notebook integration",
      "env": {
        "JUPYTER_LOG_LEVEL": "INFO"
      }
    }
  }
}
```

## 🚀 下一步

1. **重启Augment插件**
   - 完全关闭并重新打开Augment
   - 或重启IDE/编辑器

2. **测试MCP服务**
   在Augment对话中尝试：
   - "使用Graphiti创建知识图谱"
   - "用Opik跟踪实验"
   - "通过Ragie搜索文档"

## 🔧 常见问题

### Q: 如果Python路径不同怎么办？
A: 运行以下命令获取正确的Python路径：
```bash
python -c "import sys; print(sys.executable)"
```
然后在配置文件中使用这个路径。

### Q: 如果项目目录不同怎么办？
A: 确保MCP服务器脚本的路径指向正确的位置。可以运行：
```bash
python -c "from pathlib import Path; print(Path('.').resolve())"
```
获取当前项目的绝对路径。

### Q: 修复后仍然有问题？
A: 
1. 检查文件是否存在
2. 确认Python环境正确
3. 查看Augment的错误日志
4. 重新运行测试脚本验证

## 📝 备份信息

修复脚本会自动创建备份：
- 原始备份: `user_mcp_config.json.backup`
- 修复前备份: `user_mcp_config.json.backup2`

如果需要恢复，可以使用这些备份文件。
