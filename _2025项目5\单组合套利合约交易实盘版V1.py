import re
import copy
import time
import numpy as np
import pandas as pd
from talib import MA
from collections import deque
from collections import defaultdict

class TriggerManager:
    """
    交易触发器管理类，负责处理订单开平仓操作
    """
    def __init__(self):
        # 无需内部状态标记，外部由SQL表状态控制
        pass
    
    def reset_states(self):
        """保留此方法做为兼容接口，但不执行任何操作"""
        pass
    
    def _calculate_order_price(self, tcode, direction, price_mode, price_offset, fallback_price):
        """
        根据价格模式计算订单价格
        
        参数:
            tcode: 合约代码
            direction: 方向，'BUY'或'SELL'
            price_mode: 价格模式，'最新价'/'对价'/'超价'
            price_offset: 超价跳数
            fallback_price: 备用价格
            
        返回:
            float: 计算后的价格
        """
        try:
            if price_mode == '最新价':
                price = Q_Last(tcode)
                if price <= 0:
                    price = fallback_price
                    LogInfo(f"合约{tcode}无法获取最新价，使用备用价格{fallback_price}")
                return price
                
            elif price_mode == '对价':
                if direction == 'BUY':
                    # 买入用卖一价
                    price = Q_AskPrice(tcode)
                    if price <= 0:
                        price = Q_Last(tcode)
                        LogInfo(f"合约{tcode}无法获取卖一价，使用最新价{price}")
                else:
                    # 卖出用买一价
                    price = Q_BidPrice(tcode)
                    if price <= 0:
                        price = Q_Last(tcode)
                        LogInfo(f"合约{tcode}无法获取买一价，使用最新价{price}")
                
                if price <= 0:
                    price = fallback_price
                    LogInfo(f"合约{tcode}无法获取对价，使用备用价格{fallback_price}")
                return price
                
            elif price_mode == '超价':
                # 获取基础价格（对价）
                if direction == 'BUY':
                    base_price = Q_AskPrice(tcode)
                    if base_price <= 0:
                        base_price = Q_Last(tcode)
                else:
                    base_price = Q_BidPrice(tcode)
                    if base_price <= 0:
                        base_price = Q_Last(tcode)
                
                if base_price <= 0:
                    base_price = fallback_price
                
                # 获取最小变动价位
                tick_size = PriceTick(tcode)
                if tick_size <= 0:
                    tick_size = 1  # 默认最小变动价位
                
                # 计算超价
                if direction == 'BUY':
                    price = base_price + (price_offset * tick_size)
                else:
                    price = base_price - (price_offset * tick_size)
                
                LogInfo(f"合约{tcode}超价计算: 基础价格={base_price}, 超价跳数={price_offset}, 最小变动价位={tick_size}, 最终价格={price}")
                return price
                
            else:
                LogInfo(f"未知价格模式{price_mode}，使用备用价格{fallback_price}")
                return fallback_price
                
        except Exception as e:
            LogInfo(f"价格计算异常: {str(e)}，使用备用价格{fallback_price}")
            return fallback_price
    
    # ==================== 历史开仓函数 ====================
    
    def his_trigger_long(self, qty, price, tcode):
        """
        历史多头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
        """
        # 移除状态检查，直接执行开仓
        Buy(qty, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "多单买入开仓价==>", price, "买入数量==>", qty)
        return True
    
    def his_trigger_short(self, qty, price, tcode):
        """
        历史空头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
        """
        # 移除状态检查，直接执行开仓
        SellShort(qty, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "空单卖出开仓价==>", price, "卖出数量==>", qty)
        return True
    
    # ==================== 历史平仓函数 ====================
    
    def his_trigger_exit_short(self, clots, price, tcode):
        """
        历史平仓空头持仓
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
        """
        # 只保留必要的持仓检查
        if SellPosition(tcode) <= 0 or clots <= 0:
            return False
            
        _lots = min(clots, SellPosition(tcode))
        BuyToCover(_lots, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "空单买入平仓价==>", price, "买入平仓数量==>", _lots)
        return True
    
    def his_trigger_exit_long(self, clots, price, tcode):
        """
        历史平仓多头持仓
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
        """
        # 只保留必要的持仓检查
        if BuyPosition(tcode) <= 0 or clots <= 0:
            return False
            
        _lots = min(clots, BuyPosition(tcode))
        Sell(_lots, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "多单卖出平仓价==>", price, "卖出平仓数量==>", _lots)
        return True
    
    
    # ==================== 实时开仓函数 ====================
    
    def tim_trigger_long(self, qty, price, tcode, order_type='2', price_mode='最新价', price_offset=0):
        """
        实时多头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格（price_mode为'最新价'时使用）
            tcode: 合约代码
            order_type: 订单类型，默认'2'为限价单，'1'为市价单
            price_mode: 价格模式，'最新价'/'对价'/'超价'
            price_offset: 超价跳数
            
        返回:
            (状态, 订单ID)
        """
        # 移除状态检查，直接执行开仓
        
        # 对于市价单，保留传入价格不变，便于模拟账户测试
        if order_type == '1':
            checked_price = price
            LogInfo(f"市价单模式:合约 {tcode} 多头开仓使用市价单，记录价格 {price}")
        else:
            # 根据价格模式计算开仓价格
            checked_price = self._calculate_order_price(tcode, 'BUY', price_mode, price_offset, price)
            
            # 价格检查逻辑保持不变
            upper_limit = Q_UpperLimit(tcode)
            if upper_limit > 0:
                checked_price = min(checked_price, upper_limit)
                LogInfo(f"多头开仓价格调整: 模式={price_mode}, 计算价格={checked_price}, 涨停价={upper_limit}")
        
        # 发送订单，添加订单类型参数
        retCode, retMsg = A_SendOrder(Enum_Buy(), Enum_Entry(), qty, checked_price, tcode, '', order_type)
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "多单买入开仓价==>", checked_price, "买入数量==>", qty, "订单类型==>", "市价单" if order_type=='1' else "限价单", "下单状态=", retCode)
        
        # 判断订单是否成功
        # retCode=0: 实盘成功，retMsg是订单ID
        # retCode<0 且 retMsg非空: 历史环境成功(如果调用了SetAFunUseForHis)，retMsg是历史订单ID  
        # retCode<0 且 retMsg为空: 失败
        success = (retCode == 0) or (retCode < 0 and retMsg != "")
        order_id = retMsg if success else None
        
        if success:
            LogInfo(f"多头开仓订单已发送，订单号: {order_id}，环境: {'实盘' if retCode == 0 else '历史'}")
        else:
            LogInfo(f"多头开仓订单发送失败，错误码: {retCode}，错误信息: {retMsg}")
        
        return success, order_id
    
    def tim_trigger_short(self, qty, price, tcode, order_type='2', price_mode='最新价', price_offset=0):
        """
        实时空头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格（price_mode为'最新价'时使用）
            tcode: 合约代码
            order_type: 订单类型，默认'2'为限价单，'1'为市价单
            price_mode: 价格模式，'最新价'/'对价'/'超价'
            price_offset: 超价跳数
            
        返回:
            (状态, 订单ID)
        """
        # 移除状态检查，直接执行开仓
        
        # 对于市价单，保留传入价格不变，便于模拟账户测试
        if order_type == '1':
            checked_price = price
            LogInfo(f"市价单模式:合约 {tcode} 空头开仓使用市价单，记录价格 {price}")
        else:
            # 根据价格模式计算开仓价格
            checked_price = self._calculate_order_price(tcode, 'SELL', price_mode, price_offset, price)
            
            # 限价单价格检查逻辑保持不变
            lower_limit = Q_LowLimit(tcode)
            if lower_limit > 0:
                checked_price = max(checked_price, lower_limit)
                LogInfo(f"空头开仓价格调整: 模式={price_mode}, 计算价格={checked_price}, 跌停价={lower_limit}")
        
        # 发送订单，添加订单类型参数
        retCode, retMsg = A_SendOrder(Enum_Sell(), Enum_Entry(), qty, checked_price, tcode, '', order_type)
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "空单卖出开仓价==>", checked_price, "卖出数量==>", qty, "订单类型==>", "市价单" if order_type=='1' else "限价单", "下单状态=", retCode)
        
        # 判断订单是否成功
        # retCode=0: 实盘成功，retMsg是订单ID
        # retCode<0 且 retMsg非空: 历史环境成功(如果调用了SetAFunUseForHis)，retMsg是历史订单ID  
        # retCode<0 且 retMsg为空: 失败
        success = (retCode == 0) or (retCode < 0 and retMsg != "")
        order_id = retMsg if success else None
        
        if success:
            LogInfo(f"空头开仓订单已发送，订单号: {order_id}，环境: {'实盘' if retCode == 0 else '历史'}")
        else:
            LogInfo(f"空头开仓订单发送失败，错误码: {retCode}，错误信息: {retMsg}")
        
        return success, order_id

    
    # ==================== 实时平仓函数 ====================
    
    def tim_trigger_exit_short(self, clots, price, tcode, order_type='2'):
        """
        实时平仓空头持仓，支持返回多个订单状态
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
            order_type: 订单类型，默认'2'为限价单，'1'为市价单
            
        返回:
            list: 包含所有订单信息的列表，每项为 (状态, 订单ID, 平仓类型, 平仓手数)
        """
        orders_info = []  # 用于存储所有订单的状态和ID
        MarketType = check_contract_prefix(tcode)
        sell_position = A_SellPosition(tcode)
        # 检查持仓是否存在
        if clots <= 0 or (sell_position <= 0 and MarketType == '2'):
            LogInfo(f"合约 {tcode} 无空头持仓或平仓手数为0，跳过平仓")
            return orders_info
            
        _lots = min(clots, sell_position) if MarketType == '2' else clots
        LogInfo(f"准备平仓合约 {tcode} 的空头持仓，持仓量={sell_position}，平仓量={_lots}")
        
        # 对于市价单，保留传入价格不变，便于模拟账户测试
        if order_type == '1':
            checked_price = price
            LogInfo(f"市价单模式:合约 {tcode} 平空头使用市价单，记录价格 {price}")
        else:
            # 限价单价格检查逻辑保持不变
            upper_limit = Q_UpperLimit(tcode)
            if upper_limit <= 0:
                checked_price = price
            else:
                checked_price = min(price, upper_limit)
                if checked_price != price:
                    LogInfo(f"警告: 平空头价格 {price} 超过涨停价 {upper_limit}，已自动调整")
        
        # 交易所特殊处理逻辑保持不变
        if ExchangeName(tcode) not in ['SHFE', 'INE']:
            # 普通交易所情况
            retCode, retMsg = A_SendOrder(Enum_Buy(), Enum_Exit(), _lots, checked_price, tcode, '', order_type)
            success = (retCode == 0) or (retCode < 0 and retMsg != "")
            order_id = retMsg if success else None
            orders_info.append((success, order_id, "平仓", _lots))
            LogInfo(f"发送平空头单: {tcode}, 类型=平仓, 数量={_lots}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={order_id}, 状态={retCode}")
        else:
            # 上期所和能源交易所特殊处理
            lots = _lots
            tlots = A_TodaySellPosition(tcode)
            dlots = lots - tlots
            
            if tlots >= lots:
                # 今仓足够平仓,仅平今仓
                retCode, retMsg = A_SendOrder(Enum_Buy(), Enum_ExitToday(), lots, checked_price, tcode, '', order_type)
                success = (retCode == 0) or (retCode < 0 and retMsg != "")
                order_id = retMsg if success else None
                orders_info.append((success, order_id, "平今", lots))
                LogInfo(f"发送平空头单(平今): {tcode}, 数量={lots}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={order_id}, 状态={retCode}")
            elif tlots > 0:
                # 今仓不够，分别平今仓和昨仓
                # 先平今仓部分
                TretCode, TretMsg = A_SendOrder(Enum_Buy(), Enum_ExitToday(), tlots, checked_price, tcode, '', order_type)
                Tsuccess = (TretCode == 0) or (TretCode < 0 and TretMsg != "")
                Torder_id = TretMsg if Tsuccess else None
                orders_info.append((Tsuccess, Torder_id, "平今", tlots))
                LogInfo(f"发送平空头单(平今部分): {tcode}, 数量={tlots}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={Torder_id}, 状态={TretCode}")
                
                # 再平昨仓部分
                YretCode, YretMsg = A_SendOrder(Enum_Buy(), Enum_Exit(), int(dlots), checked_price, tcode, '', order_type)
                Ysuccess = (YretCode == 0) or (YretCode < 0 and YretMsg != "")
                Yorder_id = YretMsg if Ysuccess else None
                orders_info.append((Ysuccess, Yorder_id, "平昨", int(dlots)))
                LogInfo(f"发送平空头单(平昨部分): {tcode}, 数量={int(dlots)}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={Yorder_id}, 状态={YretCode}")
            elif tlots == 0:
                # 仅平昨仓
                retCode, retMsg = A_SendOrder(Enum_Buy(), Enum_Exit(), lots, checked_price, tcode, '', order_type)
                success = (retCode == 0) or (retCode < 0 and retMsg != "")
                order_id = retMsg if success else None
                orders_info.append((success, order_id, "平昨", lots))
                LogInfo(f"发送平空头单(平昨): {tcode}, 数量={lots}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={order_id}, 状态={retCode}")
        
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "空单买入平仓价==>", checked_price, "买入平仓数量==>", _lots)
        
        if any(order[1] for order in orders_info):
            LogInfo(f"空头平仓订单已发送成功")
        else:
            LogInfo(f"所有空头平仓订单发送失败")
        
        return orders_info
    
    def tim_trigger_exit_long(self, clots, price, tcode, order_type='2'):
        """
        实时平仓多头持仓
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
            order_type: 订单类型，默认'2'为限价单，'1'为市价单
            
        返回:
            list: 订单信息列表
        """
        orders_info = []  # 用于存储所有订单的状态和ID
        MarketType = check_contract_prefix(tcode)
        buy_position = A_BuyPosition(tcode)
        # 只检查持仓是否存在
        if clots <= 0 or (buy_position <= 0 and MarketType == '2'):
            LogInfo(f"合约 {tcode} 无多头持仓或平仓手数为0，跳过平仓")
            return orders_info
            
        _lots = min(clots, buy_position) if MarketType == '2' else clots
        LogInfo(f"准备平仓合约 {tcode} 的多头持仓，持仓量={buy_position}，平仓量={_lots}")
        
        # 如果是市价单且价格不为0，设置价格为0
        if order_type == '1' and price != 0:
            checked_price = 0
            LogInfo(f"市价单模式:合约 {tcode} 平多头使用市价单")
        else:
            # 价格检查逻辑保持不变
            lower_limit = Q_LowLimit(tcode)
            if lower_limit <= 0:
                checked_price = price
            else:
                checked_price = max(price, lower_limit)
                if checked_price != price:
                    LogInfo(f"警告: 平多头价格 {price} 低于跌停价 {lower_limit}，已自动调整")
        
        # 交易所特殊处理逻辑保持不变
        if ExchangeName(tcode) not in ['SHFE', 'INE']:
            # 普通交易所情况
            retCode, retMsg = A_SendOrder(Enum_Sell(), Enum_Exit(), _lots, checked_price, tcode, '', order_type)
            success = (retCode == 0) or (retCode < 0 and retMsg != "")
            order_id = retMsg if success else None
            orders_info.append((success, order_id, "平仓", _lots))
            LogInfo(f"发送平多头单: {tcode}, 类型=平仓, 数量={_lots}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={order_id}, 状态={retCode}")
        else:
            # 上期所和能源交易所特殊处理
            lots = _lots
            tlots = A_TodayBuyPosition(tcode)
            dlots = lots - tlots
            
            if tlots >= lots:
                # 今仓足够平仓,仅平今仓
                retCode, retMsg = A_SendOrder(Enum_Sell(), Enum_ExitToday(), lots, checked_price, tcode, '', order_type)
                success = (retCode == 0) or (retCode < 0 and retMsg != "")
                order_id = retMsg if success else None
                orders_info.append((success, order_id, "平今", lots))
                LogInfo(f"发送平多头单(平今): {tcode}, 数量={lots}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={order_id}, 状态={retCode}")
            elif tlots > 0:
                # 今仓不够，分别平今仓和昨仓
                # 先平今仓部分
                TretCode, TretMsg = A_SendOrder(Enum_Sell(), Enum_ExitToday(), tlots, checked_price, tcode, '', order_type)
                Tsuccess = (TretCode == 0) or (TretCode < 0 and TretMsg != "")
                Torder_id = TretMsg if Tsuccess else None
                orders_info.append((Tsuccess, Torder_id, "平今", tlots))
                LogInfo(f"发送平多头单(平今部分): {tcode}, 数量={tlots}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={Torder_id}, 状态={TretCode}")
                
                # 再平昨仓部分
                YretCode, YretMsg = A_SendOrder(Enum_Sell(), Enum_Exit(), int(dlots), checked_price, tcode, '', order_type)
                Ysuccess = (YretCode == 0) or (YretCode < 0 and YretMsg != "")
                Yorder_id = YretMsg if Ysuccess else None
                orders_info.append((Ysuccess, Yorder_id, "平昨", int(dlots)))
                LogInfo(f"发送平多头单(平昨部分): {tcode}, 数量={int(dlots)}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={Yorder_id}, 状态={YretCode}")
            elif tlots == 0:
                # 仅平昨仓
                retCode, retMsg = A_SendOrder(Enum_Sell(), Enum_Exit(), lots, checked_price, tcode, '', order_type)
                success = (retCode == 0) or (retCode < 0 and retMsg != "")
                order_id = retMsg if success else None
                orders_info.append((success, order_id, "平昨", lots))
                LogInfo(f"发送平多头单(平昨): {tcode}, 数量={lots}, 价格={checked_price}, 订单类型={'市价单' if order_type=='1' else '限价单'}, 订单ID={order_id}, 状态={retCode}")
        
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "多单卖出平仓价==>", checked_price, "卖出平仓数量==>", _lots)
        
        if any(order[1] for order in orders_info):
            LogInfo(f"多头平仓订单已发送成功")
        else:
            LogInfo(f"所有多头平仓订单发送失败")
        
        return orders_info
    

# 策略参数
g_params['K线基础时间']  = 'M'     # k线基础时间
g_params['K线基础周期']  = 1       # k线周期
g_params['回测阶段预警'] = '开'    # 开为开启历史回测阶段预警信息，否则为关
g_params['收盘倒计时分钟数设置'] = 1#距离收盘时间范围不开仓，并清仓    
g_params['订阅数据长度']  =  200  #订阅日线数据长度

g_params['配置文件夹路径'] = "D:\Quant000150v9.5\Quant\Strategy\用户策略\_2025项目5"
g_params['配置文件名'] = "配置文件单组合套利实盘版.xlsx"
trade_order_filedir = g_params['配置文件夹路径']
trade_config_file   = trade_order_filedir+"\\"+g_params['配置文件名'] 
trade_config_DATA   =pd.read_excel(trade_config_file,sheet_name = 0)
symbol_Id=str(trade_config_DATA["套利组合"].iloc[0])
均线一=int(trade_config_DATA["均线一"].iloc[0])
突破网格点数=float(trade_config_DATA["突破网格点数"].iloc[0])
加仓次数上限=int(trade_config_DATA["加仓次数上限"].iloc[0])
下单量=int(trade_config_DATA["下单量"].iloc[0])
第一腿下单模式=str(trade_config_DATA["第一腿下单模式"].iloc[0])
后腿下单模式=str(trade_config_DATA["后腿下单模式"].iloc[0])
超价跳数=int(trade_config_DATA["超价跳数"].iloc[0])
撤单秒数=int(trade_config_DATA["撤单秒数"].iloc[0])
瘸腿单自动平仓秒数=int(trade_config_DATA["瘸腿单自动平仓秒数"].iloc[0])
预警音频路径=str(trade_config_DATA["预警音频路径"].iloc[0])

# 单个套利组合的变量，不再使用列表
symbol_d,_CurrentBar,price_list=None,None,None
QMA1,QMAUT1,QMADW1=None,None,None
MAIN_CurrentBar,daily_CurrentBar=None,None
MAIN_price_list,daily_price_list=None,None
UPBreakout,DWBreakout=0,0
LastUPBreakout,LastDWBreakout=0,0
# 添加记录上次有效突破的网格编号
LastValidUP=0  # 上次有效上突破的网格编号
LastValidDW=0  # 上次有效下突破的网格编号
UPStatus,DWStatus=0,0     
BKStatus,SKStatus,BPStatus,SPStatus=0,0,0,0


k_btime,k_cycle,BackTestWarning,CloseTime,wav_path=0,0,None,None,None
SubDataLength,AluSubDataLength,setNo,display_code=0,0,0,0
all_contracts, parsed_formulas, contract_prices, last_arb_prices=None,None,None,None

# 合约正则表达式
CONTRACT_PATTERN = re.compile(r'(?:ZCE\|[A-Z]\|[A-Z]+\|\d{3}|(?:DCE|SHFE|INE|CFFEX|GFEX)\|[A-Z]\|[A-Z]+\|\d{4})')


def check_contract_prefix(contract: str) -> str:
    """
    检查合约前缀类型
    
    参数:
        contract: 合约代码
        
    返回:
        str: '1'表示期权, '2'表示期货
    """
    # 简化版本：如果合约代码包含C或P，认为是期权，否则是期货
    if 'C|' in contract or 'P|' in contract:
        return '1'  # 期权
    else:
        return '2'  # 期货

# 初始化订单管理器
g_order_manager = None

def initialize(context): 
    global g_params,k_btime,k_cycle,BackTestWarning,CloseTime,wav_path
    global SubDataLength,AluSubDataLength,setNo,display_code
    global g_order_manager  # 添加全局订单管理器引用
    global symbol_d,_CurrentBar,price_list,QMA1,QMAUT1,QMADW1
    global MAIN_CurrentBar,daily_CurrentBar,MAIN_price_list,daily_price_list
    
    # 根据策略状态初始化对应的订单管理器
    # 注意：initialize阶段context.strategyStatus()可能还未准确反映状态
    # 这里先初始化实盘管理器，在handle_data中根据HTS动态切换
    g_order_manager = ArbitrageOrderManager()
    k_btime = g_params['K线基础时间'] # k线基础时间取参数
    k_cycle = g_params['K线基础周期'] # k线基础周期取参数
    BackTestWarning = g_params['回测阶段预警'] # 回测阶段预警
    CloseTime = g_params['收盘倒计时分钟数设置']
    dailySubDataLength = g_params['订阅数据长度']  # 订阅数据长度
    MAIN_subDataLength = int(dailySubDataLength*(360/convert_bar_to_minutes(k_btime,k_cycle)))  # 订阅数据长度
    AluSubDataLength = min(int(均线一)*2,MAIN_subDataLength)  # 计算数据长度  
    setNo = 0
    wav_path = 预警音频路径
    # 单个套利组合的初始化，不再使用循环
    Qlenset = int(均线一)
    QGMPIset = int(加仓次数上限)
    symbol_d = (deque([], maxlen=Qlenset))
    QMA1 = (deque([], maxlen=Qlenset))
    QMAUT1 = (deque([], maxlen=QGMPIset))
    QMADW1 = (deque([], maxlen=QGMPIset))
    MAIN_CurrentBar = (deque([], maxlen=Qlenset))
    daily_CurrentBar = (deque([], maxlen=Qlenset))
    MAIN_price_list = (deque([], maxlen=Qlenset))
    daily_price_list = (deque([], maxlen=Qlenset))
    
    # 解析所有合约并预处理公式
    global all_contracts, parsed_formulas, contract_prices, last_arb_prices
    all_contracts, parsed_formulas = parse_arbitrage_contracts([symbol_Id], CONTRACT_PATTERN)
    
    LogInfo(f"识别到的所有合约: {all_contracts}")
    display_code = get_first_contract(symbol_Id, CONTRACT_PATTERN)
    LogInfo(f"套利组合 {symbol_Id} 的主合约是 {display_code}")
    LogInfo(f"均线一: {均线一},突破网格点数: {突破网格点数},加仓次数上限: {加仓次数上限},下单量: {下单量}")
    LogInfo(f"第一腿下单模式: {第一腿下单模式},后腿下单模式: {后腿下单模式},超价跳数: {超价跳数},撤单秒数: {撤单秒数},瘸腿单自动平仓秒数: {瘸腿单自动平仓秒数}")
    LogInfo(f"数据计算说明: 均线计算基于日线套利价格数据，突破判断基于{k_btime}{k_cycle}周期套利价格数据") 
    SetBarInterval(display_code, k_btime, k_cycle,MAIN_subDataLength,AluSubDataLength) #订阅交易合约
    # 初始化合约价格字典和上次计算的套利价格
    contract_prices = {contract: np.nan for contract in all_contracts}
    last_arb_prices = np.nan  # 单个套利价格，不再使用列表
    
    # 订阅行情
    for contract in all_contracts:
        SetBarInterval(contract, 'D', 1,dailySubDataLength,AluSubDataLength) #订阅日线合约
        if contract==display_code:
            LogInfo(f'跳过已订阅的图表显示合约 {contract}')
            continue
        LogInfo(f'遍历订阅合约==> {contract} ')
        SetBarInterval(contract, k_btime, k_cycle,MAIN_subDataLength,AluSubDataLength) #订阅交易合约
    SetTriggerType(1)
    SetTriggerType(5)
    SetOrderWay(1)
    SetActual()
    # SetAFunUseForHis()

    
def handle_data(context):
    global all_contracts, parsed_formulas, contract_prices, last_arb_prices
    global LastValidUP, LastValidDW
    global g_order_manager  # 添加全局订单管理器引用
    global symbol_d,QMA1,QMAUT1,QMADW1
    global MAIN_CurrentBar,daily_CurrentBar,MAIN_price_list,daily_price_list
    global UPBreakout,DWBreakout,LastUPBreakout,LastDWBreakout
    HTS=1 if context.strategyStatus()=="C" else 0
    
    # 根据HTS状态动态选择订单管理器
    if HTS == 1:
        # 实盘状态，使用ArbitrageOrderManager
        if not isinstance(g_order_manager, ArbitrageOrderManager):
            LogInfo("切换到实盘模式，使用ArbitrageOrderManager")
            g_order_manager = ArbitrageOrderManager()
    else:
        # 历史回测状态，使用HistoricalArbitrageOrderManager
        if not isinstance(g_order_manager, HistoricalArbitrageOrderManager):
            LogInfo("切换到历史回测模式，使用HistoricalArbitrageOrderManager")
            g_order_manager = HistoricalArbitrageOrderManager()
    current_formula = symbol_Id
    contracts_in_formula = CONTRACT_PATTERN.findall(current_formula)
    ALU_code=get_first_contract(symbol_Id, CONTRACT_PATTERN)
    # 只取最新价，避免广播问题
    for contract in contracts_in_formula:
        close_arr = Close(contract, k_btime, k_cycle)
        if len(close_arr)>0:
            contract_prices[contract] = close_arr[-1]  # 只取最后一个收盘价
    arb_price = calculate_arbitrage_price(parsed_formulas[0], contract_prices)
    last_arb_prices = arb_price
    MAIN_CurrentBar.append(CurrentBar(ALU_code,k_btime,k_cycle))
    # 小周期K线结束判断
    if len(MAIN_CurrentBar)<2:
        return
    MAIN_K_EndTrigger = MAIN_CurrentBar[0]>0 and MAIN_CurrentBar[-2]<MAIN_CurrentBar[-1]
    if MAIN_K_EndTrigger:
        # 在K线结束时保存当前网格状态
        LastUPBreakout = UPBreakout
        LastDWBreakout = DWBreakout
        MAIN_price_list.append(arb_price)
        
    ALU_CurrentBar=CurrentBar(ALU_code,'D', 1)
    daily_CurrentBar.append(ALU_CurrentBar)

    if len(daily_CurrentBar)<2:
        return
    daily_K_EndTrigger = daily_CurrentBar[0]>0 and daily_CurrentBar[-2]<daily_CurrentBar[-1]
    if daily_K_EndTrigger:
        # 获取所有合约的日线收盘价，计算日线套利组合价格
        daily_contract_prices = {}
        for contract in contracts_in_formula:
            daily_close_arr = Close(contract, 'D', 1)  # 获取日线收盘价
            if len(daily_close_arr) > 0:
                daily_contract_prices[contract] = daily_close_arr[-1]  # 取最新的日线收盘价
                LogInfo(f'合约{contract}日线收盘价: {daily_close_arr[-1]}，日线数据长度: {len(daily_close_arr)}')
            else:
                daily_contract_prices[contract] = contract_prices.get(contract, np.nan)  # 备用小周期价格
                LogInfo(f'合约{contract}无日线数据，使用小周期价格: {contract_prices.get(contract, np.nan)}')
        
        # 计算基于日线数据的套利组合价格
        daily_arb_price = calculate_arbitrage_price(parsed_formulas[0], daily_contract_prices)
        
        # 将日线套利价格添加到price_list
        daily_price_list.append(daily_arb_price)
        npprice_list=np.array(daily_price_list)
        len1=int(均线一)
        Qlenset=len1
        if len(npprice_list)>=Qlenset:
            # 添加检查确保npprice_list不全是NAN值
            if not np.isnan(npprice_list).all():
                QMA1.append(MA(npprice_list,len1)[-1])
            else:
                QMA1.append(np.nan)
        
        LogInfo(f'K线结束-日线套利价格: {daily_arb_price}, 小周期套利价格: {arb_price}, 日期: {Date()}, 时间: {Time()}')
    if len(QMA1)<2:
        return
    len1=int(均线一)
    Disptxt = "自组合套利合约"
    # 使用日线数据绘制组合K线，但颜色判断仍基于当前小周期价格变化
    _current = MAIN_price_list[-1] if len(MAIN_price_list) > 0 else arb_price
    _previous = MAIN_price_list[-2] if len(MAIN_price_list) > 1 else _current
    PlotBar('组合K线',_current,_previous,RGB_Red() if _current>_previous else RGB_Green(), False, True,0,Disptxt)
    PlotNumeric(f'均线一({len1})', QMA1[-1], 0xffffff  , False, False,0,Disptxt)
    for i in range(int(加仓次数上限)):
        NewUT = QMA1[-1]+(1+i)*突破网格点数
        QMAUT1.append(NewUT)
        PlotNumeric(f'上{1+i}轨道',NewUT,RGB_Red(),False,False,0,Disptxt)
        NewDW = QMA1[-1]-(1+i)*突破网格点数
        QMADW1.append(NewDW)
        PlotNumeric(f'下{1+i}轨道',NewDW,RGB_Green(),False,False,0,Disptxt)   
 
    _VTS=VTS(Time())
    TradeEnbTime=TimeTo_Minutes(_VTS[3])-TimeTo_Minutes(_VTS[2])>CloseTime
    TradeOffTime=CloseTime>=TimeTo_Minutes(_VTS[3])-TimeTo_Minutes(_VTS[2])>0

    # 收盘前处理
    if TradeOffTime and not MAIN_K_EndTrigger:  # 避免在K线结束时重复处理
        g_order_manager.handle_market_close(CloseTime)
    
    # 只在实盘状态下进行复杂的订单管理检查
    if HTS == 1:
        # 检查订单超时
        g_order_manager.check_order_timeout(撤单秒数)
        
        # 检查瘸腿单
        g_order_manager.check_lame_leg_orders(瘸腿单自动平仓秒数)
        
        # 主动检查订单状态更新（备用机制）
        g_order_manager.update_order_status_backup()
    # 历史回测状态下，这些检查都是空操作，不需要额外处理
    
    # 计算上突破网格数
    cur_up = int((arb_price - QMA1[-1]) // 突破网格点数)
    cur_up = max(0, min(cur_up, 加仓次数上限))
    
    # 处理上突破事件
    if cur_up > UPBreakout:
        # 检查是否为有效突破（网格编号与上次不同）
        is_valid = cur_up != LastValidUP
        
        if is_valid:
            # 有效突破
            LogInfo(f'【有效上突破】网格{cur_up}，上次有效突破网格{LastValidUP}，日期：{Date()},时间：{Time()},合约：{ALU_code}')
            LastValidUP = cur_up  # 更新上次有效突破的网格编号
            
            # 绘制上突破点
            NewUT = QMA1[-1] + cur_up * 突破网格点数
            PlotDot(f'上突破网格{cur_up}',NewUT, 7, 0x00ffff, False, 0, Disptxt)
            
            # 检查是否达到加仓次数上限
            if cur_up >= 加仓次数上限:
                LogInfo(f'【风险控制】上突破达到加仓次数上限{加仓次数上限}，执行减仓操作')
                
                # 播放报警音频
                if wav_path:
                    play_wav(wav_path)
                    LogInfo(f'播放风险报警音频: {wav_path}')
                
                # 平掉一组最早的多头套利组合
                _close_oldest_arb_order(g_order_manager, "BUY", setNo)
            else:
                # 检查是否需要发送新订单
                need_up_order = g_order_manager.should_trigger_up_breakout(setNo, LastValidUP)
                
                # 如果需要发送新订单且在交易时间内
                if need_up_order and TradeEnbTime:
                    # 获取套利组合的所有合约及系数
                    contracts_with_coefs = get_all_contracts_in_formula(symbol_Id)
                    LogInfo(f'套利组合{symbol_Id}解析结果: {contracts_with_coefs}')
                    
                    # 开多仓（做多套利价差）
                    LogInfo(f'{"实盘" if HTS==1 else "历史回测"}模式-执行上突破买入信号，网格{LastValidUP}')
                    g_order_manager.submit_arb_order(
                        setNo, 
                        "BUY", 
                        下单量, 
                        contracts_with_coefs,
                        第一腿下单模式,
                        后腿下单模式,
                        超价跳数,
                        grid_level=LastValidUP  # 添加网格编号
                    )
                    
                    # 声音提醒
                    if cur_up == 1 and wav_path:  # 第一格突破时提醒
                        play_wav(wav_path)
        # else:
        #     # 无效突破
        #     LogInfo(f'非有效上突破：当前网格{cur_up}与上次有效突破网格{LastValidUP}相同，日期：{Date()},时间：{Time()},合约：{ALU_code}')
    
    # 处理上轨回落事件
    elif cur_up < UPBreakout:
        # 新的平仓逻辑：只有当价格回到最后突破线的上一格之内时，才平掉最后突破线对应的订单
        if cur_up <= LastValidUP - 1 and LastValidUP > 0:
            LogInfo(f'上轨回落到网格{cur_up}，平仓网格{LastValidUP}的套利组合，日期：{Date()},时间：{Time()},合约：{ALU_code}')

            # 找出需要平仓的套利组合订单（只平掉指定网格的订单）
            if HTS == 1:
                # 实盘模式使用pending_arb_orders
                orders_dict = g_order_manager.pending_arb_orders
                prefix = f"ARB_{setNo}_"
            else:
                # 历史回测模式使用executed_arb_orders
                orders_dict = g_order_manager.executed_arb_orders
                prefix = f"HIS_ARB_{setNo}_"
            
            # 只平掉指定网格的BUY订单
            LogInfo(f'{"实盘" if HTS==1 else "历史回测"}模式-检查需要平仓的BUY订单，目标网格{LastValidUP}，当前有{len(orders_dict)}个订单')
            found_orders = 0
            for arb_id, arb_order in list(orders_dict.items()):
                order_grid = arb_order.get('grid_level', 0)
                order_direction = arb_order['direction']
                order_status = arb_order['status']
                LogInfo(f'  检查订单{arb_id}: 方向={order_direction}, 状态={order_status}, 网格={order_grid}')
                
                if (arb_order['direction'] == "BUY" and 
                    arb_id.startswith(prefix) and 
                    arb_order['status'] not in ['CLOSED', 'LAME_LEG_CLOSED'] and
                    arb_order.get('grid_level', 0) == LastValidUP):  # 只平掉当前有效网格的订单
                    found_orders += 1
                    LogInfo(f'{"实盘" if HTS==1 else "历史回测"}模式-上轨回落到网格{cur_up}，平仓网格{LastValidUP}的套利组合{arb_id}')
                    g_order_manager.close_arb_order(arb_id)
                    arb_order['status'] = 'CLOSED'
            
            LogInfo(f'{"实盘" if HTS==1 else "历史回测"}模式-上轨回落平仓完成，共平仓{found_orders}个订单')

            # 重要：回落时更新有效突破的网格编号
            LastValidUP = cur_up 



    if arb_price < QMA1[-1]:
        LastValidUP=0                
    
    # 更新上突破网格计数
    if cur_up != UPBreakout:
        UPBreakout = cur_up

    # 计算下突破网格数
    cur_dw = int((QMA1[-1] - arb_price) // 突破网格点数)
    cur_dw = max(0, min(cur_dw, 加仓次数上限))
    
    # 处理下突破事件
    if cur_dw > DWBreakout:
        # 检查是否为有效突破（网格编号与上次不同）+ 
        is_valid = cur_dw != LastValidDW
        
        if is_valid:
            # 有效突破
            LogInfo(f'【有效下突破】网格{cur_dw}，上次有效突破网格{LastValidDW}，日期：{Date()},时间：{Time()},合约：{ALU_code}')
            LastValidDW = cur_dw  # 更新上次有效突破的网格编号
            
            # 绘制下突破点
            NewDW = QMA1[-1] - cur_dw * 突破网格点数
            PlotDot(f'下突破网格{cur_dw}',NewDW, 6, 0xff00ff, False, 0, Disptxt)
            
            # 检查是否达到加仓次数上限
            if cur_dw >= 加仓次数上限:
                LogInfo(f'【风险控制】下突破达到加仓次数上限{加仓次数上限}，执行减仓操作')
                
                # 播放报警音频
                if wav_path:
                    play_wav(wav_path)
                    LogInfo(f'播放风险报警音频: {wav_path}')
                
                # 平掉一组最早的空头套利组合
                _close_oldest_arb_order(g_order_manager, "SELL", setNo)
            else:
                # 检查是否需要发送新订单
                need_down_order = g_order_manager.should_trigger_down_breakout(setNo, LastValidDW)
                
                # 如果需要发送新订单且在交易时间内
                if need_down_order and TradeEnbTime:
                    # 获取套利组合的所有合约及系数
                    contracts_with_coefs = get_all_contracts_in_formula(symbol_Id)
                    LogInfo(f'套利组合{symbol_Id}解析结果: {contracts_with_coefs}')
                    
                    # 开空仓（做空套利价差）
                    LogInfo(f'{"实盘" if HTS==1 else "历史回测"}模式-执行下突破卖出信号，网格{LastValidDW}')
                    g_order_manager.submit_arb_order(
                        setNo, 
                        "SELL", 
                        下单量, 
                        contracts_with_coefs,
                        第一腿下单模式,
                        后腿下单模式,
                        超价跳数,
                        grid_level=LastValidDW  # 添加网格编号
                    )
                
                # 声音提醒
                if cur_dw == 1 and wav_path:  # 第一格突破时提醒
                    play_wav(wav_path)
        # else:
        #     # 无效突破
        #     LogInfo(f'非有效下突破：当前网格{cur_dw}与上次有效突破网格{LastValidDW}相同，日期：{Date()},时间：{Time()},合约：{ALU_code}')
    
    # 处理下轨回升事件
    elif cur_dw < DWBreakout:
        # 新的平仓逻辑：只有当价格回到最后突破线的上一格之内时，才平掉最后突破线对应的订单
        if cur_dw <= LastValidDW - 1 and LastValidDW > 0:
            LogInfo(f'下轨回升到网格{cur_dw}，平仓网格{LastValidDW}的套利组合，日期：{Date()},时间：{Time()},合约：{ALU_code}')
            
            # 找出需要平仓的套利组合订单（只平掉指定网格的订单）
            if HTS == 1:
                # 实盘模式使用pending_arb_orders
                orders_dict = g_order_manager.pending_arb_orders
                prefix = f"ARB_{setNo}_"
            else:
                # 历史回测模式使用executed_arb_orders
                orders_dict = g_order_manager.executed_arb_orders
                prefix = f"HIS_ARB_{setNo}_"
            
            # 只平掉指定网格的SELL订单
            LogInfo(f'{"实盘" if HTS==1 else "历史回测"}模式-检查需要平仓的SELL订单，目标网格{LastValidDW}，当前有{len(orders_dict)}个订单')
            found_orders = 0
            for arb_id, arb_order in list(orders_dict.items()):
                order_grid = arb_order.get('grid_level', 0)
                order_direction = arb_order['direction']
                order_status = arb_order['status']
                LogInfo(f'  检查订单{arb_id}: 方向={order_direction}, 状态={order_status}, 网格={order_grid}')
                
                if (arb_order['direction'] == "SELL" and 
                    arb_id.startswith(prefix) and 
                    arb_order['status'] not in ['CLOSED', 'LAME_LEG_CLOSED'] and
                    arb_order.get('grid_level', 0) == LastValidDW):  # 只平掉当前有效网格的订单
                    found_orders += 1
                    LogInfo(f'{"实盘" if HTS==1 else "历史回测"}模式-下轨回升到网格{cur_dw}，平仓网格{LastValidDW}的套利组合{arb_id}')
                    g_order_manager.close_arb_order(arb_id)
                    arb_order['status'] = 'CLOSED'
            
            LogInfo(f'{"实盘" if HTS==1 else "历史回测"}模式-下轨回升平仓完成，共平仓{found_orders}个订单')

            # 重要：回升时更新有效突破的网格编号
            LastValidDW = cur_dw


    if arb_price > QMA1[-1]:
        LastValidDW=0 

    # 更新下突破网格计数
    if cur_dw != DWBreakout:
        DWBreakout = cur_dw

    # PlotNumeric(f'上突破次数',LastValidUP,RGB_Red(),False,False,0,"突破跟踪")
    # PlotNumeric(f'下突破次数',LastValidDW,RGB_Green(),False,False,0,"突破跟踪")
    # PlotNumeric(f'上突破次数',UPBreakout,0xffff00,False,False,0,"突破跟踪")
    # PlotNumeric(f'下突破次数',DWBreakout,0x00ffff,False,False,0,"突破跟踪")
    # LogInfo(f'日期：{Date()},时间：{Time()},合约：{ALU_code},当前上突破次数：{UPBreakout},当前下突破次数：{DWBreakout}')


def parse_arbitrage_contracts(arbitrage_formulas, contract_pattern=None):
    if contract_pattern is None:
        contract_pattern = CONTRACT_PATTERN
    unique_contracts = set() 
    parsed_formulas = []
    for formula in arbitrage_formulas:
        contracts = contract_pattern.findall(formula)
        for contract in contracts:
            unique_contracts.add(contract)
        parsed_formula = preprocess_formula(formula, contract_pattern)
        parsed_formulas.append(parsed_formula)
    return list(unique_contracts), parsed_formulas

def preprocess_formula(formula, contract_pattern=None):
    if contract_pattern is None:
        contract_pattern = CONTRACT_PATTERN
    # 标准化公式，在操作符两边添加空格以便分割
    formula = re.sub(r'([+\-*/()])', r' \1 ', formula)
    
    # 分割成标记，保留合约代码的完整性
    tokens = []
    i = 0
    formula_len = len(formula)
    
    while i < formula_len:
        # 检查当前位置是否是合约代码的开始
        match = None
        for j in range(i, formula_len):
            possible_contract = formula[i:j+1]
            if contract_pattern.fullmatch(possible_contract):
                match = possible_contract
        
        if match:
            tokens.append(match)
            i += len(match)
        else:
            # 不是合约代码，按空格分割
            next_space = formula.find(' ', i)
            if next_space == -1:
                tokens.append(formula[i:])
                break
            if next_space > i:
                token = formula[i:next_space].strip()
                if token:
                    tokens.append(token)
            i = next_space + 1
    
    # 处理括号优先级，转换为逆波兰表达式
    output_queue = []
    operator_stack = []
    
    precedence = {'+': 1, '-': 1, '*': 2, '/': 2}
    
    for token in tokens:
        if contract_pattern.fullmatch(token):
            # 合约代码
            output_queue.append(('contract', token))
        elif token.isdigit() or (token[0] == '-' and token[1:].isdigit()):
            # 数字
            output_queue.append(('number', float(token)))
        elif token in ['+', '-', '*', '/']:
            # 操作符
            while (operator_stack and operator_stack[-1] != '(' and 
                   precedence.get(operator_stack[-1], 0) >= precedence.get(token, 0)):
                output_queue.append(('op', operator_stack.pop()))
            operator_stack.append(token)
        elif token == '(':
            operator_stack.append(token)
        elif token == ')':
            while operator_stack and operator_stack[-1] != '(':
                output_queue.append(('op', operator_stack.pop()))
            if operator_stack and operator_stack[-1] == '(':
                operator_stack.pop()  # 弹出左括号
    
    # 处理剩余的操作符
    while operator_stack:
        output_queue.append(('op', operator_stack.pop()))
    
    return output_queue

def get_first_contract(arbitrage_formula, contract_pattern=None):
    if contract_pattern is None:
        contract_pattern = CONTRACT_PATTERN
    match = contract_pattern.search(arbitrage_formula)
    if match:
        return match.group(0)
    return None

# 为所有套利组合获取第一个合约的辅助函数
def get_all_first_contracts(arbitrage_formulas, contract_pattern=None):
    if contract_pattern is None:
        contract_pattern = CONTRACT_PATTERN
    return [get_first_contract(formula, contract_pattern) for formula in arbitrage_formulas]
def calculate_arbitrage_price(parsed_formula, contract_prices):
    """
    使用预处理的公式高效计算套利组合价格
    
    参数:
        parsed_formula: 预处理后的公式结构
        contract_prices: 合约价格字典，键为合约代码，值为价格
    
    返回:
        price: 组合价格
    """
    stack = []
    
    for token_type, token in parsed_formula:
        if token_type == 'contract':
            if token in contract_prices:
                stack.append(contract_prices[token])
            else:
                # 如果找不到价格，使用NaN
                stack.append(np.nan)
        elif token_type == 'number':
            stack.append(token)
        elif token_type == 'op':
            if len(stack) < 2:
                # 处理单目运算符如负号
                if token == '-' and len(stack) == 1:
                    operand = stack.pop()
                    stack.append(-operand)
                continue
                
            right_operand = stack.pop()
            left_operand = stack.pop()
            
            if token == '+':
                stack.append(left_operand + right_operand)
            elif token == '-':
                stack.append(left_operand - right_operand)
            elif token == '*':
                stack.append(left_operand * right_operand)
            elif token == '/':
                # 处理除以零的情况
                if right_operand == 0:
                    stack.append(np.nan)
                else:
                    stack.append(left_operand / right_operand)
    
    return stack[0] if stack else np.nan

# 批量计算多个套利组合价格的函数，适用于tick更新
def calculate_all_arbitrage_prices(parsed_formulas, contract_prices):
    """
    批量计算所有套利组合的价格
    
    参数:
        parsed_formulas: 预处理后的公式结构列表
        contract_prices: 合约价格字典
    
    返回:
        prices: 所有套利组合的价格列表
    """
    return [calculate_arbitrage_price(formula, contract_prices) for formula in parsed_formulas]

# 处理Tick数据更新的函数
def handle_tick_update(tick_data, parsed_formulas, contract_prices, last_prices):
    """
    处理Tick数据更新并计算受影响的套利组合价格
    
    参数:
        tick_data: 包含更新合约代码和价格的字典
        parsed_formulas: 预处理后的公式结构列表
        contract_prices: 当前所有合约的价格字典
        last_prices: 上一次计算的所有套利组合价格
    
    返回:
        updated_prices: 更新后的套利组合价格列表
        affected_indices: 受影响的套利组合索引列表
    """
    # 更新合约价格
    for contract, price in tick_data.items():
        contract_prices[contract] = price
    
    # 找出受影响的套利组合
    affected_indices = []
    updated_prices = list(last_prices)  # 复制上次的价格列表
    
    # 构建合约到套利组合的映射
    contract_to_formulas = defaultdict(list)
    for i, formula in enumerate(parsed_formulas):
        for token_type, token in formula:
            if token_type == 'contract' and token in tick_data:
                contract_to_formulas[token].append(i)
    
    # 只重新计算受影响的套利组合
    affected_indices = set()
    for contract in tick_data:
        affected_indices.update(contract_to_formulas[contract])
    
    for idx in affected_indices:
        updated_prices[idx] = calculate_arbitrage_price(parsed_formulas[idx], contract_prices)
    
    return updated_prices, list(affected_indices)

# 全局变量存储邮件配置
EMAIL_CONFIG = {
    "sender": None,
    "receivers": None,
    "smtp_server": None,
    "smtp_port": None,
    "username": None,
    "password": None,
    "enabled": False
}
def setup_email(sender, receivers, smtp_server, smtp_port, username, password, enabled):
    """
    设置邮件发送参数
    """
    try:
        EMAIL_CONFIG["sender"] = sender
        EMAIL_CONFIG["receivers"] = receivers if isinstance(receivers, list) else [receivers]
        EMAIL_CONFIG["smtp_server"] = smtp_server
        EMAIL_CONFIG["smtp_port"] = smtp_port
        EMAIL_CONFIG["username"] = username
        EMAIL_CONFIG["password"] = password
        EMAIL_CONFIG["enabled"] = enabled
        LogInfo("邮件设置成功")
        return True
    except Exception as e:
        LogInfo(f"邮件设置异常: {str(e)}")
        EMAIL_CONFIG["enabled"] = False
        return False

def send_email(subject, content):
    """
    发送邮件
    """
    if not EMAIL_CONFIG.get("enabled", False):
        LogInfo("邮件功能未启用，无法发送邮件")
        return False
    try:
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        from email.header import Header

        message = MIMEMultipart()
        message['From'] = EMAIL_CONFIG["sender"]
        message['To'] = ','.join(EMAIL_CONFIG["receivers"])
        message['Subject'] = Header(subject, 'utf-8')
        message.attach(MIMEText(content, 'plain', 'utf-8'))

        if EMAIL_CONFIG["smtp_port"] == 465:
            smtp = smtplib.SMTP_SSL(EMAIL_CONFIG["smtp_server"], EMAIL_CONFIG["smtp_port"])
        else:
            smtp = smtplib.SMTP(EMAIL_CONFIG["smtp_server"], EMAIL_CONFIG["smtp_port"])

        smtp.login(EMAIL_CONFIG["username"], EMAIL_CONFIG["password"])
        smtp.sendmail(EMAIL_CONFIG["sender"], EMAIL_CONFIG["receivers"], message.as_string())
        smtp.quit()
        LogInfo("邮件发送成功")
        return True
    except Exception as e:
        LogInfo(f"邮件发送异常: {str(e)}")
        return False

def play_wav(wav_path="alert.wav"):
    """
    播放wav音频文件（Windows平台）
    参数:
        wav_path: wav文件路径，默认为当前目录下alert.wav
    """
    try:
        import winsound
        winsound.PlaySound(wav_path, winsound.SND_FILENAME | winsound.SND_ASYNC)
        LogInfo(f"已播放音频提示: {wav_path}")
    except Exception as e:
        LogInfo(f"音频播放失败: {str(e)}")



def floattime_sum(floatin1, floatin2, len_set=12):  # 高精度浮点时间求和（精确到毫秒）
    # 设置浮点数格式，保留len_set位小数
    lensave = f"%0.{len_set}f"
    
    # 格式化浮点数并提取各时间部分
    def extract_time_parts(floatin):
        strfloat = lensave % floatin
        return int(strfloat[2:4]), int(strfloat[4:6]), int(strfloat[6:8]), int(strfloat[8:11])
    
    h1, m1, s1, ms1 = extract_time_parts(floatin1)
    h2, m2, s2, ms2 = extract_time_parts(floatin2)
    
    # 计算总和并处理进位
    total_ms = ms1 + ms2
    ms_carry = total_ms // 1000
    new_ms = total_ms % 1000
    
    total_s = s1 + s2 + ms_carry
    s_carry = total_s // 60
    new_s = total_s % 60
    
    total_m = m1 + m2 + s_carry
    m_carry = total_m // 60
    new_m = total_m % 60
    
    new_h = h1 + h2 + m_carry
    new_h = min(new_h, 99)  # 限制小时数不超过99
    
    # 组合新的浮点时间字符串并转换回浮点数
    new_str_time = f"0.{new_h:02}{new_m:02}{new_s:02}{new_ms:03}"
    return float(new_str_time)

def TimeTo_Minutes(time_in):
    timestr='%0.6f'%time_in
    hsave=int(timestr[2:4])
    msave=int(timestr[4:6])
    tcout=hsave*60+msave
    return tcout

def SessionOpenTime(contractId=''):  # 获取交易时段开盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler:continue
        tlout.append(GetSessionStartTime(contractId, i))  # 获取每个交易时段的开盘时间并加入列表
    return tlout

def SessionCloseTime(contractId=''):  # 获取交易时段收盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler-1:continue
        tlout.append(GetSessionEndTime(contractId, i))  # 获取每个交易时段的收盘时间并加入列表
    return tlout

def VTS(time_in, contractId=''):  # 根据输入时间和合约ID计算交易时段
    RTS, CTS, TSession = [], [], []  # 初始化三个列表，用于存储修正后的时间、收盘时间和交易时段
    opentimet = SessionOpenTime(contractId)  # 获取所有交易时段的开盘时间
    Closetimet = SessionCloseTime(contractId)  # 获取所有交易时段的收盘时间
    
    for open_time, close_time in zip(opentimet, Closetimet):
        if time_in >= open_time:  # 判断输入时间是否在开盘时间之后
            RTS.append(time_in)  # 如果是，加入RTS列表
        else:
            RTS.append(floattime_sum(time_in, 0.24))  # 如果不是，修正时间后加入RTS列表
        
        if close_time >= open_time:  # 判断收盘时间是否在开盘时间之后
            CTS.append(close_time)  # 如果是，加入CTS列表
        else:
            CTS.append(floattime_sum(close_time, 0.24))  # 如果不是，修正时间后加入CTS列表
        
        if open_time <= RTS[-1] <= CTS[-1]:  # 判断修正后的时间是否在交易时段内
            TSession.append(len(RTS) - 1)  # 如果是，加入TSession列表

    if len(TSession) == 1:  # 如果只有一个交易时段
        idx = TSession[0]
        return idx, opentimet[idx], RTS[idx], CTS[idx]  # 返回交易时段和相关时间
    else:
        return -1, time_in, time_in, time_in  # 否则返回默认值

class HistoricalArbitrageOrderManager(TriggerManager):
    """
    历史回测专用的套利组合订单管理类，继承TriggerManager
    专注于历史回测场景，使用his_族函数执行订单，不涉及复杂的订单状态管理
    """
    def __init__(self):
        super().__init__()
        # 简化的状态跟踪，只记录基本信息
        self.executed_arb_orders = {}  # 已执行的套利组合记录
        self.triggered_breakouts = {}  # 突破记录
    
    def reset_states(self):
        """重置所有状态"""
        super().reset_states()
        self.executed_arb_orders = {}
        self.triggered_breakouts = {}
    
    def should_trigger_up_breakout(self, setNo, LastValidUP):
        """判断是否需要触发新的上突破订单"""
        if setNo not in self.triggered_breakouts:
            self.triggered_breakouts[setNo] = {'LastValidUP': 0, 'LastValidDW': 0}
        need_up_order = (LastValidUP > 0 and LastValidUP != self.triggered_breakouts[setNo]['LastValidUP'])
        if need_up_order:
            LogInfo(f'历史回测-检测到新的有效上突破，套利组合:{setNo}，网格:{LastValidUP}')
            self.triggered_breakouts[setNo]['LastValidUP'] = LastValidUP
        return need_up_order

    def should_trigger_down_breakout(self, setNo, LastValidDW):
        """判断是否需要触发新的下突破订单"""
        if setNo not in self.triggered_breakouts:
            self.triggered_breakouts[setNo] = {'LastValidUP': 0, 'LastValidDW': 0}
        need_down_order = (LastValidDW > 0 and LastValidDW != self.triggered_breakouts[setNo]['LastValidDW'])
        if need_down_order:
            LogInfo(f'历史回测-检测到新的有效下突破，套利组合:{setNo}，网格:{LastValidDW}')
            self.triggered_breakouts[setNo]['LastValidDW'] = LastValidDW
        return need_down_order
    
    def submit_arb_order(self, setNo, direction, qty, contracts_with_coefs, first_leg_mode='最新价', other_leg_mode='最新价', price_offset=0, order_type='2', grid_level=0):
        """
        历史回测模式下提交套利组合订单，直接执行不需要复杂管理
        
        参数:
            setNo: 套利组合编号
            direction: 方向，"BUY"表示做多价差，"SELL"表示做空价差
            qty: 基础订单数量
            contracts_with_coefs: 合约列表及系数，[(contract, coef), ...]
            first_leg_mode: 第一腿下单模式（历史回测中忽略）
            other_leg_mode: 后腿下单模式（历史回测中忽略）
            price_offset: 超价跳数（历史回测中忽略）
            order_type: 订单类型（历史回测中忽略）
            grid_level: 网格编号（用于标记订单所属网格）
        
        返回:
            arb_id: 套利组合订单ID
        """
        import time
        arb_id = f"HIS_ARB_{setNo}_{direction}_{int(time.time())}"
        
        # 提取合约和系数
        contracts = [c[0] for c in contracts_with_coefs]
        coefficients = {c[0]: c[1] for c in contracts_with_coefs}
        
        # 解析套利组合公式，获取各合约在公式中的方向和倍数
        global symbol_Id
        formula_info = parse_arbitrage_directions_and_coefficients(symbol_Id)
        formula_directions = {contract: info[0] for contract, info in formula_info.items()}  # 方向
        formula_coefficients = {contract: info[1] for contract, info in formula_info.items()}  # 倍数
        
        LogInfo(f'历史回测-提交套利组合订单，ID:{arb_id}，价差方向:{direction}，基础数量:{qty}，网格:{grid_level}，合约及系数:{contracts_with_coefs}')
        LogInfo(f'历史回测-公式解析: {formula_info}')
        LogInfo(f'历史回测-外部系数: {coefficients}, 公式系数: {formula_coefficients}')
        
        # 直接执行所有合约的订单
        success_count = 0
        executed_details = []
        
        for contract, external_coef in contracts_with_coefs:
            # 计算最终的下单数量：基础数量 * 外部系数 * 公式系数
            formula_coef = formula_coefficients.get(contract, 1)
            final_coef = external_coef * formula_coef
            order_qty = qty * final_coef
            
            # 获取当前价格作为历史订单价格
            try:
                price = Q_Last(contract)
                if price <= 0:
                    LogInfo(f'历史回测-合约{contract}无法获取价格，跳过')
                    continue
                
                # 确定该合约的实际交易方向
                formula_direction = formula_directions.get(contract, 'BUY')  # 公式中的方向
                
                # 根据价差方向和公式方向确定实际交易方向
                if direction == "BUY":  # 做多价差
                    actual_direction = formula_direction  # 与公式方向一致
                else:  # 做空价差 
                    actual_direction = "SELL" if formula_direction == "BUY" else "BUY"  # 与公式方向相反
                
                # 执行历史订单
                if actual_direction == "BUY":
                    if self.his_trigger_long(order_qty, price, contract):
                        success_count += 1
                        executed_details.append(f'{contract}买入{order_qty}手@{price}(外部系数:{external_coef}*公式系数:{formula_coef})')
                        LogInfo(f'历史回测-合约{contract}买入开仓成功，数量:{order_qty}，价格:{price}，总系数:{final_coef}')
                else:
                    if self.his_trigger_short(order_qty, price, contract):
                        success_count += 1
                        executed_details.append(f'{contract}卖出{order_qty}手@{price}(外部系数:{external_coef}*公式系数:{formula_coef})')
                        LogInfo(f'历史回测-合约{contract}卖出开仓成功，数量:{order_qty}，价格:{price}，总系数:{final_coef}')
                        
            except Exception as e:
                LogInfo(f'历史回测-合约{contract}订单执行异常:{str(e)}')
        
        # 记录执行结果
        self.executed_arb_orders[arb_id] = {
            'direction': direction,
            'qty': qty,
            'contracts': contracts,
            'coefficients': coefficients,  # 外部系数
            'formula_directions': formula_directions,  # 保存公式方向信息
            'formula_coefficients': formula_coefficients,  # 保存公式系数信息
            'executed_details': executed_details,  # 保存执行详情
            'success_count': success_count,
            'total_contracts': len(contracts),
            'status': 'COMPLETED' if success_count == len(contracts) else 'PARTIAL',
            'submit_time': time.time(),
            'grid_level': grid_level  # 添加网格编号
        }
        
        LogInfo(f'历史回测-套利组合{arb_id}执行完成，成功{success_count}/{len(contracts)}个合约，网格{grid_level}')
        LogInfo(f'历史回测-执行详情: {executed_details}')
        return arb_id
    
    def close_arb_order(self, arb_id, force_market=False):
        """
        历史回测模式下平仓套利组合订单
        
        参数:
            arb_id: 套利组合订单ID
            force_market: 是否强制市价平仓（历史回测中忽略）
        """
        if arb_id not in self.executed_arb_orders:
            LogInfo(f'历史回测-未找到套利组合订单，ID:{arb_id}')
            return
        
        arb_order = self.executed_arb_orders[arb_id]
        
        if arb_order['status'] == 'CLOSED':
            LogInfo(f'历史回测-套利组合{arb_id}已经关闭')
            return
        
        grid_level = arb_order.get('grid_level', 0)
        LogInfo(f'历史回测-开始平仓套利组合{arb_id}，网格{grid_level}，方向{arb_order["direction"]}')
        
        # 直接执行所有合约的平仓订单
        success_count = 0
        close_details = []
        formula_directions = arb_order.get('formula_directions', {})
        formula_coefficients = arb_order.get('formula_coefficients', {})
        
        for contract in arb_order['contracts']:
            external_coef = arb_order['coefficients'].get(contract, 1)
            formula_coef = formula_coefficients.get(contract, 1)
            final_coef = external_coef * formula_coef
            order_qty = arb_order['qty'] * final_coef
            
            try:
                price = Q_Last(contract)
                if price <= 0:
                    LogInfo(f'历史回测-合约{contract}无法获取平仓价格，跳过')
                    continue
                
                # 确定该合约在开仓时的实际交易方向
                formula_direction = formula_directions.get(contract, 'BUY')
                if arb_order['direction'] == "BUY":  # 原来做多价差
                    open_direction = formula_direction  # 开仓时与公式方向一致
                else:  # 原来做空价差
                    open_direction = "SELL" if formula_direction == "BUY" else "BUY"  # 开仓时与公式方向相反
                
                # 平仓时执行反向操作
                if open_direction == "BUY":  # 原来买入开仓，现在卖出平仓
                    if self.his_trigger_exit_long(order_qty, price, contract):
                        success_count += 1
                        close_details.append(f'{contract}卖出平仓{order_qty}手@{price}(系数:{final_coef})')
                        LogInfo(f'历史回测-合约{contract}卖出平仓成功，数量:{order_qty}，价格:{price}，总系数:{final_coef}')
                else:  # 原来卖出开仓，现在买入平仓
                    if self.his_trigger_exit_short(order_qty, price, contract):
                        success_count += 1
                        close_details.append(f'{contract}买入平仓{order_qty}手@{price}(系数:{final_coef})')
                        LogInfo(f'历史回测-合约{contract}买入平仓成功，数量:{order_qty}，价格:{price}，总系数:{final_coef}')
                        
            except Exception as e:
                LogInfo(f'历史回测-合约{contract}平仓异常:{str(e)}')
        
        # 更新状态
        arb_order['status'] = 'CLOSED'
        arb_order['close_success_count'] = success_count
        arb_order['close_details'] = close_details
        arb_order['close_time'] = time.time()
        
        LogInfo(f'历史回测-套利组合{arb_id}平仓完成，成功{success_count}/{len(arb_order["contracts"])}个合约')
        LogInfo(f'历史回测-平仓详情: {close_details}')
    
    # 兼容接口，历史回测中这些函数为空操作
    def check_order_timeout(self, timeout_seconds=300):
        """历史回测中无需超时检查"""
        pass
    
    def check_lame_leg_orders(self, lame_leg_timeout_seconds=60):
        """历史回测中无需瘸腿单检查"""
        pass
    
    def update_order_status_backup(self):
        """历史回测中无需订单状态更新"""
        pass
    
    def handle_market_close(self, minutes_before_close=3):
        """历史回测中的收盘处理，简化版本"""
        LogInfo(f'历史回测-收盘处理，平仓所有套利组合')
        
        # 平仓所有未关闭的套利组合
        for arb_id, arb_order in self.executed_arb_orders.items():
            if arb_order['status'] != 'CLOSED':
                self.close_arb_order(arb_id)

class ArbitrageOrderManager:
    """
    套利组合订单管理系统，负责处理套利组合的订单执行、跟踪和管理
    """
    def __init__(self, trigger_manager=None):
        self.trigger_manager = trigger_manager if trigger_manager else TriggerManager()
        # 订单字典 {order_id: {contract, direction, offset, price, qty, status, submit_time}}
        self.orders = {}
        # 当前活跃的突破事件 {setNo: {up_valid: bool, up_grid: int, down_valid: bool, down_grid: int}}
        self.active_breakouts = {}
        # 待执行的套利组合订单 {setNo: {direction, qty, contracts, status, submit_time}}
        self.pending_arb_orders = {}
        # 已执行的套利组合订单的成员合约订单ID映射 {arb_id: [order_ids]}
        self.arb_order_mapping = {}
        # 已经触发过的有效突破记录 {setNo: {LastValidUP, LastValidDW}}
        self.triggered_breakouts = {}
        # 瘸腿单检测定时器 {arb_id: start_time}
        self.lame_leg_timers = {}
    
    def reset_states(self):
        """重置所有状态"""
        self.orders = {}
        self.active_breakouts = {}
        self.pending_arb_orders = {}
        self.arb_order_mapping = {}
        self.triggered_breakouts = {}
    
    def should_trigger_up_breakout(self, setNo, LastValidUP):
        """
        判断是否需要触发新的上突破订单，并更新记录
        """
        if setNo not in self.triggered_breakouts:
            self.triggered_breakouts[setNo] = {'LastValidUP': 0, 'LastValidDW': 0}
        need_up_order = (LastValidUP > 0 and LastValidUP != self.triggered_breakouts[setNo]['LastValidUP'])
        if need_up_order:
            LogInfo(f'检测到新的有效上突破，需要发送订单。套利组合:{setNo}，网格:{LastValidUP}')
            self.triggered_breakouts[setNo]['LastValidUP'] = LastValidUP
        return need_up_order

    def should_trigger_down_breakout(self, setNo, LastValidDW):
        """
        判断是否需要触发新的下突破订单，并更新记录
        """
        if setNo not in self.triggered_breakouts:
            self.triggered_breakouts[setNo] = {'LastValidUP': 0, 'LastValidDW': 0}
        need_down_order = (LastValidDW > 0 and LastValidDW != self.triggered_breakouts[setNo]['LastValidDW'])
        if need_down_order:
            LogInfo(f'检测到新的有效下突破，需要发送订单。套利组合:{setNo}，网格:{LastValidDW}')
            self.triggered_breakouts[setNo]['LastValidDW'] = LastValidDW
        return need_down_order
    
    def submit_arb_order(self, setNo, direction, qty, contracts_with_coefs, first_leg_mode='最新价', other_leg_mode='最新价', price_offset=0, order_type='2', grid_level=0):
        """
        提交套利组合订单
        
        参数:
            setNo: 套利组合编号
            direction: 方向，"BUY"或"SELL"
            qty: 基础订单数量
            contracts_with_coefs: 合约列表及系数，[(contract, coef), ...]
            first_leg_mode: 第一腿下单模式，'最新价'/'对价'/'超价'
            other_leg_mode: 后腿下单模式，'最新价'/'对价'/'超价'
            price_offset: 超价跳数
            order_type: 订单类型，默认'2'为限价单，'1'为市价单
            grid_level: 网格编号（用于标记订单所属网格）
        
        返回:
            arb_id: 套利组合订单ID
        """
        import time
        arb_id = f"ARB_{setNo}_{direction}_{int(time.time())}"
        
        # 提取合约和系数
        contracts = [c[0] for c in contracts_with_coefs]
        coefficients = {c[0]: c[1] for c in contracts_with_coefs}
        
        # 解析套利组合公式，获取各合约在公式中的方向和倍数
        global symbol_Id
        formula_info = parse_arbitrage_directions_and_coefficients(symbol_Id)
        formula_directions = {contract: info[0] for contract, info in formula_info.items()}  # 方向
        formula_coefficients = {contract: info[1] for contract, info in formula_info.items()}  # 倍数
        
        LogInfo(f'实盘-套利组合合约系数: {coefficients}')
        LogInfo(f'实盘-公式解析: {formula_info}')
        LogInfo(f'实盘-外部系数: {coefficients}, 公式系数: {formula_coefficients}')
        
        self.pending_arb_orders[arb_id] = {
            'direction': direction,
            'qty': qty,
            'contracts': contracts,
            'coefficients': coefficients,  # 存储外部系数
            'formula_directions': formula_directions,  # 存储公式方向信息
            'formula_coefficients': formula_coefficients,  # 存储公式系数信息
            'executed_qty': 0,
            'status': 'PENDING',
            'submit_time': time.time(),
            'order_ids': [],
            'completed_contracts': set(),
            'contract_executed_qty': {contract: 0 for contract in contracts},  # 跟踪每个合约已执行数量
            'first_leg_mode': first_leg_mode,
            'other_leg_mode': other_leg_mode,
            'price_offset': price_offset,
            'order_type': order_type,  # 保存订单类型
            'first_leg_filled': False,  # 第一腿是否已成交
            'first_leg_contract': contracts[-1] if contracts else None,  # 第一腿合约（低流动性）
            'grid_level': grid_level  # 添加网格编号
        }
        
        LogInfo(f'提交套利组合订单，ID:{arb_id}，方向:{direction}，基础数量:{qty}，合约及系数:{contracts_with_coefs}')
        
        # 开始分批执行订单
        self.execute_arb_order(arb_id)
        
        return arb_id
    
    def execute_arb_order(self, arb_id):
        """
        执行套利组合订单，按照1手1手分批执行，考虑合约系数
        
        参数:
            arb_id: 套利组合订单ID
        """
        if arb_id not in self.pending_arb_orders:
            LogInfo(f'错误：未找到套利组合订单，ID:{arb_id}')
            return
        
        arb_order = self.pending_arb_orders[arb_id]
        
        # 检查是否全部执行完毕
        if arb_order['executed_qty'] >= arb_order['qty']:
            arb_order['status'] = 'COMPLETED'
            LogInfo(f'套利组合订单执行完毕，ID:{arb_id}')
            return
        
        # 获取当前要执行的合约列表(已经完成的合约会被排除)
        remaining_contracts = []
        for contract in arb_order['contracts']:
            # 计算该合约应该执行的总手数（基础手数 * 系数）
            total_qty_needed = arb_order['qty'] * arb_order['coefficients'].get(contract, 1)
            # 如果已执行手数小于总需求手数，则加入待执行列表
            if arb_order['contract_executed_qty'].get(contract, 0) < total_qty_needed:
                remaining_contracts.append(contract)
        
        if not remaining_contracts:
            # 如果所有合约都执行完毕但数量还没达到，可能需要重新开始
            arb_order['completed_contracts'] = set()
            arb_order['contract_executed_qty'] = {contract: 0 for contract in arb_order['contracts']}
            remaining_contracts = arb_order['contracts']
        
        # 从最后一个开始执行(优先低流动性合约)
        contract = remaining_contracts[-1]
        # 获取合约系数（外部系数 * 公式系数）
        external_coef = arb_order['coefficients'].get(contract, 1)
        formula_coefficients = arb_order.get('formula_coefficients', {})
        formula_coef = formula_coefficients.get(contract, 1)
        final_coef = external_coef * formula_coef
        
        # 判断是第一腿还是后腿
        is_first_leg = (contract == arb_order['first_leg_contract'])
        price_mode = arb_order['first_leg_mode'] if is_first_leg else arb_order['other_leg_mode']
        price_offset = arb_order['price_offset'] if 'Super' in price_mode or '超价' in price_mode else 0
        order_type = arb_order.get('order_type', '2')  # 从套利订单中获取订单类型，默认为限价单
        
        try:
            # 获取备用价格
            fallback_price = Q_Last(contract)
            if fallback_price <= 0:
                LogInfo(f'错误：无法获取合约{contract}的有效价格，跳过')
                # 将该合约标记为已完成，下次尝试其他合约
                arb_order['completed_contracts'].add(contract)
                return
            
            # 执行实际订单，使用最终系数作为下单手数
            total_needed = final_coef * arb_order['qty']
            already_executed = arb_order['contract_executed_qty'].get(contract, 0)
            order_qty = min(final_coef, total_needed - already_executed)
            
            # 确定该合约的实际交易方向
            formula_directions = arb_order.get('formula_directions', {})
            formula_direction = formula_directions.get(contract, 'BUY')  # 公式中的方向
            
            # 根据价差方向和公式方向确定实际交易方向
            if arb_order['direction'] == "BUY":  # 做多价差
                actual_direction = formula_direction  # 与公式方向一致
            else:  # 做空价差 
                actual_direction = "SELL" if formula_direction == "BUY" else "BUY"  # 与公式方向相反
            
            LogInfo(f'实盘-准备为合约{contract}下单，腿类型:{"第一腿" if is_first_leg else "后腿"}，价格模式:{price_mode}，实际方向:{actual_direction}，手数:{order_qty}，总系数:{final_coef}(外部:{external_coef}*公式:{formula_coef})')
            
            if order_qty <= 0:
                LogInfo(f'合约{contract}无需下单，已达到所需手数')
                arb_order['completed_contracts'].add(contract)
                return
            
            # 根据实际交易方向执行订单
            if actual_direction == "BUY":
                success, order_id = self.trigger_manager.tim_trigger_long(
                    order_qty, fallback_price, contract, order_type, price_mode, price_offset)
            else:
                success, order_id = self.trigger_manager.tim_trigger_short(
                    order_qty, fallback_price, contract, order_type, price_mode, price_offset)
            
            if success and order_id:
                LogInfo(f'实盘-套利组合{arb_id}成员合约{contract}订单发送成功，订单号:{order_id}，价格模式:{price_mode}，实际方向:{actual_direction}，手数:{order_qty}')
                
                # 保存订单信息
                self.orders[order_id] = {
                    'contract': contract,
                    'direction': actual_direction,  # 保存实际交易方向
                    'arb_direction': arb_order['direction'],  # 保存套利方向
                    'offset': 'OPEN',  # 开仓
                    'price': fallback_price,
                    'qty': order_qty,
                    'status': 'SUBMITTED',
                    'submit_time': time.time(),
                    'arb_id': arb_id,
                    'is_first_leg': is_first_leg
                }
                
                # 更新套利组合订单信息
                arb_order['order_ids'].append(order_id)
                arb_order['executed_qty'] += 1  # 基础执行单位增加1
                arb_order['contract_executed_qty'][contract] = arb_order['contract_executed_qty'].get(contract, 0) + order_qty
                
                # 如果该合约已经达到所需手数，将其标记为已完成
                if arb_order['contract_executed_qty'][contract] >= arb_order['qty'] * final_coef:
                    arb_order['completed_contracts'].add(contract)
                
                # 如果需要执行多手，将继续在报单回报中执行下一手
                LogInfo(f'等待订单{order_id}成交后继续执行后续合约...')
            else:
                LogInfo(f'套利组合{arb_id}成员合约{contract}订单发送失败，跳过该合约')
        except Exception as e:
            LogInfo(f'套利组合订单执行异常:{str(e)}')
    
    def close_arb_order(self, arb_id, force_market=False):
        """
        平仓套利组合订单
        
        参数:
            arb_id: 套利组合订单ID
            force_market: 是否强制市价平仓
        """
        if arb_id not in self.pending_arb_orders:
            LogInfo(f'错误：未找到套利组合订单，ID:{arb_id}')
            return
        
        arb_order = self.pending_arb_orders[arb_id]
        
        # 找出所有已成交的开仓订单
        filled_orders = []
        for order_id in arb_order['order_ids']:
            if order_id in self.orders and self.orders[order_id]['status'] == 'FILLED':
                filled_orders.append(self.orders[order_id])
        
        if not filled_orders:
            LogInfo(f'套利组合{arb_id}没有需要平仓的已成交订单')
            return
        
        # 按合约分组
        contracts_orders = {}
        for order in filled_orders:
            contract = order['contract']
            if contract not in contracts_orders:
                contracts_orders[contract] = []
            contracts_orders[contract].append(order)
        
        # 根据合约列表优先级顺序平仓(最后一个合约可以用市价单)
        contracts = list(contracts_orders.keys())
        contracts.sort(key=lambda x: arb_order['contracts'].index(x))
        
        for i, contract in enumerate(contracts):
            orders = contracts_orders[contract]
            total_qty = sum(order['qty'] for order in orders)
            
            # 最后一个合约使用市价单，其他用限价单
            use_market = force_market or (i == len(contracts) - 1)
            order_type = '1' if use_market else '2'
            
            try:
                # 确定平仓方向
                if arb_order['direction'] == "BUY":
                    # 多单平仓
                    price = Q_AskPrice(contract) if not use_market else 0
                    if price <= 0 and not use_market:
                        price = Q_Last(contract)
                        LogInfo(f'警告：合约{contract}无卖一价，使用最新价{price}')
                    
                    order_infos = self.trigger_manager.tim_trigger_exit_long(total_qty, price, contract, order_type)
                else:
                    # 空单平仓
                    price = Q_BidPrice(contract) if not use_market else 0
                    if price <= 0 and not use_market:
                        price = Q_Last(contract)
                        LogInfo(f'警告：合约{contract}无买一价，使用最新价{price}')
                    
                    order_infos = self.trigger_manager.tim_trigger_exit_short(total_qty, price, contract, order_type)
                
                # 记录平仓订单
                for success, order_id, offset_type, qty in order_infos:
                    if success and order_id:
                        LogInfo(f'套利组合{arb_id}成员合约{contract}平仓成功，订单号:{order_id}，类型:{offset_type}，数量:{qty}')
                        
                        # 保存订单信息
                        self.orders[order_id] = {
                            'contract': contract,
                            'direction': "SELL" if arb_order['direction'] == "BUY" else "BUY",
                            'offset': 'CLOSE',  # 平仓
                            'price': price,
                            'qty': qty,
                            'status': 'SUBMITTED',
                            'submit_time': time.time(),
                            'arb_id': arb_id
                        }
                        
                        # 更新套利组合订单信息
                        arb_order['order_ids'].append(order_id)
            except Exception as e:
                LogInfo(f'套利组合平仓异常:{str(e)}')
    
    def is_order_cancellable(self, order_id):
        """
        检查订单是否处于可撤销状态
        
        参数:
            order_id: 订单ID
            
        返回:
            bool: True表示可撤销，False表示不可撤销
        """
        try:
            # 获取订单状态
            status = A_OrderStatus(order_id)
            
            # 可撤销的状态：已发送、已受理、已生效、已排队、部分成交
            cancellable_statuses = ['0', '1', '3', '4', '5']
            
            if status in cancellable_statuses:
                LogInfo(f'订单{order_id}状态为{status}，可以撤销')
                return True
            elif status == '':
                LogInfo(f'订单{order_id}状态为空，可能已不存在或无效')
                return False
            else:
                LogInfo(f'订单{order_id}状态为{status}，不可撤销')
                return False
                
        except Exception as e:
            LogInfo(f'检查订单{order_id}状态异常:{str(e)}')
            return False
    
    def cancel_pending_orders(self, arb_id=None):
        """
        撤销未成交的挂单，在构建撤单列表时检查订单状态
        
        参数:
            arb_id: 指定的套利组合订单ID，None表示撤销所有未成交订单
        """
        # 找出所有需要撤销的订单ID，在此阶段检查订单状态
        to_cancel = []
        
        if arb_id:
            # 只撤销指定套利组合的订单
            if arb_id in self.pending_arb_orders:
                for order_id in self.pending_arb_orders[arb_id]['order_ids']:
                    if (order_id in self.orders and 
                        self.orders[order_id]['status'] in ['SUBMITTED', 'PARTIALLY_FILLED'] and
                        self.is_order_cancellable(order_id)):
                        to_cancel.append(order_id)
        else:
            # 撤销所有未成交订单
            for order_id, order in self.orders.items():
                if (order['status'] in ['SUBMITTED', 'PARTIALLY_FILLED'] and
                    self.is_order_cancellable(order_id)):
                    to_cancel.append(order_id)
        
        # 逐个撤单，此时列表中的订单都已确认可撤销
        for order_id in to_cancel:
            try:
                # 发送撤单指令
                if A_DeleteOrder(order_id):
                    LogInfo(f'订单{order_id}撤销指令已发送')
                    self.orders[order_id]['status'] = 'CANCELLING'
                else:
                    LogInfo(f'订单{order_id}撤销失败')
            except Exception as e:
                LogInfo(f'撤单异常:{str(e)}')
    
    def on_order_status(self, order_id, status, traded_qty=0):
        """
        处理订单状态变更
        
        参数:
            order_id: 订单ID
            status: 新状态
            traded_qty: 已成交数量
        """
        if order_id not in self.orders:
            LogInfo(f'收到未知订单{order_id}的状态更新，状态:{status}')
            return
        
        order = self.orders[order_id]
        old_status = order['status']
        order['status'] = status
        
        LogInfo(f'订单{order_id}状态从{old_status}变更为{status}，合约:{order["contract"]}，方向:{order["direction"]}，手数:{order["qty"]}')
        
        if 'arb_id' in order and order['arb_id'] in self.pending_arb_orders:
            arb_id = order['arb_id']
            arb_order = self.pending_arb_orders[arb_id]
            
            # 对于已成交的开仓订单，继续下一手订单
            if status == 'FILLED' and order['offset'] == 'OPEN':
                LogInfo(f'订单{order_id}已成交，继续执行套利组合{arb_id}的后续订单')
                
                # 检查是否是第一腿成交
                if order.get('is_first_leg', False):
                    LogInfo(f'第一腿合约{order["contract"]}已成交，开启瘸腿单检测')
                    arb_order['first_leg_filled'] = True
                    # 开始瘸腿单检测计时
                    import time
                    self.lame_leg_timers[arb_id] = time.time()
                
                # 如果还有未执行完的数量，继续执行
                if arb_order['executed_qty'] < arb_order['qty']:
                    self.execute_arb_order(arb_id)
            
            # 处理已撤单的情况
            elif status == 'CANCELLED' and order['offset'] == 'OPEN':
                # 将该订单从套利组合订单已执行数量中减去
                if traded_qty == 0:  # 完全没有成交
                    LogInfo(f'订单{order_id}已撤单且未成交，更新套利组合{arb_id}的执行数量')
                    arb_order['executed_qty'] -= 1
                    # 将该合约的已执行手数减去
                    contract = order['contract']
                    arb_order['contract_executed_qty'][contract] = max(0, arb_order['contract_executed_qty'].get(contract, 0) - order['qty'])
                    # 将该合约从已完成集合中移除
                    if contract in arb_order['completed_contracts']:
                        arb_order['completed_contracts'].remove(contract)
    
    def check_lame_leg_orders(self, lame_leg_timeout_seconds=60):
        """
        检查瘸腿单情况，并进行善后处理
        
        参数:
            lame_leg_timeout_seconds: 瘸腿单自动平仓秒数
        """
        import time
        current_time = time.time()
        
        lame_leg_to_process = []
        
        # 检查所有正在计时的套利组合
        for arb_id, start_time in list(self.lame_leg_timers.items()):
            if current_time - start_time > lame_leg_timeout_seconds:
                if arb_id in self.pending_arb_orders:
                    arb_order = self.pending_arb_orders[arb_id]
                    
                    # 检查是否确实存在瘸腿单情况
                    if arb_order['first_leg_filled'] and arb_order['status'] != 'COMPLETED':
                        # 检查是否有其他合约未完全成交
                        first_leg_contract = arb_order['first_leg_contract']
                        first_leg_needed = arb_order['qty'] * arb_order['coefficients'].get(first_leg_contract, 1)
                        first_leg_filled = arb_order['contract_executed_qty'].get(first_leg_contract, 0)
                        
                        has_incomplete_legs = False
                        for contract in arb_order['contracts']:
                            if contract != first_leg_contract:
                                external_coef = arb_order['coefficients'].get(contract, 1)
                                formula_coef = arb_order.get('formula_coefficients', {}).get(contract, 1)
                                needed_qty = arb_order['qty'] * external_coef * formula_coef
                                filled_qty = arb_order['contract_executed_qty'].get(contract, 0)
                                if filled_qty < needed_qty:
                                    has_incomplete_legs = True
                                    break
                        
                        if has_incomplete_legs and first_leg_filled > 0:
                            LogInfo(f'检测到瘸腿单: 套利组合{arb_id}，第一腿{first_leg_contract}已成交{first_leg_filled}手，但其他腿未完全成交')
                            lame_leg_to_process.append(arb_id)
                
                # 移除计时器
                del self.lame_leg_timers[arb_id]
        
        # 处理瘸腿单
        for arb_id in lame_leg_to_process:
            self._handle_lame_leg_order(arb_id)
    
    def _handle_lame_leg_order(self, arb_id):
        """
        处理瘸腿单，对已成交的第一腿进行平仓操作
        
        参数:
            arb_id: 套利组合订单ID
        """
        if arb_id not in self.pending_arb_orders:
            return
        
        arb_order = self.pending_arb_orders[arb_id]
        first_leg_contract = arb_order['first_leg_contract']
        
        if not first_leg_contract:
            LogInfo(f'套利组合{arb_id}未找到第一腿合约')
            return
        
        # 撤销所有该套利组合的未成交订单
        LogInfo(f'瘸腿单处理: 撤销套利组合{arb_id}的所有未成交订单')
        self.cancel_pending_orders(arb_id)
        
        # 计算第一腿已成交的数量
        first_leg_filled = arb_order['contract_executed_qty'].get(first_leg_contract, 0)
        
        if first_leg_filled > 0:
            LogInfo(f'瘸腿单处理: 对第一腿合约{first_leg_contract}进行市价平仓，数量:{first_leg_filled}')
            
            try:
                # 使用市价单平仓第一腿
                if arb_order['direction'] == "BUY":
                    # 如果原来是买入开仓，现在要卖出平仓
                    order_infos = self.trigger_manager.tim_trigger_exit_long(first_leg_filled, 0, first_leg_contract, '1')
                else:
                    # 如果原来是卖出开仓，现在要买入平仓
                    order_infos = self.trigger_manager.tim_trigger_exit_short(first_leg_filled, 0, first_leg_contract, '1')
                
                # 记录平仓订单
                for success, order_id, offset_type, qty in order_infos:
                    if success and order_id:
                        LogInfo(f'瘸腿单平仓订单发送成功: 合约{first_leg_contract}，订单号:{order_id}，类型:{offset_type}，数量:{qty}')
                        
                        # 保存订单信息
                        self.orders[order_id] = {
                            'contract': first_leg_contract,
                            'direction': "SELL" if arb_order['direction'] == "BUY" else "BUY",
                            'offset': 'CLOSE',  # 平仓
                            'price': 0,  # 市价单
                            'qty': qty,
                            'status': 'SUBMITTED',
                            'submit_time': time.time(),
                            'arb_id': arb_id,
                            'is_lame_leg_close': True  # 标记为瘸腿单平仓
                        }
                        
                        # 更新套利组合订单信息
                        arb_order['order_ids'].append(order_id)
                
            except Exception as e:
                LogInfo(f'瘸腿单平仓异常: {str(e)}')
        
        # 标记套利组合为已关闭
        arb_order['status'] = 'LAME_LEG_CLOSED'
        LogInfo(f'套利组合{arb_id}已标记为瘸腿单关闭状态')

    def check_order_timeout(self, timeout_seconds=300):
        """
        检查订单超时情况，在构建撤单列表时检查订单状态
        
        参数:
            timeout_seconds: 超时时间（秒）
        """
        import time
        current_time = time.time()
        
        # 找出超时且可撤销的订单
        timeout_orders = []
        # LogInfo('订单表跟踪',self.orders.items())        
        for order_id, order in self.orders.items():
            if order['status'] in ['SUBMITTED', 'PARTIALLY_FILLED']:
                if current_time - order['submit_time'] > timeout_seconds:
                    # 检查订单是否可撤销
                    if self.is_order_cancellable(order_id):
                        timeout_orders.append(order_id)
                        LogInfo(f'订单{order_id}已超时且可撤销，加入撤单列表')
                    else:
                        LogInfo(f'订单{order_id}已超时但当前状态不可撤销，跳过')
        
        # 撤销超时订单，此时列表中的订单都已确认可撤销
        for order_id in timeout_orders:
            try:
                # 发送撤单指令
                if A_DeleteOrder(order_id):
                    LogInfo(f'超时订单{order_id}撤销指令已发送')
                    self.orders[order_id]['status'] = 'CANCELLING'
                else:
                    LogInfo(f'超时订单{order_id}撤销失败')
            except Exception as e:
                LogInfo(f'撤单异常:{str(e)}')
    
    def update_order_status_backup(self):
        """
        备用的订单状态检查机制
        主动查询订单状态，以防OnOrderChange回调丢失或延迟
        """
        import time
        current_time = time.time()
        
        # 创建订单列表的副本，避免在遍历时字典大小发生变化
        orders_to_check = []
        for order_id, order in self.orders.items():
            if (order['status'] in ['SUBMITTED', 'PARTIALLY_FILLED'] and 
                current_time - order['submit_time'] > 2):  # 2秒后开始检查
                orders_to_check.append((order_id, order.copy()))  # 复制订单信息
        
        # 遍历副本列表，避免字典修改冲突
        for order_id, order in orders_to_check:
            # 再次检查订单是否还存在于字典中（可能已被删除）
            if order_id not in self.orders:
                continue
                
            try:
                # 获取订单状态
                status = A_OrderStatus(order_id)
                quantity_filled = A_OrderFilledLot(order_id)
                
                # 映射订单状态
                status_map = {
                    'N': 'UNKNOWN',           # 无
                    '0': 'SUBMITTED',         # 已发送
                    '1': 'SUBMITTED',         # 已受理
                    '2': 'SUBMITTED',         # 待触发
                    '3': 'SUBMITTED',         # 已生效
                    '4': 'SUBMITTED',         # 已排队
                    '5': 'PARTIALLY_FILLED', # 部分成交
                    '6': 'FILLED',            # 完全成交
                    '7': 'CANCELLING',        # 待撤
                    '8': 'SUBMITTED',         # 待改
                    '9': 'CANCELLED',         # 已撤单
                    'A': 'CANCELLED',         # 已撤余单
                    'B': 'REJECTED',          # 指令失败
                    'C': 'SUBMITTED',         # 待审核
                    'D': 'SUBMITTED',         # 已挂起
                    'E': 'SUBMITTED',         # 已申请
                    'F': 'REJECTED',          # 无效单
                    'G': 'PARTIALLY_FILLED', # 部分触发
                    'H': 'FILLED',            # 完全触发
                    'I': 'REJECTED',          # 余单失败
                }
                
                new_status = status_map.get(status, 'UNKNOWN')
                
                # 再次检查订单是否还存在，并且状态确实有变化
                if (order_id in self.orders and 
                    new_status != self.orders[order_id]['status'] and 
                    new_status != 'UNKNOWN'):
                    LogInfo(f'备用检查发现订单{order_id}状态变更: {self.orders[order_id]["status"]} -> {new_status} (原始状态码:{status})')
                    self.on_order_status(order_id, new_status, quantity_filled)
            except Exception as e:
                LogInfo(f'备用订单状态检查异常: {str(e)}')
    
    def handle_market_close(self, minutes_before_close=3):
        """
        处理收盘前操作，撤销所有挂单并市价平仓
        
        参数:
            minutes_before_close: 收盘前多少分钟执行
        """
        LogInfo(f'即将收盘，撤销所有挂单并市价平仓')
        
        # 撤销所有挂单
        self.cancel_pending_orders()
        
        # 获取所有需要平仓的套利组合
        for arb_id, arb_order in self.pending_arb_orders.items():
            if arb_order['status'] != 'CLOSED':
                LogInfo(f'收盘前市价平仓套利组合{arb_id}')
                self.close_arb_order(arb_id, force_market=True)
                arb_order['status'] = 'CLOSED'



def parse_arbitrage_directions_and_coefficients(formula):
    """
    解析套利组合公式中各合约的交易方向和倍数系数
    
    参数:
        formula: 套利组合公式，如 "DCE|F|P|2509*3-DCE|F|P|2507-DCE|F|Y|2509*2"
    
    返回:
        contract_info: 字典 {contract: (direction, coefficient)}, 
                      direction为'BUY'或'SELL', coefficient为倍数
    """
    contract_info = {}
    
    # 替换括号，便于处理
    simplified = re.sub(r'[()]', ' ', formula)
    
    # 分割公式，保留操作符
    # 先在操作符两边加空格，但要小心不要拆分合约代码中的内容
    simplified = re.sub(r'([+\-])', r' \1 ', simplified)
    tokens = simplified.split()
    
    current_sign = '+'  # 默认第一个合约为正
    
    for token in tokens:
        token = token.strip()
        if not token:
            continue
            
        if token in ['+', '-']:
            current_sign = token
        else:
            # 检查是否包含倍数（*号）
            if '*' in token:
                # 分离合约代码和倍数
                parts = token.split('*')
                if len(parts) == 2:
                    contract_part = parts[0]
                    try:
                        coefficient = int(parts[1])
                    except ValueError:
                        coefficient = 1
                        LogInfo(f"警告：无法解析倍数 {parts[1]}，使用默认倍数1")
                else:
                    contract_part = token
                    coefficient = 1
            else:
                contract_part = token
                coefficient = 1
            
            # 检查是否是合约代码
            if CONTRACT_PATTERN.fullmatch(contract_part):
                direction = 'BUY' if current_sign == '+' else 'SELL'
                contract_info[contract_part] = (direction, coefficient)
                # 重置为正号，为下一个合约做准备
                current_sign = '+'
    
    LogInfo(f"套利公式解析结果: {formula} -> {contract_info}")
    return contract_info

def parse_arbitrage_directions(formula):
    """
    解析套利组合公式中各合约的交易方向（保持兼容性）
    
    参数:
        formula: 套利组合公式
    
    返回:
        contract_directions: 字典 {contract: direction}, direction为'BUY'或'SELL'
    """
    contract_info = parse_arbitrage_directions_and_coefficients(formula)
    # 只返回方向信息，忽略倍数
    return {contract: info[0] for contract, info in contract_info.items()}

def _close_oldest_arb_order(order_manager, direction, setNo):
    """
    平掉最早的指定方向的套利组合订单
    
    参数:
        order_manager: 订单管理器实例
        direction: 方向，"BUY"或"SELL"
        setNo: 套利组合编号
    """
    import time
    
    # 根据订单管理器类型选择合适的订单字典
    if isinstance(order_manager, HistoricalArbitrageOrderManager):
        # 历史回测模式
        orders_dict = order_manager.executed_arb_orders
        prefix = f"HIS_ARB_{setNo}_"
    else:
        # 实盘模式
        orders_dict = order_manager.pending_arb_orders
        prefix = f"ARB_{setNo}_"
    
    # 找出指定方向且未关闭的套利组合
    eligible_orders = []
    for arb_id, arb_order in orders_dict.items():
        if (arb_order['direction'] == direction and 
            arb_id.startswith(prefix) and 
            arb_order['status'] not in ['CLOSED', 'LAME_LEG_CLOSED']):
            grid_level = arb_order.get('grid_level', 0)
            eligible_orders.append((arb_id, arb_order['submit_time'], grid_level))
    
    if not eligible_orders:
        LogInfo(f'未找到可平仓的{direction}方向套利组合')
        return
    
    # 按提交时间排序，找出最早的
    eligible_orders.sort(key=lambda x: x[1])
    oldest_arb_id, oldest_time, oldest_grid = eligible_orders[0]
    
    LogInfo(f'【风险控制减仓】平仓最早的{direction}方向套利组合: {oldest_arb_id}，网格{oldest_grid}，提交时间{oldest_time}')
    
    # 执行平仓
    order_manager.close_arb_order(oldest_arb_id, force_market=True)
    
    # 更新状态
    if oldest_arb_id in orders_dict:
        orders_dict[oldest_arb_id]['status'] = 'CLOSED'
        orders_dict[oldest_arb_id]['close_reason'] = 'RISK_CONTROL'  # 标记为风险控制平仓
        orders_dict[oldest_arb_id]['close_time'] = time.time()

def get_all_contracts_in_formula(formula):
    """
    获取套利组合公式中的所有合约及其系数，并按照合约代码排序(倒序)
    
    参数:
        formula: 套利组合公式
    
    返回:
        contracts_info: 包含合约和系数的列表 [(contract, coefficient), ...]，按照代码排序(倒序)
    """
    # 先使用预处理函数解析公式
    parsed_formula = preprocess_formula(formula)
    
    # 提取所有合约及其系数
    contracts_info = {}
    
    # 分析堆栈运算来获取系数
    stack = []
    temp_coefs = {}
    temp_ops = []
    
    for token_type, token in parsed_formula:
        if token_type == 'contract':
            # 默认系数为1
            if token not in contracts_info:
                contracts_info[token] = 1
            else:
                # 如果合约已存在，增加系数
                contracts_info[token] += 1
        elif token_type == 'number':
            # 数字直接入栈
            stack.append(token)
        elif token_type == 'op' and token == '*':
            # 处理乘法操作，这可能影响系数
            if len(stack) >= 2:
                right = stack.pop()
                left = stack.pop()
                
                # 检查是否涉及合约
                if isinstance(right, str) and CONTRACT_PATTERN.fullmatch(right):
                    if isinstance(left, (int, float)):
                        # 数字 * 合约，设置合约系数
                        if right not in contracts_info:
                            contracts_info[right] = left
                        else:
                            contracts_info[right] = max(contracts_info[right], abs(left))
                    stack.append(right)
                elif isinstance(left, str) and CONTRACT_PATTERN.fullmatch(left):
                    if isinstance(right, (int, float)):
                        # 合约 * 数字，设置合约系数
                        if left not in contracts_info:
                            contracts_info[left] = right
                        else:
                            contracts_info[left] = max(contracts_info[left], abs(right))
                    stack.append(left)
                else:
                    # 普通乘法
                    stack.append(left * right)
    
    # 提取所有合约及其系数
    contracts_with_coefs = []
    for contract in CONTRACT_PATTERN.findall(formula):
        if contract in contracts_info:
            coef = contracts_info.get(contract, 1)
            contracts_with_coefs.append((contract, abs(int(coef))))
        else:
            # 默认系数为1
            contracts_with_coefs.append((contract, 1))
    
    # 直接从套利公式中提取系数的备用方法（简化版）
    if not contracts_with_coefs:
        # 替换括号和操作符以便分割
        simplified = re.sub(r'[()]', ' ', formula)
        simplified = re.sub(r'([+\-*/])', r' \1 ', simplified)
        tokens = simplified.split()
        
        i = 0
        while i < len(tokens):
            if CONTRACT_PATTERN.fullmatch(tokens[i]):
                contract = tokens[i]
                coef = 1
                
                # 检查是否有系数（前面是数字和乘号）
                if i >= 2 and tokens[i-1] == '*' and tokens[i-2].isdigit():
                    coef = int(tokens[i-2])
                
                contracts_with_coefs.append((contract, coef))
            i += 1
    
    # 倒序排列
    return sorted(contracts_with_coefs, key=lambda x: x[0], reverse=True)

# 交易事件回调函数
def OnOrderChange(context, exchange_id, order_id, order_status, quantity_filled, price_filled, error_id, error_msg):
    """
    订单状态变更事件回调函数
    """
    global g_order_manager
    
    LogInfo(f'订单状态变更：订单={order_id}，状态={order_status}，成交量={quantity_filled}，成交价={price_filled}')
    
    # 映射订单状态
    status_map = {
        0: 'SUBMITTED',     # 已提交
        1: 'CANCELLED',     # 已撤单
        2: 'PARTIALLY_FILLED', # 部分成交
        3: 'FILLED',        # 全部成交
        4: 'REJECTED'       # 已拒绝
    }
    
    status = status_map.get(order_status, 'UNKNOWN')
    
    # 更新订单状态
    if g_order_manager:
        g_order_manager.on_order_status(order_id, status, quantity_filled)


def convert_bar_to_minutes(bar_type=None, bar_interval=None):
    """
    将策略订阅的主合约周期转换为分钟值
    
    参数:
        bar_type: K线类型，如果为None则自动获取当前BarType()
        bar_interval: K线周期数，如果为None则自动获取当前BarInterval()
    
    返回:
        int: 转换后的分钟数，不支持日线时返回None
    
    示例:
        'M' + 5  -> 5分钟
        'T' + 60 -> 1分钟 (60秒)
        'T' + 30 -> 0.5分钟 (30秒)
        'D' + 1  -> None (不考虑日线)
    """
    # 如果没有传入参数，则获取当前图表的周期信息
    if bar_type is None:
        bar_type = BarType()
    if bar_interval is None:
        bar_interval = BarInterval()
    
    LogInfo(f'周期转换-输入: 类型={bar_type}, 周期数={bar_interval}')
    
    if bar_type == 'M':
        # 分钟线，直接返回周期数
        minutes = bar_interval
        LogInfo(f'周期转换-分钟线: {bar_interval}分钟 -> {minutes}分钟')
        return minutes
        
    elif bar_type == 'T':
        # 分笔/秒线，转换为分钟
        if bar_interval == 0:
            # 0秒表示分笔，按1秒计算
            minutes = 1 / 60.0
        else:
            minutes = bar_interval / 60.0
        LogInfo(f'周期转换-秒线: {bar_interval}秒 -> {minutes}分钟')
        return minutes
        
    elif bar_type == 'D':
        # 日线，按要求不考虑
        LogInfo(f'周期转换-日线: 不支持日线转换，返回None')
        return None
        
    else:
        # 未知类型
        LogInfo(f'周期转换-未知类型: {bar_type}，返回None')
        return None
