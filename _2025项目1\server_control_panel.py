#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import time
import os
import mmap
import struct
import pickle
import datetime
import subprocess
import threading
import traceback
from enum import Enum
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QPushButton, QLabel, QFrame, QMessageBox,
                            QGridLayout, QSizePolicy, QGroupBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QDateTime
from PyQt5.QtGui import QColor, QFont, QPalette

# 共享内存区域常量定义
MEM_SIZE = 1024 * 1024  # 1MB共享内存区
HEADER_SIZE = 64        # 头部信息大小
COMMAND_SIZE = 256 * 1024  # 命令区域大小
RESPONSE_SIZE = 256 * 1024  # 响应区域大小
DATA_SIZE = 512 * 1024  # 数据区域大小

# 共享内存区域名称
SHMEM_NAME = "QMT_TRADING_GATEWAY"

# 获取共享内存文件路径
def get_shared_memory_path():
    """与网关保持一致的获取共享内存路径的方法"""
    # 如果环境变量中定义了特定路径，优先使用环境变量
    if "QMT_SHARED_MEM_DIR" in os.environ:
        base_dir = os.environ["QMT_SHARED_MEM_DIR"]
        print(f"使用环境变量中的共享内存目录: {base_dir}")
    else:
        # 使用当前脚本所在目录作为基础路径
        try:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            base_dir = os.path.join(script_dir, "qmt_trading_shm")  # 在脚本同级目录下创建专用子文件夹
            print(f"使用默认共享内存目录: {base_dir}")
        except Exception as e:
            print(f"获取脚本目录失败，将使用用户主目录: {e}")
            base_dir = os.path.expanduser("~")

    # 如果指定了后缀，用于支持多实例
    suffix = ""
    if "QMT_SHARED_MEM_SUFFIX" in os.environ:
        suffix = f"_{os.environ['QMT_SHARED_MEM_SUFFIX']}"

    # 确保目录存在
    try:
        os.makedirs(base_dir, exist_ok=True)
    except OSError as e:
        print(f"创建共享内存目录失败: {base_dir}, 错误: {e}")
        # 如果目录创建失败，回退到用户主目录
        base_dir = os.path.expanduser("~")

    # 构建并规范化文件路径
    file_path = os.path.abspath(os.path.join(base_dir, f"QMT_TRADING_GATEWAY{suffix}.dat"))
    print(f"控制面板使用共享内存文件路径: {file_path}")
    return file_path

# 根据新方法获取共享内存文件路径
SHMEM_FILE = get_shared_memory_path()

# 数据区域索引
DATA_ACCOUNT = 0
DATA_POSITIONS = 1
DATA_ORDERS = 2
DATA_TRADES = 3
DATA_STATUS = 4

# 命令常量
CMD_NONE = 0
CMD_CONNECT = 1
CMD_PLACE_ORDER = 2
CMD_CANCEL_ORDER = 3
CMD_QUERY = 4
CMD_SHUTDOWN = 99

# 响应常量
RESP_NONE = 0
RESP_SUCCESS = 1
RESP_FAILURE = 2
RESP_DATA = 3

class SharedMemoryReader:
    """读取共享内存数据"""
    
    def __init__(self):
        self.shmem_file = SHMEM_FILE
        self.is_initialized = False
        
        try:
            # 检查共享内存文件是否存在
            if not os.path.exists(self.shmem_file):
                print(f"共享内存文件不存在: {self.shmem_file}")
                return
            
            # 打开共享内存
            self.file = open(self.shmem_file, 'r+b')
            self.mm = mmap.mmap(self.file.fileno(), MEM_SIZE)
            self.is_initialized = True
            print(f"成功初始化共享内存读取器: {self.shmem_file}")
        except Exception as e:
            print(f"初始化共享内存读取器失败: {e}")
            traceback.print_exc()
    
    def __del__(self):
        """析构函数，关闭共享内存"""
        if hasattr(self, 'mm') and self.mm:
            try:
                self.mm.close()
            except:
                pass
        if hasattr(self, 'file') and self.file:
            try:
                self.file.close()
            except:
                pass
    
    def get_data(self, idx):
        """获取数据区域"""
        if not self.is_initialized:
            return None
            
        try:
            offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + idx * DATA_SIZE // 5
            self.mm.seek(offset)
            data = self.mm.read(DATA_SIZE // 5)
            try:
                return pickle.loads(data)
            except:
                return None
        except Exception as e:
            print(f"读取共享内存数据失败: {e}")
            return None
    
    def set_command(self, cmd, data=None):
        """设置命令"""
        if not self.is_initialized:
            return False
            
        try:
            # 写入命令区域
            self.mm.seek(0)
            self.mm.write(struct.pack('I', cmd))  # 命令标志
            
            # 写入命令数据
            if data:
                self.mm.seek(HEADER_SIZE)
                serialized = pickle.dumps(data)
                self.mm.write(serialized[:COMMAND_SIZE])
            return True
        except Exception as e:
            print(f"写入命令失败: {e}")
            return False

    def get_response(self):
        """获取响应"""
        if not self.is_initialized:
            return RESP_NONE, None
            
        try:
            self.mm.seek(4)
            resp = struct.unpack('I', self.mm.read(4))[0]
            
            if resp != RESP_NONE:
                # 读取响应数据
                self.mm.seek(HEADER_SIZE + COMMAND_SIZE)
                data = self.mm.read(RESPONSE_SIZE)
                try:
                    data = pickle.loads(data)
                except:
                    data = None
                
                return resp, data
            
            return RESP_NONE, None
        except Exception as e:
            print(f"读取响应失败: {e}")
            return RESP_NONE, None
    
    def clear_response(self):
        """清除响应"""
        if not self.is_initialized:
            return
            
        try:
            self.mm.seek(4)
            self.mm.write(struct.pack('I', RESP_NONE))
        except Exception as e:
            print(f"清除响应失败: {e}")
    
    def is_gateway_running(self):
        """检查网关是否运行中"""
        if not self.is_initialized:
            return False
            
        try:
            self.mm.seek(16)
            heartbeat = struct.unpack('I', self.mm.read(4))[0]
            current_time = int(time.time())
            # 心跳超过10秒视为网关不在运行
            return current_time - heartbeat < 10
        except:
            return False

class ServiceStatus(Enum):
    STOPPED = 0  # 红色
    RUNNING = 1  # 绿色
    FAULT = 2    # 黄色

class ServiceControl(QThread):
    """服务控制类"""
    status_changed = pyqtSignal(ServiceStatus)
    log_message = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.status = ServiceStatus.STOPPED
        self._running = False
        self.simulate_fault = False
        
        # 创建共享内存读取器
        self.mem_reader = SharedMemoryReader()
        
        # 服务进程
        self.service_process = None
        
        # 策略进程
        self.strategy_process = None
    
    def run(self):
        """线程运行函数，监控服务状态"""
        self._running = True
        
        while self._running:
            time.sleep(1)
            
            # 检查网关服务状态
            if self.mem_reader.is_initialized and self.mem_reader.is_gateway_running():
                status_data = self.mem_reader.get_data(DATA_STATUS) or {}
                
                if status_data.get("is_healthy", False):
                    # 服务健康
                    if self.status != ServiceStatus.RUNNING:
                        self.status = ServiceStatus.RUNNING
                        self.status_changed.emit(self.status)
                        self.log_message.emit("服务运行正常")
                else:
                    # 服务故障
                    if self.status != ServiceStatus.FAULT:
                        self.status = ServiceStatus.FAULT
                        self.status_changed.emit(self.status)
                        self.log_message.emit("服务异常")
            else:
                # 服务停止
                if self.status != ServiceStatus.STOPPED:
                    self.status = ServiceStatus.STOPPED
                    self.status_changed.emit(self.status)
                    self.log_message.emit("服务已停止")
                    
                # 检查服务进程是否存在
                if self.service_process and self.service_process.poll() is not None:
                    self.service_process = None
                    self.log_message.emit("网关进程已终止")
        
        # 线程结束
        if self.status != ServiceStatus.STOPPED:
            self.status = ServiceStatus.STOPPED
            self.status_changed.emit(self.status)
            self.log_message.emit("服务已停止")
    
    def start_service(self):
        """启动服务，返回布尔值表示成功/失败"""
        if self.status != ServiceStatus.STOPPED:
            self.log_message.emit("服务已在运行")
            return True
            
        # 启动网关服务
        if self._start_gateway_service():
            self.log_message.emit("网关服务已启动")
            time.sleep(2)  # 等待服务初始化
            
            # 检查服务状态
            if self.mem_reader.is_gateway_running():
                self.status = ServiceStatus.RUNNING
                self.status_changed.emit(self.status)
                self.log_message.emit("网关服务连接成功")
                return True
                
        self.log_message.emit("网关服务启动失败")
        return False
    
    def _start_gateway_service(self):
        """启动网关服务进程（增强版）"""
        try:
            # 获取当前Python解释器路径
            python_exec = sys.executable
            self.log_message.emit(f"使用Python解释器: {python_exec}")
            
            # 查找网关脚本路径（增加更多搜索路径）
            script_dir = os.path.dirname(os.path.abspath(__file__))
            gateway_paths = [
                os.path.join(script_dir, "SmartOrderManagerGateWay.py"),
                os.path.join(script_dir, "gateway_service", "SmartOrderManagerGateWay.py"),
                os.path.join(script_dir, "..", "gateway_service", "SmartOrderManagerGateWay.py"),
                os.path.join(script_dir, "用户策略", "SmartOrderManagerGateWay.py"),
                os.path.join(os.path.dirname(script_dir), "gateway_service", "SmartOrderManagerGateWay.py"),
                # 添加更多可能的路径
                os.path.join(script_dir, "..", "用户策略", "SmartOrderManagerGateWay.py"),
                os.path.join(script_dir, "..", "服务", "SmartOrderManagerGateWay.py"),
                os.path.join(script_dir, "..", "..", "gateway_service", "SmartOrderManagerGateWay.py")
            ]
            
            # 输出搜索路径，方便调试
            self.log_message.emit(f"搜索网关脚本路径...")
            for i, path in enumerate(gateway_paths):
                self.log_message.emit(f"  路径{i+1}: {path} {'(存在)' if os.path.exists(path) else '(不存在)'}")
                
            gateway_path = None
            for path in gateway_paths:
                if os.path.exists(path):
                    gateway_path = path
                    self.log_message.emit(f"找到网关脚本: {gateway_path}")
                    break
                    
            if not gateway_path:
                # 尝试在整个工作目录递归搜索
                self.log_message.emit("在工作目录递归搜索网关脚本...")
                
                for root, dirs, files in os.walk(os.path.dirname(os.path.dirname(script_dir))):
                    if "SmartOrderManagerGateWay.py" in files:
                        gateway_path = os.path.join(root, "SmartOrderManagerGateWay.py")
                        self.log_message.emit(f"找到网关脚本: {gateway_path}")
                        break
                
                if not gateway_path:
                    self.log_message.emit("错误: 找不到网关服务脚本，请检查安装路径")
                    
                    # 尝试直接复制脚本到临时目录
                    try:
                        import shutil
                        import tempfile
                        
                        # 尝试导入模块
                        try:
                            from gateway_service import SmartOrderManagerGateWay
                            
                            # 获取模块文件路径
                            module_path = os.path.abspath(SmartOrderManagerGateWay.__file__)
                            self.log_message.emit(f"找到网关模块: {module_path}")
                            
                            # 创建临时目录
                            temp_dir = os.path.join(tempfile.gettempdir(), "gateway_service")
                            os.makedirs(temp_dir, exist_ok=True)
                            
                            # 复制到临时目录
                            temp_gateway_path = os.path.join(temp_dir, "SmartOrderManagerGateWay.py")
                            shutil.copy2(module_path, temp_gateway_path)
                            
                            gateway_path = temp_gateway_path
                            self.log_message.emit(f"已复制网关脚本到临时目录: {gateway_path}")
                        except ImportError:
                            self.log_message.emit("无法导入网关模块")
                            
                            # 尝试从用户指定路径搜索
                            gateway_path = os.path.join(os.path.expanduser("~"), "SmartOrderManagerGateWay.py")
                            if os.path.exists(gateway_path):
                                self.log_message.emit(f"找到备用网关脚本: {gateway_path}")
                            else:
                                self.log_message.emit("无法找到网关脚本文件")
                                return False
                    except Exception as e:
                        self.log_message.emit(f"无法复制网关脚本: {str(e)}")
                        return False
            
            if not gateway_path or not os.path.exists(gateway_path):
                self.log_message.emit("无法找到网关脚本文件")
                return False
            
            # 检查脚本是否有执行权限
            if os.name == 'posix':  # Linux/Mac
                if not os.access(gateway_path, os.X_OK):
                    self.log_message.emit(f"警告: 脚本可能没有执行权限: {gateway_path}")
                    try:
                        os.chmod(gateway_path, 0o755)
                        self.log_message.emit("已尝试增加执行权限")
                    except:
                        pass
            
            # 创建临时目录用于输出日志
            log_dir = os.path.join(os.path.dirname(gateway_path), "logs")
            os.makedirs(log_dir, exist_ok=True)
            
            log_file = os.path.join(log_dir, f"gateway_{int(time.time())}.log")
            
            # 启动服务进程
            self.log_message.emit(f"正在启动网关服务...")
            
            try:
                # 使用子进程启动网关服务
                self.service_process = None  # 确保初始为None
                
                if os.name == 'nt':  # Windows系统
                    # 使用创建无窗口的方式启动
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    
                    # 确保设置正确的工作目录
                    gateway_dir = os.path.dirname(gateway_path)
                    self.log_message.emit(f"设置工作目录: {gateway_dir}")
                    
                    # 使用subprocess创建进程
                    command = [python_exec, gateway_path]
                    self.log_message.emit(f"执行命令: {' '.join(command)}")
                    
                    # 尝试使用不同的创建进程方式
                    try:
                        self.service_process = subprocess.Popen(
                            command,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            stdin=subprocess.PIPE,
                            startupinfo=startupinfo,  # 使用startupinfo隐藏窗口
                            creationflags=subprocess.CREATE_NO_WINDOW,  # 创建无窗口进程
                            cwd=gateway_dir,  # 设置当前工作目录
                            shell=False  # 不使用shell执行
                        )
                        self.log_message.emit(f"使用标准方式启动进程")
                    except Exception as e1:
                        self.log_message.emit(f"标准启动方式失败: {str(e1)}，尝试备用方式...")
                        
                        try:
                            # 备用方式：使用shell启动
                            self.service_process = subprocess.Popen(
                                f'"{python_exec}" "{gateway_path}"',
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                stdin=subprocess.PIPE,
                                creationflags=subprocess.CREATE_NO_WINDOW,
                                cwd=gateway_dir,
                                shell=True  # 使用shell启动
                            )
                            self.log_message.emit(f"使用shell方式启动进程成功")
                        except Exception as e2:
                            self.log_message.emit(f"备用启动方式也失败: {str(e2)}")
                            
                            # 最后尝试执行命令
                            try:
                                os.chdir(gateway_dir)
                                self.log_message.emit(f"切换到网关目录: {gateway_dir}")
                                self.service_process = subprocess.Popen(
                                    [python_exec, os.path.basename(gateway_path)],
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE
                                )
                                self.log_message.emit(f"直接在网关目录执行成功")
                            except Exception as e3:
                                self.log_message.emit(f"所有启动方式均失败: {str(e3)}")
                                return False
                else:  # Linux/Mac系统
                    self.service_process = subprocess.Popen(
                        [python_exec, gateway_path],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        stdin=subprocess.PIPE,
                        cwd=os.path.dirname(gateway_path)
                    )
                    
                # 确保进程成功启动
                if self.service_process:
                    self.log_message.emit(f"网关服务进程已启动 (PID: {self.service_process.pid})")
                    
                    # 监控输出
                    threading.Thread(target=self._monitor_process_output, 
                                    args=(self.service_process, "网关"), 
                                    daemon=True).start()
                    
                    # 等待一段时间，检查进程是否存活
                    time.sleep(2)
                    if self.service_process.poll() is not None:
                        exit_code = self.service_process.poll()
                        self.log_message.emit(f"错误: 网关进程启动后立即退出，退出码: {exit_code}")
                        
                        # 获取更多错误信息
                        stderr_output = self.service_process.stderr.read().decode('utf-8', errors='replace')
                        if stderr_output:
                            self.log_message.emit(f"错误输出: {stderr_output}")
                        return False
                    
                    # 等待共享内存初始化
                    self.log_message.emit("等待共享内存初始化...")
                    for _ in range(10):  # 最多等待10秒
                        if self.mem_reader.is_initialized and self.mem_reader.is_gateway_running():
                            self.log_message.emit("共享内存初始化成功")
                            return True
                        time.sleep(1)
                    
                    self.log_message.emit("警告: 网关进程已启动，但共享内存初始化超时")
                    # 即使超时也返回成功，因为进程已经启动
                    return True
                else:
                    self.log_message.emit("启动网关服务失败: 无法创建进程")
                    return False
                    
            except Exception as e:
                self.log_message.emit(f"启动网关服务失败: {str(e)}")
                traceback.print_exc()
                return False
                
        except Exception as e:
            self.log_message.emit(f"启动网关服务初始化失败: {str(e)}")
            traceback.print_exc()
            return False
    
    def start_strategy(self):
        """启动策略"""
        if self.strategy_process and self.strategy_process.poll() is None:
            self.log_message.emit("策略已在运行")
            return
            
        # 检查网关服务状态
        if self.status != ServiceStatus.RUNNING:
            self.log_message.emit("网关服务未就绪，无法启动策略")
            return
            
        try:
            # 获取当前Python解释器路径
            python_exec = sys.executable
            
            # 查找策略脚本路径
            script_dir = os.path.dirname(os.path.abspath(__file__))
            strategy_paths = [
                os.path.join(script_dir, "用户策略", "ETF资产管理策略03.py"),
                os.path.join(script_dir, "..", "用户策略", "ETF资产管理策略03.py")
            ]
            
            strategy_path = None
            for path in strategy_paths:
                if os.path.exists(path):
                    strategy_path = path
                    break
                    
            if not strategy_path:
                self.log_message.emit("找不到策略脚本")
                return False
                
            # 启动策略进程
            self.strategy_process = subprocess.Popen(
                [python_exec, strategy_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP | subprocess.CREATE_NO_WINDOW
            )
            
            # 监控输出
            threading.Thread(target=self._monitor_process_output, args=(self.strategy_process, "策略")).start()
            
            self.log_message.emit(f"策略进程已启动 (PID: {self.strategy_process.pid})")
            return True
            
        except Exception as e:
            self.log_message.emit(f"启动策略失败: {str(e)}")
            traceback.print_exc()
            return False
    
    def _monitor_process_output(self, process, name):
        """监控进程输出"""
        try:
            # 读取标准输出
            for line in iter(process.stdout.readline, b''):
                msg = line.decode('utf-8', errors='replace').strip()
                if msg:
                    self.log_message.emit(f"[{name}] {msg}")
                    
            # 读取标准错误
            for line in iter(process.stderr.readline, b''):
                msg = line.decode('utf-8', errors='replace').strip()
                if msg:
                    self.log_message.emit(f"[{name}错误] {msg}")
        except Exception as e:
            self.log_message.emit(f"监控{name}输出失败: {str(e)}")
    
    def stop(self):
        """停止服务"""
        if self.status == ServiceStatus.STOPPED:
            self.log_message.emit("服务已经是停止状态")
            return
            
        # 停止策略
        if self.strategy_process and self.strategy_process.poll() is None:
            try:
                self.strategy_process.terminate()
                self.log_message.emit("已发送策略终止信号")
            except Exception as e:
                self.log_message.emit(f"停止策略失败: {str(e)}")
        
        # 通过共享内存发送关闭命令
        if self.mem_reader.is_initialized:
            if self.mem_reader.set_command(CMD_SHUTDOWN, {}):
                self.log_message.emit("已发送网关关闭命令")
                
                # 等待响应
                for _ in range(5):
                    resp, data = self.mem_reader.get_response()
                    if resp == RESP_SUCCESS:
                        self.mem_reader.clear_response()
                        self.log_message.emit("网关服务已确认关闭")
                        break
                    time.sleep(0.5)
            
        # 强制终止服务进程
        if self.service_process and self.service_process.poll() is None:
            try:
                self.service_process.terminate()
                self.log_message.emit("已发送网关进程终止信号")
            except Exception as e:
                self.log_message.emit(f"停止网关进程失败: {str(e)}")
                
        self._running = False
        self.log_message.emit("正在停止服务...")
    
    def restart(self):
        """重启服务，返回布尔值表示成功/失败"""
        self.log_message.emit("正在执行网关服务重启...")
        
        # 先停止服务
        self.stop()
        
        # 等待服务停止，最多等待15秒
        stopped = False
        for _ in range(30):
            if self.status == ServiceStatus.STOPPED:
                stopped = True
                self.log_message.emit("服务已成功停止")
                break
            time.sleep(0.5)
            
        if not stopped:
            self.log_message.emit("等待服务停止超时，尝试强制结束进程...")
            # 尝试进一步确保服务进程已终止
            if hasattr(self, 'service_process') and self.service_process:
                try:
                    if self.service_process.poll() is None:
                        self.service_process.terminate()
                        time.sleep(1)
                        if self.service_process.poll() is None:
                            self.service_process.kill()
                        self.log_message.emit("已强制结束服务进程")
                except Exception as e:
                    self.log_message.emit(f"强制结束进程失败: {e}")
            
            # 再等待一段时间确保资源释放
            time.sleep(3)
        
        # 确保资源充分释放后再启动
        time.sleep(2)
        
        # 重新启动服务
        result = self.start_service()
        
        if result:
            self.log_message.emit("网关服务重启成功")
            return True
        else:
            self.log_message.emit("网关服务重启失败")
            self.status = ServiceStatus.FAULT
            self.status_changed.emit(self.status)
            return False
    
    def trigger_fault(self):
        """触发模拟故障（用于测试）"""
        if self.status == ServiceStatus.RUNNING:
            self.simulate_fault = True
            self.log_message.emit("模拟故障已触发，10秒后生效")

    def liquidate_all_positions(self):
        """一键清仓操作"""
        # 显示确认对话框
        reply = QMessageBox.question(
            self, '确认清仓操作', 
            '确定要清仓所有持仓吗？此操作无法撤销!',
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.log_status_message("正在执行一键清仓操作...")
            self.liquidate_button.setEnabled(False)
            
            # 执行清仓
            result = self.liquidate_all_positions()
            
            self.liquidate_button.setEnabled(True)
    
    def _get_current_positions(self):
        """获取当前持仓"""
        if not self.mem_reader.is_initialized:
            return []
        try:
            # 从共享内存获取持仓信息
            positions_data = self.mem_reader.get_data(DATA_POSITIONS)
            if not positions_data:
                return []
            
            # 格式化持仓数据
            positions = []
            for symbol, pos in positions_data.items():
                available_volume = pos.get('can_use_volume', 0)
                positions.append({
                    'symbol': symbol,
                    'total_volume': pos.get('volume', 0),
                    'available_volume': available_volume,
                    'cost_price': pos.get('cost_price', 0),
                    'market_value': pos.get('market_value', 0)
                })
            return positions
        except Exception as e:
            self.log_message.emit(f"获取持仓失败: {str(e)}")
            return []
    
    def _sell_position(self, symbol, volume):
        """发送卖出指令"""
        if not self.mem_reader.is_initialized:
            return False
        try:
            # 准备卖出命令
            cmd_data = {
                "symbol": symbol,
                "direction": "SELL",
                "quantity": volume,
                "price_type": "LATEST_PRICE",  # 使用市价委托
                "price": 0,
                "strategy_name": "liquidate_all",
                "remark": "一键清仓"
            }
            
            if self.mem_reader.set_command(CMD_PLACE_ORDER, cmd_data):
                # 等待响应
                for _ in range(5):  # 最多等待5秒
                    resp, resp_data = self.mem_reader.get_response()
                    if resp != RESP_NONE:
                        self.mem_reader.clear_response()
                        if resp == RESP_SUCCESS:
                            order_id = resp_data.get("order_id", "未知")
                            self.log_message.emit(f"卖出委托已发送: {symbol}, 订单号: {order_id}")
                            return True
                        else:
                            self.log_message.emit(f"卖出委托失败: {symbol}")
                            return False
                    time.sleep(0.2)
                
                self.log_message.emit(f"卖出委托超时: {symbol}")
                return False
            else:
                self.log_message.emit(f"发送卖出命令失败: {symbol}")
                return False
                
        except Exception as e:
            self.log_message.emit(f"卖出{symbol}时发生错误: {str(e)}")
            return False

class ServerControlPanel(QMainWindow):
    """服务器控制面板主窗口"""
    
    def __init__(self):
        super().__init__()
        self.service_control = ServiceControl()
        self.service_control.status_changed.connect(self.update_status_indicator)
        self.service_control.log_message.connect(self.log_status_message)
        
        # 设置应用样式
        self.apply_global_style()
        
        self.init_ui()
        
        # 启动定时器更新时间和账户信息
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_time_and_account)
        self.update_timer.start(1000)  # 每秒更新一次
        
        # 启动服务监控线程
        self.service_control.start()
    
    def apply_global_style(self):
        """应用全局样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QLabel {
                font-family: 'Microsoft YaHei', Arial;
                color: #333;
            }
            QPushButton {
                background-color: #2c7be5;
                color: white;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                border: none;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #1a68d1;
            }
            QPushButton:pressed {
                background-color: #1557b0;
            }
            QPushButton:disabled {
                background-color: #a0a0a0;
            }
            QPushButton#exitButton {
                background-color: #e74c3c;
            }
            QPushButton#exitButton:hover {
                background-color: #c0392b;
            }
            QGroupBox {
                border-radius: 8px;
                border: 1px solid #dcdcdc;
                margin-top: 16px;
                font-weight: bold;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
                color: #555;
            }
            QFrame {
                border-radius: 8px;
            }
        """)
    
    def init_ui(self):
        """初始化UI界面"""
        self.setWindowTitle('服务控制面板')
        self.setGeometry(300, 300, 700, 500)
        
        # 创建中央部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 顶部面板 - 包含状态和时间
        top_panel = QWidget()
        top_layout = QHBoxLayout(top_panel)
        top_layout.setContentsMargins(0, 0, 0, 0)
        
        # 状态指示器面板
        status_group = QGroupBox("服务状态")
        status_layout = QVBoxLayout(status_group)
        
        status_indicator_layout = QHBoxLayout()
        self.status_indicator = QFrame()
        self.status_indicator.setFixedSize(80, 80)
        self.status_indicator.setFrameShape(QFrame.StyledPanel)
        self.status_indicator.setStyleSheet("background-color: red; border-radius: 40px;")
        status_indicator_layout.addWidget(self.status_indicator, 0, Qt.AlignCenter)
        
        self.status_text = QLabel("停止")
        self.status_text.setAlignment(Qt.AlignCenter)
        self.status_text.setStyleSheet("font-size: 16px; font-weight: bold;")
        
        status_layout.addLayout(status_indicator_layout)
        status_layout.addWidget(self.status_text)
        
        # 日期时间面板
        time_group = QGroupBox("系统时间")
        time_layout = QVBoxLayout(time_group)
        
        self.date_label = QLabel()
        self.date_label.setAlignment(Qt.AlignCenter)
        self.date_label.setStyleSheet("font-size: 16px;")
        
        self.time_label = QLabel()
        self.time_label.setAlignment(Qt.AlignCenter)
        self.time_label.setStyleSheet("font-size: 24px; font-weight: bold;")
        
        time_layout.addWidget(self.date_label)
        time_layout.addWidget(self.time_label)
        
        # 将状态和时间添加到顶部面板
        top_layout.addWidget(status_group, 1)
        top_layout.addWidget(time_group, 1)
        
        # 中部面板 - 账户信息和按钮
        middle_panel = QWidget()
        middle_layout = QHBoxLayout(middle_panel)
        middle_layout.setContentsMargins(0, 0, 0, 0)
        
        # 账户信息面板
        account_group = QGroupBox("账户信息")
        account_layout = QGridLayout(account_group)
        
        labels = [
            ("账户权益:", "total_asset_label", "- 元", "total_asset_value"),
            ("可用资金:", "cash_label", "- 元", "cash_value"),
            ("持仓市值:", "market_value_label", "- 元", "market_value"),
            ("持仓盈亏:", "profit_label", "- 元", "profit_value")
        ]
        
        for i, (text, label_name, value_text, value_name) in enumerate(labels):
            label = QLabel(text)
            setattr(self, label_name, label)
            
            value = QLabel(value_text)
            value.setStyleSheet("font-weight: bold; font-size: 14px;")
            value.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            setattr(self, value_name, value)
            
            account_layout.addWidget(label, i, 0)
            account_layout.addWidget(value, i, 1)
        
        # 按钮面板 - 只保留重启服务和退出程序按钮
        buttons_group = QGroupBox("操作")
        buttons_layout = QGridLayout(buttons_group)
        
        # 只创建重启服务和退出程序按钮
        self.restart_button = QPushButton('重启服务')
        self.exit_button = QPushButton('退出程序')
        self.exit_button.setObjectName("exitButton")
        
        # 设置按钮初始状态 - 重启服务按钮默认禁用
        self.restart_button.setEnabled(False)
        
        # 设置重启按钮样式 - 增加提示性
        self.restart_button.setStyleSheet("""
            background-color: #f39c12;
            color: white;
            font-weight: bold;
        """)
        
        # 连接按钮点击事件
        self.restart_button.clicked.connect(self.restart_service)
        self.exit_button.clicked.connect(self.exit_application)
        
        # 放置按钮 - 两个按钮并排放置
        buttons_layout.addWidget(self.restart_button, 0, 0)
        buttons_layout.addWidget(self.exit_button, 0, 1)
        
        # 将账户信息和按钮添加到中部面板
        middle_layout.addWidget(account_group, 3)
        middle_layout.addWidget(buttons_group, 2)
        
        # 底部面板 - 状态日志
        bottom_panel = QGroupBox("运行日志")
        bottom_layout = QVBoxLayout(bottom_panel)
        
        self.status_log = QLabel('准备就绪')
        self.status_log.setAlignment(Qt.AlignCenter)
        self.status_log.setStyleSheet("padding: 10px;")
        self.status_log.setMinimumHeight(50)
        bottom_layout.addWidget(self.status_log)
        
        # 添加所有部件到主布局
        main_layout.addWidget(top_panel)
        main_layout.addWidget(middle_panel)
        main_layout.addWidget(bottom_panel)
        
        # 更新状态指示器
        self.update_status_indicator(ServiceStatus.STOPPED)
        
        # 初次更新时间
        self.update_time_and_account()
    
    def update_time_and_account(self):
        """更新时间和账户信息"""
        current_time = QDateTime.currentDateTime()
        
        # 更新日期和时间
        self.date_label.setText(current_time.toString("yyyy年MM月dd日 dddd"))
        self.time_label.setText(current_time.toString("HH:mm:ss"))
        
        # 读取账户信息
        self.update_account_info()
    
    def update_account_info(self):
        """更新账户信息"""
        try:
            # 从共享内存读取账户数据
            if self.service_control.mem_reader.is_initialized:
                # 获取账户资产信息
                account_data = self.service_control.mem_reader.get_data(DATA_ACCOUNT)
                if account_data:
                    # 更新账户资产显示
                    self.total_asset_value.setText(f"{account_data.get('total_asset', 0):,.2f} 元")
                    self.cash_value.setText(f"{account_data.get('cash', 0):,.2f} 元")
                    self.market_value.setText(f"{account_data.get('market_value', 0):,.2f} 元")
                
                # 获取持仓信息
                positions_data = self.service_control.mem_reader.get_data(DATA_POSITIONS)
                if positions_data:
                    # 计算持仓盈亏
                    total_profit = 0
                    for pos in positions_data.values():
                        # 市值减去成本价*数量
                        if 'market_value' in pos and 'cost_price' in pos and 'volume' in pos:
                            profit = pos['market_value'] - (pos['cost_price'] * pos['volume'])
                            total_profit += profit
                    
                    # 更新持仓盈亏显示
                    if total_profit >= 0:
                        self.profit_value.setStyleSheet("color: red; font-weight: bold; font-size: 14px;")
                        self.profit_value.setText(f"+{total_profit:,.2f} 元")
                    else:
                        self.profit_value.setStyleSheet("color: green; font-weight: bold; font-size: 14px;")
                        self.profit_value.setText(f"{total_profit:,.2f} 元")
        except Exception as e:
            print(f"更新账户信息时发生异常: {e}")
    
    def restart_service(self):
        """重启服务 - 改进版以确保可靠重启"""
        self.log_status_message("正在重启网关服务...")
        self.restart_button.setEnabled(False)
        
        try:
            # 显示忙碌状态
            self.status_indicator.setStyleSheet("background-color: orange; border-radius: 40px;")
            self.status_text.setText("重启中")
            
            # 停止服务 - 使用改进的停止方法
            self.service_control.stop()
            
            # 等待服务完全停止
            for _ in range(20):  # 最多等待10秒
                QApplication.processEvents()  # 保持UI响应
                if self.service_control.status == ServiceStatus.STOPPED:
                    self.log_status_message("服务已停止，准备重新启动...")
                    break
                time.sleep(0.5)
            
            # 确保完全停止后再启动
            time.sleep(2)  # 额外等待2秒确保资源完全释放
            
            # 启动服务
            success = self.service_control.start_service()
            
            if success:
                self.log_status_message("网关服务已重新启动")
            else:
                self.log_status_message("重启服务失败，请检查日志")
                # 重新启用重启按钮（只有在故障状态时）
                if self.service_control.status == ServiceStatus.FAULT:
                    self.restart_button.setEnabled(True)
        except Exception as e:
            self.log_status_message(f"重启服务时发生错误: {e}")
            # 发生错误时设置为故障状态
            self.service_control.status = ServiceStatus.FAULT
            self.update_status_indicator(ServiceStatus.FAULT)
            self.restart_button.setEnabled(True)
    
    def exit_application(self):
        """退出应用程序"""
        reply = QMessageBox.question(
            self, '确认退出', 
            '确定要退出应用程序吗？服务将被停止',
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 停止服务
            self.log_status_message("正在关闭服务...")
            self.service_control.stop()
            
            # 等待服务停止
            for _ in range(10):
                QApplication.processEvents()
                if self.service_control.status == ServiceStatus.STOPPED:
                    break
                time.sleep(0.5)
                
            QApplication.quit()
    
    def update_status_indicator(self, status):
        """更新状态指示器颜色"""
        if status == ServiceStatus.STOPPED:
            self.status_indicator.setStyleSheet("background-color: red; border-radius: 40px;")
            self.status_text.setText("已停止")
            # 停止状态下不允许点击重启按钮
            self.restart_button.setEnabled(False)
        elif status == ServiceStatus.RUNNING:
            self.status_indicator.setStyleSheet("background-color: #2ecc71; border-radius: 40px;")
            self.status_text.setText("运行中")
            # 正常运行状态下不允许点击重启按钮
            self.restart_button.setEnabled(False)
        elif status == ServiceStatus.FAULT:
            self.status_indicator.setStyleSheet("background-color: #f1c40f; border-radius: 40px;")
            self.status_text.setText("故障")
            # 只有故障状态下允许点击重启按钮
            self.restart_button.setEnabled(True)
    
    def log_status_message(self, message):
        """记录状态消息到日志显示区域"""
        current_time = time.strftime("%H:%M:%S")
        self.status_log.setText(f"[{current_time}] {message}")
    
    def trigger_fault(self):
        """触发模拟故障（用于测试）"""
        self.service_control.trigger_fault()

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        reply = QMessageBox.question(
            self, '确认退出', 
            '确定要退出应用程序吗？服务将被停止',
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 停止服务
            self.service_control.stop()
            
            # 等待服务停止
            for _ in range(10):
                QApplication.processEvents()
                if self.service_control.status == ServiceStatus.STOPPED:
                    break
                time.sleep(0.5)
                
            event.accept()
        else:
            event.ignore()

def check_gateway_service():
    """检查网关服务是否运行"""
    try:
        reader = SharedMemoryReader()
        if not reader.is_initialized:
            return False
            
        # 检查心跳
        if not reader.is_gateway_running():
            return False
            
        # 检查健康状态
        status_data = reader.get_data(DATA_STATUS) or {}
        return status_data.get("is_healthy", False)
    except:
        return False

def startup_check():
    """启动检查，确保网关服务和界面都已启动"""
    try:
        # 检查网关服务
        if check_gateway_service():
            print("网关服务已运行")
            # 控制面板总是启动
            return True
        else:
            print("网关服务未运行，启动控制面板...")
            
            # 检查是否能找到网关脚本
            script_found = False
            # 查找网关脚本路径
            script_dir = os.path.dirname(os.path.abspath(__file__))
            gateway_paths = [
                os.path.join(script_dir, "SmartOrderManagerGateWay.py"),
                os.path.join(script_dir, "gateway_service", "SmartOrderManagerGateWay.py"),
                os.path.join(script_dir, "..", "gateway_service", "SmartOrderManagerGateWay.py"),
                os.path.join(script_dir, "用户策略", "SmartOrderManagerGateWay.py"),
                os.path.join(os.path.dirname(script_dir), "gateway_service", "SmartOrderManagerGateWay.py")
            ]
            
            for path in gateway_paths:
                if os.path.exists(path):
                    script_found = True
                    break
                    
            if not script_found:
                print("无法找到网关脚本，请手动指定路径或复制脚本文件到当前目录")
            
            return True
    except Exception as e:
        print(f"启动检查异常: {str(e)}")
        return True  # 出错情况下依然启动控制面板，以便用户手动操作


if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    # 启动检查
    if startup_check():
        # 启动控制面板
        window = ServerControlPanel()
        window.show()
        sys.exit(app.exec_())
    else:
        # 如果服务已就绪，启动控制面板
        window = ServerControlPanel()
        window.show()
        sys.exit(app.exec_()) 