import talib
import math
import copy
import numpy as np
import pandas as pd
from collections import deque
from xtquant.xttrader import XtQuantTrader
from xtquant import xtconstant

class TradeConnectionManager:
    """
    交易连接管理类 - 专门负责交易连接和订单处理
    
    功能：
    1. 管理交易账户连接
    2. 处理交易连接中断与重连
    3. 提供下单接口
    4. 发送邮件通知
    """
    def __init__(self, client_path, session_id=None, account_id=None, log_type=0):
        """
        初始化交易连接管理器
        
        参数:
            client_path: 客户端路径，如"D:\\国金QMT交易端模拟\\userdata_mini"
            session_id: 会话编号，如果为None则自动生成
            account_id: 交易账号，如果为None则需要后续设置
            log_type: 日志输出类型，0为print输出(通用环境)，1为LogInfo输出(极星量化环境)
        """
        # 导入必要的库
        import threading
        import time
        import datetime
        from xtquant import xttrader, xtconstant, xttype
        
        self.threading = threading
        self.time = time
        self.datetime = datetime
        self.xttrader = xttrader
        self.xtconstant = xtconstant
        self.xttype = xttype  # 直接导入xttype模块
        
        # 日志类型
        self.log_type = log_type
        
        # 设置会话ID（如果未提供则生成）
        if session_id is None:
            # 使用当前时间戳作为会话ID
            session_id = int(datetime.datetime.now().timestamp())
        
        # 交易连接参数
        self.client_path = client_path
        self.session_id = session_id
        self.account_id = account_id
        
        # 交易对象
        self.trader = None
        self.account = None
        
        # 连接状态
        self.connected = False
        self.last_heartbeat = 0
        self.heartbeat_timeout = 30   # 心跳超时秒数
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 3  # 最大重连次数
        
        # 日志相关
        self.log_prefix = "[交易] "
        
        # 邮件相关设置
        self.email_enabled = False
        self.email_sender = ""
        self.email_receivers = []
        self.email_smtp_server = ""
        self.email_smtp_port = 465
        self.email_username = ""
        self.email_password = ""
        
        # 初始化交易对象
        self._init_trader()
        
        # 启动连接监控线程
        self.stop_monitor = False
        self.monitor_thread = threading.Thread(target=self._monitor_connection)
        self.monitor_thread.daemon = True
        
        self._log(f"{self.log_prefix}交易连接管理器初始化完成")
    
    def _log(self, message):
        """
        根据日志类型输出日志
        
        参数:
            message: 日志信息
        """
        if self.log_type == 0:
            # 使用print输出日志(通用环境)
            print(message)
        else:
            # 使用LogInfo输出日志(极星量化环境)
            try:
                LogInfo(message)
            except:
                # 如果LogInfo未定义，回退到print
                print(message)
    
    def _init_trader(self):
        """初始化交易对象"""
        try:
            # 创建XtQuantTrader实例
            self.trader = self.xttrader.XtQuantTrader(self.client_path, self.session_id)
            
            # 创建回调对象并注册
            from xtquant.xttrader import XtQuantTraderCallback
            
            class MyCallback(XtQuantTraderCallback):
                def __init__(self, manager):
                    self.manager = manager
                    
                def on_disconnected(self):
                    self.manager._log(f"{self.manager.log_prefix}连接断开，将尝试重连")
                    self.manager.connected = False
                    
                def on_order_stock_async_response(self, response):
                    self.manager._log(f"{self.manager.log_prefix}异步下单响应: {response.order_id}, 备注: {response.order_remark}")
                    
                def on_stock_order(self, order):
                    self.manager._log(f"{self.manager.log_prefix}委托回报: {order.order_id}, 状态: {order.order_status}")
                    
                def on_stock_trade(self, trade):
                    self.manager._log(f"{self.manager.log_prefix}成交回报: {trade.order_id}, 数量: {trade.traded_volume}")
                    
                def on_order_error(self, error):
                    self.manager._log(f"{self.manager.log_prefix}委托错误: {error.error_msg}")
            
            # 创建并注册回调
            self.callback = MyCallback(self)
            
            # 如果已提供账号，则创建账号对象
            if self.account_id:
                # 直接使用xttype模块
                self.account = self.xttype.StockAccount(self.account_id)
                
            # 注册回调
            if self.trader:
                self.trader.register_callback(self.callback)
                
            self._log(f"{self.log_prefix}交易对象初始化完成")
        except Exception as e:
            self._log(f"{self.log_prefix}交易对象初始化异常: {str(e)}")
            
    def set_account(self, account_id, account_type="STOCK"):
        """
        设置交易账号
        
        参数:
            account_id: 交易账号
            account_type: 账号类型，默认为STOCK，可选项有STOCK, CREDIT, FUTURE等
        """
        try:
            self.account_id = account_id
            
            # 根据账号类型创建不同的账号对象
            if account_type == "CREDIT":
                self.account = self.xttype.StockAccount(account_id, self.xtconstant.CREDIT_ACCOUNT)
            elif account_type == "FUTURE":
                self.account = self.xttype.FutureAccount(account_id)
            else:  # 默认为股票账户
                self.account = self.xttype.StockAccount(account_id)
                
            self._log(f"{self.log_prefix}账号设置成功: {account_id}, 类型: {account_type}")
            return True
        except Exception as e:
            self._log(f"{self.log_prefix}账号设置异常: {str(e)}")
            return False
    
    def register_callback(self, callback_obj):
        """
        注册交易回调对象
        
        参数:
            callback_obj: 实现了XtQuantTraderCallback接口的回调对象
        """
        if not self.trader:
            self._log(f"{self.log_prefix}交易对象未初始化，无法注册回调")
            return False
            
        try:
            self.trader.register_callback(callback_obj)
            self._log(f"{self.log_prefix}交易回调注册成功")
            return True
        except Exception as e:
            self._log(f"{self.log_prefix}交易回调注册异常: {str(e)}")
            return False
    
    def start(self):
        """启动交易线程"""
        if not self.trader:
            self._log(f"{self.log_prefix}交易对象未初始化，无法启动")
            return False
            
        try:
            self.trader.start()
            
            # 启动监控线程
            if not self.monitor_thread.is_alive():
                self.monitor_thread.start()
                
            self._log(f"{self.log_prefix}交易线程启动成功")
            return True
        except Exception as e:
            self._log(f"{self.log_prefix}交易线程启动异常: {str(e)}")
            return False
    
    def connect(self):
        """建立交易连接，支持重试和C++环境"""
        if not self.trader:
            self._log(f"{self.log_prefix}交易对象未初始化，无法连接")
            return False
            
        # 设置最大尝试次数和延迟
        max_attempts = 3
        retry_delay = 2  # 秒
        
        # 检查客户端路径是否存在
        import os
        if not os.path.exists(self.client_path):
            self._log(f"{self.log_prefix}警告: 客户端路径不存在: {self.client_path}")
            # 尝试修正路径斜杠格式 (适应C++环境)
            fixed_path = self.client_path.replace('\\', '/')
            if fixed_path != self.client_path:
                self._log(f"{self.log_prefix}尝试使用修正后的路径: {fixed_path}")
                self.client_path = fixed_path
        
        # 尝试多次连接
        for attempt in range(1, max_attempts + 1):
            try:
                self._log(f"{self.log_prefix}尝试连接 (第{attempt}次)...")
                
                # 连接交易服务器
                result = self.trader.connect()
                
                if result == 0:
                    self.connected = True
                    self.last_heartbeat = self.time.time()
                    self.reconnect_attempts = 0
                    self._log(f"{self.log_prefix}交易服务器连接成功")
                    return True
                else:
                    self.connected = False
                    self._log(f"{self.log_prefix}交易服务器连接失败，错误码: {result}")
                    
                    # 特殊处理某些错误码
                    if result == -2:  # 假设-2表示session_id冲突
                        # 生成新的session_id
                        import random
                        new_session_id = int(self.time.time()) + random.randint(100, 999)
                        self._log(f"{self.log_prefix}可能是会话ID冲突，尝试新的会话ID: {new_session_id}")
                        # 重新初始化交易对象
                        self.session_id = new_session_id
                        self._init_trader()
                    
                    # 延迟后重试
                    if attempt < max_attempts:
                        self.time.sleep(retry_delay)
                        
            except Exception as e:
                self.connected = False
                self._log(f"{self.log_prefix}交易服务器连接异常: {str(e)}")
                if attempt < max_attempts:
                    self.time.sleep(retry_delay)
        
        # 如果常规连接方法失败，尝试诊断并使用高级连接方法(适用于C++嵌入环境)
        self._log(f"{self.log_prefix}常规连接方法失败，尝试进行全面诊断...")
        try:
            # 检查是否是C++环境
            import sys
            is_cpp_env = hasattr(sys, 'getwindowsversion') and sys.platform == 'win32' and not hasattr(sys, 'ps1')
            if is_cpp_env:
                self._log(f"{self.log_prefix}检测到可能是C++嵌入环境，启用高级连接诊断")
            
            # 无论什么环境，失败后都尝试诊断方法
            if self.try_connect_with_diagnostic():
                self._log(f"{self.log_prefix}高级连接方法成功")
                return True
        except Exception as e:
            self._log(f"{self.log_prefix}高级连接诊断过程中发生异常: {str(e)}")
        
        # 所有尝试都失败
        self._log(f"{self.log_prefix}连接失败，请检查:")
        self._log(f" - QMT客户端是否正在运行")
        self._log(f" - 是否启用了极简模式")
        self._log(f" - 路径是否正确: {self.client_path}")
        self._log(f" - 账户权限是否正确")
        
        return False
    
    def subscribe(self):
        """订阅交易主推"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法订阅")
            return False
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法订阅")
            return False
            
        try:
            # 订阅交易主推
            result = self.trader.subscribe(self.account)
            
            if result == 0:
                self._log(f"{self.log_prefix}交易主推订阅成功")
                return True
            else:
                self._log(f"{self.log_prefix}交易主推订阅失败，错误码: {result}")
                return False
        except Exception as e:
            self._log(f"{self.log_prefix}交易主推订阅异常: {str(e)}")
            return False
    
    def check_connection_health(self):
        """检查连接健康状态"""
        if not self.connected:
            return False
        
        try:
            # 检查心跳
            current_time = self.time.time()
            if current_time - self.last_heartbeat > self.heartbeat_timeout:
                self._log(f"{self.log_prefix}连接心跳超时")
                return False
            
            # 尝试获取账户信息以验证连接
            try:
                if self.trader and self.account:
                    asset = self.trader.query_stock_asset(self.account)
                    if asset:
                        self.last_heartbeat = current_time
                        return True
                return False
            except:
                return False
                
        except Exception as e:
            self._log(f"{self.log_prefix}检查连接健康状态异常: {str(e)}")
            return False
    
    def _monitor_connection(self):
        """连接监控线程"""
        while not self.stop_monitor:
            try:
                if self.connected and not self.check_connection_health():
                    self._log(f"{self.log_prefix}检测到连接异常，尝试重连")
                    self._reconnect()
                
                self.time.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                self._log(f"{self.log_prefix}连接监控异常: {str(e)}")
                self.time.sleep(5)
    
    def _reconnect(self):
        """重新连接"""
        try:
            if self.reconnect_attempts >= self.max_reconnect_attempts:
                self._log(f"{self.log_prefix}达到最大重连次数，停止重连")
                return False
            
            self.reconnect_attempts += 1
            self._log(f"{self.log_prefix}开始第 {self.reconnect_attempts} 次重连")
            
            # 断开现有连接
            if self.trader:
                try:
                    self.trader.disconnect()
                except:
                    pass
            
            # 初始化交易对象
            self._init_trader()
            
            # 重新连接
            connect_success = self.connect()
            
            # 如果连接成功，则重新订阅
            if connect_success:
                subscribe_success = self.subscribe()
                self._log(f"{self.log_prefix}重连成功，订阅状态: {subscribe_success}")
                self.reconnect_attempts = 0
                return True
            else:
                self._log(f"{self.log_prefix}重连失败")
                return False
                
        except Exception as e:
            self._log(f"{self.log_prefix}重连异常: {str(e)}")
            return False
    
    def order_stock(self, symbol, direction, quantity, price_type, price, 
                   strategy_name="auto_trading", remark=""):
        """
        下单
        
        参数:
            symbol: 证券代码，如"600000.SH"
            direction: 交易方向，使用xtconstant.STOCK_BUY或xtconstant.STOCK_SELL
            quantity: 数量
            price_type: 价格类型，使用xtconstant.FIX_PRICE(限价)或xtconstant.LATEST_PRICE(市价)
            price: 价格，市价单可传0
            strategy_name: 策略名称
            remark: 备注
            
        返回:
            下单成功返回订单ID，失败返回None
        """
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法下单")
            return None
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法下单")
            return None
            
        try:
            # 下单
            self._log(f"{self.log_prefix}准备下单: {symbol}, 方向={direction}, 数量={quantity}, 价格={price}, 类型={price_type}")
            
            order_id = self.trader.order_stock(
                self.account, 
                symbol, 
                direction, 
                quantity, 
                price_type, 
                price, 
                strategy_name, 
                remark
            )
            
            if order_id:
                self._log(f"{self.log_prefix}下单成功，订单ID: {order_id}")
                return order_id
            else:
                self._log(f"{self.log_prefix}下单失败")
                return None
                
        except Exception as e:
            self._log(f"{self.log_prefix}下单异常: {str(e)}")
            return None
    
    def cancel_order(self, order_id):
        """
        撤单
        
        参数:
            order_id: 订单ID
            
        返回:
            撤单成功返回True，失败返回False
        """
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法撤单")
            return False
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法撤单")
            return False
            
        try:
            # 撤单
            self._log(f"{self.log_prefix}准备撤单: {order_id}")
            
            result = self.trader.cancel_order_stock(self.account, order_id)
            
            if result:
                self._log(f"{self.log_prefix}撤单成功，订单ID: {order_id}")
                return True
            else:
                self._log(f"{self.log_prefix}撤单失败，订单ID: {order_id}")
                return False
                
        except Exception as e:
            self._log(f"{self.log_prefix}撤单异常: {str(e)}")
            return False

    def liquidate_all_positions(self, price_discount_percent=0, use_market_order=True, remark="策略清仓"):
        """
        清仓所有持仓，撤销所有未成交订单
        
        参数:
            price_discount_percent: 价格折扣百分比，用于确保成交。例如，0.01表示比市价低1%
            use_market_order: 是否使用市价单，True使用市价单，False使用限价单
            remark: 订单备注
            
        返回:
            成功标志，清仓订单ID列表
        """
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法清仓")
            return False, []
        
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法清仓")
            return False, []
    
        # 1. 撤销所有未完成订单
        try:
            self._log(f"{self.log_prefix}开始撤销所有未完成订单...")
            orders = self.query_orders()
            cancel_count = 0
            
            for order in orders:
                # 检查订单状态，只撤销未完全成交的订单
                if hasattr(order, 'order_status') and order.order_status not in [2, 5]:  # 2-已成交，5-已撤单
                    if self.cancel_order(order.order_id):
                        cancel_count += 1
            
            self._log(f"{self.log_prefix}成功撤销 {cancel_count} 个未完成订单")
        except Exception as e:
            self._log(f"{self.log_prefix}撤销订单异常: {str(e)}")
        
        # 等待一段时间，确保撤单完成
        self.time.sleep(1)
        
        # 2. 清仓所有持仓
        try:
            self._log(f"{self.log_prefix}开始清仓所有持仓...")
            positions = self.query_positions()
            sell_orders = []
            
            for position in positions:
                if not hasattr(position, 'stock_code') or not hasattr(position, 'can_use_volume') or position.can_use_volume <= 0:
                    continue
                    
                symbol = position.stock_code
                quantity = position.can_use_volume
                
                # 获取当前价格
                from xtquant import xtdata
                try:
                    # 获取市场价格
                    last_price = 0
                    tick_data = xtdata.get_full_tick([symbol])
                    if symbol in tick_data and "lastPrice" in tick_data[symbol]:
                        last_price = tick_data[symbol]["lastPrice"]
                    
                    # 如果无法获取最新价，尝试获取证券详情中的价格
                    if last_price <= 0:
                        detail = xtdata.get_instrument_detail(symbol)
                        if detail and "pre_close" in detail:
                            last_price = detail["pre_close"]
                    
                    # 计算带折扣的卖出价格
                    sell_price = last_price * (1 - price_discount_percent)
                    
                    # 获取涨跌停价格，确保卖出价格在合理范围内
                    lower_limit = 0
                    detail = xtdata.get_instrument_detail(symbol)
                    if detail and "lower_limit" in detail:
                        lower_limit = detail["lower_limit"]
                    
                    # 确保价格不低于跌停价
                    if lower_limit > 0 and sell_price < lower_limit:
                        sell_price = lower_limit
                    
                    # 根据参数选择市价单或限价单
                    price_type = self.xtconstant.LATEST_PRICE if use_market_order else self.xtconstant.FIX_PRICE
                    if use_market_order:
                        sell_price = 0  # 市价单价格参数不重要
                    
                    # 下卖单
                    self._log(f"{self.log_prefix}清仓卖出: {symbol}, 数量={quantity}, 价格={sell_price}, 类型={'市价单' if use_market_order else '限价单'}")
                    order_id = self.order_stock(
                        symbol=symbol,
                        direction=self.xtconstant.STOCK_SELL,
                        quantity=quantity,
                        price_type=price_type,
                        price=sell_price,
                        strategy_name="liquidate_all",
                        remark=remark
                    )
                    
                    if order_id:
                        sell_orders.append(order_id)
                        self._log(f"{self.log_prefix}清仓卖出成功，订单ID: {order_id}")
                    else:
                        self._log(f"{self.log_prefix}清仓卖出失败: {symbol}")
                        
                except Exception as e:
                    self._log(f"{self.log_prefix}清仓卖出 {symbol} 异常: {str(e)}")
            
            self._log(f"{self.log_prefix}清仓完成，共发出 {len(sell_orders)} 个卖单")
            return len(sell_orders) > 0, sell_orders
            
        except Exception as e:
            self._log(f"{self.log_prefix}清仓异常: {str(e)}")
            return False, []
    def query_asset(self):
        """查询资产"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询资产")
            return None
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询资产")
            return None
            
        try:
            return self.trader.query_stock_asset(self.account)
        except Exception as e:
            self._log(f"{self.log_prefix}查询资产异常: {str(e)}")
            return None
    
    def query_orders(self):
        """查询当日委托"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询委托")
            return []
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询委托")
            return []
            
        try:
            return self.trader.query_stock_orders(self.account)
        except Exception as e:
            self._log(f"{self.log_prefix}查询委托异常: {str(e)}")
            return []
    
    def query_trades(self):
        """查询当日成交"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询成交")
            return []
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询成交")
            return []
            
        try:
            return self.trader.query_stock_trades(self.account)
        except Exception as e:
            self._log(f"{self.log_prefix}查询成交异常: {str(e)}")
            return []
    
    def query_positions(self):
        """查询持仓"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询持仓")
            return []
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询持仓")
            return []
            
        try:
            return self.trader.query_stock_positions(self.account)
        except Exception as e:
            self._log(f"{self.log_prefix}查询持仓异常: {str(e)}")
            return []
    
    def query_position(self, symbol):
        """查询单个持仓"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询持仓")
            return None
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询持仓")
            return None
            
        try:
            return self.trader.query_stock_position(self.account, symbol)
        except Exception as e:
            self._log(f"{self.log_prefix}查询持仓异常: {str(e)}")
            return None
    
    def query_order(self, order_id):
        """
        查询订单
        
        参数:
            order_id: 订单ID
            
        返回:
            订单对象，失败返回None
        """
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询订单")
            return None
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询订单")
            return None
            
        try:
            # 使用正确的方法名：query_stock_order，而不是query_order_by_code
            return self.trader.query_stock_order(self.account, order_id)
        except Exception as e:
            self._log(f"{self.log_prefix}查询订单异常: {str(e)}")
            return None
    
    def run_forever(self):
        """阻塞线程，接收交易推送"""
        if not self.trader:
            self._log(f"{self.log_prefix}交易对象未初始化，无法等待推送")
            return
            
        try:
            self.trader.run_forever()
        except Exception as e:
            self._log(f"{self.log_prefix}等待推送异常: {str(e)}")
    
    def shutdown(self):
        """关闭连接管理器"""
        self._log(f"{self.log_prefix}正在关闭交易连接管理器...")
        self.stop_monitor = True
        
        if hasattr(self, 'monitor_thread') and self.monitor_thread.is_alive():
            self.monitor_thread.join(5)
        
        if self.trader:
            try:
                self.trader.disconnect()
            except:
                pass
        
        self._log(f"{self.log_prefix}交易连接管理器已关闭")
    
    def setup_email(self, sender, receivers, smtp_server, smtp_port, username, password):
        """
        设置邮件发送参数
        
        参数:
            sender: 发件人邮箱地址
            receivers: 收件人邮箱地址列表
            smtp_server: SMTP服务器地址
            smtp_port: SMTP服务器端口
            username: 邮箱账号
            password: 邮箱密码
            
        返回:
            设置成功返回True，失败返回False
        """
        try:
            self.email_sender = sender
            self.email_receivers = receivers if isinstance(receivers, list) else [receivers]
            self.email_smtp_server = smtp_server
            self.email_smtp_port = smtp_port
            self.email_username = username
            self.email_password = password
            self.email_enabled = True
            
            self._log(f"{self.log_prefix}邮件设置成功")
            return True
        except Exception as e:
            self._log(f"{self.log_prefix}邮件设置异常: {str(e)}")
            self.email_enabled = False
            return False
    
    def send_email(self, subject, content):
        """
        发送邮件
        
        参数:
            subject: 邮件主题
            content: 邮件内容
            
        返回:
            发送成功返回True，失败返回False
        """
        if not self.email_enabled:
            self._log(f"{self.log_prefix}邮件功能未启用，无法发送邮件")
            return False
            
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            from email.header import Header
            
            # 创建邮件对象
            message = MIMEMultipart()
            message['From'] = self.email_sender
            message['To'] = ','.join(self.email_receivers)
            message['Subject'] = Header(subject, 'utf-8')
            
            # 添加邮件内容
            message.attach(MIMEText(content, 'plain', 'utf-8'))
            
            # 发送邮件
            if self.email_smtp_port == 465:
                # SSL加密方式
                smtp = smtplib.SMTP_SSL(self.email_smtp_server, self.email_smtp_port)
            else:
                # 普通方式
                smtp = smtplib.SMTP(self.email_smtp_server, self.email_smtp_port)
                
            # 登录邮箱
            smtp.login(self.email_username, self.email_password)
            
            # 发送邮件
            smtp.sendmail(self.email_sender, self.email_receivers, message.as_string())
            
            # 关闭连接
            smtp.quit()
            
            self._log(f"{self.log_prefix}邮件发送成功")
            return True
        except Exception as e:
            self._log(f"{self.log_prefix}邮件发送异常: {str(e)}")
            return False

    def try_connect_with_diagnostic(self):
        """专门处理C++环境下的连接问题，尝试多种路径组合和诊断报告"""
        import os
        import random
        import psutil  # 如果没有该模块，需要先安装
        
        self._log(f"{self.log_prefix}开始诊断连接问题...")
        
        # 1. 检查QMT客户端是否在运行
        qmt_running = False
        for proc in psutil.process_iter(['name']):
            if 'qmt' in proc.info['name'].lower() or 'mini' in proc.info['name'].lower():
                qmt_running = True
                self._log(f"{self.log_prefix}检测到QMT客户端正在运行: {proc.info['name']}")
        
        if not qmt_running:
            self._log(f"{self.log_prefix}警告: 未检测到QMT客户端进程，请确保QMT客户端已启动")
        
        # 2. 检查原始路径
        original_path = self.client_path
        path_exists = os.path.exists(original_path)
        self._log(f"{self.log_prefix}原始路径: {original_path}, 存在: {path_exists}")
        
        # 3. 生成备选路径
        possible_paths = []
        
        # 原始路径（尝试不同的斜杠格式）
        possible_paths.append(original_path)
        possible_paths.append(original_path.replace('\\', '/'))
        
        # 从原始路径中提取基础路径和子目录
        base_parts = []
        if '\\' in original_path:
            base_parts = original_path.split('\\')
        elif '/' in original_path:
            base_parts = original_path.split('/')
            
        # 如果基础路径中有userdata或userdata_mini，提取基础部分
        base_path = ""
        for i, part in enumerate(base_parts):
            if 'userdata' in part.lower():
                base_path = '/'.join(base_parts[:i])
                break
        
        if base_path:
            # 添加标准组合
            for subdir in ['userdata', 'userdata_mini']:
                possible_paths.append(f"{base_path}/{subdir}")
                possible_paths.append(f"{base_path}\\{subdir}")
        
        # 常见路径组合
        common_bases = [
            "D:/国金QMT交易端模拟",
            "D:\\国金QMT交易端模拟", 
            "D:/迅投QMT交易终端",
            "D:\\迅投QMT交易终端",
            "C:/国金QMT交易端模拟",
            "C:\\国金QMT交易端模拟"
        ]
        
        for base in common_bases:
            for subdir in ['userdata', 'userdata_mini']:
                possible_paths.append(f"{base}/{subdir}")
                possible_paths.append(f"{base}\\{subdir}")
        
        # 移除重复路径并过滤不存在的路径
        possible_paths = list(set(possible_paths))
        valid_paths = [p for p in possible_paths if os.path.exists(p)]
        
        self._log(f"{self.log_prefix}已找到{len(valid_paths)}个有效路径可尝试")
        
        # 4. 生成会话ID列表
        base_session_id = int(self.time.time()) % 10000
        session_ids = [base_session_id]
        for i in range(5):  # 生成5个额外的随机ID
            session_ids.append(base_session_id + random.randint(1, 1000))
        
        # 5. 尝试每个路径和会话ID的组合
        for path in valid_paths:
            # 检查关键目录是否存在
            critical_dirs = ['account', 'configfiles', 'dict', 'instruments']
            missing_dirs = []
            for d in critical_dirs:
                check_dir = os.path.join(path, d)
                if not os.path.exists(check_dir):
                    missing_dirs.append(d)
            
            if missing_dirs:
                self._log(f"{self.log_prefix}路径 {path} 缺少关键目录: {', '.join(missing_dirs)}")
                continue
                
            # 检查极简模式配置
            sim_mode_file = os.path.join(path, 'SimulationMode.txt')
            sim_mode_exists = os.path.exists(sim_mode_file)
            self._log(f"{self.log_prefix}极简模式配置文件: {sim_mode_exists}")
            
            for sid in session_ids:
                self._log(f"{self.log_prefix}尝试路径={path}, session_id={sid}")
                
                # 保存当前设置
                old_path = self.client_path
                old_sid = self.session_id
                
                # 尝试新设置
                self.client_path = path
                self.session_id = sid
                
                # 重新初始化交易对象并尝试连接
                self._init_trader()
                if self.trader:
                    try:
                        self.trader.start()
                        result = self.trader.connect()
                        
                        if result == 0:
                            self._log(f"{self.log_prefix}连接成功! 有效路径={path}, session_id={sid}")
                            
                            # 如果提供了账户，尝试订阅
                            if self.account:
                                sub_result = self.trader.subscribe(self.account)
                                if sub_result == 0:
                                    self._log(f"{self.log_prefix}账户订阅成功: {self.account_id}")
                                    self.connected = True
                                    self.last_heartbeat = self.time.time()
                                    return True
                                else:
                                    self._log(f"{self.log_prefix}账户订阅失败，错误码: {sub_result}")
                            else:
                                self.connected = True
                                self.last_heartbeat = self.time.time()
                                return True
                        else:
                            self._log(f"{self.log_prefix}连接失败，错误码: {result}")
                    except Exception as e:
                        self._log(f"{self.log_prefix}连接异常: {str(e)}")
                        
                # 还原原始设置如果失败
                if not self.connected:
                    self.client_path = old_path
                    self.session_id = old_sid
                
        # 6. 所有尝试失败，提供详细诊断报告
        self._log(f"{self.log_prefix}===== 连接诊断报告 =====")
        self._log(f"{self.log_prefix}1. QMT客户端状态: {'运行中' if qmt_running else '未检测到'}")
        self._log(f"{self.log_prefix}2. 有效路径数量: {len(valid_paths)}")
        if valid_paths:
            self._log(f"{self.log_prefix}3. 第一个有效路径: {valid_paths[0]}")
            sim_mode_path = os.path.join(valid_paths[0], 'SimulationMode.txt')
            self._log(f"{self.log_prefix}4. 极简模式配置: {'存在' if os.path.exists(sim_mode_path) else '不存在'}")
        self._log(f"{self.log_prefix}5. 尝试的会话ID数量: {len(session_ids)}")
        
        self._log(f"{self.log_prefix}===== 建议操作 =====")
        self._log(f"{self.log_prefix}1. 确保QMT客户端正在运行")
        self._log(f"{self.log_prefix}2. 确保QMT客户端已勾选'极简模式'")
        self._log(f"{self.log_prefix}3. 确保交易账户已登录")
        self._log(f"{self.log_prefix}4. 检查配置路径是否正确：{original_path}")
        self._log(f"{self.log_prefix}5. 尝试重启QMT客户端并再次运行程序")
        
        return False


class SmartOrderManager(TradeConnectionManager):
    """
    智能拆单交易管理类，支持买入和卖出的自动拆单执行。
    根据市场挂单量动态调整每笔订单大小，异步跟踪订单状态直到全部完成。
    
    特点:
    - 自动读取盘口挂单量，按比例拆单
    - 分批执行，成交后自动发送后续订单
    - 支持价格偏离和时间窗口限制
    - 完整的订单生命周期跟踪
    - 继承了交易连接管理功能
    - 支持邮件通知订单执行状态
    """
    
    def __init__(self, client_path=None, session_id=None, account_id=None, 
                 price_limit_percent=0, time_limit_seconds=0, order_size_ratio=1/3, 
                 max_retry=3, price_step_percent=0.02, status_check_interval=1, log_type=0,
                 heartbeat_timeout=30, max_reconnect_attempts=3):
        """
        初始化智能拆单管理器
        
        参数:
            client_path: 客户端路径，如"D:\\国金QMT交易端模拟\\userdata_mini"
            session_id: 会话编号，如果为None则自动生成
            account_id: 交易账号，如果为None则需要后续设置
            price_limit_percent: 价格偏离限制百分比，0表示不限制
            time_limit_seconds: 订单超时时间(秒)，0表示不限制
            order_size_ratio: 相对于盘口挂单量的下单比例，默认1/3
            max_retry: 单笔订单最大重试次数
            price_step_percent: 价格调整步长百分比
            status_check_interval: 订单状态检查间隔(秒)
            log_type: 日志输出类型，0为print输出(通用环境)，1为LogInfo输出(极星量化环境)
            heartbeat_timeout: 心跳超时秒数
            max_reconnect_attempts: 最大重连次数
        """
        # 首先调用父类的初始化方法
        TradeConnectionManager.__init__(
            self, 
            client_path=client_path,
            session_id=session_id,
            account_id=account_id,
            log_type=log_type
        )
        
        self.heartbeat_timeout = heartbeat_timeout
        self.max_reconnect_attempts = max_reconnect_attempts
        
        # 智能拆单相关参数
        self.price_limit_percent = price_limit_percent
        self.time_limit_seconds = time_limit_seconds
        self.order_size_ratio = order_size_ratio
        self.max_retry = max_retry
        self.price_step_percent = price_step_percent
        self.status_check_interval = status_check_interval
        
        # 订单跟踪相关变量
        self.active_orders = {}      # 活跃订单字典
        self.completed_orders = {}   # 已完成订单字典
        self.failed_orders = {}      # 失败订单字典
        self.order_tasks = {}        # 订单任务跟踪
        self.subscribed_symbols = {}  # 存储已订阅的股票代码
        # 订单状态常量
        self.ORDER_SUBMITTED = "已提交"
        self.ORDER_ACCEPTED = "已接受"
        self.ORDER_FILLED = "已成交"
        self.ORDER_PARTIALLY_FILLED = "部分成交"
        self.ORDER_CANCELLED = "已撤销"
        self.ORDER_REJECTED = "已拒绝"
        self.ORDER_EXPIRED = "已过期"
        
        # 日志相关
        self.log_prefix = "[智能拆单] "
        
        # 创建线程锁
        self.lock = self.threading.Lock()
        
        # 初始化回调对象
        self._init_callback()
        
        # 启动订单状态监控线程
        self.stop_monitor = False
        self.monitor_thread = self.threading.Thread(target=self._monitor_orders)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        self._log(f"{self.log_prefix}智能拆单交易管理器已初始化")
        
        # 自动启动交易连接（如果提供了客户端路径）
        if client_path:
            try:
                # 启动交易线程
                self._log(f"{self.log_prefix}正在启动交易线程...")
                start_result = self.start()
                
                if start_result:
                    # 建立交易连接
                    self._log(f"{self.log_prefix}正在建立交易连接...")
                    connect_result = self.connect()
                    
                    if connect_result and self.account_id:
                        # 订阅交易主推
                        self._log(f"{self.log_prefix}正在订阅交易主推...")
                        subscribe_result = self.subscribe()
                        self._log(f"{self.log_prefix}交易主推订阅结果: {'成功' if subscribe_result else '失败'}")
                    elif not connect_result:
                        self._log(f"{self.log_prefix}交易连接失败，无法订阅主推")
                    else:
                        self._log(f"{self.log_prefix}未设置账号，无法订阅主推")
                else:
                    self._log(f"{self.log_prefix}交易线程启动失败")
            except Exception as e:
                self._log(f"{self.log_prefix}启动交易服务异常: {str(e)}")
        else:
            self._log(f"{self.log_prefix}未提供客户端路径，跳过交易连接初始化")
    
    def _init_callback(self):
        """初始化并注册回调对象"""
        if not self.trader:
            self._log(f"{self.log_prefix}交易对象未初始化，无法注册回调")
            return
            
        try:
            from xtquant.xttrader import XtQuantTraderCallback
            
            class SmartOrderCallback(XtQuantTraderCallback):
                def __init__(self, manager):
                    self.manager = manager
                    
                def on_disconnected(self):
                    """连接断开时的回调"""
                    self.manager._log(f"{self.manager.log_prefix}交易连接断开，将尝试重连")
                    self.manager.connected = False
                    
                def on_stock_order(self, order):
                    """委托回报回调"""
                    order_id = order.order_id
                    
                    self.manager._log(f"{self.manager.log_prefix}收到委托回报: ID={order_id}, 状态={order.order_status}, 已成交量={order.traded_volume}")
                    
                    # 更新订单状态
                    with self.manager.lock:
                        if order_id in self.manager.active_orders:
                            order_info = self.manager.active_orders[order_id]
                            order_info["status"] = order.order_status
                            order_info["traded_volume"] = order.traded_volume
                            
                            # 检查是否完成
                            if order.order_status in [4, 5, 6]:  # 假设这些状态码表示已完成、已撤销、已拒绝
                                self.manager._log(f"{self.manager.log_prefix}订单已最终状态: ID={order_id}, 状态={order.order_status}")
                                self.manager.completed_orders[order_id] = order_info
                                del self.manager.active_orders[order_id]
                                
                                # 通知任务处理
                                task_id = order_info.get("task_id")
                                if task_id and task_id in self.manager.order_tasks:
                                    task_info = self.manager.order_tasks[task_id]
                                    if "callback" in task_info and callable(task_info["callback"]):
                                        self.manager._log(f"{self.manager.log_prefix}订单完成，调用任务回调: Task={task_id}")
                                        try:
                                            task_info["callback"](order_id, order_info, task_id)
                                        except Exception as e:
                                            self.manager._log(f"{self.manager.log_prefix}任务回调执行异常: {str(e)}")
                    
                def on_stock_trade(self, trade):
                    """成交回报回调"""
                    order_id = trade.order_id
                    self.manager._log(f"{self.manager.log_prefix}收到成交回报: ID={order_id}, 成交量={trade.traded_volume}")
                    
                def on_order_error(self, error):
                    """委托错误回调"""
                    order_id = error.order_id if hasattr(error, 'order_id') else "未知"
                    self.manager._log(f"{self.manager.log_prefix}委托错误: ID={order_id}, 错误={error.error_msg}")
                    
                    # 添加到失败订单列表
                    with self.manager.lock:
                        if hasattr(error, 'order_id') and error.order_id:
                            self.manager.failed_orders[error.order_id] = {
                                "error_msg": error.error_msg,
                                "error_code": error.error_id if hasattr(error, 'error_id') else -1,
                                "time": self.manager.time.time()
                            }
                            
                            # 如果订单在活跃列表中，移除
                            if error.order_id in self.manager.active_orders:
                                order_info = self.manager.active_orders[error.order_id]
                                task_id = order_info.get("task_id")
                                del self.manager.active_orders[error.order_id]
                                
                                # 通知任务处理
                                if task_id and task_id in self.manager.order_tasks:
                                    task_info = self.manager.order_tasks[task_id]
                                    if "error_callback" in task_info and callable(task_info["error_callback"]):
                                        self.manager._log(f"{self.manager.log_prefix}订单失败，调用错误回调: Task={task_id}")
                                        try:
                                            task_info["error_callback"](error.order_id, error.error_msg, task_id)
                                        except Exception as e:
                                            self.manager._log(f"{self.manager.log_prefix}错误回调执行异常: {str(e)}")
                    
                def on_order_stock_async_response(self, response):
                    """异步下单响应回调"""
                    if not hasattr(response, 'order_id') or not response.order_id:
                        self.manager._log(f"{self.manager.log_prefix}收到未知异步下单响应，缺少订单ID")
                        return
                        
                    order_id = response.order_id
                    self.manager._log(f"{self.manager.log_prefix}收到异步下单响应: ID={order_id}, 备注={response.order_remark if hasattr(response, 'order_remark') else '无'}")
            
            # 创建并注册回调
            self.order_callback = SmartOrderCallback(self)
            self.trader.register_callback(self.order_callback)
            self._log(f"{self.log_prefix}智能订单回调已注册")
            
        except Exception as e:
            self._log(f"{self.log_prefix}初始化回调异常: {str(e)}")
    
    def _ensure_subscribed(self, symbol):
        """确保股票代码已经订阅"""
        if symbol not in self.subscribed_symbols or not self.subscribed_symbols[symbol]:
            try:
                from xtquant import xtdata
                xtdata.subscribe_whole_quote([symbol])
                self.subscribed_symbols[symbol] = True
                self._log(f"{self.log_prefix}订阅 {symbol} 全推行情成功")
                # 短暂等待数据就绪
                self.time.sleep(0.5)
            except Exception as e:
                self._log(f"{self.log_prefix}订阅 {symbol} 全推行情异常: {str(e)}")
                return False
        return True
    
    def _get_last_price(self, symbol):
        """
        获取最新价
        
        参数:
            symbol: 股票代码
            
        返回:
            最新价，获取失败返回0
        """
        try:
            if self.log_type == 0:
                # 通用环境使用QMT的API
                from xtquant import xtdata

                # 确保已订阅
                self._ensure_subscribed(symbol)
                
                # 获取最新价
                tick_data = xtdata.get_full_tick([symbol])
                if symbol in tick_data and "lastPrice" in tick_data[symbol]:
                    return tick_data[symbol]["lastPrice"]
                return 0
            else:
                # 极星平台环境使用平台API
                return Q_Last(symbol)
        except Exception as e:
            self._log(f"{self.log_prefix}获取 {symbol} 最新价异常: {str(e)}")
            return 0
    
    def _get_ask1_info(self, symbol):
        """
        获取卖一档价格和量
        
        参数:
            symbol: 股票代码
            
        返回:
            (卖一价, 卖一量)元组，获取失败返回(0, 0)
        """
        try:
            if self.log_type == 0:
                # 通用环境使用QMT的API
                from xtquant import xtdata

                # 确保已订阅
                self._ensure_subscribed(symbol)
                
                # 获取卖一价和卖一量
                tick_data = xtdata.get_full_tick([symbol])
                if symbol in tick_data:
                    ask1_price = tick_data[symbol]["askPrice"][0] if "askPrice" in tick_data[symbol] and len(tick_data[symbol]["askPrice"]) > 0 else 0
                    ask1_volume = tick_data[symbol]["askVol"][0] if "askVol" in tick_data[symbol] and len(tick_data[symbol]["askVol"]) > 0 else 0
                    return ask1_price, ask1_volume
                return 0, 0
            else:
                # 极星平台环境使用平台API
                ask1_price = Q_AskPrice(symbol, 0)  # 卖一价
                ask1_volume = Q_AskVolume(symbol, 0)  # 卖一量
                return ask1_price, ask1_volume
        except Exception as e:
            self._log(f"{self.log_prefix}获取 {symbol} 卖一档信息异常: {str(e)}")
            return 0, 0
    
    def _get_bid1_info(self, symbol):
        """
        获取买一档价格和量
        
        参数:
            symbol: 股票代码
            
        返回:
            (买一价, 买一量)元组，获取失败返回(0, 0)
        """
        try:
            if self.log_type == 0:
                # 通用环境使用QMT的API
                from xtquant import xtdata

                # 确保已订阅
                self._ensure_subscribed(symbol)
                
                # 获取买一价和买一量
                tick_data = xtdata.get_full_tick([symbol])
                if symbol in tick_data:
                    bid1_price = tick_data[symbol]["bidPrice"][0] if "bidPrice" in tick_data[symbol] and len(tick_data[symbol]["bidPrice"]) > 0 else 0
                    bid1_volume = tick_data[symbol]["bidVol"][0] if "bidVol" in tick_data[symbol] and len(tick_data[symbol]["bidVol"]) > 0 else 0
                    return bid1_price, bid1_volume
                return 0, 0
            else:
                # 极星平台环境使用平台API
                bid1_price = Q_BidPrice(symbol, 0)  # 买一价
                bid1_volume = Q_BidVolume(symbol, 0)  # 买一量
                return bid1_price, bid1_volume
        except Exception as e:
            self._log(f"{self.log_prefix}获取 {symbol} 买一档信息异常: {str(e)}")
            return 0, 0
    
    def place_buy_order(self, symbol, total_quantity, limit_price=None, 
                        min_quantity=100, callback=None, **kwargs):
        """
        执行买入订单，自动拆分为多笔小订单
        
        参数:
            symbol: 股票代码
            total_quantity: 总买入数量
            limit_price: 限价，None表示市价
            min_quantity: 最小下单数量，低于此数量将直接一次性下单
            callback: 订单完成后的回调函数
            **kwargs: 其他参数传递给下单API
            
        返回:
            订单任务ID
        """
        # 标准化股票代码
        symbol = normalize_stock_code(symbol)
        
        # 创建订单任务ID
        task_id = f"BUY_{symbol}_{self.datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')}"
        
        # 获取当前市场价格和涨停价
        current_price = limit_price if limit_price else self._get_last_price(symbol)
        upper_limit = self._get_upper_limit(symbol)
        
        # 如果指定了限价并超过涨停，提前调整限价
        if limit_price and upper_limit > 0 and limit_price > upper_limit:
            self._log(f"{self.log_prefix}指定买入价格 {limit_price} 超过涨停价 {upper_limit}，自动调整为涨停价")
            limit_price = upper_limit
        
        # 计算价格限制
        max_price = current_price * (1 + self.price_limit_percent/100) if self.price_limit_percent else float('inf')
        
        # 计算时间限制
        end_time = self.time.time() + self.time_limit_seconds if self.time_limit_seconds else float('inf')
        
        # 创建任务数据结构
        task_data = {
            "task_id": task_id,
            "symbol": symbol,
            "action": "BUY",
            "total_quantity": total_quantity,
            "remaining_quantity": total_quantity,
            "filled_quantity": 0,
            "start_price": current_price,
            "limit_price": limit_price,
            "max_price": max_price,
            "end_time": end_time,
            "min_quantity": min_quantity,
            "status": "ACTIVE",
            "start_time": self.time.time(),
            "orders": [],
            "callback": callback,
            "kwargs": kwargs
        }
        
        # 添加到任务列表
        with self.lock:
            self.order_tasks[task_id] = task_data
        
        # 启动执行线程
        execution_thread = self.threading.Thread(
            target=self._execute_buy_task,
            args=(task_id,)
        )
        execution_thread.daemon = True
        execution_thread.start()
        
        self._log(f"{self.log_prefix}已创建买入任务 {task_id}，总数量: {total_quantity}，初始价格: {current_price}")
        return task_id
    
    def place_sell_order(self, symbol, total_quantity, limit_price=None, 
                         min_quantity=100, callback=None, **kwargs):
        """
        执行卖出订单，自动拆分为多笔小订单
        
        参数:
            symbol: 股票代码
            total_quantity: 总卖出数量
            limit_price: 限价，None表示以市价卖出
            min_quantity: 最小下单数量，低于此数量将直接一次性下单
            callback: 订单完成后的回调函数
            **kwargs: 其他参数传递给下单API
            
        返回:
            订单任务ID
        """
        # 标准化股票代码
        symbol = normalize_stock_code(symbol)
        
        # 创建订单任务ID
        task_id = f"SELL_{symbol}_{self.datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')}"
        
        # 获取当前市场价格和跌停价
        current_price = limit_price if limit_price else self._get_last_price(symbol)
        lower_limit = self._get_lower_limit(symbol)
        
        # 如果指定了限价并低于跌停，提前调整限价
        if limit_price and lower_limit > 0 and limit_price < lower_limit:
            self._log(f"{self.log_prefix}指定卖出价格 {limit_price} 低于跌停价 {lower_limit}，自动调整为跌停价")
            limit_price = lower_limit
        
        # 计算价格限制
        min_price = current_price * (1 - self.price_limit_percent/100) if self.price_limit_percent else float('-inf')
        
        # 计算时间限制
        end_time = self.time.time() + self.time_limit_seconds if self.time_limit_seconds else float('inf')
        
        # 创建任务数据结构
        task_data = {
            "task_id": task_id,
            "symbol": symbol,
            "action": "SELL",
            "total_quantity": total_quantity,
            "remaining_quantity": total_quantity,
            "filled_quantity": 0,
            "start_price": current_price,
            "limit_price": limit_price,
            "min_price": min_price,
            "end_time": end_time,
            "min_quantity": min_quantity,
            "status": "ACTIVE",
            "start_time": self.time.time(),
            "orders": [],
            "callback": callback,
            "kwargs": kwargs
        }
        
        # 添加到任务列表
        with self.lock:
            self.order_tasks[task_id] = task_data
        
        # 启动执行线程
        execution_thread = self.threading.Thread(
            target=self._execute_sell_task,
            args=(task_id,)
        )
        execution_thread.daemon = True
        execution_thread.start()
        
        self._log(f"{self.log_prefix}已创建卖出任务 {task_id}，总数量: {total_quantity}，初始价格: {current_price}")
        return task_id
    
    def _execute_buy_task(self, task_id):
        """执行买入任务的内部方法"""
        task = self.order_tasks.get(task_id)
        if not task:
            self._log(f"{self.log_prefix}找不到任务 {task_id}")
            return
        
        # 导入随机数模块
        import random
        
        symbol = task["symbol"]
        retry_count = 0
        
        while (task["remaining_quantity"] > 0 and 
               task["status"] == "ACTIVE" and 
               self.time.time() < task["end_time"] and
               retry_count < self.max_retry):
            
            # 检查价格是否超出限制
            current_price = self._get_last_price(symbol)
            if current_price > task["max_price"]:
                self._log(f"{self.log_prefix}任务 {task_id} 价格 {current_price} 超出限制 {task['max_price']}，停止执行")
                with self.lock:
                    task["status"] = "PRICE_LIMIT_EXCEEDED"
                break
            
            # 获取涨停价格
            upper_limit = self._get_upper_limit(symbol)
            if upper_limit <= 0:
                self._log(f"{self.log_prefix}无法获取 {symbol} 的涨停价，使用当前价格")
                upper_limit = None
            
            # 获取卖一档挂单量
            ask1_price, ask1_volume = self._get_ask1_info(symbol)
            if not ask1_volume or ask1_volume <= 0:
                self._log(f"{self.log_prefix}无法获取 {symbol} 的卖一档信息，等待重试")
                self.time.sleep(self.status_check_interval)
                retry_count += 1
                continue
            
            # 使用更复杂的随机因子，让下单量在50%-100%之间随机浮动
            random_factor = random.uniform(0.5, 1.0)
            
            # 也随机调整订单比例参数，使每次比例不同
            order_ratio_factor = random.uniform(0.8, 1.2) * self.order_size_ratio
            
            # 计算本次下单量，使用随机因子调整
            base_size = int(ask1_volume * order_ratio_factor)
            batch_size = min(
                int(base_size * random_factor),  # 加入随机因素的卖一档挂单量的比例
                task["remaining_quantity"]       # 剩余需要买入的数量
            )
            
            # 随机调整批量，增加随机扰动
            batch_random_adjustment = random.choice([0, 100, 200, 300, 400, 500])
            batch_size = (batch_size // 100) * 100 + batch_random_adjustment
            batch_size = min(batch_size, task["remaining_quantity"])
            
            # 随机调整最小下单量，在80%-120%之间浮动
            min_qty_factor = random.uniform(0.8, 1.2)
            adjusted_min_qty = int(task["min_quantity"] * min_qty_factor)
            adjusted_min_qty = (adjusted_min_qty // 100) * 100  # 确保是100的整数倍
            if adjusted_min_qty < 100:
                adjusted_min_qty = 100
            
            # 确保批量符合最小交易单位(通常是100股)
            batch_size = (batch_size // 100) * 100
            if batch_size < adjusted_min_qty:
                batch_size = min(adjusted_min_qty, task["remaining_quantity"])
            
            # 如果剩余很少，一次性买入
            if task["remaining_quantity"] < adjusted_min_qty:
                batch_size = task["remaining_quantity"]
            
            # 如果批量合法，执行买入
            if batch_size > 0:
                # 计算下单价格（限价或卖一价），同时确保不超过涨停价
                if task["limit_price"]:
                    order_price = task["limit_price"]
                else:
                    order_price = ask1_price
                    
                # 检查是否超过涨停价
                if upper_limit and order_price > upper_limit:
                    self._log(f"{self.log_prefix}买入价格 {order_price} 超过涨停价 {upper_limit}，已调整为涨停价")
                    order_price = upper_limit
                
                # 调用API买入
                try:
                    self._log(f"{self.log_prefix}任务 {task_id} 准备买入 {batch_size} 股 {symbol}，价格 {order_price}")
                    # 使用QMT的API执行买入
                    order_id = self._place_order(
                        symbol=symbol,
                        direction="BUY",
                        quantity=batch_size,
                        price=order_price,
                        **task["kwargs"]
                    )
                    
                    if order_id:
                        self._log(f"{self.log_prefix}任务 {task_id} 发送买入订单 {order_id}，数量 {batch_size}，价格 {order_price}")
                        with self.lock:
                            task["orders"].append({
                                "order_id": order_id,
                                "quantity": batch_size,
                                "price": order_price,
                                "status": "SUBMITTED",
                                "filled_quantity": 0,
                                "submit_time": self.time.time()
                            })
                            self.active_orders[order_id] = {
                                "task_id": task_id,
                                "symbol": symbol,
                                "action": "BUY",
                                "quantity": batch_size,
                                "price": order_price,
                                "status": "SUBMITTED"
                            }
                        
                        # 随机等待时间，2-5秒不等
                        wait_time = random.uniform(2, 5) * self.status_check_interval
                        self.time.sleep(wait_time)
                    else:
                        self._log(f"{self.log_prefix}任务 {task_id} 买入订单发送失败")
                        retry_count += 1
                except Exception as e:
                    self._log(f"{self.log_prefix}任务 {task_id} 买入异常: {str(e)}")
                    retry_count += 1
            
            # 检查任务状态和剩余数量
            with self.lock:
                task = self.order_tasks.get(task_id)
                if not task or task["status"] != "ACTIVE" or task["remaining_quantity"] <= 0:
                    break
            
            # 随机设置下一次尝试的等待时间，0.5-2倍的标准间隔
            wait_factor = random.uniform(0.5, 2.0)
            self.time.sleep(self.status_check_interval * wait_factor)
        
        # 最终检查任务状态
        with self.lock:
            task = self.order_tasks.get(task_id)
            if task and task["status"] == "ACTIVE":
                if task["remaining_quantity"] <= 0:
                    task["status"] = "COMPLETED"
                    self._log(f"{self.log_prefix}任务 {task_id} 已完成，总成交: {task['filled_quantity']}")
                    
                    # 发送邮件通知
                    if hasattr(self, 'email_enabled') and self.email_enabled:
                        subject = f"买入任务 {task_id} 已完成"
                        content = f"""
                        任务ID: {task_id}
                        股票代码: {task['symbol']}
                        总成交数量: {task['filled_quantity']}
                        成交均价: {task['filled_quantity'] > 0 and sum([order.get('avg_price', 0) * order.get('filled_quantity', 0) for order in task['orders'] if order.get('filled_quantity', 0) > 0]) / task['filled_quantity'] or 0}
                        执行时间: {self.datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                        """
                        self.send_email(subject, content)
                    
                elif self.time.time() >= task["end_time"]:
                    task["status"] = "TIMEOUT"
                    self._log(f"{self.log_prefix}任务 {task_id} 超时，剩余: {task['remaining_quantity']}")
                    
                    # 发送邮件通知
                    if hasattr(self, 'email_enabled') and self.email_enabled:
                        subject = f"买入任务 {task_id} 超时"
                        content = f"""
                        任务ID: {task_id}
                        股票代码: {task['symbol']}
                        已成交数量: {task['filled_quantity']}
                        未完成数量: {task['remaining_quantity']}
                        执行时间: {self.datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                        """
                        self.send_email(subject, content)
                    
                else:
                    task["status"] = "FAILED"
                    self._log(f"{self.log_prefix}任务 {task_id} 失败，剩余: {task['remaining_quantity']}")
                    
                    # 发送邮件通知
                    if hasattr(self, 'email_enabled') and self.email_enabled:
                        subject = f"买入任务 {task_id} 失败"
                        content = f"""
                        任务ID: {task_id}
                        股票代码: {task['symbol']}
                        已成交数量: {task['filled_quantity']}
                        未完成数量: {task['remaining_quantity']}
                        执行时间: {self.datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                        """
                        self.send_email(subject, content)
                
                # 执行回调
                if task["callback"]:
                    try:
                        task["callback"](task_id, task)
                    except Exception as e:
                        self._log(f"{self.log_prefix}任务 {task_id} 回调异常: {str(e)}")
    
    def _execute_sell_task(self, task_id):
        """执行卖出任务的内部方法"""
        task = self.order_tasks.get(task_id)
        if not task:
            self._log(f"{self.log_prefix}找不到任务 {task_id}")
            return
        
        # 导入随机数模块
        import random
        
        symbol = task["symbol"]
        retry_count = 0
        
        while (task["remaining_quantity"] > 0 and 
               task["status"] == "ACTIVE" and 
               self.time.time() < task["end_time"] and
               retry_count < self.max_retry):
            
            # 检查价格是否超出限制
            current_price = self._get_last_price(symbol)
            if current_price < task["min_price"]:
                self._log(f"{self.log_prefix}任务 {task_id} 价格 {current_price} 低于限制 {task['min_price']}，停止执行")
                with self.lock:
                    task["status"] = "PRICE_LIMIT_EXCEEDED"
                break
            
            # 获取跌停价格
            lower_limit = self._get_lower_limit(symbol)
            if lower_limit <= 0:
                self._log(f"{self.log_prefix}无法获取 {symbol} 的跌停价，使用当前价格")
                lower_limit = None
            
            # 获取买一档挂单量
            bid1_price, bid1_volume = self._get_bid1_info(symbol)
            if not bid1_volume or bid1_volume <= 0:
                self._log(f"{self.log_prefix}无法获取 {symbol} 的买一档信息，等待重试")
                self.time.sleep(self.status_check_interval)
                retry_count += 1
                continue
            
            # 使用更复杂的随机因子，让下单量在50%-100%之间随机浮动
            random_factor = random.uniform(0.5, 1.0)
            
            # 也随机调整订单比例参数，使每次比例不同
            order_ratio_factor = random.uniform(0.8, 1.2) * self.order_size_ratio
            
            # 计算本次下单量，使用随机因子调整
            base_size = int(bid1_volume * order_ratio_factor)
            batch_size = min(
                int(base_size * random_factor),  # 加入随机因素的买一档挂单量的比例
                task["remaining_quantity"]       # 剩余需要卖出的数量
            )
            
            # 随机调整批量，增加随机扰动
            batch_random_adjustment = random.choice([0, 100, 200, 300, 400, 500])
            batch_size = (batch_size // 100) * 100 + batch_random_adjustment
            batch_size = min(batch_size, task["remaining_quantity"])
            
            # 随机调整最小下单量，在80%-120%之间浮动
            min_qty_factor = random.uniform(0.8, 1.2)
            adjusted_min_qty = int(task["min_quantity"] * min_qty_factor)
            adjusted_min_qty = (adjusted_min_qty // 100) * 100  # 确保是100的整数倍
            if adjusted_min_qty < 100:
                adjusted_min_qty = 100
            
            # 确保批量符合最小交易单位(通常是100股)
            batch_size = (batch_size // 100) * 100
            if batch_size < adjusted_min_qty:
                batch_size = min(adjusted_min_qty, task["remaining_quantity"])
            
            # 如果剩余很少，一次性卖出
            if task["remaining_quantity"] < adjusted_min_qty:
                batch_size = task["remaining_quantity"]
            
            # 如果批量合法，执行卖出
            if batch_size > 0:
                # 计算下单价格（限价或买一价），同时确保不低于跌停价
                if task["limit_price"]:
                    order_price = task["limit_price"]
                else:
                    order_price = bid1_price
                    
                # 检查是否低于跌停价
                if lower_limit and order_price < lower_limit:
                    self._log(f"{self.log_prefix}卖出价格 {order_price} 低于跌停价 {lower_limit}，已调整为跌停价")
                    order_price = lower_limit
                
                # 调用API卖出
                try:
                    self._log(f"{self.log_prefix}任务 {task_id} 准备卖出 {batch_size} 股 {symbol}，价格 {order_price}")
                    # 使用QMT的API执行卖出
                    order_id = self._place_order(
                        symbol=symbol,
                        direction="SELL",
                        quantity=batch_size,
                        price=order_price,
                        **task["kwargs"]
                    )
                    
                    if order_id:
                        self._log(f"{self.log_prefix}任务 {task_id} 发送卖出订单 {order_id}，数量 {batch_size}，价格 {order_price}")
                        with self.lock:
                            task["orders"].append({
                                "order_id": order_id,
                                "quantity": batch_size,
                                "price": order_price,
                                "status": "SUBMITTED",
                                "filled_quantity": 0,
                                "submit_time": self.time.time()
                            })
                            self.active_orders[order_id] = {
                                "task_id": task_id,
                                "symbol": symbol,
                                "action": "SELL",
                                "quantity": batch_size,
                                "price": order_price,
                                "status": "SUBMITTED"
                            }
                        
                        # 随机等待时间，2-5秒不等
                        wait_time = random.uniform(2, 5) * self.status_check_interval
                        self.time.sleep(wait_time)
                    else:
                        self._log(f"{self.log_prefix}任务 {task_id} 卖出订单发送失败")
                        retry_count += 1
                except Exception as e:
                    self._log(f"{self.log_prefix}任务 {task_id} 卖出异常: {str(e)}")
                    retry_count += 1
            
            # 检查任务状态和剩余数量
            with self.lock:
                task = self.order_tasks.get(task_id)
                if not task or task["status"] != "ACTIVE" or task["remaining_quantity"] <= 0:
                    break
            
            # 随机设置下一次尝试的等待时间，0.5-2倍的标准间隔
            wait_factor = random.uniform(0.5, 2.0)
            self.time.sleep(self.status_check_interval * wait_factor)
        
        # 最终检查任务状态
        with self.lock:
            task = self.order_tasks.get(task_id)
            if task and task["status"] == "ACTIVE":
                if task["remaining_quantity"] <= 0:
                    task["status"] = "COMPLETED"
                    self._log(f"{self.log_prefix}任务 {task_id} 已完成，总成交: {task['filled_quantity']}")
                    
                    # 发送邮件通知
                    if hasattr(self, 'email_enabled') and self.email_enabled:
                        subject = f"卖出任务 {task_id} 已完成"
                        content = f"""
                        任务ID: {task_id}
                        股票代码: {task['symbol']}
                        总成交数量: {task['filled_quantity']}
                        成交均价: {task['filled_quantity'] > 0 and sum([order.get('avg_price', 0) * order.get('filled_quantity', 0) for order in task['orders'] if order.get('filled_quantity', 0) > 0]) / task['filled_quantity'] or 0}
                        执行时间: {self.datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                        """
                        self.send_email(subject, content)
                    
                elif self.time.time() >= task["end_time"]:
                    task["status"] = "TIMEOUT"
                    self._log(f"{self.log_prefix}任务 {task_id} 超时，剩余: {task['remaining_quantity']}")
                    
                    # 发送邮件通知
                    if hasattr(self, 'email_enabled') and self.email_enabled:
                        subject = f"卖出任务 {task_id} 超时"
                        content = f"""
                        任务ID: {task_id}
                        股票代码: {task['symbol']}
                        已成交数量: {task['filled_quantity']}
                        未完成数量: {task['remaining_quantity']}
                        执行时间: {self.datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                        """
                        self.send_email(subject, content)
                    
                else:
                    task["status"] = "FAILED"
                    self._log(f"{self.log_prefix}任务 {task_id} 失败，剩余: {task['remaining_quantity']}")
                    
                    # 发送邮件通知
                    if hasattr(self, 'email_enabled') and self.email_enabled:
                        subject = f"卖出任务 {task_id} 失败"
                        content = f"""
                        任务ID: {task_id}
                        股票代码: {task['symbol']}
                        已成交数量: {task['filled_quantity']}
                        未完成数量: {task['remaining_quantity']}
                        执行时间: {self.datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                        """
                        self.send_email(subject, content)
                
                # 执行回调
                if task["callback"]:
                    try:
                        task["callback"](task_id, task)
                    except Exception as e:
                        self._log(f"{self.log_prefix}任务 {task_id} 回调异常: {str(e)}")
    
    def _place_order(self, symbol, direction, quantity, price, **kwargs):
        """
        实际下单操作，调用QMT API
        
        返回订单ID或None
        """
        try:
            # 直接使用继承的方法，不再需要全局变量
            if direction == "BUY":
                order_id = self.order_stock(
                    symbol=symbol,
                    direction=self.xtconstant.STOCK_BUY, 
                    quantity=quantity,
                    price_type=self.xtconstant.FIX_PRICE,
                    price=price, 
                    strategy_name='智能拆单系统',
                    remark='买入'
                )
            else:  # SELL
                order_id = self.order_stock(
                    symbol=symbol,
                    direction=self.xtconstant.STOCK_SELL, 
                    quantity=quantity,
                    price_type=self.xtconstant.FIX_PRICE,
                    price=price, 
                    strategy_name='智能拆单系统',
                    remark='卖出'
                )
            
            return order_id
        except Exception as e:
            self._log(f"{self.log_prefix}下单异常: {str(e)}")
            return None
    
    def _monitor_orders(self):
        """
        订单状态监控线程
        持续监控活跃订单的状态变化
        """
        while not self.stop_monitor:
            try:
                # 复制活跃订单列表，避免遍历时修改
                with self.lock:
                    active_orders = list(self.active_orders.items())
                
                for order_id, order_info in active_orders:
                    try:
                        # 获取订单状态
                        order_status = self._get_order_status(order_id)
                        
                        if not order_status:
                            continue
                            
                        task_id = order_info["task_id"]
                        
                        # 更新订单状态
                        with self.lock:
                            if task_id in self.order_tasks and order_id in self.active_orders:
                                task = self.order_tasks[task_id]
                                
                                # 找到对应的订单记录
                                for order in task["orders"]:
                                    if order["order_id"] == order_id:
                                        order["status"] = order_status["status"]
                                        order["filled_quantity"] = order_status["filled_quantity"]
                                        
                                        # 如果订单状态改变
                                        if order_status["status"] != self.active_orders[order_id]["status"]:
                                            self.active_orders[order_id]["status"] = order_status["status"]
                                            
                                            # 处理已成交或部分成交
                                            if order_status["status"] in [self.ORDER_FILLED, self.ORDER_PARTIALLY_FILLED]:
                                                filled_qty = order_status["filled_quantity"] - order.get("reported_filled", 0)
                                                if filled_qty > 0:
                                                    order["reported_filled"] = order_status["filled_quantity"]
                                                    task["filled_quantity"] += filled_qty
                                                    task["remaining_quantity"] -= filled_qty
                                                    self._log(f"{self.log_prefix}任务 {task_id} 订单 {order_id} 成交 {filled_qty} 股，剩余 {task['remaining_quantity']} 股")
                                            
                                            # 处理已完成(成交/撤销/拒绝/过期)
                                            if order_status["status"] in [self.ORDER_FILLED, self.ORDER_CANCELLED, self.ORDER_REJECTED, self.ORDER_EXPIRED]:
                                                # 从活跃订单中移除
                                                self.completed_orders[order_id] = self.active_orders.pop(order_id)
                                                self.completed_orders[order_id]["completion_time"] = self.time.time()
                                                self._log(f"{self.log_prefix}订单 {order_id} 已完成，状态: {order_status['status']}")
                                        
                                        break
                    except Exception as e:
                        self._log(f"{self.log_prefix}监控订单 {order_id} 异常: {str(e)}")
            
            except Exception as e:
                self._log(f"{self.log_prefix}订单监控线程异常: {str(e)}")
            
            self.time.sleep(self.status_check_interval)
    
    def _get_order_status(self, order_id):
        """
        获取订单状态
        
        返回订单状态字典或None
        """
        try:
            # 使用继承的方法查询订单状态
            order_info = self.query_order(order_id)
            if not order_info:
                return None
            
            # 解析订单状态 - 对照xtconstant订单状态常量
            # 直接使用数字代替常量，避免属性不存在的问题
            # 0: 未报, 1: 待报, 2: 已报, 3: 已报待撤, 4: 部成待撤, 5: 部撤, 6: 已撤, 7: 已成, 8: 废单
            status_map = {
                0: self.ORDER_SUBMITTED,        # 未报
                1: self.ORDER_SUBMITTED,        # 待报
                2: self.ORDER_ACCEPTED,         # 已报
                3: self.ORDER_CANCELLED,        # 已报待撤
                4: self.ORDER_PARTIALLY_FILLED, # 部成待撤
                5: self.ORDER_PARTIALLY_FILLED, # 部撤
                6: self.ORDER_CANCELLED,        # 已撤
                7: self.ORDER_FILLED,           # 已成
                8: self.ORDER_REJECTED,         # 废单
            }
            
            # 根据提供的订单对象属性列表使用正确的属性名
            order_status = order_info.order_status if hasattr(order_info, 'order_status') else order_info.m_nOrderStatus
            status = status_map.get(order_status, "UNKNOWN")
            
            # 使用正确的成交数量属性
            filled_quantity = 0
            if hasattr(order_info, 'traded_volume'):
                filled_quantity = order_info.traded_volume
            elif hasattr(order_info, 'm_nTradedVolume'):
                filled_quantity = order_info.m_nTradedVolume
            
            # 使用正确的成交均价属性
            avg_price = 0
            if hasattr(order_info, 'traded_price'):
                avg_price = order_info.traded_price
            elif hasattr(order_info, 'm_dTradedPrice'):
                avg_price = order_info.m_dTradedPrice
            
            # 使用正确的订单时间属性
            order_time = ""
            if hasattr(order_info, 'order_time'):
                order_time = order_info.order_time
            elif hasattr(order_info, 'm_nOrderTime'):
                order_time = order_info.m_nOrderTime
            
            return {
                "status": status,
                "filled_quantity": filled_quantity,
                "avg_price": avg_price,
                "order_time": order_time
            }
        except Exception as e:
            self._log(f"{self.log_prefix}获取订单 {order_id} 状态异常: {str(e)}")
            return None
    
    def get_position_details(self):
        """
        获取所有持仓的详细信息，返回以合约代码为键的字典
        
        返回字典格式：
        {
            "600000.SH": {
                "symbol": "600000.SH",        # 合约代码
                "symbol_name": "浦发银行",    # 合约名称
                "direction": "LONG",          # 持仓方向：LONG多仓，SHORT空仓
                "total_amount": 1000,         # 总持仓量
                "today_amount": 500,          # 今日买入量
                "history_amount": 500,        # 历史持仓量
                "available_amount": 800,      # 可用持仓量
                "frozen_amount": 200,         # 冻结持仓量
                "position_price": 10.5,       # 持仓价格
                "market_price": 10.6,         # 最新市场价格
                "profit_loss": 100.0,         # 浮动盈亏
                "profit_loss_ratio": 0.01     # 盈亏比例
            },
            ...
        }
        """
        try:
            # 检查连接状态
            if not self.connected:
                self._log(f"{self.log_prefix}交易连接未建立，无法获取持仓信息")
                return {}
            
            # 查询所有持仓
            positions = self.query_positions()
            if not positions:
                return {}
            
            # 以合约代码为键构建持仓字典
            position_dict = {}
            
            for pos in positions:
                symbol = pos.stock_code if hasattr(pos, 'stock_code') else pos.m_strInstrumentID
                
                # 获取合约名称
                symbol_name = ""
                if hasattr(pos, 'stock_name'):
                    symbol_name = pos.stock_name
                elif hasattr(pos, 'm_strInstrumentName'):
                    symbol_name = pos.m_strInstrumentName
                
                # 获取持仓方向
                direction = "LONG"
                if hasattr(pos, 'direction'):
                    # 对于期货和期权，可能有方向区分
                    if pos.direction == 1:  # 假设1为空仓
                        direction = "SHORT"
                
                # 获取总持仓量
                total_amount = 0
                if hasattr(pos, 'volume'):
                    total_amount = pos.volume
                elif hasattr(pos, 'm_nVolume'):
                    total_amount = pos.m_nVolume
                
                # 获取今日持仓量
                today_amount = 0
                if hasattr(pos, 'today_volume'):
                    today_amount = pos.today_volume
                elif hasattr(pos, 'm_nTodayVolume'):
                    today_amount = pos.m_nTodayVolume
                
                # 获取历史持仓量
                history_amount = total_amount - today_amount
                
                # 获取可用持仓量
                available_amount = 0
                if hasattr(pos, 'can_use_volume'):
                    available_amount = pos.can_use_volume
                elif hasattr(pos, 'm_nCanUseVolume'):
                    available_amount = pos.m_nCanUseVolume
                
                # 获取冻结持仓量
                frozen_amount = total_amount - available_amount
                
                # 获取持仓价格
                position_price = 0
                if hasattr(pos, 'avg_price'):  # 优先使用成本价
                    position_price = pos.avg_price
                elif hasattr(pos, 'open_price'):  # 其次使用开仓价
                    position_price = pos.open_price
                elif hasattr(pos, 'm_dOpenPrice'):
                    position_price = pos.m_dOpenPrice
                
                # 获取最新市场价格和计算盈亏
                market_price = self._get_last_price(symbol)
                profit_loss = (market_price - position_price) * total_amount
                profit_loss_ratio = (market_price / position_price - 1) if position_price > 0 else 0
                
                # 构建持仓详情字典
                position_dict[symbol] = {
                    "symbol": symbol,
                    "symbol_name": symbol_name,
                    "direction": direction,
                    "total_amount": total_amount,
                    "today_amount": today_amount,
                    "history_amount": history_amount,
                    "available_amount": available_amount,
                    "frozen_amount": frozen_amount,
                    "position_price": position_price,
                    "market_price": market_price,
                    "profit_loss": profit_loss,
                    "profit_loss_ratio": profit_loss_ratio
                }
            
            return position_dict
            
        except Exception as e:
            self._log(f"{self.log_prefix}获取持仓详情异常: {str(e)}")
            return {}
    
    def cancel_task(self, task_id):
        """
        取消整个任务
        
        参数:
            task_id: 任务ID
            
        返回:
            是否成功
        """
        try:
            with self.lock:
                if task_id not in self.order_tasks:
                    self._log(f"{self.log_prefix}找不到任务 {task_id}")
                    return False
                
                task = self.order_tasks[task_id]
                if task["status"] != "ACTIVE":
                    self._log(f"{self.log_prefix}任务 {task_id} 已不是活跃状态，当前状态: {task['status']}")
                    return False
                
                task["status"] = "CANCELLING"
            
            # 取消所有活跃订单
            cancel_success = True
            for order in task["orders"]:
                order_id = order["order_id"]
                if order_id in self.active_orders:
                    if not self.cancel_order(order_id):
                        cancel_success = False
            
            with self.lock:
                task = self.order_tasks[task_id]
                task["status"] = "CANCELLED"
            
            self._log(f"{self.log_prefix}任务 {task_id} 已取消，成交量: {task['filled_quantity']}, 剩余: {task['remaining_quantity']}")
            
            # 发送邮件通知
            if hasattr(self, 'email_enabled') and self.email_enabled:
                subject = f"任务 {task_id} 已取消"
                content = f"""
                任务ID: {task_id}
                股票代码: {task['symbol']}
                操作类型: {"买入" if task["action"] == "BUY" else "卖出"}
                已成交数量: {task['filled_quantity']}
                未完成数量: {task['remaining_quantity']}
                取消时间: {self.datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                """
                self.send_email(subject, content)
            
            return cancel_success
        except Exception as e:
            self._log(f"{self.log_prefix}取消任务 {task_id} 异常: {str(e)}")
            return False
    
    def get_task_status(self, task_id):
        """
        获取任务状态
        
        参数:
            task_id: 任务ID
            
        返回:
            任务状态信息或None
        """
        with self.lock:
            return self.order_tasks.get(task_id, None)
    
    def get_all_tasks(self):
        """
        获取所有任务信息
        
        返回:
            任务字典的副本
        """
        with self.lock:
            return dict(self.order_tasks)
    
    def shutdown(self):
        """关闭管理器"""
        self._log(f"{self.log_prefix}正在关闭智能拆单交易管理器...")
        self.stop_monitor = True
        if hasattr(self, 'monitor_thread') and self.monitor_thread.is_alive():
            self.monitor_thread.join(5)  # 等待最多5秒
        
        # 调用父类的shutdown方法
        TradeConnectionManager.shutdown(self)
        
        self._log(f"{self.log_prefix}智能拆单交易管理器已关闭")


# 在策略关闭函数中添加清理代码
def on_strategy_stop():
    global g_order_manager, g_active_orders
    
    # 取消所有活跃订单
    for symbol, task_id in g_active_orders.items():
        if hasattr(g_order_manager, '_log'):
            g_order_manager._log(f"{g_order_manager.log_prefix}策略停止，取消 {symbol} 的订单 {task_id}")
        else:
            try:
                LogInfo(f"策略停止，取消 {symbol} 的订单 {task_id}")
            except:
                print(f"策略停止，取消 {symbol} 的订单 {task_id}")
        g_order_manager.cancel_task(task_id)
    
    # 关闭智能拆单管理器
    if g_order_manager:
        # 发送策略停止邮件通知
        if hasattr(g_order_manager, 'email_enabled') and g_order_manager.email_enabled:
            try:
                g_order_manager.send_email(
                    "交易策略已停止",
                    f"ETF资产管理策略已于 {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 停止运行。"
                )
            except Exception as e:
                try:
                    LogInfo(f"发送策略停止邮件失败: {str(e)}")
                except:
                    print(f"发送策略停止邮件失败: {str(e)}")
        
        g_order_manager.shutdown()
        if hasattr(g_order_manager, '_log'):
            g_order_manager._log(f"{g_order_manager.log_prefix}智能拆单管理器已关闭")
        else:
            try:
                LogInfo("智能拆单管理器已关闭")
            except:
                print("智能拆单管理器已关闭")



# 策略参数
g_params['K线基础时间']  = 'D'   # k线基础时间
g_params['K线基础周期']  = 1     # k线周期
g_params['选择显示合约序号']  = 1 # 选择显示合约序号

g_params['疯牛乖离阈值']  =  2000  #疯牛乖离阈值
g_params['疯熊乖离阈值']  = -2000  #疯熊乖离阈值

g_params['订阅数据长度']  =  20000  #订阅数据长度
g_params['配置文件夹路径'] = "E:\stock_trade"
g_params['配置文件名'] = "配置文件与股票ETF代码.xlsx"
trade_order_filedir = g_params['配置文件夹路径']
trade_config_file   = trade_order_filedir+"\\"+g_params['配置文件名'] 
trade_config_DATA   =pd.read_excel(trade_config_file,sheet_name = 0)

symbol_Id =trade_config_DATA["股票ETF代码"].dropna()
资金权重=trade_config_DATA["资金权重"].dropna()
是否交易=trade_config_DATA["是否交易"].dropna()
短周期=trade_config_DATA["短周期"].dropna()
长周期=trade_config_DATA["长周期"].dropna()
初始资金百分比 =trade_config_DATA["初始资金百分比"].dropna()
减仓阈值=trade_config_DATA["减仓阈值"].dropna()
加仓阈值=trade_config_DATA["加仓阈值"].dropna()
信息发送邮箱=str(trade_config_DATA["信息发送邮箱"].iloc[0])
邮箱SMTP地址=str(trade_config_DATA["邮箱SMTP地址"].iloc[0])
邮箱端口=str(int(trade_config_DATA["邮箱端口"].iloc[0]))
邮箱用户名=str(trade_config_DATA["邮箱用户名"].iloc[0])
邮箱授权码=str(trade_config_DATA["邮箱授权码"].iloc[0])
信息接收邮箱=list(trade_config_DATA["信息接收邮箱"].dropna())
交易账号=str(int(trade_config_DATA["交易账号"].iloc[0]))
客户端路径=str(trade_config_DATA["客户端路径"].iloc[0])
心跳超时秒数=int(trade_config_DATA["心跳超时秒数"].iloc[0])
最大重连次数=int(trade_config_DATA["最大重连次数"].iloc[0])

symbol_d=[]
for i in range(len(symbol_Id)):
    symbol_d.append(copy.deepcopy(deque([0]*9,maxlen=9)))
TotalNumber=len(symbol_Id)    
UPSA,DWSA=[0]*TotalNumber,[0]*TotalNumber     
BKStatus,SKStatus,BPStatus,SPStatus=[0]*TotalNumber,[0]*TotalNumber,[0]*TotalNumber,[0]*TotalNumber
UPSQ=copy.deepcopy(symbol_d) 
DWSQ=copy.deepcopy(symbol_d) 

k_btime,k_cycle,SetDisplayNo=0,0,0
BullishLimit,BearishLimit=0,0
FinancialWeighting=0
def initialize(context): 
    global g_params,k_btime,k_cycle,SetDisplayNo,BullishLimit,BearishLimit,FinancialWeighting,g_order_manager,g_active_orders
    k_btime = g_params['K线基础时间'] # k线基础时间取参数
    k_cycle = g_params['K线基础周期'] # k线基础周期取参数
    SetDisplayNo = g_params['选择显示合约序号']-1 # 选择显示合约序号
    BullishLimit = g_params['疯牛乖离阈值']         # 疯牛乖离阈值
    BearishLimit = g_params['疯熊乖离阈值']         # 疯熊乖离阈值 

    DaySubDataLength = g_params['订阅数据长度']   # 订阅日线数据长度
    SubDataLength = int(DaySubDataLength)      # 订阅数据长度
    AluSubDataLength = min(1000,SubDataLength)  # 计算数据长度    
    DisplayCode=stock_code_mapping(int(symbol_Id[SetDisplayNo]))
    SetBarInterval(DisplayCode, k_btime, k_cycle,DaySubDataLength,AluSubDataLength) #订阅分钟线数据
    for i in range(len(symbol_Id)):
        tcode=stock_code_mapping(int(symbol_Id[i]))
        if 是否交易[i]=="是":
            LogInfo("订阅",symbol_Id[i],"的合约"+tcode,"资金权重==>",资金权重[i],"短周期==>",短周期[i],"长周期==>",长周期[i],"初始资金百分比==>",初始资金百分比[i],"减仓阈值==>",减仓阈值[i],"加仓阈值==>",加仓阈值[i])
            FinancialWeighting+=资金权重[i]
            if i==SetDisplayNo:
                continue
            SetBarInterval(tcode, k_btime, k_cycle,SubDataLength,AluSubDataLength) #订阅交易合约
            # SetBarInterval(tcode, 'M', 1 ,DaySubDataLength,AluSubDataLength) #订阅分钟线数据      
    LogInfo("交易账号==>",交易账号,"客户端路径==>",客户端路径,"心跳超时秒数==>",心跳超时秒数,"最大重连次数==>",最大重连次数)     
    LogInfo("信息发送邮箱==>",信息发送邮箱,"邮箱SMTP地址==>",邮箱SMTP地址,"邮箱端口==>",邮箱端口,"邮箱用户名==>",邮箱用户名,"信息接收邮箱==>",信息接收邮箱)

    # 初始化智能拆单管理器 - SmartOrderManager 在初始化时会自动启动交易服务
    g_order_manager = SmartOrderManager(
        client_path=客户端路径,     # 客户端路径
        session_id=None,           # 自动生成
        account_id=交易账号,       # 交易账号
        price_limit_percent=1,     # 价格偏离限制1%
        time_limit_seconds=600,    # 10分钟超时 
        order_size_ratio=0.3,      # 相对盘口挂单量的30%
        max_retry=3,               # 最大重试3次
        price_step_percent=0.02,   # 价格调整步长0.02%
        status_check_interval=1,   # 每秒检查一次订单状态
        log_type=1,                # 使用LogInfo输出日志
        heartbeat_timeout=心跳超时秒数,      # 心跳超时秒数
        max_reconnect_attempts=最大重连次数  # 最大重连次数
    )
    # 设置邮件
    g_order_manager.setup_email(
        sender=信息发送邮箱,
        receivers=信息接收邮箱,
        smtp_server=邮箱SMTP地址,
        smtp_port=邮箱端口,
        username=邮箱用户名,  
        password=邮箱授权码
    )
    # 初始化活跃订单跟踪字典
    g_active_orders = {}
    
    SetTriggerType(1)
    SetTriggerType(5)
    SetOrderWay(1)
    SetActual()

    # 其他策略逻辑...
# def handle_data(context):
#     HTS=1 if context.strategyStatus()=="C" else 0
#     for i in range(len(symbol_Id)):
#         if 是否交易[i]=="否":
#             continue
#         tcode=stock_code_mapping(int(symbol_Id[i]))
#         # O = Open(tcode, k_btime, k_cycle)
#         H = High(tcode, k_btime, k_cycle)
#         L = Low(tcode, k_btime, k_cycle)
#         C = Close(tcode, k_btime, k_cycle)
#         CD = Close(tcode, 'D', 1)
#         if len(CD) < SlowLength:
#             return
#         financial_weighting=资金权重[i]/FinancialWeighting
#         MarginEquity= Margin(tcode)/(初始权益*financial_weighting)


from typing import Union
def stock_code_mapping(code: Union[int, str]) -> str:
    # 整数处理分支（进一步优化）
    if isinstance(code, int):
        if not (1 <= code <= 999999):
            raise ValueError("输入必须为6位以下正整数")
        
        # 格式化代码字符串（只做一次）
        code_str = f"{code:06d}"
        
        # 快速分类 - 使用整数除法和模运算
        first_digit = code // 100000
        first_two = code // 10000
        first_three = code // 1000
        
        # 沪市股票 (6开头)
        if first_digit == 6:
            if first_three == 688:
                return f"SSE|T|KSHARES|{code_str}"  # 科创板
            elif first_three in {600, 601, 603, 605}:
                return f"SSE|T|ASHARES|{code_str}"      # 沪主板
            
        # 深主板 (0,1,3开头或4-9开头)
        if first_three  in {0, 1, 3}:
            return f"SZSE|T|ASHARES|{code_str}"    # 深主板            
        # 中小板 (002开头)    
        if first_three == 2:
            return f"SZSE|T|SMESHARES|{code_str}"  # 中小板
        # 创业板 (30开头)
        if first_two == 30:
            return f"SZSE|T|CHSHARES|{code_str}"   # 创业板   
        # 深B股 (200开头)
        if first_three == 200:
            return f"SZSE|T|BSHARES|{code_str}"    # 深B股
        # ETF (159开头)
        if first_three == 159:
            return f"SZSE|T|FUNDS|{code_str}"      # ETF
             
        # 基金 (5开头)
        if first_digit == 5:
            return f"SSE|T|FUNDS|{code_str}"       # 沪基金
            
        # REITs (16-18开头)
        if first_two in {16, 18}:
            return f"SZSE|T|FUNDS|{code_str}"      # REITs
            
        # 沪B股 (9开头)
        if first_digit == 9:
            return f"SSE|T|BSHARES|{code_str}"     # 沪B股
            
        # 北交所和新三板
        if first_three in {830, 831, 832, 833, 834, 835, 836, 837, 838, 839, 
                          870, 871, 872, 873, 874, 875, 876, 877, 878, 879, 
                          920, 921, 922, 923, 924, 925, 926, 927, 928, 929}:
            return f"BJSE|T|STOCK|{code_str}"
        if first_three in {400, 430, 830}:
            return f"NEEQ|T|OTC|{code_str}"
            
        return f"UNKNOWN|{code_str}"
    
    # 字符串处理分支（原逻辑）
    elif isinstance(code, str):
        if not (code.isdigit() and len(code) == 6):
            raise ValueError("输入必须为6位数字字符串")

        if code.startswith('688'):
            return f"SSE|T|KSHARES|{code}"
        elif code.startswith(('600','601','603','605')):
            return f"SSE|T|ASHARES|{code}"
        elif code.startswith('5'):
            return f"SSE|T|FUNDS|{code}"
        elif code.startswith('900'):
            return f"SSE|T|BSHARES|{code}"
        elif code.startswith('159'):
            return f"SZSE|T|FUNDS|{code}"
        elif code.startswith(('000','001','003')):
            return f"SZSE|T|ASHARES|{code}"
        elif code.startswith('002'):
            return f"SZSE|T|SMESHARES|{code}"
        elif code.startswith('30'):
            return f"SZSE|T|CHSHARES|{code}"
        elif code.startswith('200'):
            return f"SZSE|T|BSHARES|{code}"
        elif code.startswith(('16','18')):
            return f"SZSE|T|FUNDS|{code}"
        elif code.startswith(('83','87','920')):
            return f"BJSE|T|STOCK|{code}"
        elif code.startswith(('400','430','830')):
            return f"NEEQ|T|OTC|{code}"
        else:
            return f"UNKNOWN|{code}"
    
    else:
        raise TypeError("输入类型必须为int或str")
    
def stock_index_code_mapping(code: Union[int, str]) -> str:
    if isinstance(code, int):
        code_str = f"{code:06d}"
        prefix_three = code // 1000
        if prefix_three == 399:
            return f"SZSE|T|INDEX|{code_str}"
        if prefix_three == 0:
            return f"SSE|T|INDEX|{code_str}"
    elif isinstance(code, str):
        if code.startswith('399'):
            return f"SZSE|T|INDEX|{code}"
        if code.startswith('000'):
            return f"SSE|T|INDEX|{code}"

def normalize_stock_code(code):
    """
    将各种格式的股票代码转换为标准的QMT API格式
    沪市：数字代码.SH（例如：600000.SH）
    深市：数字代码.SZ（例如：000001.SZ）
    北交所：数字代码.BJ（例如：430047.BJ）
    
    参数:
        code: 字符串型股票代码，可以是多种格式（sh600000, SH600000, 600000, 600000.SH, 430047.BJ等）
    
    返回:
        标准化后的股票代码字符串
    """
    # 去除所有空格并转换为大写
    code = str(code).strip().upper()
    
    # 去除任何前缀和后缀，只保留数字部分
    numeric_part = ''.join(filter(str.isdigit, code))
    
    # 判断市场类型
    if any(prefix in code for prefix in ['SH', 'SHSE', 'SSE', 'SH.', '.SH']):
        return f"{numeric_part}.SH"
    elif any(prefix in code for prefix in ['SZ', 'SZSE', 'SZ.', '.SZ']):
        return f"{numeric_part}.SZ"
    elif any(prefix in code for prefix in ['BJ', 'BSE', 'BJSE', 'BJ.', '.BJ']):
        return f"{numeric_part}.BJ"
    else:
        # 根据股票代码规则判断市场类型
        # 沪市：以6开头（主板）、5开头（基金）、7开头（衍生品）
        # 深市：以0开头（主板）、1开头（SME）、2开头（中小板）、3开头（创业板）
        # 北交所：以4、8开头，68开头或82、83、87、88开头的股票代码
        if numeric_part.startswith('6'):
            return f"{numeric_part}.SH"
        elif numeric_part.startswith(('0', '1', '2', '3')):
            return f"{numeric_part}.SZ"
        elif numeric_part.startswith('4') or numeric_part.startswith('8'):
            # 北交所代码通常以4开头（如43开头的新三板精选层挂牌公司）
            # 或8开头（部分北交所特定代码）
            return f"{numeric_part}.BJ"
        elif numeric_part.startswith('68') or numeric_part.startswith(('82', '83', '87', '88')):
            # 特定的北交所其他代码规则
            return f"{numeric_part}.BJ"
        elif numeric_part.startswith('5') or numeric_part.startswith('7'):
            # 上交所基金和衍生品
            return f"{numeric_part}.SH"
        else:
            # 无法确定市场类型，保持原样返回
            return code

