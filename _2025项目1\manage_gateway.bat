@echo off
color 0A
title 量化交易网关服务管理工具

:menu
cls
echo ====================================
echo       量化交易网关服务管理工具
echo ====================================
echo.
echo   [1] 启动网关服务
echo   [2] 停止网关服务
echo   [3] 重启网关服务
echo   [4] 查看服务状态
echo   [5] 查看服务日志
echo   [0] 退出
echo.
echo ====================================

set /p choice=请选择操作 (0-5): 

if "%choice%"=="1" goto start
if "%choice%"=="2" goto stop
if "%choice%"=="3" goto restart
if "%choice%"=="4" goto status
if "%choice%"=="5" goto logs
if "%choice%"=="0" goto end

echo 无效的选择，请重新输入！
timeout /t 2 >nul
goto menu

:start
cls
echo 正在启动量化交易网关服务...
net start QMTTradingGateway
if %errorlevel% neq 0 (
    echo 启动服务失败，请确认服务是否已安装或以管理员身份运行此工具。
) else (
    echo 服务已成功启动！
)
pause
goto menu

:stop
cls
echo 正在停止量化交易网关服务...
net stop QMTTradingGateway
if %errorlevel% neq 0 (
    echo 停止服务失败，请确认服务是否已安装或以管理员身份运行此工具。
) else (
    echo 服务已成功停止！
)
pause
goto menu

:restart
cls
echo 正在重启量化交易网关服务...
net stop QMTTradingGateway
timeout /t 3 >nul
net start QMTTradingGateway
if %errorlevel% neq 0 (
    echo 重启服务失败，请确认服务是否已安装或以管理员身份运行此工具。
) else (
    echo 服务已成功重启！
)
pause
goto menu

:status
cls
echo 正在查询量化交易网关服务状态...
sc query QMTTradingGateway
pause
goto menu

:logs
cls
echo 正在打开日志文件夹...
set log_dir=%~dp0logs
if not exist "%log_dir%" (
    echo 日志目录不存在，正在创建...
    mkdir "%log_dir%"
)
start explorer "%log_dir%"
pause
goto menu

:end
exit /b