#!/usr/bin/env python3
"""
Opik MCP Server Wrapper
为Opik ML实验跟踪服务提供MCP接口
"""

import asyncio
import json
import sys
from typing import Any, Dict, List, Optional

try:
    import opik
    from mcp.server import Server
    from mcp.types import Tool, TextContent
    import mcp.server.stdio
except ImportError as e:
    print(f"Error importing required modules: {e}", file=sys.stderr)
    sys.exit(1)

# 创建MCP服务器实例
server = Server("opik-mcp-server")

# 全局Opik客户端
opik_client: Optional[Any] = None

@server.list_tools()
async def list_tools() -> List[Tool]:
    """列出可用的工具"""
    return [
        Tool(
            name="init_opik",
            description="Initialize Opik client",
            inputSchema={
                "type": "object",
                "properties": {
                    "project_name": {"type": "string", "description": "Project name"},
                    "api_key": {"type": "string", "description": "API key (optional)"}
                },
                "required": ["project_name"]
            }
        ),
        Tool(
            name="log_experiment",
            description="Log an experiment",
            inputSchema={
                "type": "object",
                "properties": {
                    "name": {"type": "string", "description": "Experiment name"},
                    "parameters": {"type": "object", "description": "Experiment parameters"},
                    "metrics": {"type": "object", "description": "Experiment metrics"}
                },
                "required": ["name"]
            }
        ),
        Tool(
            name="get_experiments",
            description="Get list of experiments",
            inputSchema={
                "type": "object",
                "properties": {
                    "limit": {"type": "integer", "description": "Number of experiments to retrieve", "default": 10}
                }
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """处理工具调用"""
    global opik_client
    
    try:
        if name == "init_opik":
            # 初始化Opik客户端
            project_name = arguments.get("project_name", "default")
            api_key = arguments.get("api_key")
            
            # 这里需要根据Opik的实际API调整
            opik_client = opik.Client(project_name=project_name)
            
            return [TextContent(
                type="text",
                text=f"Opik client initialized for project '{project_name}'"
            )]
        
        elif name == "log_experiment":
            if not opik_client:
                return [TextContent(
                    type="text",
                    text="Error: Opik client not initialized. Please initialize first."
                )]
            
            exp_name = arguments.get("name", "")
            parameters = arguments.get("parameters", {})
            metrics = arguments.get("metrics", {})
            
            # 记录实验逻辑（这里需要根据Opik的实际API调整）
            return [TextContent(
                type="text",
                text=f"Experiment '{exp_name}' logged with parameters: {json.dumps(parameters)} and metrics: {json.dumps(metrics)}"
            )]
        
        elif name == "get_experiments":
            if not opik_client:
                return [TextContent(
                    type="text",
                    text="Error: Opik client not initialized. Please initialize first."
                )]
            
            limit = arguments.get("limit", 10)
            
            # 获取实验列表逻辑（这里需要根据Opik的实际API调整）
            return [TextContent(
                type="text",
                text=f"Retrieved {limit} experiments: [Experiment list would be displayed here]"
            )]
        
        else:
            return [TextContent(
                type="text",
                text=f"Unknown tool: {name}"
            )]
    
    except Exception as e:
        return [TextContent(
            type="text",
            text=f"Error executing tool '{name}': {str(e)}"
        )]

async def main():
    """主函数"""
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())
