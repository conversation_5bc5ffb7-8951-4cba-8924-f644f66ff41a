"""
历史回测模式平仓逻辑调试脚本
模拟历史回测环境，验证平仓逻辑是否正确工作
"""

# 模拟历史回测订单管理器
class MockHistoricalArbitrageOrderManager:
    def __init__(self):
        self.executed_arb_orders = {}
        self.triggered_breakouts = {}
    
    def submit_arb_order(self, setNo, direction, qty, contracts_with_coefs, 
                        first_leg_mode='最新价', other_leg_mode='最新价', 
                        price_offset=0, order_type='2', grid_level=0):
        """模拟提交套利组合订单"""
        import time
        import random
        arb_id = f"HIS_ARB_{setNo}_{direction}_{int(time.time())}_{random.randint(1000,9999)}"
        
        # 记录执行结果
        self.executed_arb_orders[arb_id] = {
            'direction': direction,
            'qty': qty,
            'contracts': [c[0] for c in contracts_with_coefs],
            'coefficients': {c[0]: c[1] for c in contracts_with_coefs},
            'status': 'COMPLETED',
            'submit_time': time.time(),
            'grid_level': grid_level
        }
        
        print(f"历史回测-提交套利组合订单，ID:{arb_id}，方向:{direction}，网格:{grid_level}")
        return arb_id
    
    def close_arb_order(self, arb_id, force_market=False):
        """模拟平仓套利组合订单"""
        import time
        if arb_id not in self.executed_arb_orders:
            print(f'历史回测-未找到套利组合订单，ID:{arb_id}')
            return
        
        arb_order = self.executed_arb_orders[arb_id]
        if arb_order['status'] == 'CLOSED':
            print(f'历史回测-套利组合{arb_id}已经关闭')
            return
        
        grid_level = arb_order.get('grid_level', 0)
        print(f'历史回测-开始平仓套利组合{arb_id}，网格{grid_level}，方向{arb_order["direction"]}')
        
        # 更新状态
        arb_order['status'] = 'CLOSED'
        arb_order['close_time'] = time.time()
        print(f'历史回测-套利组合{arb_id}平仓完成')

def test_historical_close_logic():
    """测试历史回测模式下的平仓逻辑"""
    print("=== 历史回测模式平仓逻辑测试 ===\n")
    
    # 创建模拟订单管理器
    order_manager = MockHistoricalArbitrageOrderManager()
    
    # 模拟参数
    setNo = 0
    突破网格点数 = 10.0
    QMA1 = 100.0
    下单量 = 1
    contracts_with_coefs = [('SHFE|F|rb|2505', 1), ('SHFE|F|rb|2501', 1)]
    
    # 模拟开仓过程
    print("## 开仓阶段")
    LastValidUP = 0
    
    # 模拟价格上涨突破
    breakthrough_prices = [110.5, 121.2, 132.8]
    opened_orders = []
    
    for price in breakthrough_prices:
        cur_up = int((price - QMA1) // 突破网格点数)
        if cur_up > LastValidUP:
            # 有效突破，开仓
            arb_id = order_manager.submit_arb_order(
                setNo, "BUY", 下单量, contracts_with_coefs, 
                grid_level=cur_up
            )
            opened_orders.append(arb_id)
            LastValidUP = cur_up
            print(f"价格 {price:.1f} → 突破网格{cur_up} → 开多仓 {arb_id}")
    
    print(f"\n当前已开仓订单: {len(opened_orders)}个")
    for arb_id in opened_orders:
        order = order_manager.executed_arb_orders[arb_id]
        print(f"  {arb_id}: 网格{order['grid_level']}, 状态={order['status']}")
    print(f"当前LastValidUP: {LastValidUP}")
    
    # 模拟平仓过程
    print(f"\n## 平仓阶段")
    
    # 模拟价格回落
    falling_prices = [128.5, 125.0, 119.8, 115.0, 108.5]
    
    for price in falling_prices:
        cur_up = max(0, int((price - QMA1) // 突破网格点数))
        print(f"\n当前价格: {price:.1f}, 当前网格: {cur_up}, LastValidUP: {LastValidUP}")
        
        # 新的平仓逻辑测试
        if cur_up <= LastValidUP - 1 and LastValidUP > 0:
            print(f'上轨回落到网格{cur_up}，平仓网格{LastValidUP}的套利组合')
            
            # 模拟主逻辑中的平仓代码
            orders_dict = order_manager.executed_arb_orders
            prefix = f"HIS_ARB_{setNo}_"
            
            print(f'历史回测模式-检查需要平仓的BUY订单，目标网格{LastValidUP}，当前有{len(orders_dict)}个订单')
            found_orders = 0
            
            for arb_id, arb_order in list(orders_dict.items()):
                order_grid = arb_order.get('grid_level', 0)
                order_direction = arb_order['direction']
                order_status = arb_order['status']
                print(f'  检查订单{arb_id}: 方向={order_direction}, 状态={order_status}, 网格={order_grid}')
                
                if (arb_order['direction'] == "BUY" and 
                    arb_id.startswith(prefix) and 
                    arb_order['status'] not in ['CLOSED', 'LAME_LEG_CLOSED'] and
                    arb_order.get('grid_level', 0) == LastValidUP):
                    found_orders += 1
                    print(f'历史回测模式-上轨回落到网格{cur_up}，平仓网格{LastValidUP}的套利组合{arb_id}')
                    order_manager.close_arb_order(arb_id)
                    arb_order['status'] = 'CLOSED'
            
            print(f'历史回测模式-上轨回落平仓完成，共平仓{found_orders}个订单')
            
            # 更新LastValidUP
            LastValidUP = cur_up
            print(f'更新LastValidUP为: {LastValidUP}')
        else:
            print(f'未满足平仓条件 (cur_up={cur_up} > LastValidUP-1={LastValidUP-1})')
        
        # 显示当前订单状态
        open_orders = [arb_id for arb_id, order in order_manager.executed_arb_orders.items() 
                      if order['status'] != 'CLOSED']
        if open_orders:
            print(f"剩余开仓订单:")
            for arb_id in open_orders:
                order = order_manager.executed_arb_orders[arb_id]
                print(f"  {arb_id}: 网格{order['grid_level']}")
        else:
            print(f"所有订单已平仓")

def test_potential_issues():
    """测试可能的问题场景"""
    print("\n\n=== 潜在问题场景测试 ===\n")
    
    # 创建订单管理器
    order_manager = MockHistoricalArbitrageOrderManager()
    
    # 测试场景1：grid_level为0的情况
    print("场景1: grid_level为0的订单")
    arb_id1 = order_manager.submit_arb_order(0, "BUY", 1, [('TEST', 1)], grid_level=0)
    print(f"订单{arb_id1}的grid_level: {order_manager.executed_arb_orders[arb_id1].get('grid_level', 'NOT_FOUND')}")
    
    # 测试场景2：多个相同网格的订单
    print(f"\n场景2: 多个相同网格的订单")
    arb_id2 = order_manager.submit_arb_order(0, "BUY", 1, [('TEST', 1)], grid_level=2)
    arb_id3 = order_manager.submit_arb_order(0, "BUY", 1, [('TEST', 1)], grid_level=2)
    arb_id4 = order_manager.submit_arb_order(0, "SELL", 1, [('TEST', 1)], grid_level=2)
    
    print("现有订单:")
    for arb_id, order in order_manager.executed_arb_orders.items():
        print(f"  {arb_id}: 方向={order['direction']}, 网格={order.get('grid_level', 'NOT_FOUND')}, 状态={order['status']}")
    
    # 模拟平仓网格2的BUY订单
    print(f"\n模拟平仓网格2的BUY订单:")
    target_grid = 2
    prefix = "HIS_ARB_0_"
    found_orders = 0
    
    for arb_id, arb_order in list(order_manager.executed_arb_orders.items()):
        order_grid = arb_order.get('grid_level', 0)
        order_direction = arb_order['direction']
        order_status = arb_order['status']
        print(f'  检查订单{arb_id}: 方向={order_direction}, 状态={order_status}, 网格={order_grid}')
        
        if (arb_order['direction'] == "BUY" and 
            arb_id.startswith(prefix) and 
            arb_order['status'] not in ['CLOSED', 'LAME_LEG_CLOSED'] and
            arb_order.get('grid_level', 0) == target_grid):
            found_orders += 1
            print(f'  → 平仓订单{arb_id}')
            order_manager.close_arb_order(arb_id)
            arb_order['status'] = 'CLOSED'
    
    print(f"共平仓{found_orders}个BUY订单")
    
    print(f"\n平仓后的订单状态:")
    for arb_id, order in order_manager.executed_arb_orders.items():
        print(f"  {arb_id}: 方向={order['direction']}, 网格={order.get('grid_level', 'NOT_FOUND')}, 状态={order['status']}")

if __name__ == "__main__":
    test_historical_close_logic()
    test_potential_issues()
    print("\n调试测试完成!") 