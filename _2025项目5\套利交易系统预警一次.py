import re
import copy
import numpy as np
import pandas as pd
from talib import MA
from collections import deque
from collections import defaultdict

# 策略参数
g_params['K线基础时间']  = 'M'     # k线基础时间
g_params['K线基础周期']  = 1       # k线周期
g_params['选择显示合约序号'] = 1   # 选择显示合约序号
g_params['回测阶段预警'] = '开'    # 开为开启历史回测阶段预警信息，否则为关
g_params['选择预警声音'] = 0       # 0为普通话预警声音，1为粤语预警声音
g_params['订阅数据长度']  =  2000  #订阅数据长度


g_params['配置文件夹路径'] = "D:\Quant000150v9.5\Quant\Strategy\用户策略\_2025项目5"
g_params['配置文件名'] = "配置文件与合约代码.xlsx"
trade_order_filedir = g_params['配置文件夹路径']
trade_config_file   = trade_order_filedir+"\\"+g_params['配置文件名'] 
trade_config_DATA   =pd.read_excel(trade_config_file,sheet_name = 0)
symbol_Id=list(trade_config_DATA["套利组合"].dropna())
均线一=list(trade_config_DATA["均线一"].dropna())
均线二=list(trade_config_DATA["均线二"].dropna())
均线三=list(trade_config_DATA["均线三"].dropna())
初预警上突破点数=list(trade_config_DATA["初预警上突破点数"].dropna())
初预警下突破点数=list(trade_config_DATA["初预警下突破点数"].dropna())
正式预警上突破点数=list(trade_config_DATA["正式预警上突破点数"].dropna())
正式预警下突破点数=list(trade_config_DATA["正式预警下突破点数"].dropna())
邮件发送=str(trade_config_DATA["邮件发送"].iloc[0])
信息发送邮箱=str(trade_config_DATA["信息发送邮箱"].iloc[0])
邮箱SMTP地址=str(trade_config_DATA["邮箱SMTP地址"].iloc[0])
邮箱端口=str(int(trade_config_DATA["邮箱端口"].iloc[0]))
邮箱用户名=str(trade_config_DATA["邮箱用户名"].iloc[0])
邮箱授权码=str(trade_config_DATA["邮箱授权码"].iloc[0])
信息接收邮箱=list(trade_config_DATA["信息接收邮箱"].dropna())


symbol_d,QMA1,QMA2,QMA3,_CurrentBar,price_list=[],[],[],[],[],[]
TotalNumber=len(symbol_Id)    
UPStatus,DWStatus=[0]*TotalNumber,[0]*TotalNumber     
BKStatus,SKStatus,BPStatus,SPStatus=[0]*TotalNumber,[0]*TotalNumber,[0]*TotalNumber,[0]*TotalNumber

k_btime,k_cycle,SetDisplayNo,BackTestWarning,wav_path=0,0,0,None,None
SubDataLength,AluSubDataLength,setNo,display_code=0,0,0,0
all_contracts, parsed_formulas, contract_prices, last_arb_prices=None,None,None,None

CONTRACT_PATTERN = re.compile(r'(?:ZCE\|[A-Z]\|[A-Z]+\|\d{3}|(?:DCE|SHFE|INE|CFFEX|GFEX)\|[A-Z]\|[A-Z]+\|\d{4})')

# 全局变量存储邮件配置
EMAIL_CONFIG = {
    "sender": None,
    "receivers": None,
    "smtp_server": None,
    "smtp_port": None,
    "username": None,
    "password": None,
    "enabled": False
}

def initialize(context): 
    global g_params,k_btime,k_cycle,SetDisplayNo,BackTestWarning,wav_path
    global SubDataLength,AluSubDataLength,setNo,display_code
    k_btime = g_params['K线基础时间'] # k线基础时间取参数
    k_cycle = g_params['K线基础周期'] # k线基础周期取参数
    SetDisplayNo = g_params['选择显示合约序号']-1 # 选择显示合约序号
    BackTestWarning = g_params['回测阶段预警'] # 回测阶段预警
    wav_path = trade_order_filedir+"\\预警粤语.wav" if g_params['选择预警声音']==1 else trade_order_filedir+"\\预警普通话.wav"
    SubDataLength = g_params['订阅数据长度']  # 订阅数据长度
    AluSubDataLength = min(2000,SubDataLength)  # 计算数据长度  

    for i in range(len(symbol_Id)):
        Qlenset=max(int(均线一[i]),int(均线二[i]),int(均线三[i]))
        symbol_d.append(copy.deepcopy(deque([],maxlen=Qlenset)))
        QMA1.append(copy.deepcopy(deque([],maxlen=Qlenset)))
        QMA2.append(copy.deepcopy(deque([],maxlen=Qlenset)))
        QMA3.append(copy.deepcopy(deque([],maxlen=Qlenset)))
        _CurrentBar.append(copy.deepcopy(deque([],maxlen=Qlenset)))
        price_list.append(copy.deepcopy(deque([],maxlen=Qlenset)))
    # 解析所有合约并预处理公式
    global all_contracts, parsed_formulas, contract_prices, last_arb_prices
    all_contracts, parsed_formulas = parse_arbitrage_contracts(symbol_Id, CONTRACT_PATTERN)
    
    LogInfo(f"识别到的所有合约: {all_contracts}")
    setNo=min(SetDisplayNo,len(symbol_Id)-1) 
    display_code = get_first_contract(symbol_Id[setNo], CONTRACT_PATTERN)
    LogInfo(f"套利组合 {symbol_Id[setNo]} 的主合约是 {display_code}")

    SetBarInterval(display_code, k_btime, k_cycle,SubDataLength,AluSubDataLength) #订阅交易合约
    # 初始化合约价格字典和上次计算的套利价格
    contract_prices = {contract: np.nan for contract in all_contracts}
    last_arb_prices = [np.nan] * len(symbol_Id)
    
    # 订阅行情
    for contract in all_contracts:
        if contract==display_code:
            LogInfo(f'跳过已订阅的图表显示合约 {contract}')
            continue
        LogInfo(f'遍历订阅合约==> {contract} ')
        SetBarInterval(contract, k_btime, k_cycle,SubDataLength,AluSubDataLength) #订阅交易合约
    LogInfo("信息发送邮箱==>",信息发送邮箱,"邮箱SMTP地址==>",邮箱SMTP地址,"邮箱端口==>",邮箱端口,"邮箱用户名==>",邮箱用户名,"信息接收邮箱==>",信息接收邮箱)    
    SetTriggerType(1)
    SetTriggerType(5)
    SetOrderWay(1)
    SetActual()     
        
    # 初始化时设置邮件
    setup_email(
        sender=信息发送邮箱,
        receivers=信息接收邮箱,
        smtp_server=邮箱SMTP地址,
        smtp_port=int(邮箱端口),
        username=邮箱用户名,
        password=邮箱授权码,
        enabled=邮件发送=="开"
    )

def handle_data(context):
    for i in range(len(symbol_Id)):
        HTS=1 if context.strategyStatus()=="C" else 0
        current_formula = symbol_Id[i]
        contracts_in_formula = CONTRACT_PATTERN.findall(current_formula)
        
        # 只取最新价，避免广播问题
        for contract in contracts_in_formula:
            close_arr = Close(contract, k_btime, k_cycle)
            if len(close_arr)>0:
                contract_prices[contract] = close_arr[-1]  # 只取最后一个收盘价
        arb_price = calculate_arbitrage_price(parsed_formulas[i], contract_prices)
        last_arb_prices[i] = arb_price
        # LogInfo(f"当前套利组合 {current_formula} 的价格是 {arb_price}")
        ALU_code=get_first_contract(symbol_Id[i], CONTRACT_PATTERN)
        ALU_CurrentBar=CurrentBar(ALU_code, k_btime, k_cycle)
        _CurrentBar[i].append(ALU_CurrentBar)
        if len(_CurrentBar[i])<2:
            continue
        K_EndTrigger = _CurrentBar[i][0]>0 and _CurrentBar[i][-2]<_CurrentBar[i][-1]
        if K_EndTrigger:
            price_list[i].append(arb_price)
            npprice_list=np.array(price_list[i])
            len1=int(均线一[i])
            len2=int(均线二[i])
            len3=int(均线三[i])
            Qlenset=max(len1,len2,len3)
            if len(npprice_list)>=Qlenset:
                # 添加检查确保npprice_list不全是NAN值
                if not np.isnan(npprice_list).all():
                    QMA1[i].append(MA(npprice_list,len1)[-1])
                    QMA2[i].append(MA(npprice_list,len2)[-1])
                    QMA3[i].append(MA(npprice_list,len3)[-1])
                else:
                    # LogInfo(f"警告：第{i+1}组{symbol_Id[i]}的均线数据全为NAN，跳过MA计算")
                    QMA1[i].append(np.nan)
                    QMA2[i].append(np.nan)
                    QMA3[i].append(np.nan)
        if (HTS==1 or BackTestWarning=="开") and len(QMA1[i])>1 and not np.isnan(QMA1[i][-1]):
            # LogInfo(f"当前套利组合 {current_formula} 的均线一 {QMA1[i][-1]}, 均线二 {QMA2[i][-1]}, 均线三 {QMA3[i][-1]}")
            if UPStatus[i]==0 and QMA1[i][-1]+正式预警上突破点数[i]>arb_price>QMA1[i][-1]+初预警上突破点数[i] :
                UPStatus[i]=1
                EmailTitle=f"初预警自组合套利合约({symbol_Id[i]})价格{arb_price}上突破"
                send_info=f"初预警在{Date()}日{Time()}分,第{i+1}组自组合套利合约({symbol_Id[i]})价格{arb_price}上突破均线一==>{QMA1[i][-1]}，发送"
                LogInfo(send_info)
                play_wav(wav_path)
                send_email(EmailTitle,send_info)
            elif UPStatus[i]<2 and arb_price>QMA1[i][-1]+正式预警上突破点数[i]:
                UPStatus[i]=2
                EmailTitle=f"正式预警自组合套利合约({symbol_Id[i]})价格{arb_price}上突破"
                send_info=f"正式预警在{Date()}日{Time()}分,第{i+1}组自组合套利合约({symbol_Id[i]})价格{arb_price}上突破均线一==>{QMA1[i][-1]}，发送"
                LogInfo(send_info)
                play_wav(wav_path)
                send_email(EmailTitle,send_info)
            if DWStatus[i]==0 and QMA1[i][-1]-正式预警下突破点数[i]<arb_price<QMA1[i][-1]-初预警下突破点数[i] :
                DWStatus[i]=1
                EmailTitle=f"初预警自组合套利合约({symbol_Id[i]})价格{arb_price}下突破"
                send_info=f"初预警在{Date()}日{Time()}分,第{i+1}组自组合套利合约({symbol_Id[i]})价格{arb_price}下突破均线一==>{QMA1[i][-1]}，发送"
                LogInfo(send_info)
                play_wav(wav_path)
                send_email(EmailTitle,send_info)
            elif DWStatus[i]<2 and arb_price<QMA1[i][-1]-正式预警下突破点数[i]:
                DWStatus[i]=2
                EmailTitle=f"正式预警自组合套利合约({symbol_Id[i]})价格{arb_price}下突破"
                send_info=f"正式预警在{Date()}日{Time()}分,第{i+1}组自组合套利合约({symbol_Id[i]})价格{arb_price}下突破均线一==>{QMA1[i][-1]}，发送"
                LogInfo(send_info)
                play_wav(wav_path)
                send_email(EmailTitle,send_info)
            # if UPStatus[i]==1 and arb_price<=QMA1[i][-1]+初预警上突破点数[i]:
            #     UPStatus[i]=0
            # if DWStatus[i]==1 and arb_price>=QMA1[i][-1]-初预警下突破点数[i]:
            #     DWStatus[i]=0
            # if UPStatus[i]==2 and arb_price<=QMA1[i][-1]+正式预警上突破点数[i]:
            #     UPStatus[i]=1
            # if DWStatus[i]==2 and arb_price>=QMA1[i][-1]-正式预警下突破点数[i]:
            #     DWStatus[i]=1

        if setNo==i and len(QMA1[i])>1:    
            Disptxt = '第'+str(setNo+1)+"组自组合套利合约"
            len1=int(均线一[i])
            len2=int(均线二[i])
            len3=int(均线三[i])
            PlotBar('组合K线',arb_price,price_list[i][-2],RGB_Red() if arb_price>price_list[i][-2] else RGB_Green(), False, True,0,Disptxt)
            PlotNumeric(f'均线一({len1})', QMA1[i][-1], 0xffffff  , False, False,0,Disptxt)
            PlotNumeric(f'均线二({len2})', QMA2[i][-1], 0xffff00  , False, False,0,Disptxt)
            PlotNumeric(f'均线三({len3})', QMA3[i][-1], 0x00ffff  , False, False,0,Disptxt)

                    
def parse_arbitrage_contracts(arbitrage_formulas, contract_pattern=None):
    if contract_pattern is None:
        contract_pattern = CONTRACT_PATTERN
    unique_contracts = set() 
    parsed_formulas = []
    for formula in arbitrage_formulas:
        contracts = contract_pattern.findall(formula)
        for contract in contracts:
            unique_contracts.add(contract)
        parsed_formula = preprocess_formula(formula, contract_pattern)
        parsed_formulas.append(parsed_formula)
    return list(unique_contracts), parsed_formulas

def preprocess_formula(formula, contract_pattern=None):
    if contract_pattern is None:
        contract_pattern = CONTRACT_PATTERN
    # 标准化公式，在操作符两边添加空格以便分割
    formula = re.sub(r'([+\-*/()])', r' \1 ', formula)
    
    # 分割成标记，保留合约代码的完整性
    tokens = []
    i = 0
    formula_len = len(formula)
    
    while i < formula_len:
        # 检查当前位置是否是合约代码的开始
        match = None
        for j in range(i, formula_len):
            possible_contract = formula[i:j+1]
            if contract_pattern.fullmatch(possible_contract):
                match = possible_contract
        
        if match:
            tokens.append(match)
            i += len(match)
        else:
            # 不是合约代码，按空格分割
            next_space = formula.find(' ', i)
            if next_space == -1:
                tokens.append(formula[i:])
                break
            if next_space > i:
                token = formula[i:next_space].strip()
                if token:
                    tokens.append(token)
            i = next_space + 1
    
    # 处理括号优先级，转换为逆波兰表达式
    output_queue = []
    operator_stack = []
    
    precedence = {'+': 1, '-': 1, '*': 2, '/': 2}
    
    for token in tokens:
        if contract_pattern.fullmatch(token):
            # 合约代码
            output_queue.append(('contract', token))
        elif token.isdigit() or (token[0] == '-' and token[1:].isdigit()):
            # 数字
            output_queue.append(('number', float(token)))
        elif token in ['+', '-', '*', '/']:
            # 操作符
            while (operator_stack and operator_stack[-1] != '(' and 
                   precedence.get(operator_stack[-1], 0) >= precedence.get(token, 0)):
                output_queue.append(('op', operator_stack.pop()))
            operator_stack.append(token)
        elif token == '(':
            operator_stack.append(token)
        elif token == ')':
            while operator_stack and operator_stack[-1] != '(':
                output_queue.append(('op', operator_stack.pop()))
            if operator_stack and operator_stack[-1] == '(':
                operator_stack.pop()  # 弹出左括号
    
    # 处理剩余的操作符
    while operator_stack:
        output_queue.append(('op', operator_stack.pop()))
    
    return output_queue

def get_first_contract(arbitrage_formula, contract_pattern=None):
    if contract_pattern is None:
        contract_pattern = CONTRACT_PATTERN
    match = contract_pattern.search(arbitrage_formula)
    if match:
        return match.group(0)
    return None

# 为所有套利组合获取第一个合约的辅助函数
def get_all_first_contracts(arbitrage_formulas, contract_pattern=None):
    if contract_pattern is None:
        contract_pattern = CONTRACT_PATTERN
    return [get_first_contract(formula, contract_pattern) for formula in arbitrage_formulas]
def calculate_arbitrage_price(parsed_formula, contract_prices):
    """
    使用预处理的公式高效计算套利组合价格
    
    参数:
        parsed_formula: 预处理后的公式结构
        contract_prices: 合约价格字典，键为合约代码，值为价格
    
    返回:
        price: 组合价格
    """
    stack = []
    
    for token_type, token in parsed_formula:
        if token_type == 'contract':
            if token in contract_prices:
                stack.append(contract_prices[token])
            else:
                # 如果找不到价格，使用NaN
                stack.append(np.nan)
        elif token_type == 'number':
            stack.append(token)
        elif token_type == 'op':
            if len(stack) < 2:
                # 处理单目运算符如负号
                if token == '-' and len(stack) == 1:
                    operand = stack.pop()
                    stack.append(-operand)
                continue
                
            right_operand = stack.pop()
            left_operand = stack.pop()
            
            if token == '+':
                stack.append(left_operand + right_operand)
            elif token == '-':
                stack.append(left_operand - right_operand)
            elif token == '*':
                stack.append(left_operand * right_operand)
            elif token == '/':
                # 处理除以零的情况
                if right_operand == 0:
                    stack.append(np.nan)
                else:
                    stack.append(left_operand / right_operand)
    
    return stack[0] if stack else np.nan

# 批量计算多个套利组合价格的函数，适用于tick更新
def calculate_all_arbitrage_prices(parsed_formulas, contract_prices):
    """
    批量计算所有套利组合的价格
    
    参数:
        parsed_formulas: 预处理后的公式结构列表
        contract_prices: 合约价格字典
    
    返回:
        prices: 所有套利组合的价格列表
    """
    return [calculate_arbitrage_price(formula, contract_prices) for formula in parsed_formulas]

# 处理Tick数据更新的函数
def handle_tick_update(tick_data, parsed_formulas, contract_prices, last_prices):
    """
    处理Tick数据更新并计算受影响的套利组合价格
    
    参数:
        tick_data: 包含更新合约代码和价格的字典
        parsed_formulas: 预处理后的公式结构列表
        contract_prices: 当前所有合约的价格字典
        last_prices: 上一次计算的所有套利组合价格
    
    返回:
        updated_prices: 更新后的套利组合价格列表
        affected_indices: 受影响的套利组合索引列表
    """
    # 更新合约价格
    for contract, price in tick_data.items():
        contract_prices[contract] = price
    
    # 找出受影响的套利组合
    affected_indices = []
    updated_prices = list(last_prices)  # 复制上次的价格列表
    
    # 构建合约到套利组合的映射
    contract_to_formulas = defaultdict(list)
    for i, formula in enumerate(parsed_formulas):
        for token_type, token in formula:
            if token_type == 'contract' and token in tick_data:
                contract_to_formulas[token].append(i)
    
    # 只重新计算受影响的套利组合
    affected_indices = set()
    for contract in tick_data:
        affected_indices.update(contract_to_formulas[contract])
    
    for idx in affected_indices:
        updated_prices[idx] = calculate_arbitrage_price(parsed_formulas[idx], contract_prices)
    
    return updated_prices, list(affected_indices)

def setup_email(sender, receivers, smtp_server, smtp_port, username, password, enabled):
    """
    设置邮件发送参数
    """
    try:
        EMAIL_CONFIG["sender"] = sender
        EMAIL_CONFIG["receivers"] = receivers if isinstance(receivers, list) else [receivers]
        EMAIL_CONFIG["smtp_server"] = smtp_server
        EMAIL_CONFIG["smtp_port"] = smtp_port
        EMAIL_CONFIG["username"] = username
        EMAIL_CONFIG["password"] = password
        EMAIL_CONFIG["enabled"] = enabled
        LogInfo("邮件设置成功")
        return True
    except Exception as e:
        LogInfo(f"邮件设置异常: {str(e)}")
        EMAIL_CONFIG["enabled"] = False
        return False

def send_email(subject, content):
    """
    发送邮件
    """
    if not EMAIL_CONFIG.get("enabled", False):
        LogInfo("邮件功能未启用，无法发送邮件")
        return False
    try:
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        from email.header import Header

        message = MIMEMultipart()
        message['From'] = EMAIL_CONFIG["sender"]
        message['To'] = ','.join(EMAIL_CONFIG["receivers"])
        message['Subject'] = Header(subject, 'utf-8')
        message.attach(MIMEText(content, 'plain', 'utf-8'))

        if EMAIL_CONFIG["smtp_port"] == 465:
            smtp = smtplib.SMTP_SSL(EMAIL_CONFIG["smtp_server"], EMAIL_CONFIG["smtp_port"])
        else:
            smtp = smtplib.SMTP(EMAIL_CONFIG["smtp_server"], EMAIL_CONFIG["smtp_port"])

        smtp.login(EMAIL_CONFIG["username"], EMAIL_CONFIG["password"])
        smtp.sendmail(EMAIL_CONFIG["sender"], EMAIL_CONFIG["receivers"], message.as_string())
        smtp.quit()
        LogInfo("邮件发送成功")
        return True
    except Exception as e:
        LogInfo(f"邮件发送异常: {str(e)}")
        return False

def play_wav(wav_path="alert.wav"):
    """
    播放wav音频文件（Windows平台）
    参数:
        wav_path: wav文件路径，默认为当前目录下alert.wav
    """
    try:
        import winsound
        winsound.PlaySound(wav_path, winsound.SND_FILENAME | winsound.SND_ASYNC)
        LogInfo(f"已播放音频提示: {wav_path}")
    except Exception as e:
        LogInfo(f"音频播放失败: {str(e)}")