"""
风险控制功能测试脚本
测试当突破网格数达到加仓次数上限时的处理逻辑
"""

def test_risk_control_logic():
    """模拟风险控制逻辑的测试"""
    print("=== 风险控制逻辑测试 ===\n")
    
    # 模拟配置参数
    加仓次数上限 = 3
    
    # 模拟网格突破场景
    test_scenarios = [
        {"cur_up": 1, "direction": "上突破", "expected": "正常开仓"},
        {"cur_up": 2, "direction": "上突破", "expected": "正常开仓"}, 
        {"cur_up": 3, "direction": "上突破", "expected": "触发风险控制"},
        {"cur_up": 4, "direction": "上突破", "expected": "触发风险控制"},
        {"cur_dw": 1, "direction": "下突破", "expected": "正常开仓"},
        {"cur_dw": 2, "direction": "下突破", "expected": "正常开仓"},
        {"cur_dw": 3, "direction": "下突破", "expected": "触发风险控制"},
        {"cur_dw": 5, "direction": "下突破", "expected": "触发风险控制"},
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"测试场景 {i}:")
        
        if "cur_up" in scenario:
            cur_value = scenario["cur_up"]
            direction_type = "上突破"
            trade_direction = "BUY"
        else:
            cur_value = scenario["cur_dw"] 
            direction_type = "下突破"
            trade_direction = "SELL"
            
        print(f"  当前{direction_type}网格数: {cur_value}")
        print(f"  加仓次数上限: {加仓次数上限}")
        
        # 模拟风险控制判断
        if cur_value >= 加仓次数上限:
            print(f"  → 【风险控制】{direction_type}达到加仓次数上限，执行减仓操作")
            print(f"  → 播放报警音频")
            print(f"  → 平掉最早的{trade_direction}方向套利组合") 
            print(f"  → 使用市价单强制平仓")
        else:
            print(f"  → 正常执行{trade_direction}方向开仓")
            if cur_value == 1:
                print(f"  → 播放第一格突破提醒音频")
        
        expected = scenario["expected"]
        actual = "触发风险控制" if cur_value >= 加仓次数上限 else "正常开仓"
        status = "✓ 通过" if expected == actual else "✗ 失败"
        print(f"  预期结果: {expected}")
        print(f"  实际结果: {actual}")
        print(f"  测试状态: {status}")
        print("-" * 50)

def test_close_oldest_order_logic():
    """测试平仓最早订单的逻辑"""
    print("\n=== 平仓最早订单逻辑测试 ===\n")
    
    # 模拟套利组合订单
    mock_orders = {
        "ARB_0_BUY_1703123456": {"direction": "BUY", "submit_time": 1703123456, "status": "COMPLETED"},
        "ARB_0_BUY_1703123500": {"direction": "BUY", "submit_time": 1703123500, "status": "COMPLETED"},
        "ARB_0_SELL_1703123480": {"direction": "SELL", "submit_time": 1703123480, "status": "COMPLETED"},
        "ARB_0_BUY_1703123520": {"direction": "BUY", "submit_time": 1703123520, "status": "COMPLETED"},
        "ARB_0_SELL_1703123510": {"direction": "SELL", "submit_time": 1703123510, "status": "CLOSED"},  # 已关闭
    }
    
    print("当前套利组合订单:")
    for arb_id, order in mock_orders.items():
        print(f"  {arb_id}: {order['direction']}, 提交时间={order['submit_time']}, 状态={order['status']}")
    
    # 测试找出BUY方向的最早订单
    print(f"\n测试: 风险控制需要平掉BUY方向的最早订单")
    
    setNo = 0
    direction = "BUY"
    prefix = f"ARB_{setNo}_"
    
    # 找出指定方向且未关闭的套利组合
    eligible_orders = []
    for arb_id, arb_order in mock_orders.items():
        if (arb_order['direction'] == direction and 
            arb_id.startswith(prefix) and 
            arb_order['status'] not in ['CLOSED', 'LAME_LEG_CLOSED']):
            eligible_orders.append((arb_id, arb_order['submit_time']))
    
    print(f"符合条件的{direction}方向订单:")
    for arb_id, submit_time in eligible_orders:
        print(f"  {arb_id}: 提交时间={submit_time}")
    
    if eligible_orders:
        # 按提交时间排序，找出最早的
        eligible_orders.sort(key=lambda x: x[1])
        oldest_arb_id = eligible_orders[0][0]
        print(f"\n最早的订单: {oldest_arb_id}")
        print(f"将被平仓的订单: {oldest_arb_id} (提交时间: {eligible_orders[0][1]})")
    else:
        print(f"\n未找到可平仓的{direction}方向套利组合")
    
    print("-" * 50)

if __name__ == "__main__":
    test_risk_control_logic()
    test_close_oldest_order_logic()
    print("\n测试完成!") 