"""
网格级别精确平仓逻辑测试脚本
测试新的平仓机制：只有当价格回到最后突破线的上一格之内时，才平掉最后突破线对应的订单
"""

def test_precise_close_logic():
    """测试精确平仓逻辑"""
    print("=== 网格级别精确平仓逻辑测试 ===\n")
    
    # 模拟突破网格点数
    突破网格点数 = 10.0
    QMA1 = 100.0  # 基准价格
    
    # 模拟开仓场景
    print("## 开仓阶段")
    print(f"基准价格(均线): {QMA1}")
    print(f"突破网格点数: {突破网格点数}")
    print()
    
    # 模拟价格上涨突破过程
    opening_scenarios = [
        {"price": 110.5, "expected_grid": 1, "action": "开多仓"},
        {"price": 121.2, "expected_grid": 2, "action": "开多仓"}, 
        {"price": 132.8, "expected_grid": 3, "action": "开多仓"},
    ]
    
    opened_orders = []  # 记录已开仓的订单
    LastValidUP = 0
    
    for scenario in opening_scenarios:
        price = scenario["price"]
        cur_up = int((price - QMA1) // 突破网格点数)
        
        if cur_up > LastValidUP:
            # 有效突破，开仓
            grid_level = cur_up
            order_id = f"ARB_0_BUY_grid{grid_level}"
            opened_orders.append({
                'id': order_id,
                'grid_level': grid_level,
                'direction': 'BUY',
                'status': 'OPEN'
            })
            print(f"价格 {price:.1f} → 突破网格{cur_up} → 开多仓 (订单ID: {order_id})")
            LastValidUP = cur_up
        else:
            print(f"价格 {price:.1f} → 网格{cur_up} → 无效突破，不开仓")
    
    print(f"\n当前已开仓订单:")
    for order in opened_orders:
        print(f"  {order['id']}: 网格{order['grid_level']}, 状态: {order['status']}")
    print(f"当前LastValidUP: {LastValidUP}")
    print()
    
    # 模拟价格回落平仓过程
    print("## 平仓阶段")
    
    closing_scenarios = [
        {"price": 128.5, "description": "轻微回落"},
        {"price": 125.0, "description": "回落到网格2范围"},
        {"price": 119.8, "description": "回落到网格1范围"},
        {"price": 115.0, "description": "继续回落到网格1范围"},
        {"price": 108.5, "description": "回落到网格0范围"},
        {"price": 102.0, "description": "继续回落到网格0范围"},
    ]
    
    for scenario in closing_scenarios:
        price = scenario["price"]
        description = scenario["description"]
        cur_up = max(0, int((price - QMA1) // 突破网格点数))
        
        print(f"\n当前价格: {price:.1f} ({description})")
        print(f"当前网格: {cur_up}, LastValidUP: {LastValidUP}")
        
        # 新的平仓逻辑：只有当价格回到最后突破线的上一格之内时，才平掉最后突破线对应的订单
        if cur_up <= LastValidUP - 1 and LastValidUP > 0:
            # 找出需要平仓的订单（网格级别 = LastValidUP）
            orders_to_close = [order for order in opened_orders 
                             if order['grid_level'] == LastValidUP and order['status'] == 'OPEN']
            
            if orders_to_close:
                for order in orders_to_close:
                    print(f"  → 平仓订单: {order['id']} (网格{order['grid_level']})")
                    order['status'] = 'CLOSED'
                
                # 更新LastValidUP
                LastValidUP = cur_up
                print(f"  → 更新LastValidUP为: {LastValidUP}")
            else:
                print(f"  → 无对应网格{LastValidUP}的开仓订单需要平仓")
        else:
            print(f"  → 未满足平仓条件 (cur_up={cur_up} > LastValidUP-1={LastValidUP-1})")
        
        # 显示当前订单状态
        open_orders = [order for order in opened_orders if order['status'] == 'OPEN']
        if open_orders:
            print(f"  剩余开仓订单:")
            for order in open_orders:
                print(f"    {order['id']}: 网格{order['grid_level']}")
        else:
            print(f"  所有订单已平仓")

def test_comparison_old_vs_new():
    """对比旧逻辑与新逻辑的差异"""
    print("\n\n=== 旧逻辑 vs 新逻辑对比 ===\n")
    
    QMA1 = 100.0
    突破网格点数 = 10.0
    
    print("假设已在网格1、2、3都开了多仓")
    print("当前价格在网格3，开始回落...")
    print()
    
    test_prices = [132.0, 125.0, 118.0, 115.0, 108.0]
    
    for price in test_prices:
        cur_up = max(0, int((price - QMA1) // 突破网格点数))
        
        print(f"价格: {price:.1f}, 当前网格: {cur_up}")
        
        # 旧逻辑（有问题的）
        print(f"  旧逻辑: 只要 cur_up < UPBreakout，就平掉所有多单")
        print(f"  → 会立即平掉网格1、2、3的所有订单")
        
        # 新逻辑（正确的）
        if cur_up == 3:
            print(f"  新逻辑: cur_up(3) = LastValidUP(3)，无平仓")
        elif cur_up == 2:
            print(f"  新逻辑: cur_up(2) <= LastValidUP-1(2)，平掉网格3的订单")
            print(f"  → 只平掉网格3的订单，保留网格1、2的订单")
        elif cur_up == 1:
            print(f"  新逻辑: cur_up(1) <= LastValidUP-1(1)，平掉网格2的订单") 
            print(f"  → 只平掉网格2的订单，保留网格1的订单")
        elif cur_up == 0:
            print(f"  新逻辑: cur_up(0) <= LastValidUP-1(0)，平掉网格1的订单")
            print(f"  → 平掉网格1的订单，所有订单平仓完毕")
        
        print()

if __name__ == "__main__":
    test_precise_close_logic()
    test_comparison_old_vs_new()
    print("测试完成!") 