# -*- coding: utf-8 -*-
"""
Alpha-Tick Pro RB 螺纹钢高频策略 - 机智量化版高级订单跟踪版
集成优化的交易封装类，支持订单全寿命周期跟踪
"""

import numpy as np
import pandas as pd
import time
from datetime import datetime, timedelta

# 导入优化的交易封装类
# 注意：在实际使用时，需要将AdvancedTradingManager类的代码包含在此文件中
# 或者通过import导入

# ==================== 策略参数配置 ====================
g_params['大单标准差倍数'] = 3.2
g_params['订单流分析周期'] = 15
g_params['基础突破系数'] = 0.28
g_params['波动率调整系数'] = 0.12
g_params['ATR计算周期'] = 14
g_params['单笔最大风险'] = 0.02
g_params['单日止损线'] = -0.03
g_params['最低流动性阈值'] = 80
g_params['合约乘数'] = 10
g_params['最小价格变动'] = 1
g_params['最大持仓时间'] = 180
g_params['交易间隔'] = 3000
g_params['K线基础时间'] = 'T'
g_params['K线基础周期'] = 1
g_params['订阅数据长度'] = 2000

# 交易管理器配置
g_params['开仓超价跳数'] = 2
g_params['平仓超价跳数'] = 1
g_params['订单超时时间'] = 300  # 5分钟
g_params['最大重试次数'] = 2

# ==================== 全局变量 ====================
# 核心状态变量
big_vol = 0
trading_paused = False
daily_high = 0
daily_low = 0
pre_close = 0
observe_sell_price = 0
observe_buy_price = 0
cumulative_delta = 0
ema_delta = 0
buy_market_vol = 0
sell_market_vol = 0
position_size = 1
volatility_ratio = 0
dynamic_k = 0
last_trade_time = 0
loss_streak = 0
daily_max_profit = 0
account_equity = 0
trend_confidence = 0
flow_strength = 0

# 基准合约
基准合约 = 'SHFE|F|RB|MAIN'

# 交易管理器实例
trading_manager = None

# ==================== 交易封装类（简化版，完整版见优化交易封装类_订单跟踪版.py） ====================

class AdvancedTradingManager:
    """高级交易管理器 - 简化版"""
    
    def __init__(self, contract_code='SHFE|F|RB|MAIN', 
                 entry_tick_offset=2, exit_tick_offset=1,
                 order_timeout=300, max_retry_count=3):
        self.contract_code = contract_code
        self.entry_tick_offset = entry_tick_offset
        self.exit_tick_offset = exit_tick_offset
        self.order_timeout = order_timeout
        self.max_retry_count = max_retry_count
        
        # 交易状态管理
        self.BKS = 0  # 多头开仓状态
        self.SKS = 0  # 空头开仓状态
        self.BPS = 0  # 多头平仓状态
        self.SPS = 0  # 空头平仓状态
        
        # 订单跟踪字典
        self.pending_entry_orders = {}
        self.pending_exit_orders = {}
        
        # 统计信息
        self.stats = {
            'total_orders': 0,
            'successful_orders': 0,
            'failed_orders': 0,
            'timeout_orders': 0
        }
        
        LogInfo(f"交易管理器初始化完成 - 合约: {contract_code}")
    
    def reset_trading_status(self):
        """重置交易状态"""
        self.BKS = 0
        self.SKS = 0
        self.BPS = 0
        self.SPS = 0
        LogInfo("交易状态已重置")
    
    def is_real_time(self):
        """判断是否为实时阶段"""
        try:
            return Q_UpdateTime() is not None
        except:
            return False
    
    def get_safe_price(self, base_price, tick_offset, direction, tcode):
        """获取安全的交易价格"""
        try:
            tick_size = PriceTick(tcode)
            
            if direction == 'BUY':
                target_price = base_price + tick_offset * tick_size
                upper_limit = Q_UpperLimit(tcode)
                safe_price = min(target_price, upper_limit) if upper_limit > 0 else target_price
            else:  # SELL
                target_price = base_price - tick_offset * tick_size
                lower_limit = Q_LowLimit(tcode)
                safe_price = max(target_price, lower_limit) if lower_limit > 0 else target_price
            
            return safe_price
            
        except Exception as e:
            LogError(f"计算安全价格异常: {str(e)}")
            return base_price
    
    def trigger_entry(self, BK, SK, qty, tcode=None):
        """统一开仓接口"""
        if tcode is None:
            tcode = self.contract_code
        
        if self.is_real_time():
            return self.tim_trigger_entry(BK, SK, qty, tcode)
        else:
            return self.his_trigger(BK, SK, qty, self.entry_tick_offset, tcode)
    
    def trigger_exit(self, BP, SP, clots, tcode=None):
        """统一平仓接口"""
        if tcode is None:
            tcode = self.contract_code
        
        if self.is_real_time():
            return self.tim_trigger_exit(BP, SP, clots, tcode)
        else:
            return self.his_trigger_Exit(BP, SP, self.exit_tick_offset, clots, tcode)
    
    def tim_trigger_entry(self, BK, SK, qty, tcode):
        """实时开仓"""
        order_results = []
        
        try:
            if BK and self.BKS == 0:
                ask_price = Q_AskPrice(tcode)
                if ask_price > 0:
                    entry_price = self.get_safe_price(ask_price, self.entry_tick_offset, 'BUY', tcode)
                    ret_code, order_id = A_SendOrder(Enum_Buy(), Enum_Entry(), qty, entry_price, tcode)
                    
                    if ret_code == 0 and order_id:
                        self.pending_entry_orders[order_id] = {
                            'direction': 'BUY',
                            'quantity': qty,
                            'price': entry_price,
                            'timestamp': time.time(),
                            'symbol': tcode
                        }
                        self.BKS = 1
                        self.stats['total_orders'] += 1
                        LogInfo(f"多头开仓订单已发送 - 订单号: {order_id}, 价格: {entry_price:.2f}")
                        order_results.append(('BUY_ENTRY', ret_code, order_id))
            
            if SK and self.SKS == 0:
                bid_price = Q_BidPrice(tcode)
                if bid_price > 0:
                    entry_price = self.get_safe_price(bid_price, self.entry_tick_offset, 'SELL', tcode)
                    ret_code, order_id = A_SendOrder(Enum_Sell(), Enum_Entry(), qty, entry_price, tcode)
                    
                    if ret_code == 0 and order_id:
                        self.pending_entry_orders[order_id] = {
                            'direction': 'SELL',
                            'quantity': qty,
                            'price': entry_price,
                            'timestamp': time.time(),
                            'symbol': tcode
                        }
                        self.SKS = 1
                        self.stats['total_orders'] += 1
                        LogInfo(f"空头开仓订单已发送 - 订单号: {order_id}, 价格: {entry_price:.2f}")
                        order_results.append(('SELL_ENTRY', ret_code, order_id))
            
            return order_results
            
        except Exception as e:
            LogError(f"实时开仓异常: {str(e)}")
            return order_results
    
    def tim_trigger_exit(self, BP, SP, clots, tcode):
        """实时平仓"""
        order_results = []
        
        try:
            if BP and self.BPS == 0:
                sell_position = A_SellPosition(tcode)
                if sell_position > 0:
                    actual_lots = min(clots, sell_position)
                    ask_price = Q_AskPrice(tcode)
                    exit_price = self.get_safe_price(ask_price, self.exit_tick_offset, 'BUY', tcode)
                    
                    ret_code, order_id = A_SendOrder(Enum_Buy(), Enum_Exit(), actual_lots, exit_price, tcode)
                    
                    if ret_code == 0 and order_id:
                        self.pending_exit_orders[order_id] = {
                            'direction': 'BUY',
                            'quantity': actual_lots,
                            'price': exit_price,
                            'timestamp': time.time(),
                            'symbol': tcode,
                            'original_signal': 'BP'
                        }
                        self.BPS = 1
                        LogInfo(f"空头平仓订单已发送 - 订单号: {order_id}")
                        order_results.append(('BUY_EXIT', ret_code, order_id))
            
            if SP and self.SPS == 0:
                buy_position = A_BuyPosition(tcode)
                if buy_position > 0:
                    actual_lots = min(clots, buy_position)
                    bid_price = Q_BidPrice(tcode)
                    exit_price = self.get_safe_price(bid_price, self.exit_tick_offset, 'SELL', tcode)
                    
                    ret_code, order_id = A_SendOrder(Enum_Sell(), Enum_Exit(), actual_lots, exit_price, tcode)
                    
                    if ret_code == 0 and order_id:
                        self.pending_exit_orders[order_id] = {
                            'direction': 'SELL',
                            'quantity': actual_lots,
                            'price': exit_price,
                            'timestamp': time.time(),
                            'symbol': tcode,
                            'original_signal': 'SP'
                        }
                        self.SPS = 1
                        LogInfo(f"多头平仓订单已发送 - 订单号: {order_id}")
                        order_results.append(('SELL_EXIT', ret_code, order_id))
            
            return order_results
            
        except Exception as e:
            LogError(f"实时平仓异常: {str(e)}")
            return order_results
    
    def his_trigger(self, BK, SK, qty, itk, tcode):
        """历史开仓"""
        try:
            if BK and self.BKS == 0:
                price = Close()[-1] + itk * PriceTick(tcode)
                Buy(qty, price)
                self.BKS = 1
                LogInfo(f"历史多头开仓 - 价格: {price:.2f}, 数量: {qty}")
                return True
                
            elif SK and self.SKS == 0:
                price = Close()[-1] - itk * PriceTick(tcode)
                SellShort(qty, price)
                self.SKS = 1
                LogInfo(f"历史空头开仓 - 价格: {price:.2f}, 数量: {qty}")
                return True
            
            return False
            
        except Exception as e:
            LogError(f"历史开仓异常: {str(e)}")
            return False
    
    def his_trigger_Exit(self, BP, SP, otk, clots, tcode):
        """历史平仓"""
        try:
            if BP and self.BPS == 0 and SellPosition(tcode) > 0:
                actual_lots = min(clots, SellPosition(tcode))
                price = Close()[-1] + otk * PriceTick(tcode)
                BuyToCover(actual_lots, price)
                self.BPS = 1
                if self.SKS == 1:
                    self.SKS = 2
                LogInfo(f"历史平空 - 价格: {price:.2f}, 数量: {actual_lots}")
                return True
                
            elif SP and self.SPS == 0 and BuyPosition(tcode) > 0:
                actual_lots = min(clots, BuyPosition(tcode))
                price = Close()[-1] - otk * PriceTick(tcode)
                Sell(actual_lots, price)
                self.SPS = 1
                if self.BKS == 1:
                    self.BKS = 2
                LogInfo(f"历史平多 - 价格: {price:.2f}, 数量: {actual_lots}")
                return True
            
            return False
            
        except Exception as e:
            LogError(f"历史平仓异常: {str(e)}")
            return False
    
    def check_and_update_orders(self):
        """检查和更新订单状态 - 核心功能"""
        try:
            current_time = time.time()
            
            # 检查开仓订单
            orders_to_remove = []
            for order_id, order_info in list(self.pending_entry_orders.items()):
                try:
                    if current_time - order_info['timestamp'] > self.order_timeout:
                        LogWarn(f"开仓订单超时: {order_id}")
                        self._handle_timeout_entry_order(order_id, order_info)
                        orders_to_remove.append(order_id)
                        continue
                    
                    order_status = A_OrderStatus(order_id)
                    if order_status == '6':  # 完全成交
                        LogInfo(f"开仓订单已成交: {order_id}")
                        self.stats['successful_orders'] += 1
                        orders_to_remove.append(order_id)
                    elif order_status in ['7', '9', 'F']:  # 已撤单、拒绝、错误
                        LogWarn(f"开仓订单失败: {order_id}, 状态: {order_status}")
                        self._handle_failed_entry_order(order_id, order_info)
                        orders_to_remove.append(order_id)
                        
                except Exception as e:
                    LogError(f"检查开仓订单 {order_id} 异常: {str(e)}")
            
            for order_id in orders_to_remove:
                self.pending_entry_orders.pop(order_id, None)
            
            # 检查平仓订单
            orders_to_remove = []
            for order_id, order_info in list(self.pending_exit_orders.items()):
                try:
                    if current_time - order_info['timestamp'] > self.order_timeout:
                        LogWarn(f"平仓订单超时: {order_id}")
                        self._handle_timeout_exit_order(order_id, order_info)
                        orders_to_remove.append(order_id)
                        continue
                    
                    order_status = A_OrderStatus(order_id)
                    if order_status == '6':  # 完全成交
                        LogInfo(f"平仓订单已成交: {order_id}")
                        self.stats['successful_orders'] += 1
                        orders_to_remove.append(order_id)
                    elif order_status in ['7', '9', 'F']:  # 已撤单、拒绝、错误
                        LogWarn(f"平仓订单失败: {order_id}, 状态: {order_status}")
                        self._handle_failed_exit_order(order_id, order_info)
                        orders_to_remove.append(order_id)
                        
                except Exception as e:
                    LogError(f"检查平仓订单 {order_id} 异常: {str(e)}")
            
            for order_id in orders_to_remove:
                self.pending_exit_orders.pop(order_id, None)
                
        except Exception as e:
            LogError(f"检查订单状态异常: {str(e)}")
    
    def _handle_timeout_entry_order(self, order_id, order_info):
        """处理超时开仓订单"""
        try:
            A_DeleteOrder(order_id)
            if order_info['direction'] == 'BUY':
                self.BKS = 0
            else:
                self.SKS = 0
            self.stats['timeout_orders'] += 1
        except:
            pass
    
    def _handle_timeout_exit_order(self, order_id, order_info):
        """处理超时平仓订单"""
        try:
            A_DeleteOrder(order_id)
            if order_info['original_signal'] == 'BP':
                self.BPS = 0
            else:
                self.SPS = 0
            self.stats['timeout_orders'] += 1
        except:
            pass
    
    def _handle_failed_entry_order(self, order_id, order_info):
        """处理失败开仓订单"""
        if order_info['direction'] == 'BUY':
            self.BKS = 0
        else:
            self.SKS = 0
        self.stats['failed_orders'] += 1
    
    def _handle_failed_exit_order(self, order_id, order_info):
        """处理失败平仓订单"""
        if order_info['original_signal'] == 'BP':
            self.BPS = 0
        else:
            self.SPS = 0
        self.stats['failed_orders'] += 1
    
    def get_trading_status(self):
        """获取交易状态"""
        return {
            'BKS': self.BKS,
            'SKS': self.SKS,
            'BPS': self.BPS,
            'SPS': self.SPS,
            'pending_entry': len(self.pending_entry_orders),
            'pending_exit': len(self.pending_exit_orders)
        }

# ==================== 策略初始化 ====================

def initialize(context):
    """策略初始化"""
    global account_equity, trading_manager
    
    LogInfo("=== Alpha-Tick Pro RB 螺纹钢高频策略初始化（高级订单跟踪版） ===")
    
    # 创建交易管理器
    trading_manager = AdvancedTradingManager(
        contract_code=基准合约,
        entry_tick_offset=g_params['开仓超价跳数'],
        exit_tick_offset=g_params['平仓超价跳数'],
        order_timeout=g_params['订单超时时间'],
        max_retry_count=g_params['最大重试次数']
    )
    
    # 订阅数据
    SetBarInterval(基准合约, g_params['K线基础时间'], g_params['K线基础周期'], g_params['订阅数据长度'])
    
    # 初始化账户信息
    try:
        account_equity = A_TotalAssets()
    except:
        account_equity = 1000000
    
    # 初始化变量
    init_daily_variables()
    
    LogInfo(f"策略初始化完成，基准合约: {基准合约}")
    LogInfo(f"账户总资产: {account_equity:.2f}")
    LogInfo(f"交易管理器配置 - 开仓超价:{g_params['开仓超价跳数']}跳, 平仓超价:{g_params['平仓超价跳数']}跳")

def init_daily_variables():
    """初始化日变量"""
    global daily_high, daily_low, pre_close, cumulative_delta
    global daily_max_profit, loss_streak, buy_market_vol, sell_market_vol
    
    try:
        close_prices = Close()
        if len(close_prices) > 1:
            pre_close = close_prices[-2]
            daily_high = High()[-1]
            daily_low = Low()[-1]
        else:
            pre_close = Close()[-1] if len(close_prices) > 0 else 0
            daily_high = High()[-1] if len(High()) > 0 else 0
            daily_low = Low()[-1] if len(Low()) > 0 else 0
    except:
        pre_close = 0
        daily_high = 0
        daily_low = 0
    
    cumulative_delta = 0
    daily_max_profit = 0
    loss_streak = 0
    buy_market_vol = 0
    sell_market_vol = 0
    
    # 重置交易状态
    if trading_manager:
        trading_manager.reset_trading_status()
    
    LogInfo(f"日变量初始化完成 - 前收盘: {pre_close:.1f}, 当日高: {daily_high:.1f}, 当日低: {daily_low:.1f}")
    LogInfo("交易状态已重置")

def handle_data(context):
    """主策略逻辑 - 集成高级订单跟踪"""
    global trading_manager

    try:
        # 1. 首先检查和更新订单状态（必须调用）
        if trading_manager:
            trading_manager.check_and_update_orders()

        # 2. 检查日期变化
        check_daily_reset()

        # 3. 更新账户信息
        update_account_info()

        # 4. 核心计算
        calculate_big_volume_threshold()
        analyze_order_flow()
        calculate_rbreaker_levels()
        calculate_trend_strength()

        # 5. 信号生成
        long_signal, short_signal = generate_trading_signals()

        # 6. 仓位计算
        calculate_position_size()

        # 7. 风险控制
        if risk_control_check():
            return

        # 8. 交易执行（使用高级交易管理器）
        if not trading_paused and trading_manager:
            execute_trading_signals_advanced(long_signal, short_signal)
            manage_existing_positions_advanced()

        # 9. 收盘检查
        check_market_close()

        # 10. 可视化
        update_visualization()

        # 11. 定期记录交易状态
        if CurrentBar() % 100 == 0:
            log_trading_status()

    except Exception as e:
        LogError(f"策略执行异常: {str(e)}")

def execute_trading_signals_advanced(long_signal, short_signal):
    """使用高级交易管理器执行交易信号"""
    global last_trade_time, trading_manager

    try:
        current_time_ms = int(time.time() * 1000)

        # 检查交易间隔
        if current_time_ms - last_trade_time < g_params['交易间隔']:
            return

        # 检查是否可以开仓
        status = trading_manager.get_trading_status()

        # 执行开仓信号
        if (long_signal and status['BKS'] == 0) or (short_signal and status['SKS'] == 0):
            results = trading_manager.trigger_entry(long_signal, short_signal, position_size)

            if results:
                LogInfo(f"开仓信号执行结果: {results}")
                last_trade_time = current_time_ms

                # 记录信号详情
                if long_signal:
                    LogInfo(f"【多头开仓信号】价格: {Close()[-1]:.1f}, 观察卖出价: {observe_sell_price:.1f}, "
                           f"订单流强度: {flow_strength:.1f}, 趋势置信度: {trend_confidence:.1f}")

                if short_signal:
                    LogInfo(f"【空头开仓信号】价格: {Close()[-1]:.1f}, 观察买入价: {observe_buy_price:.1f}, "
                           f"订单流强度: {flow_strength:.1f}, 趋势置信度: {trend_confidence:.1f}")

    except Exception as e:
        LogError(f"高级交易信号执行异常: {str(e)}")

def manage_existing_positions_advanced():
    """使用高级交易管理器管理现有持仓"""
    global trading_manager

    try:
        # 生成平仓信号
        exit_long, exit_short = generate_exit_signals()

        if exit_long or exit_short:
            # 获取当前持仓
            if trading_manager.is_real_time():
                long_position = A_BuyPosition(基准合约)
                short_position = A_SellPosition(基准合约)
            else:
                long_position = BuyPosition(基准合约)
                short_position = SellPosition(基准合约)

            # 执行平仓
            if exit_long and long_position > 0:
                results = trading_manager.trigger_exit(False, True, long_position)
                if results:
                    LogInfo(f"多头平仓信号执行: {results}")

            if exit_short and short_position > 0:
                results = trading_manager.trigger_exit(True, False, short_position)
                if results:
                    LogInfo(f"空头平仓信号执行: {results}")

    except Exception as e:
        LogError(f"高级持仓管理异常: {str(e)}")

def generate_exit_signals():
    """生成平仓信号"""
    try:
        close_prices = Close()
        current_close = close_prices[-1]

        # 多头平仓信号
        exit_long = False
        exit_score_long = 0

        # 短期趋势反转
        if len(close_prices) >= 5:
            ma5 = np.mean(close_prices[-5:])
            if current_close < ma5:
                exit_score_long += 30

        # 订单流反转
        if flow_strength > 20:
            exit_score_long += 25

        # 价格新低
        if len(close_prices) >= 3:
            lowest_3 = min(close_prices[-3:])
            if current_close < lowest_3:
                exit_score_long += 20

        # 趋势置信度转弱
        if trend_confidence < -30:
            exit_score_long += 15

        exit_long = exit_score_long >= 50

        # 空头平仓信号
        exit_short = False
        exit_score_short = 0

        # 短期趋势反转
        if len(close_prices) >= 5:
            ma5 = np.mean(close_prices[-5:])
            if current_close > ma5:
                exit_score_short += 30

        # 订单流反转
        if flow_strength < -20:
            exit_score_short += 25

        # 价格新高
        if len(close_prices) >= 3:
            highest_3 = max(close_prices[-3:])
            if current_close > highest_3:
                exit_score_short += 20

        # 趋势置信度转弱
        if trend_confidence > 30:
            exit_score_short += 15

        exit_short = exit_score_short >= 50

        return exit_long, exit_short

    except Exception as e:
        LogError(f"平仓信号生成异常: {str(e)}")
        return False, False

def log_trading_status():
    """记录交易状态"""
    global trading_manager

    try:
        if trading_manager:
            status = trading_manager.get_trading_status()
            stats = trading_manager.stats

            LogInfo(f"【交易状态】BKS:{status['BKS']} SKS:{status['SKS']} BPS:{status['BPS']} SPS:{status['SPS']}")
            LogInfo(f"【待处理订单】开仓:{status['pending_entry']} 平仓:{status['pending_exit']}")
            LogInfo(f"【订单统计】总计:{stats['total_orders']} 成功:{stats['successful_orders']} "
                   f"失败:{stats['failed_orders']} 超时:{stats['timeout_orders']}")

            # 持仓信息
            if trading_manager.is_real_time():
                long_pos = A_BuyPosition(基准合约)
                short_pos = A_SellPosition(基准合约)
            else:
                long_pos = BuyPosition(基准合约) if 'BuyPosition' in globals() else 0
                short_pos = SellPosition(基准合约) if 'SellPosition' in globals() else 0

            if long_pos > 0 or short_pos > 0:
                LogInfo(f"【当前持仓】多头:{long_pos}手 空头:{short_pos}手")

    except Exception as e:
        LogError(f"记录交易状态异常: {str(e)}")

# ==================== 核心计算函数（保持原有逻辑） ====================

def check_daily_reset():
    """检查日期重置"""
    try:
        current_date = Date()
        # 这里可以添加日期变化检测逻辑
    except:
        pass

def update_account_info():
    """更新账户信息"""
    global account_equity, daily_max_profit

    try:
        account_equity = A_TotalAssets()
        current_profit = A_FloatProfit()
        if current_profit > daily_max_profit:
            daily_max_profit = current_profit
    except:
        pass

def calculate_big_volume_threshold():
    """计算大单阈值"""
    global big_vol

    try:
        volumes = Vol()
        if len(volumes) < 10:
            big_vol = 100
            return

        recent_volumes = volumes[-30:]
        mean_vol = np.mean(recent_volumes)
        std_vol = np.std(recent_volumes)

        big_vol = mean_vol + g_params['大单标准差倍数'] * std_vol

    except Exception as e:
        big_vol = 100

def analyze_order_flow():
    """订单流分析"""
    global cumulative_delta, ema_delta, flow_strength

    try:
        if trading_manager and trading_manager.is_real_time():
            bid_vol1 = Q_BidVolume(0)
            ask_vol1 = Q_AskVolume(0)
        else:
            bid_vol1 = 50
            ask_vol1 = 50

        weighted_delta = bid_vol1 - ask_vol1
        cumulative_delta += weighted_delta

        alpha = 2.0 / (g_params['订单流分析周期'] + 1)
        ema_delta = alpha * weighted_delta + (1 - alpha) * ema_delta

        flow_strength = ema_delta

    except Exception as e:
        flow_strength = 0

def calculate_rbreaker_levels():
    """计算R-Breaker价位"""
    global observe_sell_price, observe_buy_price, dynamic_k, volatility_ratio

    try:
        if daily_high == 0 or daily_low == 0 or pre_close == 0:
            return

        close_prices = Close()
        if len(close_prices) >= 20:
            volatility_ratio = np.std(close_prices[-20:]) / np.mean(close_prices[-20:])
        else:
            volatility_ratio = 0.02

        dynamic_k = g_params['基础突破系数'] + g_params['波动率调整系数'] * volatility_ratio

        observe_sell_price = daily_high + dynamic_k * (pre_close - daily_low)
        observe_buy_price = daily_low - dynamic_k * (daily_high - pre_close)

    except Exception as e:
        pass

def calculate_trend_strength():
    """计算趋势强度"""
    global trend_confidence

    try:
        close_prices = Close()
        if len(close_prices) < 20:
            trend_confidence = 0
            return

        current_close = close_prices[-1]
        ma5 = np.mean(close_prices[-5:])
        ma20 = np.mean(close_prices[-20:])

        trend_confidence = 0
        if ma5 > ma20:
            trend_confidence += 30
        if current_close > ma20:
            trend_confidence += 20

        trend_confidence = min(100, max(-100, trend_confidence))

    except Exception as e:
        trend_confidence = 0

def generate_trading_signals():
    """生成交易信号"""
    try:
        current_close = Close()[-1]

        # 多头信号
        long_score = 0
        if current_close >= observe_sell_price and observe_sell_price > 0:
            long_score += 25
        if flow_strength < -20:
            long_score += 25
        if trend_confidence > 30:
            long_score += 20

        long_signal = long_score >= 50

        # 空头信号
        short_score = 0
        if current_close <= observe_buy_price and observe_buy_price > 0:
            short_score += 25
        if flow_strength > 20:
            short_score += 25
        if trend_confidence < -30:
            short_score += 20

        short_signal = short_score >= 50

        return long_signal, short_signal

    except Exception as e:
        return False, False

def calculate_position_size():
    """计算仓位大小"""
    global position_size

    try:
        if account_equity <= 0:
            position_size = 1
            return

        risk_unit = g_params['单笔最大风险'] * account_equity
        current_close = Close()[-1]

        position_size = max(1, min(10, int(risk_unit / (current_close * g_params['合约乘数'] * 0.02))))

    except Exception as e:
        position_size = 1

def risk_control_check():
    """风险控制检查"""
    global trading_paused, trading_manager

    try:
        current_profit_ratio = A_FloatProfit() / account_equity if account_equity > 0 else 0

        if current_profit_ratio < g_params['单日止损线']:
            # 强制平仓
            if trading_manager:
                trading_manager.force_close_all_positions()
            trading_paused = True
            LogWarn(f"【风控】单日最大损失触发: {current_profit_ratio:.2%}")
            return True

        return False

    except Exception as e:
        return False

def check_market_close():
    """检查收盘时间"""
    global trading_manager

    try:
        current_time = Time()
        if current_time >= 0.1455:  # 14:55
            if trading_manager:
                trading_manager.force_close_all_positions()
            LogInfo("【收盘平仓】强制平仓所有持仓")
    except Exception as e:
        pass

def update_visualization():
    """更新可视化"""
    try:
        if observe_sell_price > 0:
            PlotNumeric('观察卖出价', observe_sell_price, 0xFF0000, False)
        if observe_buy_price > 0:
            PlotNumeric('观察买入价', observe_buy_price, 0x0000FF, False)

        PlotBar('订单流强度', flow_strength, 0xFF0000 if flow_strength > 0 else 0x00FF00, False, True, 1)
        PlotBar('趋势置信度', trend_confidence, 0xFF0000 if trend_confidence > 0 else 0x0000FF, False, True, 2)

    except Exception as e:
        pass

# 策略信息
LogInfo("Alpha-Tick Pro RB 螺纹钢高频策略 - 机智量化版高级订单跟踪版")
LogInfo("集成优化的交易封装类，支持订单全寿命周期跟踪")
LogInfo("特性：类属性状态管理、订单跟踪字典、自动重试机制、超时处理")
