# 下载股票历史数据
股票历史数据支持的类型:
- 公用数据
    - 股票代码表
    - 股票交易日数据
- 个股数据
    - 个股,指数和ETF（50,300,500）历史日线数据,含如下指标
        - OHLCV
        - 成交额
        - 换手率
        - 复权因子
    - 个股和ETF（50,300,500）历史分笔数据
        - 腾讯和通达信好像不支持大盘指数的历史分笔,如果策略需要参考指数分笔数据,可以用ETF50,ETF300,ETF500代替 

# 自动更新方式
1. 手动下载某一历史**交易日**的日线数据,日期格式是yyyy-mm-dd。下载历史日线数据时,会同时下载股票代码表和对应的交易日数据。  
![image](https://github.com/moyuanz/DevilYuan/blob/master/docs/data/mannualDaysConfig.png)
2. 手动下载步骤1里的对应的**交易日**的历史分笔数据  
![image](https://github.com/moyuanz/DevilYuan/blob/master/docs/data/mannualTicksConfig.png)
3. 一键更新,自动更新历史日线和分笔数据到今天。如果今天是交易日，**请在18:30后更新**，因为数据源也需要数据落库。  
![image](https://github.com/moyuanz/DevilYuan/blob/master/docs/data/result.png)

**注意:一键更新会读取数据库最新的交易日数据,根据数据库里最新的日期,自动更新到今天。也就是锚是交易日最新日期。如果分笔数据的最新日期比交易日数据早,则一键更新会导致分笔数据的缺失。当然你可以用手动更新分笔数据,来下载缺失的数据。程序会根据数据库的数据自己判断是否要下载数据。**


# 手动更新方式
1. 手动更新日线数据,输入开始日期和结束日期。如果结束日期正好是今天,并且今天是交易日，**请在18:30后更新**，因为数据源也需要数据落库。
2. 手动更新分笔数据,输入开始日期和结束日期。分笔数据的时间段可以是日线数据时间段的子集,或者一样。因为每次下载分笔数据,程序会自动根据日线数据判断出个股有没有停牌,停牌日期无须下载个股分笔数据。但建议两者时间段是一样的,这样自己不会把数据搞错位了。

# 定时一键更新
启动此功能时,程序会每天定时在18:30过几分钟的时间发起自动更新,不管当天是否是交易日。由于Python的GIL原因，**请独立开启一个程序运行定时一键更新**。

# 注意
- 当日线数据用TuShare或者TuSharePro更新时,ETF的日线数据缺少复权因子,换手率和成交额。策略中使用ETF日线数据,要注意这种情况。
- 个股TuShare日线数据的复权因子好像不是特别准确。建议用TuSharePro。
- 由于Wind会对免费账号做限流,所以下载数据最好是一段一段的下载。比如每次下载三个月或者一个月的数据。
- 历史分笔数据会优先从腾讯下载,如果没有则从通达信下载。通达信的分笔数据没有秒的信息,DevilYuan系统使用随机算法生成秒的信息。
- 若日线数据和分笔数据下载有缺失,log会分别用抬头￥DyStockDataDaysEngine￥和￥DyStockDataTicksEngine￥标示。用户可以利用此信息,再次下载补全数据。
- TuShare和TuSharePro的股票代码表,交易日数据和日线数据共用数据库的表,如果日线数据下载既使用了TuShare又使用了TuSharePro,会导致数据的不一致性。
