# 周期转换函数测试示例

def convert_bar_to_minutes(bar_type=None, bar_interval=None):
    """
    将策略订阅的主合约周期转换为分钟值
    
    参数:
        bar_type: K线类型，'T'(秒), 'M'(分钟), 'D'(日线)
        bar_interval: K线周期数
    
    返回:
        float: 转换后的分钟数，不支持日线时返回None
    """
    print(f'周期转换-输入: 类型={bar_type}, 周期数={bar_interval}')
    
    if bar_type == 'M':
        # 分钟线，直接返回周期数
        minutes = bar_interval
        print(f'周期转换-分钟线: {bar_interval}分钟 -> {minutes}分钟')
        return minutes
        
    elif bar_type == 'T':
        # 分笔/秒线，转换为分钟
        if bar_interval == 0:
            # 0秒表示分笔，按1秒计算
            minutes = 1 / 60.0
        else:
            minutes = bar_interval / 60.0
        print(f'周期转换-秒线: {bar_interval}秒 -> {minutes}分钟')
        return minutes
        
    elif bar_type == 'D':
        # 日线，按要求不考虑
        print(f'周期转换-日线: 不支持日线转换，返回None')
        return None
        
    else:
        # 未知类型
        print(f'周期转换-未知类型: {bar_type}，返回None')
        return None

# 测试用例
def test_convert_bar_to_minutes():
    """测试周期转换函数"""
    
    test_cases = [
        # (bar_type, bar_interval, 期望结果, 描述)
        ('M', 1, 1.0, '1分钟线'),
        ('M', 5, 5.0, '5分钟线'),
        ('M', 15, 15.0, '15分钟线'),
        ('M', 30, 30.0, '30分钟线'),
        ('M', 60, 60.0, '60分钟线(1小时)'),
        
        ('T', 15, 0.25, '15秒线'),
        ('T', 30, 0.5, '30秒线'),
        ('T', 60, 1.0, '60秒线(1分钟)'),
        ('T', 120, 2.0, '120秒线(2分钟)'),
        ('T', 0, 1/60.0, '分笔数据'),
        
        ('D', 1, None, '1日线'),
        ('D', 5, None, '5日线'),
        
        ('X', 1, None, '未知类型'),
        (None, 1, None, '空类型'),
    ]
    
    print("=" * 60)
    print("周期转换函数测试")
    print("=" * 60)
    
    for i, (bar_type, bar_interval, expected, description) in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {description}")
        print("-" * 40)
        
        result = convert_bar_to_minutes(bar_type, bar_interval)
        
        # 检查结果
        if expected is None:
            success = result is None
        else:
            success = result is not None and abs(result - expected) < 0.001
        
        status = "✓ 通过" if success else "✗ 失败"
        print(f"结果: {result} (期望: {expected}) - {status}")
        
        if not success:
            print(f"❌ 测试失败: 期望 {expected}, 实际 {result}")
    
    print("\n" + "=" * 60)
    print("测试完成")

# 实际使用示例
def usage_examples():
    """使用示例"""
    
    print("\n" + "=" * 60)
    print("实际使用示例")
    print("=" * 60)
    
    # 示例1: 检查当前主图表周期
    print("\n示例1: 模拟检查主图表周期")
    print("-" * 30)
    
    # 模拟从主图表获取的数据
    simulated_bar_types = ['M', 'T', 'D']
    simulated_intervals = [1, 60, 1]
    descriptions = ['1分钟线', '60秒线', '1日线']
    
    for bar_type, interval, desc in zip(simulated_bar_types, simulated_intervals, descriptions):
        print(f"\n假设主图表为: {desc} (类型={bar_type}, 周期={interval})")
        minutes = convert_bar_to_minutes(bar_type, interval)
        if minutes is not None:
            print(f"转换结果: {minutes}分钟")
        else:
            print("转换结果: 不支持 (日线或未知类型)")
    
    # 示例2: 策略周期匹配检查
    print("\n\n示例2: 策略周期匹配检查")
    print("-" * 30)
    
    strategy_config = ('M', 1)  # 策略配置为1分钟
    chart_configs = [
        ('M', 1, '1分钟线'),     # 匹配
        ('T', 60, '60秒线'),     # 匹配 (60秒=1分钟)
        ('M', 5, '5分钟线'),     # 不匹配
        ('D', 1, '日线'),        # 不支持
    ]
    
    strategy_minutes = convert_bar_to_minutes(*strategy_config)
    print(f"策略配置: {strategy_config[0]}{strategy_config[1]} -> {strategy_minutes}分钟")
    
    for chart_type, chart_interval, desc in chart_configs:
        chart_minutes = convert_bar_to_minutes(chart_type, chart_interval)
        print(f"\n主图表: {desc} ({chart_type}{chart_interval})")
        
        if chart_minutes is not None and strategy_minutes is not None:
            if abs(strategy_minutes - chart_minutes) < 0.01:
                print("✓ 周期匹配")
            else:
                print(f"⚠ 周期不匹配 (策略:{strategy_minutes}分钟 vs 图表:{chart_minutes}分钟)")
        else:
            print("⚠ 无法比较 (不支持的周期类型)")

if __name__ == "__main__":
    # 运行测试
    test_convert_bar_to_minutes()
    
    # 运行使用示例
    usage_examples() 