# 套利交易控制系统功能说明

## 概述
基于Excel配置文件的多腿套利交易控制系统，支持灵活的下单模式、瘸腿单处理和智能订单管理。

## 新增配置参数

### 1. 下单模式控制
- **第一腿下单模式**: 低流动性合约的下单价格模式
- **后腿下单模式**: 其他合约的下单价格模式
- **可选模式**:
  - `最新价`: 使用合约的最新成交价
  - `对价`: 买入用卖一价，卖出用买一价
  - `超价`: 在对价基础上加减超价跳数

### 2. 超价控制
- **超价跳数**: 超价模式下的价格调整跳数
- 计算公式: 
  - 买入超价 = 卖一价 + (超价跳数 × 最小变动价位)
  - 卖出超价 = 买一价 - (超价跳数 × 最小变动价位)

### 3. 订单管理参数
- **撤单秒数**: 订单超时自动撤单的时间阈值
- **瘸腿单自动平仓秒数**: 第一腿成交后，其他腿未完全成交的处理时间

## 核心功能

### 1. 智能分腿执行
- **第一腿优先**: 自动识别低流动性合约作为第一腿
- **分批执行**: 1手1手分批执行，确保成交顺序
- **价格模式**: 不同腿可配置不同的价格策略

### 2. 瘸腿单处理机制
- **自动检测**: 第一腿成交后开启倒计时
- **超时处理**: 指定时间内其他腿未完全成交
- **自动平仓**: 对已成交的第一腿进行市价平仓
- **风险控制**: 避免部分成交导致的敞口风险

### 3. 订单状态管理
- **实时跟踪**: 监控所有订单状态变化
- **超时撤单**: 自动撤销超时未成交订单
- **状态同步**: 支持OnOrderChange事件和备用检查机制

### 4. 风险控制机制
- **加仓上限控制**: 当突破网格数达到加仓次数上限时自动触发风险控制
- **自动减仓**: 平掉最早的同方向套利组合，防止过度加仓
- **声音报警**: 触发风险控制时播放报警音频
- **强制市价平仓**: 风险控制平仓使用市价单，确保快速执行

## 技术实现

### 1. 类设计
```python
class TriggerManager:
    # 新增价格计算功能
    def _calculate_order_price(self, tcode, direction, price_mode, price_offset, fallback_price)
    # 支持多种下单模式
    def tim_trigger_long/short(self, qty, price, tcode, order_type, price_mode, price_offset)
    # 历史回测订单函数
    def his_trigger_long/short(self, qty, price, tcode)
    def his_trigger_exit_long/short(self, clots, price, tcode)

class ArbitrageOrderManager:
    # 实盘模式订单管理器
    # 瘸腿单检测
    def check_lame_leg_orders(self, lame_leg_timeout_seconds)
    # 瘸腿单处理
    def _handle_lame_leg_order(self, arb_id)
    # 分腿执行控制
    def submit_arb_order(self, setNo, direction, qty, contracts_with_coefs, 
                        first_leg_mode, other_leg_mode, price_offset, order_type)

class HistoricalArbitrageOrderManager(TriggerManager):
    # 历史回测模式订单管理器，继承TriggerManager
    # 简化的订单执行
    def submit_arb_order(self, setNo, direction, qty, contracts_with_coefs, ...)
    # 简化的平仓处理
    def close_arb_order(self, arb_id, force_market=False)
    # 空操作的兼容接口
    def check_order_timeout/check_lame_leg_orders/update_order_status_backup(...)
```

### 2. 参数解耦
- 配置参数只在全局变量、initialize()和handle_data()中引用
- 类和函数通过参数传递获取配置信息
- 支持运行时动态调整参数

### 3. 双模式设计
- **实盘模式（HTS=1）**: 使用ArbitrageOrderManager，支持完整的订单管理功能
- **历史回测模式（HTS=0）**: 使用HistoricalArbitrageOrderManager，专注历史订单执行
- **动态切换**: 系统根据context.strategyStatus()自动选择合适的管理器
- **接口统一**: 两种模式使用相同的调用接口，确保代码兼容性

### 4. 事件驱动（仅实盘模式）
- OnOrderChange回调函数处理订单状态
- 备用状态检查机制确保可靠性
- 历史回测模式无需复杂的事件处理

## 配置文件格式

Excel文件应包含以下列：
```
套利组合               | DCE|F|P|2509*3-DCE|F|P|2507-DCE|F|Y|2509*2
均线一                | 20
突破网格点数          | 10.0
加仓次数上限          | 3
下单量               | 1
第一腿下单模式        | 对价
后腿下单模式          | 最新价
超价跳数             | 2
撤单秒数             | 300
瘸腿单自动平仓秒数    | 60
预警音频路径          | D:\audio\alert.wav
```

### 套利组合公式说明
系统支持复杂的套利公式，包括合约倍数：
- **基础格式**: `合约1-合约2` (如: SHFE|F|rb|2505-SHFE|F|rb|2501)
- **倍数格式**: `合约1*3-合约2-合约3*2` (如: DCE|F|P|2509*3-DCE|F|P|2507-DCE|F|Y|2509*2)
- **方向解析**: 
  - 公式中第一个合约默认为买入(+)
  - 减号(-)表示卖出方向
  - 加号(+)表示买入方向
- **倍数计算**: 最终下单量 = 基础下单量 × 公式倍数 × 外部系数

## 平仓信号逻辑

### 精确平仓控制
- **网格级别平仓**: 系统为每个套利订单标记所属网格编号
- **精确回撤平仓**: 当价格回落到`最后突破线-1`网格时，只平掉`最后突破线`网格的订单
- **避免过度平仓**: 不会因为轻微回落就平掉所有同方向订单

### 示例说明
假设当前有3笔多单，分别在网格1、2、3开仓：
- 当前价格在网格3（最后突破线）
- 只有当价格回落到网格2（最后突破线-1）时，才平掉网格3的订单
- 如果价格继续回落到网格1，则平掉网格2的订单
- 网格1的订单只有在价格回落到网格0时才会被平掉

## 使用说明

1. **配置设置**: 在Excel文件中设置各项参数
2. **策略启动**: 系统自动读取配置并初始化
3. **自动执行**: 根据突破信号自动执行套利交易
4. **精确平仓**: 根据网格级别精确控制平仓时机
5. **风险控制**: 自动处理瘸腿单和超时订单
6. **状态监控**: 实时跟踪所有订单和套利组合状态

## 模式差异

### 实盘模式（HTS=1）
- **复杂订单管理**: 支持分腿执行、瘸腿单处理、订单超时等
- **价格模式**: 支持最新价、对价、超价三种模式
- **状态跟踪**: 实时监控订单状态变化
- **风险控制**: 完整的风险控制机制

### 历史回测模式（HTS=0）
- **简化执行**: 直接调用his_族函数执行历史订单
- **即时成交**: 所有订单立即执行，无需等待
- **无复杂管理**: 不涉及订单状态跟踪、超时检查等
- **专注回测**: 只关注历史信号的执行效果
- **正确套利**: 根据公式解析各合约交易方向，确保套利组合的正确执行

## 注意事项

1. **第一腿识别**: 按合约代码倒序排列，最后一个为第一腿（低流动性）
2. **价格模式**: 
   - 实盘模式：建议第一腿使用"对价"或"超价"，后腿使用"最新价"
   - 历史回测模式：价格模式配置会被忽略，使用最新价执行
3. **超时设置**: 撤单秒数建议300秒，瘸腿单处理建议60秒（仅实盘模式有效）
4. **风险提醒**: 瘸腿单处理会产生额外的交易成本和滑点（仅实盘模式）
5. **模式切换**: 系统会自动根据运行环境选择合适的执行模式
6. **套利方向**: 
   - 例如公式`DCE|F|P|2509*3-DCE|F|P|2507-DCE|F|Y|2509*2`
   - 做多价差（BUY）：买入P2509(3手)，卖出P2507(1手)，卖出Y2509(2手)
   - 做空价差（SELL）：卖出P2509(3手)，买入P2507(1手)，买入Y2509(2手)
7. **倍数处理**:
   - 公式倍数由*号指定（如P2509*3表示3倍）
   - 无倍数标记的合约默认为1倍
   - 最终执行数量 = 配置下单量 × 公式倍数
8. **风险控制**:
   - 当突破网格数 ≥ 加仓次数上限时，触发风险控制
   - 自动平掉最早的同方向套利组合（按提交时间排序）
   - 播放报警音频提醒交易员注意
   - 使用市价单强制平仓，确保执行效率 