#!/usr/bin/env python3
"""
安装MCP服务到Augment插件配置
将已安装的MCP服务添加到Augment的配置文件中
"""

import json
import os
import shutil
import sys
from pathlib import Path

def find_augment_config_paths():
    """查找Augment配置文件路径"""
    home = Path.home()
    possible_paths = [
        home / ".codegeex" / "agent" / "configs" / "user_mcp_config.json",
        home / ".vscode" / "extensions" / "*" / "dist" / "agent" / "configs" / "user_mcp_config.json",
        home / ".cursor" / "mcp.json",
        # 添加更多可能的路径
    ]
    
    found_paths = []
    for path_pattern in possible_paths:
        if "*" in str(path_pattern):
            # 处理通配符路径
            parent = path_pattern.parent.parent
            if parent.exists():
                for ext_dir in parent.glob("*"):
                    full_path = ext_dir / "dist" / "agent" / "configs" / "user_mcp_config.json"
                    if full_path.exists():
                        found_paths.append(full_path)
        else:
            if path_pattern.exists():
                found_paths.append(path_pattern)
    
    return found_paths

def load_config(config_path):
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading config from {config_path}: {e}")
        return None

def save_config(config_path, config_data):
    """保存配置文件"""
    try:
        # 备份原文件
        backup_path = str(config_path) + ".backup"
        if config_path.exists():
            shutil.copy2(config_path, backup_path)
            print(f"Backup created: {backup_path}")
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"Error saving config to {config_path}: {e}")
        return False

def get_mcp_servers_config():
    """获取MCP服务器配置"""
    current_dir = Path(__file__).parent
    
    return {
        "graphiti": {
            "command": "python",
            "args": [str(current_dir / "mcp_servers" / "graphiti_server.py")],
            "description": "Graphiti knowledge graph and memory management service",
            "env": {
                "GRAPHITI_LOG_LEVEL": "INFO"
            }
        },
        "opik": {
            "command": "python",
            "args": [str(current_dir / "mcp_servers" / "opik_server.py")],
            "description": "Opik ML experiment tracking and monitoring service",
            "env": {
                "OPIK_LOG_LEVEL": "INFO"
            }
        },
        "ragie": {
            "command": "python",
            "args": [str(current_dir / "mcp_servers" / "ragie_server.py")],
            "description": "Ragie RAG (Retrieval Augmented Generation) service",
            "env": {
                "RAGIE_LOG_LEVEL": "INFO"
            }
        },
        "jupyter_mcp": {
            "command": "jupyter-mcp-server",
            "args": ["start", "--transport", "stdio"],
            "description": "Jupyter MCP Server for notebook integration",
            "env": {
                "JUPYTER_LOG_LEVEL": "INFO"
            }
        }
    }

def main():
    """主函数"""
    print("=== MCP服务安装到Augment插件 ===")
    
    # 查找配置文件
    config_paths = find_augment_config_paths()
    
    if not config_paths:
        print("未找到Augment配置文件。")
        print("请确保Augment插件已安装并至少运行过一次。")
        return False
    
    print(f"找到 {len(config_paths)} 个配置文件:")
    for i, path in enumerate(config_paths):
        print(f"  {i+1}. {path}")
    
    # 让用户选择配置文件
    if len(config_paths) == 1:
        selected_path = config_paths[0]
        print(f"自动选择: {selected_path}")
    else:
        try:
            choice = int(input("请选择要更新的配置文件 (输入数字): ")) - 1
            if 0 <= choice < len(config_paths):
                selected_path = config_paths[choice]
            else:
                print("无效选择")
                return False
        except ValueError:
            print("无效输入")
            return False
    
    # 加载现有配置
    config = load_config(selected_path)
    if config is None:
        print("无法加载配置文件")
        return False
    
    # 确保mcpServers键存在
    if "mcpServers" not in config:
        config["mcpServers"] = {}
    
    # 获取新的MCP服务器配置
    new_servers = get_mcp_servers_config()
    
    # 添加新服务器
    added_count = 0
    for server_name, server_config in new_servers.items():
        if server_name not in config["mcpServers"]:
            config["mcpServers"][server_name] = server_config
            added_count += 1
            print(f"添加服务: {server_name}")
        else:
            print(f"服务已存在，跳过: {server_name}")
    
    # 保存配置
    if added_count > 0:
        if save_config(selected_path, config):
            print(f"成功添加 {added_count} 个MCP服务到配置文件")
            print("请重启Augment插件以使配置生效")
            return True
        else:
            print("保存配置文件失败")
            return False
    else:
        print("没有新服务需要添加")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
