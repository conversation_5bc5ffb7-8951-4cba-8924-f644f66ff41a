# DevilYuan API文档生成指南

## 简介

DevilYuan量化交易系统的API文档生成工具旨在帮助开发者为系统中的Python文件添加符合规范的API文档注释。
本工具遵循 `.cursorrules` 文件中定义的规则：

- 所有函数注释必须包含参数类型和返回值说明
- 类注释需描述继承关系和主要方法
- 使用 reStructuredText 语法标记代码示例

## 安装依赖

```bash
pip install ast typing
```

## 使用方法

### 基本用法

1. 处理单个文件：

```bash
python generate_api_docs.py path/to/file.py
```

2. 处理整个目录（包括子目录）：

```bash
python generate_api_docs.py path/to/directory -r
```

### 参数说明

- `path`：要处理的文件或目录路径（必需）
- `-r, --recursive`：递归处理子目录（可选，默认为False）
- `-v, --verbose`：显示详细处理信息（可选，默认为False）

### 使用示例

1. 为交易策略类添加文档注释：

```bash
python generate_api_docs.py Stock/Trade/DyStockStrategyBase.py -v
```

2. 为整个Stock模块添加文档注释：

```bash
python generate_api_docs.py Stock -r
```

## 文档注释格式

### 类注释示例

```python
class DyStockPos:
    """
    股票持仓类，表示一只股票的持仓信息。
    
    继承关系：无继承
    
    主要方法：
        - addPos: 增加持仓
        - removePos: 减少持仓
        - onOpen: 开盘前处理
        - onTick: 处理Tick数据
        - onClose: 收盘后处理
    
    ::
    
        # 创建持仓示例
        pos = DyStockPos('2021-01-01', Strategy, '000001', '平安银行', 12.5, 100)
        
        # 增加持仓
        pos.addPos('2021-01-02', Strategy, 12.6, 100)
    """
```

### 函数注释示例

```python
def onOpen(self, date, dataEngine):
    """
    持仓开盘前的操作。回测时有效。
    由于实盘时，账户的仓位依赖于券商，所以实盘时不调用此接口。
    
    :param date: 当前日期
    :type date: str
    :param dataEngine: 数据引擎
    :type dataEngine: DyStockDataEngine
    :return: 成功返回True，失败返回False
    :rtype: bool
    """
```

## 注意事项

1. 生成的文档注释可能需要人工调整，工具只是提供了基本的结构
2. 文档注释应尽量详细描述函数的功能、参数的作用和返回值的含义
3. 对于复杂的方法或类，建议添加代码示例来说明如何使用
4. 建议在提交代码前先运行此工具，确保新添加的代码包含标准的API文档注释

## 自定义文档生成规则

可以通过修改 `DocStringGenerator` 类中的 `generate_class_docstring` 和 `generate_function_docstring` 方法来自定义文档生成规则。例如，您可以：

- 添加更多的文档元素
- 修改文档格式
- 增加自动生成代码示例的功能
- 调整参数和返回值说明的格式

## 目标文件列表

以下是建议优先添加API文档注释的关键文件：

1. `Stock/Trade/DyStockStrategyBase.py` - 股票策略基类
2. `Stock/Select/Strategy/DyStockSelectStrategyTemplate.py` - 选股策略模板
3. `Stock/Trade/AccountManager/DyStockPos.py` - 股票持仓类
4. `DyMainWindow.py` - 主窗口类

## 常见问题

**Q: 生成的注释不是我想要的格式，怎么办？**

A: 工具生成的注释是基本的框架，您可以根据需要手动调整，或修改生成工具的代码来适应您喜欢的格式。

**Q: 注释中的参数类型和描述不准确怎么办？**

A: 工具无法自动判断参数的实际意义，请手动调整参数描述和类型说明。

**Q: 如何处理已有注释的文件？**

A: 工具会检查是否已有符合格式的注释，如果已有则不会修改，如果不符合格式则会提示但不会自动替换。

## 贡献

欢迎提交PR来改进此工具，比如：

- 提高注释生成的准确性
- 添加更多的文档元素
- 改进代码分析能力
- 优化输出格式 