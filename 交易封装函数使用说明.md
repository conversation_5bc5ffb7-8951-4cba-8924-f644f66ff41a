# 交易封装函数使用说明

## 概述

本文档详细说明了Alpha-Tick Pro策略中使用的交易封装函数，这些函数解决了机智量化平台中简易交易函数（Buy、Sell、BuyToCover、SellShort）在实盘交易中的局限性。

## 核心问题

### 简易交易函数的局限性
- `Buy()`, `Sell()`, `BuyToCover()`, `SellShort()` 只适合历史阶段的简易指令
- 无法妥善执行实际的下单操作
- 缺乏价格限制、交易所规则处理
- 没有状态管理，容易造成重复开仓

### 解决方案
使用封装的交易函数，提供：
1. **实时和历史阶段的差异化处理**
2. **完善的价格控制机制**
3. **交易所规则适配**
4. **状态管理避免重复开仓**

## 交易状态管理变量

### 全局状态变量
```python
BKS = 0  # 多头开仓状态 (0:未开仓, 1:已开仓, 2:已平仓)
SKS = 0  # 空头开仓状态 (0:未开仓, 1:已开仓, 2:已平仓)
BPS = 0  # 多头平仓状态 (0:未平仓, 1:已平仓)
SPS = 0  # 空头平仓状态 (0:未平仓, 1:已平仓)
```

### 状态说明
- **0**: 初始状态，可以执行相应操作
- **1**: 已执行操作，防止重复执行
- **2**: 已完成平仓，标记交易结束

## 核心封装函数

### 1. tim_trigger() - 实时开仓函数

```python
def tim_trigger(BK, SK, qty, itk, tcode):
    """
    盘中实时开仓
    
    参数:
    BK: 多头开仓信号 (True/False)
    SK: 空头开仓信号 (True/False)
    qty: 开仓数量
    itk: 超价跳数
    tcode: 合约代码
    """
```

#### 功能特点
- **对盘超价**: 自动计算超价，确保成交
- **价格限制**: 自动检查涨跌停限制
- **状态管理**: 通过BKS/SKS避免重复开仓
- **日志记录**: 详细记录交易信息

#### 价格计算逻辑
```python
# 多头开仓价格
iprc = min(Q_AskPrice(tcode) + itk * PriceTick(tcode), Q_UpperLimit(tcode))

# 空头开仓价格  
iprc = max(Q_BidPrice(tcode) - itk * PriceTick(tcode), Q_LowLimit(tcode))
```

### 2. tim_trigger_Exit() - 实时平仓函数

```python
def tim_trigger_Exit(BP, SP, otk, tcode, clots):
    """
    盘中实时平仓
    
    参数:
    BP: 多头平仓信号 (True/False)
    SP: 空头平仓信号 (True/False)
    otk: 平仓超价跳数
    tcode: 合约代码
    clots: 平仓数量
    """
```

#### 功能特点
- **智能平仓**: 自动选择最优平仓数量
- **交易所规则**: 区分上期所和其他交易所规则
- **今昨仓处理**: 上期所优先平今仓逻辑
- **状态更新**: 自动更新开仓和平仓状态

#### 上期所特殊处理
```python
if ExchangeName(tcode) not in ['SHFE', 'INE']:
    # 非上期所：直接平仓
    A_SendOrder(Enum_Buy(), Enum_Exit(), _lots, prc, tcode)
else:
    # 上期所：区分今昨仓
    tlots = A_TodaySellPosition(tcode)  # 今仓
    if tlots >= lots:
        # 今仓足够，全部平今仓
        A_SendOrder(Enum_Buy(), Enum_ExitToday(), lots, prc, tcode)
    elif tlots > 0:
        # 今仓不够，部分平今仓，剩余平昨仓
        A_SendOrder(Enum_Buy(), Enum_ExitToday(), tlots, prc, tcode)
        A_SendOrder(Enum_Buy(), Enum_Exit(), int(dlots), prc, tcode)
    else:
        # 无今仓，全部平昨仓
        A_SendOrder(Enum_Buy(), Enum_Exit(), lots, prc, tcode)
```

### 3. his_trigger() - 历史开仓函数

```python
def his_trigger(BK, SK, qty, itk):
    """
    历史开仓（回测阶段）
    
    参数:
    BK: 多头开仓信号
    SK: 空头开仓信号  
    qty: 开仓数量
    itk: 超价跳数
    """
```

#### 功能特点
- **简化处理**: 基于收盘价计算开仓价格
- **状态管理**: 同样使用BKS/SKS状态控制
- **兼容性**: 与实时函数保持一致的接口

### 4. his_trigger_Exit() - 历史平仓函数

```python
def his_trigger_Exit(BP, SP, otk, clots):
    """
    历史平仓（回测阶段）
    
    参数:
    BP: 多头平仓信号
    SP: 空头平仓信号
    otk: 平仓超价跳数
    clots: 平仓数量
    """
```

## 辅助函数

### 1. reset_trading_status() - 重置交易状态

```python
def reset_trading_status():
    """重置所有交易状态变量"""
    global BKS, SKS, BPS, SPS
    BKS = 0
    SKS = 0  
    BPS = 0
    SPS = 0
```

**使用场景**:
- 每日开盘前重置
- 强制平仓后重置
- 策略重启时重置

### 2. is_real_time() - 判断运行阶段

```python
def is_real_time():
    """判断是否为实时阶段"""
    try:
        return Q_UpdateTime() is not None
    except:
        return False
```

**判断逻辑**:
- 实时阶段：有实时行情数据，使用tim_trigger系列函数
- 历史阶段：回测模式，使用his_trigger系列函数

## 使用示例

### 完整交易流程

```python
def execute_trading_signals(long_signal, short_signal):
    """执行交易信号"""
    global last_trade_time
    
    # 检查交易间隔
    current_time_ms = int(time.time() * 1000)
    if current_time_ms - last_trade_time < g_params['交易间隔']:
        return
    
    # 根据运行阶段选择函数
    if is_real_time():
        # 实时阶段
        tim_trigger(long_signal, short_signal, position_size, 超价跳数, 基准合约)
    else:
        # 历史阶段
        his_trigger(long_signal, short_signal, position_size, 超价跳数)
    
    # 更新交易时间
    if long_signal or short_signal:
        last_trade_time = current_time_ms

def manage_existing_positions():
    """管理现有持仓"""
    # 生成平仓信号
    exit_long, exit_short = generate_exit_signals()
    
    if is_real_time():
        # 实时平仓
        if exit_long:
            long_position = A_BuyPosition(基准合约)
            if long_position > 0:
                tim_trigger_Exit(False, True, 平仓超价跳数, 基准合约, long_position)
        
        if exit_short:
            short_position = A_SellPosition(基准合约)
            if short_position > 0:
                tim_trigger_Exit(True, False, 平仓超价跳数, 基准合约, short_position)
    else:
        # 历史平仓
        if exit_long:
            long_position = BuyPosition()
            if long_position > 0:
                his_trigger_Exit(False, True, 平仓超价跳数, long_position)
        
        if exit_short:
            short_position = SellPosition()
            if short_position > 0:
                his_trigger_Exit(True, False, 平仓超价跳数, short_position)
```

### 强制平仓示例

```python
def close_all_positions():
    """强制平仓所有持仓"""
    try:
        if is_real_time():
            # 实时强制平仓
            long_position = A_BuyPosition(基准合约)
            short_position = A_SellPosition(基准合约)
            
            if long_position > 0:
                tim_trigger_Exit(False, True, 平仓超价跳数, 基准合约, long_position)
                LogInfo(f"【强制平多】手数: {long_position}")
            
            if short_position > 0:
                tim_trigger_Exit(True, False, 平仓超价跳数, 基准合约, short_position)
                LogInfo(f"【强制平空】手数: {short_position}")
        else:
            # 历史强制平仓
            long_position = BuyPosition()
            short_position = SellPosition()
            
            if long_position > 0:
                his_trigger_Exit(False, True, 平仓超价跳数, long_position)
            
            if short_position > 0:
                his_trigger_Exit(True, False, 平仓超价跳数, short_position)
            
    except Exception as e:
        LogError(f"强制平仓异常: {str(e)}")
```

## 参数配置

### 关键参数设置

```python
# 交易参数
超价跳数 = 2          # 开仓超价跳数，建议1-3
平仓超价跳数 = 1      # 平仓超价跳数，建议1-2  
基准合约 = 'SHFE|F|RB|MAIN'  # 交易合约

# 风险控制参数
g_params['交易间隔'] = 3000    # 交易间隔(毫秒)
g_params['单笔最大风险'] = 0.02  # 单笔最大风险比例
```

### 参数调优建议

1. **超价跳数**:
   - 流动性好的品种可以设置较小值(1-2)
   - 流动性差的品种建议设置较大值(2-3)
   - 实时阶段可以比历史阶段设置更大的超价

2. **交易间隔**:
   - 高频策略建议1000-3000毫秒
   - 中频策略建议5000-10000毫秒
   - 避免过于频繁的交易

## 注意事项

### 1. 状态管理
- 必须在每日开盘前调用`reset_trading_status()`
- 强制平仓后建议重置状态
- 策略重启时检查状态一致性

### 2. 异常处理
- 所有交易函数都包含异常处理
- 建议在调用前检查持仓状态
- 关注日志输出，及时发现问题

### 3. 性能优化
- 避免在每个tick都调用交易函数
- 使用交易间隔控制交易频率
- 合理设置超价跳数平衡成交率和滑点

### 4. 风险控制
- 始终检查账户资金充足性
- 设置合理的单笔最大风险
- 实施严格的止损机制

## 总结

通过使用这套交易封装函数，可以：

1. **提高交易执行质量**: 更好的价格控制和成交率
2. **简化代码逻辑**: 统一的接口处理实时和历史阶段
3. **增强风险控制**: 完善的状态管理和异常处理
4. **适配交易所规则**: 自动处理不同交易所的特殊规则

这套函数已经在Alpha-Tick Pro策略中得到验证，可以作为其他策略的标准交易模块使用。
