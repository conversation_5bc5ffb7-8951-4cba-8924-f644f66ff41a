# -*- coding: utf-8 -*-
"""
优化交易封装类 - 订单全寿命周期跟踪版
基于机智量化平台，支持实时和历史阶段差异化处理
包含完整的订单跟踪机制，避免重复开仓和滑点损耗
"""

import time
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict

class AdvancedTradingManager:
    """
    高级交易管理器 - 支持订单全寿命周期跟踪
    """
    
    def __init__(self, contract_code='SHFE|F|RB|MAIN', 
                 entry_tick_offset=2, exit_tick_offset=1,
                 order_timeout=300, max_retry_count=3):
        """
        初始化交易管理器
        
        参数:
            contract_code: 基准合约代码
            entry_tick_offset: 开仓超价跳数
            exit_tick_offset: 平仓超价跳数
            order_timeout: 订单超时时间(秒)
            max_retry_count: 最大重试次数
        """
        # 基础配置
        self.contract_code = contract_code
        self.entry_tick_offset = entry_tick_offset
        self.exit_tick_offset = exit_tick_offset
        self.order_timeout = order_timeout
        self.max_retry_count = max_retry_count
        
        # 交易状态管理 (替代全局变量BKS, SKS, BPS, SPS)
        self.BKS = 0  # 多头开仓状态 (0:未开仓, 1:已开仓, 2:已平仓)
        self.SKS = 0  # 空头开仓状态 (0:未开仓, 1:已开仓, 2:已平仓)
        self.BPS = 0  # 多头平仓状态 (0:未平仓, 1:已平仓)
        self.SPS = 0  # 空头平仓状态 (0:未平仓, 1:已平仓)
        
        # 订单跟踪字典 - 核心功能
        self.pending_orders = {}  # 待处理订单: {order_id: order_info}
        self.pending_entry_orders = {}  # 待成交开仓订单
        self.pending_exit_orders = {}   # 待成交平仓订单
        
        # 订单状态常量
        self.ORDER_STATUS = {
            'PENDING': '0',      # 待报
            'SUBMITTED': '1',    # 已报
            'PARTIAL': '2',      # 部分成交
            'FILLED': '6',       # 完全成交
            'CANCELLED': '7',    # 已撤单
            'REJECTED': '9',     # 拒绝
            'ERROR': 'F'         # 错误
        }
        
        # 统计信息
        self.stats = {
            'total_orders': 0,
            'successful_orders': 0,
            'failed_orders': 0,
            'timeout_orders': 0,
            'retry_orders': 0
        }
        
        LogInfo(f"交易管理器初始化完成 - 合约: {contract_code}")
    
    def reset_trading_status(self):
        """重置交易状态"""
        self.BKS = 0
        self.SKS = 0
        self.BPS = 0
        self.SPS = 0
        LogInfo("交易状态已重置")
    
    def is_real_time(self):
        """判断是否为实时阶段"""
        try:
            return Q_UpdateTime() is not None
        except:
            return False
    
    def get_safe_price(self, base_price, tick_offset, direction, tcode):
        """
        获取安全的交易价格，考虑涨跌停限制
        
        参数:
            base_price: 基础价格
            tick_offset: 跳数偏移
            direction: 方向 ('BUY' 或 'SELL')
            tcode: 合约代码
        """
        try:
            tick_size = PriceTick(tcode)
            
            if direction == 'BUY':
                # 买入时向上偏移
                target_price = base_price + tick_offset * tick_size
                upper_limit = Q_UpperLimit(tcode)
                if upper_limit > 0:
                    safe_price = min(target_price, upper_limit)
                else:
                    safe_price = target_price
            else:  # SELL
                # 卖出时向下偏移
                target_price = base_price - tick_offset * tick_size
                lower_limit = Q_LowLimit(tcode)
                if lower_limit > 0:
                    safe_price = max(target_price, lower_limit)
                else:
                    safe_price = target_price
            
            return safe_price
            
        except Exception as e:
            LogError(f"计算安全价格异常: {str(e)}")
            return base_price
    
    def tim_trigger_entry(self, BK, SK, qty, tcode=None):
        """
        实时开仓函数 - 支持订单跟踪
        
        参数:
            BK: 多头开仓信号
            SK: 空头开仓信号
            qty: 开仓数量
            tcode: 合约代码，默认使用初始化时的合约
        """
        if tcode is None:
            tcode = self.contract_code
        
        order_results = []
        
        try:
            # 多头开仓
            if BK and self.BKS == 0:
                ask_price = Q_AskPrice(tcode)
                if ask_price <= 0:
                    LogWarn(f"无效的卖一价: {ask_price}")
                    return order_results
                
                entry_price = self.get_safe_price(ask_price, self.entry_tick_offset, 'BUY', tcode)
                
                # 发送订单
                ret_code, order_id = A_SendOrder(Enum_Buy(), Enum_Entry(), qty, entry_price, tcode)
                
                if ret_code == 0 and order_id:
                    # 订单发送成功，记录到跟踪字典
                    order_info = {
                        'order_id': order_id,
                        'direction': 'BUY',
                        'action': 'ENTRY',
                        'symbol': tcode,
                        'quantity': qty,
                        'price': entry_price,
                        'timestamp': time.time(),
                        'status': 'PENDING',
                        'retry_count': 0,
                        'original_signal': 'BK'
                    }
                    
                    self.pending_entry_orders[order_id] = order_info
                    self.BKS = 1  # 标记为已开仓
                    self.stats['total_orders'] += 1
                    
                    LogInfo(f"多头开仓订单已发送 - 订单号: {order_id}, 价格: {entry_price:.2f}, 数量: {qty}")
                    order_results.append(('BUY_ENTRY', ret_code, order_id))
                else:
                    LogError(f"多头开仓订单发送失败 - 返回码: {ret_code}")
                    order_results.append(('BUY_ENTRY', ret_code, None))
            
            # 空头开仓
            if SK and self.SKS == 0:
                bid_price = Q_BidPrice(tcode)
                if bid_price <= 0:
                    LogWarn(f"无效的买一价: {bid_price}")
                    return order_results
                
                entry_price = self.get_safe_price(bid_price, self.entry_tick_offset, 'SELL', tcode)
                
                # 发送订单
                ret_code, order_id = A_SendOrder(Enum_Sell(), Enum_Entry(), qty, entry_price, tcode)
                
                if ret_code == 0 and order_id:
                    # 订单发送成功，记录到跟踪字典
                    order_info = {
                        'order_id': order_id,
                        'direction': 'SELL',
                        'action': 'ENTRY',
                        'symbol': tcode,
                        'quantity': qty,
                        'price': entry_price,
                        'timestamp': time.time(),
                        'status': 'PENDING',
                        'retry_count': 0,
                        'original_signal': 'SK'
                    }
                    
                    self.pending_entry_orders[order_id] = order_info
                    self.SKS = 1  # 标记为已开仓
                    self.stats['total_orders'] += 1
                    
                    LogInfo(f"空头开仓订单已发送 - 订单号: {order_id}, 价格: {entry_price:.2f}, 数量: {qty}")
                    order_results.append(('SELL_ENTRY', ret_code, order_id))
                else:
                    LogError(f"空头开仓订单发送失败 - 返回码: {ret_code}")
                    order_results.append(('SELL_ENTRY', ret_code, None))
            
            return order_results
            
        except Exception as e:
            LogError(f"实时开仓异常: {str(e)}")
            return order_results
    
    def tim_trigger_exit(self, BP, SP, clots, tcode=None):
        """
        实时平仓函数 - 支持订单跟踪
        
        参数:
            BP: 平空头信号
            SP: 平多头信号
            clots: 平仓数量
            tcode: 合约代码
        """
        if tcode is None:
            tcode = self.contract_code
        
        order_results = []
        
        try:
            # 平空头持仓
            if BP and self.BPS == 0:
                sell_position = A_SellPosition(tcode)
                if sell_position <= 0:
                    LogWarn(f"无空头持仓可平: {sell_position}")
                    return order_results
                
                actual_lots = min(clots, sell_position)
                ask_price = Q_AskPrice(tcode)
                exit_price = self.get_safe_price(ask_price, self.exit_tick_offset, 'BUY', tcode)
                
                # 处理上期所特殊规则
                exit_orders = self._handle_shfe_exit(tcode, 'BUY', actual_lots, exit_price)
                
                for ret_code, order_id, exit_type, lots in exit_orders:
                    if ret_code == 0 and order_id:
                        order_info = {
                            'order_id': order_id,
                            'direction': 'BUY',
                            'action': 'EXIT',
                            'symbol': tcode,
                            'quantity': lots,
                            'price': exit_price,
                            'timestamp': time.time(),
                            'status': 'PENDING',
                            'retry_count': 0,
                            'exit_type': exit_type,
                            'original_signal': 'BP'
                        }
                        
                        self.pending_exit_orders[order_id] = order_info
                        order_results.append(('BUY_EXIT', ret_code, order_id))
                
                if order_results:
                    self.BPS = 1
                    LogInfo(f"空头平仓订单已发送 - 数量: {actual_lots}")
            
            # 平多头持仓
            if SP and self.SPS == 0:
                buy_position = A_BuyPosition(tcode)
                if buy_position <= 0:
                    LogWarn(f"无多头持仓可平: {buy_position}")
                    return order_results
                
                actual_lots = min(clots, buy_position)
                bid_price = Q_BidPrice(tcode)
                exit_price = self.get_safe_price(bid_price, self.exit_tick_offset, 'SELL', tcode)
                
                # 处理上期所特殊规则
                exit_orders = self._handle_shfe_exit(tcode, 'SELL', actual_lots, exit_price)
                
                for ret_code, order_id, exit_type, lots in exit_orders:
                    if ret_code == 0 and order_id:
                        order_info = {
                            'order_id': order_id,
                            'direction': 'SELL',
                            'action': 'EXIT',
                            'symbol': tcode,
                            'quantity': lots,
                            'price': exit_price,
                            'timestamp': time.time(),
                            'status': 'PENDING',
                            'retry_count': 0,
                            'exit_type': exit_type,
                            'original_signal': 'SP'
                        }
                        
                        self.pending_exit_orders[order_id] = order_info
                        order_results.append(('SELL_EXIT', ret_code, order_id))
                
                if order_results:
                    self.SPS = 1
                    LogInfo(f"多头平仓订单已发送 - 数量: {actual_lots}")
            
            return order_results
            
        except Exception as e:
            LogError(f"实时平仓异常: {str(e)}")
            return order_results
    
    def _handle_shfe_exit(self, tcode, direction, lots, price):
        """
        处理上期所平仓特殊规则
        
        返回: [(ret_code, order_id, exit_type, lots), ...]
        """
        results = []
        
        try:
            exchange_name = ExchangeName(tcode)
            
            if exchange_name not in ['SHFE', 'INE']:
                # 非上期所，直接平仓
                if direction == 'BUY':
                    ret_code, order_id = A_SendOrder(Enum_Buy(), Enum_Exit(), lots, price, tcode)
                else:
                    ret_code, order_id = A_SendOrder(Enum_Sell(), Enum_Exit(), lots, price, tcode)
                
                results.append((ret_code, order_id, "平仓", lots))
            else:
                # 上期所特殊处理
                if direction == 'BUY':
                    today_lots = A_TodaySellPosition(tcode)
                else:
                    today_lots = A_TodayBuyPosition(tcode)
                
                remaining_lots = lots - today_lots
                
                if today_lots >= lots:
                    # 今仓足够，全部平今
                    if direction == 'BUY':
                        ret_code, order_id = A_SendOrder(Enum_Buy(), Enum_ExitToday(), lots, price, tcode)
                    else:
                        ret_code, order_id = A_SendOrder(Enum_Sell(), Enum_ExitToday(), lots, price, tcode)
                    
                    results.append((ret_code, order_id, "平今", lots))
                    
                elif today_lots > 0:
                    # 部分平今，部分平昨
                    if direction == 'BUY':
                        ret1, id1 = A_SendOrder(Enum_Buy(), Enum_ExitToday(), today_lots, price, tcode)
                        ret2, id2 = A_SendOrder(Enum_Buy(), Enum_Exit(), remaining_lots, price, tcode)
                    else:
                        ret1, id1 = A_SendOrder(Enum_Sell(), Enum_ExitToday(), today_lots, price, tcode)
                        ret2, id2 = A_SendOrder(Enum_Sell(), Enum_Exit(), remaining_lots, price, tcode)
                    
                    results.append((ret1, id1, "平今", today_lots))
                    results.append((ret2, id2, "平昨", remaining_lots))
                    
                else:
                    # 全部平昨
                    if direction == 'BUY':
                        ret_code, order_id = A_SendOrder(Enum_Buy(), Enum_Exit(), lots, price, tcode)
                    else:
                        ret_code, order_id = A_SendOrder(Enum_Sell(), Enum_Exit(), lots, price, tcode)
                    
                    results.append((ret_code, order_id, "平昨", lots))
            
            return results
            
        except Exception as e:
            LogError(f"处理上期所平仓规则异常: {str(e)}")
            return results

    def check_and_update_orders(self):
        """
        检查和更新所有待处理订单状态 - 核心订单跟踪功能
        每次策略触发时都应该调用此函数
        """
        try:
            current_time = time.time()

            # 检查开仓订单
            self._check_entry_orders(current_time)

            # 检查平仓订单
            self._check_exit_orders(current_time)

            # 清理超时订单
            self._cleanup_timeout_orders(current_time)

        except Exception as e:
            LogError(f"检查订单状态异常: {str(e)}")

    def _check_entry_orders(self, current_time):
        """检查开仓订单状态"""
        orders_to_remove = []

        for order_id, order_info in list(self.pending_entry_orders.items()):
            try:
                # 检查订单是否超时
                if current_time - order_info['timestamp'] > self.order_timeout:
                    LogWarn(f"开仓订单超时: {order_id}")
                    self._handle_timeout_order(order_id, order_info, 'entry')
                    orders_to_remove.append(order_id)
                    continue

                # 获取订单状态
                order_status = A_OrderStatus(order_id)

                if order_status == self.ORDER_STATUS['FILLED']:
                    # 订单已成交
                    self._handle_filled_entry_order(order_id, order_info)
                    orders_to_remove.append(order_id)

                elif order_status in [self.ORDER_STATUS['CANCELLED'],
                                    self.ORDER_STATUS['REJECTED'],
                                    self.ORDER_STATUS['ERROR']]:
                    # 订单失败
                    self._handle_failed_entry_order(order_id, order_info, order_status)
                    orders_to_remove.append(order_id)

                # 其他状态继续等待

            except Exception as e:
                LogError(f"检查开仓订单 {order_id} 状态异常: {str(e)}")

        # 移除已处理的订单
        for order_id in orders_to_remove:
            self.pending_entry_orders.pop(order_id, None)

    def _check_exit_orders(self, current_time):
        """检查平仓订单状态"""
        orders_to_remove = []

        for order_id, order_info in list(self.pending_exit_orders.items()):
            try:
                # 检查订单是否超时
                if current_time - order_info['timestamp'] > self.order_timeout:
                    LogWarn(f"平仓订单超时: {order_id}")
                    self._handle_timeout_order(order_id, order_info, 'exit')
                    orders_to_remove.append(order_id)
                    continue

                # 获取订单状态
                order_status = A_OrderStatus(order_id)

                if order_status == self.ORDER_STATUS['FILLED']:
                    # 订单已成交
                    self._handle_filled_exit_order(order_id, order_info)
                    orders_to_remove.append(order_id)

                elif order_status in [self.ORDER_STATUS['CANCELLED'],
                                    self.ORDER_STATUS['REJECTED'],
                                    self.ORDER_STATUS['ERROR']]:
                    # 订单失败
                    self._handle_failed_exit_order(order_id, order_info, order_status)
                    orders_to_remove.append(order_id)

                # 其他状态继续等待

            except Exception as e:
                LogError(f"检查平仓订单 {order_id} 状态异常: {str(e)}")

        # 移除已处理的订单
        for order_id in orders_to_remove:
            self.pending_exit_orders.pop(order_id, None)

    def _handle_filled_entry_order(self, order_id, order_info):
        """处理已成交的开仓订单"""
        try:
            # 获取成交信息
            filled_list = A_OrderFilledList(order_id)
            if filled_list:
                filled_info = filled_list[0]
                filled_price = filled_info.get('MatchPrice', order_info['price'])
                filled_time = filled_info.get('MatchDateTime', '')

                LogInfo(f"开仓订单已成交 - 订单号: {order_id}, "
                       f"方向: {order_info['direction']}, "
                       f"成交价: {filled_price:.2f}, "
                       f"数量: {order_info['quantity']}")

                # 更新状态
                if order_info['direction'] == 'BUY':
                    self.BKS = 2  # 标记为已完成
                else:
                    self.SKS = 2  # 标记为已完成

                self.stats['successful_orders'] += 1

        except Exception as e:
            LogError(f"处理已成交开仓订单异常: {str(e)}")

    def _handle_filled_exit_order(self, order_id, order_info):
        """处理已成交的平仓订单"""
        try:
            # 获取成交信息
            filled_list = A_OrderFilledList(order_id)
            if filled_list:
                filled_info = filled_list[0]
                filled_price = filled_info.get('MatchPrice', order_info['price'])
                filled_time = filled_info.get('MatchDateTime', '')

                LogInfo(f"平仓订单已成交 - 订单号: {order_id}, "
                       f"方向: {order_info['direction']}, "
                       f"成交价: {filled_price:.2f}, "
                       f"数量: {order_info['quantity']}, "
                       f"类型: {order_info.get('exit_type', '平仓')}")

                # 更新状态
                if order_info['original_signal'] == 'BP':
                    self.BPS = 2  # 平空完成
                    if self.SKS == 1:
                        self.SKS = 2  # 空头交易完成
                elif order_info['original_signal'] == 'SP':
                    self.SPS = 2  # 平多完成
                    if self.BKS == 1:
                        self.BKS = 2  # 多头交易完成

                self.stats['successful_orders'] += 1

        except Exception as e:
            LogError(f"处理已成交平仓订单异常: {str(e)}")

    def _handle_failed_entry_order(self, order_id, order_info, order_status):
        """处理失败的开仓订单"""
        try:
            LogWarn(f"开仓订单失败 - 订单号: {order_id}, "
                   f"状态: {order_status}, "
                   f"方向: {order_info['direction']}")

            # 重置状态，允许重新开仓
            if order_info['direction'] == 'BUY':
                self.BKS = 0
            else:
                self.SKS = 0

            self.stats['failed_orders'] += 1

            # 可以在这里实现重试逻辑
            if order_info['retry_count'] < self.max_retry_count:
                self._retry_entry_order(order_info)

        except Exception as e:
            LogError(f"处理失败开仓订单异常: {str(e)}")

    def _handle_failed_exit_order(self, order_id, order_info, order_status):
        """处理失败的平仓订单"""
        try:
            LogWarn(f"平仓订单失败 - 订单号: {order_id}, "
                   f"状态: {order_status}, "
                   f"方向: {order_info['direction']}")

            # 重置状态，允许重新平仓
            if order_info['original_signal'] == 'BP':
                self.BPS = 0
            elif order_info['original_signal'] == 'SP':
                self.SPS = 0

            self.stats['failed_orders'] += 1

            # 可以在这里实现重试逻辑
            if order_info['retry_count'] < self.max_retry_count:
                self._retry_exit_order(order_info)

        except Exception as e:
            LogError(f"处理失败平仓订单异常: {str(e)}")

    def _handle_timeout_order(self, order_id, order_info, order_type):
        """处理超时订单"""
        try:
            LogWarn(f"{order_type}订单超时 - 订单号: {order_id}")

            # 尝试撤销订单
            try:
                A_DeleteOrder(order_id)
                LogInfo(f"已撤销超时订单: {order_id}")
            except:
                LogWarn(f"撤销超时订单失败: {order_id}")

            # 重置相关状态
            if order_type == 'entry':
                if order_info['direction'] == 'BUY':
                    self.BKS = 0
                else:
                    self.SKS = 0
            else:  # exit
                if order_info['original_signal'] == 'BP':
                    self.BPS = 0
                elif order_info['original_signal'] == 'SP':
                    self.SPS = 0

            self.stats['timeout_orders'] += 1

        except Exception as e:
            LogError(f"处理超时订单异常: {str(e)}")

    def _cleanup_timeout_orders(self, current_time):
        """清理超时订单"""
        # 这个函数在_check_entry_orders和_check_exit_orders中已经处理了
        # 保留作为扩展接口
        pass

    def _retry_entry_order(self, order_info):
        """重试开仓订单"""
        try:
            if order_info['retry_count'] >= self.max_retry_count:
                return

            LogInfo(f"重试开仓订单 - 方向: {order_info['direction']}, "
                   f"重试次数: {order_info['retry_count'] + 1}")

            # 重新发送订单
            if order_info['original_signal'] == 'BK':
                self.BKS = 0  # 重置状态
                self.tim_trigger_entry(True, False, order_info['quantity'], order_info['symbol'])
            elif order_info['original_signal'] == 'SK':
                self.SKS = 0  # 重置状态
                self.tim_trigger_entry(False, True, order_info['quantity'], order_info['symbol'])

            self.stats['retry_orders'] += 1

        except Exception as e:
            LogError(f"重试开仓订单异常: {str(e)}")

    def _retry_exit_order(self, order_info):
        """重试平仓订单"""
        try:
            if order_info['retry_count'] >= self.max_retry_count:
                return

            LogInfo(f"重试平仓订单 - 方向: {order_info['direction']}, "
                   f"重试次数: {order_info['retry_count'] + 1}")

            # 重新发送订单
            if order_info['original_signal'] == 'BP':
                self.BPS = 0  # 重置状态
                self.tim_trigger_exit(True, False, order_info['quantity'], order_info['symbol'])
            elif order_info['original_signal'] == 'SP':
                self.SPS = 0  # 重置状态
                self.tim_trigger_exit(False, True, order_info['quantity'], order_info['symbol'])

            self.stats['retry_orders'] += 1

        except Exception as e:
            LogError(f"重试平仓订单异常: {str(e)}")

    # ==================== 历史阶段兼容函数 ====================

    def his_trigger(self, BK, SK, qty, itk, tcode=None):
        """
        历史开仓兼容函数

        参数:
            BK: 多头开仓信号
            SK: 空头开仓信号
            qty: 开仓数量
            itk: 超价跳数
            tcode: 合约代码
        """
        if tcode is None:
            tcode = self.contract_code

        try:
            if BK and self.BKS == 0:
                price = Close()[-1] + itk * PriceTick(tcode)
                Buy(qty, price)
                self.BKS = 1
                LogInfo(f"历史多头开仓 - 价格: {price:.2f}, 数量: {qty}")
                return True

            elif SK and self.SKS == 0:
                price = Close()[-1] - itk * PriceTick(tcode)
                SellShort(qty, price)
                self.SKS = 1
                LogInfo(f"历史空头开仓 - 价格: {price:.2f}, 数量: {qty}")
                return True

            return False

        except Exception as e:
            LogError(f"历史开仓异常: {str(e)}")
            return False

    def his_trigger_Exit(self, BP, SP, otk, clots, tcode=None):
        """
        历史平仓兼容函数

        参数:
            BP: 平空头信号
            SP: 平多头信号
            otk: 超价跳数
            clots: 平仓数量
            tcode: 合约代码
        """
        if tcode is None:
            tcode = self.contract_code

        try:
            if BP and self.BPS == 0 and SellPosition(tcode) > 0:
                actual_lots = min(clots, SellPosition(tcode))
                price = Close()[-1] + otk * PriceTick(tcode)
                BuyToCover(actual_lots, price)
                self.BPS = 1
                if self.SKS == 1:
                    self.SKS = 2
                LogInfo(f"历史平空 - 价格: {price:.2f}, 数量: {actual_lots}")
                return True

            elif SP and self.SPS == 0 and BuyPosition(tcode) > 0:
                actual_lots = min(clots, BuyPosition(tcode))
                price = Close()[-1] - otk * PriceTick(tcode)
                Sell(actual_lots, price)
                self.SPS = 1
                if self.BKS == 1:
                    self.BKS = 2
                LogInfo(f"历史平多 - 价格: {price:.2f}, 数量: {actual_lots}")
                return True

            return False

        except Exception as e:
            LogError(f"历史平仓异常: {str(e)}")
            return False

    # ==================== 统一接口函数 ====================

    def trigger_entry(self, BK, SK, qty, itk=None, tcode=None):
        """
        统一开仓接口 - 自动判断实时/历史阶段

        参数:
            BK: 多头开仓信号
            SK: 空头开仓信号
            qty: 开仓数量
            itk: 超价跳数（历史阶段使用）
            tcode: 合约代码
        """
        if self.is_real_time():
            # 实时阶段
            return self.tim_trigger_entry(BK, SK, qty, tcode)
        else:
            # 历史阶段
            if itk is None:
                itk = self.entry_tick_offset
            return self.his_trigger(BK, SK, qty, itk, tcode)

    def trigger_exit(self, BP, SP, clots, otk=None, tcode=None):
        """
        统一平仓接口 - 自动判断实时/历史阶段

        参数:
            BP: 平空头信号
            SP: 平多头信号
            clots: 平仓数量
            otk: 超价跳数（历史阶段使用）
            tcode: 合约代码
        """
        if self.is_real_time():
            # 实时阶段
            return self.tim_trigger_exit(BP, SP, clots, tcode)
        else:
            # 历史阶段
            if otk is None:
                otk = self.exit_tick_offset
            return self.his_trigger_Exit(BP, SP, otk, clots, tcode)

    # ==================== 辅助功能函数 ====================

    def cancel_all_pending_orders(self):
        """取消所有待处理订单"""
        try:
            canceled_count = 0

            # 取消开仓订单
            for order_id in list(self.pending_entry_orders.keys()):
                try:
                    A_DeleteOrder(order_id)
                    canceled_count += 1
                    LogInfo(f"已取消开仓订单: {order_id}")
                except:
                    LogWarn(f"取消开仓订单失败: {order_id}")

            # 取消平仓订单
            for order_id in list(self.pending_exit_orders.keys()):
                try:
                    A_DeleteOrder(order_id)
                    canceled_count += 1
                    LogInfo(f"已取消平仓订单: {order_id}")
                except:
                    LogWarn(f"取消平仓订单失败: {order_id}")

            # 清空跟踪字典
            self.pending_entry_orders.clear()
            self.pending_exit_orders.clear()

            # 重置状态
            self.reset_trading_status()

            LogInfo(f"已取消 {canceled_count} 个待处理订单")
            return canceled_count

        except Exception as e:
            LogError(f"取消待处理订单异常: {str(e)}")
            return 0

    def force_close_all_positions(self):
        """强制平仓所有持仓"""
        try:
            if self.is_real_time():
                # 实时阶段
                long_position = A_BuyPosition(self.contract_code)
                short_position = A_SellPosition(self.contract_code)

                if long_position > 0:
                    self.tim_trigger_exit(False, True, long_position)
                    LogInfo(f"强制平多: {long_position}手")

                if short_position > 0:
                    self.tim_trigger_exit(True, False, short_position)
                    LogInfo(f"强制平空: {short_position}手")
            else:
                # 历史阶段
                long_position = BuyPosition(self.contract_code)
                short_position = SellPosition(self.contract_code)

                if long_position > 0:
                    self.his_trigger_Exit(False, True, self.exit_tick_offset, long_position)
                    LogInfo(f"强制平多: {long_position}手")

                if short_position > 0:
                    self.his_trigger_Exit(True, False, self.exit_tick_offset, short_position)
                    LogInfo(f"强制平空: {short_position}手")

        except Exception as e:
            LogError(f"强制平仓异常: {str(e)}")

    def get_pending_orders_count(self):
        """获取待处理订单数量"""
        return {
            'entry_orders': len(self.pending_entry_orders),
            'exit_orders': len(self.pending_exit_orders),
            'total': len(self.pending_entry_orders) + len(self.pending_exit_orders)
        }

    def get_trading_status(self):
        """获取交易状态"""
        return {
            'BKS': self.BKS,  # 多头开仓状态
            'SKS': self.SKS,  # 空头开仓状态
            'BPS': self.BPS,  # 多头平仓状态
            'SPS': self.SPS,  # 空头平仓状态
            'can_open_long': self.BKS == 0,
            'can_open_short': self.SKS == 0,
            'can_close_long': self.SPS == 0,
            'can_close_short': self.BPS == 0
        }

    def get_statistics(self):
        """获取统计信息"""
        total = self.stats['total_orders']
        success_rate = (self.stats['successful_orders'] / total * 100) if total > 0 else 0

        return {
            **self.stats,
            'success_rate': f"{success_rate:.2f}%",
            'pending_orders': self.get_pending_orders_count()
        }

    def log_status(self):
        """记录当前状态"""
        status = self.get_trading_status()
        pending = self.get_pending_orders_count()
        stats = self.get_statistics()

        LogInfo(f"交易状态 - BKS:{status['BKS']} SKS:{status['SKS']} BPS:{status['BPS']} SPS:{status['SPS']}")
        LogInfo(f"待处理订单 - 开仓:{pending['entry_orders']} 平仓:{pending['exit_orders']} 总计:{pending['total']}")
        LogInfo(f"统计信息 - 总订单:{stats['total_orders']} 成功:{stats['successful_orders']} 成功率:{stats['success_rate']}")

# ==================== 使用示例 ====================

def create_trading_manager_example():
    """创建交易管理器示例"""

    # 创建交易管理器实例
    trading_manager = AdvancedTradingManager(
        contract_code='SHFE|F|RB|MAIN',
        entry_tick_offset=2,
        exit_tick_offset=1,
        order_timeout=300,  # 5分钟超时
        max_retry_count=2
    )

    return trading_manager

def strategy_main_loop_example(trading_manager):
    """策略主循环示例"""

    # 每次策略触发时的标准流程
    try:
        # 1. 检查和更新订单状态（必须调用）
        trading_manager.check_and_update_orders()

        # 2. 生成交易信号
        long_signal = False  # 你的多头信号逻辑
        short_signal = False  # 你的空头信号逻辑
        exit_long_signal = False  # 平多信号
        exit_short_signal = False  # 平空信号

        # 3. 执行交易
        if long_signal or short_signal:
            results = trading_manager.trigger_entry(long_signal, short_signal, 1)
            if results:
                LogInfo(f"开仓订单结果: {results}")

        if exit_long_signal or exit_short_signal:
            results = trading_manager.trigger_exit(exit_short_signal, exit_long_signal, 1)
            if results:
                LogInfo(f"平仓订单结果: {results}")

        # 4. 定期记录状态（可选）
        if CurrentBar() % 100 == 0:
            trading_manager.log_status()

    except Exception as e:
        LogError(f"策略主循环异常: {str(e)}")

# ==================== 全局实例（可选） ====================

# 可以创建全局实例供策略使用
# global_trading_manager = create_trading_manager_example()
