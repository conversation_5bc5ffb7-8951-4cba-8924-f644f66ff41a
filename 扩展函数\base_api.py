from c_base_api import *
from statistics import *
# ////////////////////全局函数定义//////////////
# K线函数
def Date(contractNo='', kLineType='', kLineValue=0):
    return E_Date(contractNo, kLineType, kLineValue)


def D(contractNo='', kLineType='', kLineValue=0):
    return E_Date(contractNo, kLineType, kLineValue)


def Time(contractNo='', kLineType='', kLineValue=0):
    return E_Time(contractNo, kLineType, kLineValue)


def T(contractNo='', kLineType='', kLineValue=0):
    return E_Time(contractNo, kLineType, kLineValue)


def Open(contractNo='', kLineType='', kLineValue=0):
    return E_Open(contractNo, kLineType, kLineValue)


def O(contractNo='', kLineType='', kLineValue=0):
    return L_Open(contractNo, kLineType, kLineValue)


def High(contractNo='', kLineType='', kLineValue=0):
    return E_High(contractNo, kLineType, kLineValue)


def H(contractNo='', kLineType='', kLineValue=0):
    return E_High(contractNo, kLineType, kLineValue)


def Low(contractNo='', kLineType='', kLineValue=0):
    return E_Low(contractNo, kLineType, kLineValue)


def L(contractNo='', kLineType='', kLineValue=0):
    return E_Low(contractNo, kLineType, kLineValue)


def Close(contractNo='', kLineType='', kLineValue=0):
    return E_Close(contractNo, kLineType, kLineValue)


def OpenD(daysAgo=0, contractNo=''):
    return E_OpenD(daysAgo, contractNo)


def CloseD(daysAgo=0, contractNo=''):
    return E_CloseD(daysAgo, contractNo)


def HighD(daysAgo=0, contractNo=''):
    return E_HighD(daysAgo, contractNo)


def LowD(daysAgo=0, contractNo=''):
    return E_LowD(daysAgo, contractNo)


def C(contractNo='', kLineType='', kLineValue=0):
    return E_Close(contractNo, kLineType, kLineValue)


def Vol(contractNo='', kLineType='', kLineValue=0):
    return E_Vol(contractNo, kLineType, kLineValue)


def V(contractNo='', kLineType='', kLineValue=0):
    return E_Vol(contractNo, kLineType, kLineValue)


def OpenInt(contractNo='', kLineType='', kLineValue=0):
    return E_OpenInt(contractNo, kLineType, kLineValue)


def TradeDate(contractNo='', kLineType='', kLineValue=0):
    return E_TradeDate(contractNo, kLineType, kLineValue)


def BarCount(contractNo='', kLineType='', kLineValue=0):
    return E_BarCount(contractNo, kLineType, kLineValue)


def CurrentBar(contractNo='', kLineType='', kLineValue=0):
    return E_CurrentBar(contractNo, kLineType, kLineValue)


def CurrentBarEntity(contractNo='', kLineType='', kLineValue=0):
    return E_CurrentBarEntity(contractNo, kLineType, kLineValue)


def BarStatus(contractNo='', kLineType='', kLineValue=0):
    return E_BarStatus(contractNo, kLineType, kLineValue)


def HistoryDataExist(contractNo='', kLineType='', kLineValue=0):
    return E_HistoryDataExist(contractNo, kLineType, kLineValue)


def HisData(dataType, kLineType='', kLineValue=0, contractNo='', maxLength=100):
    return E_HisData(dataType, kLineType, kLineValue, contractNo, maxLength)

def HisBarsInfo(contractNo='', kLineType='', kLineValue=0, maxLength=100):
    return E_HisBarsInfo(contractNo, kLineType, kLineValue, maxLength)


# 即时行情
def Q_UpdateTime(contractNo=''):
    return E_Q_UpdateTime(contractNo)


def Q_PreClose(contractNo=''):
    return E_Q_PreClose(contractNo)


def Q_SettlePrice(contractNo=''):
    return E_Q_SettlePrice(contractNo)


def Q_BuyTotalVol(contractNo=''):
    return E_Q_BuyTotalVol(contractNo)


def Q_SellTotalVol(contractNo=''):
    return E_Q_SellTotalVol(contractNo)


def Q_AskPrice(contractNo='', level=1):
    return E_Q_AskPrice(contractNo, level)


def Q_AskPriceFlag(contractNo=''):
    return E_Q_AskPriceFlag(contractNo)


def Q_AskVol(contractNo='', level=1):
    return E_Q_AskVol(contractNo, level)


def Q_AvgPrice(contractNo=''):
    return E_Q_AvgPrice(contractNo)


def Q_BidPrice(contractNo='', level=1):
    return E_Q_BidPrice(contractNo, level)


def Q_BidPriceFlag(contractNo=''):
    return E_Q_BidPriceFlag(contractNo)


def Q_BidVol(contractNo='', level=1):
    return E_Q_BidVol(contractNo, level)


def Q_Close(contractNo=''):
    return E_Q_Close(contractNo)


def Q_High(contractNo=''):
    return E_Q_High(contractNo)


def Q_HisHigh(contractNo=''):
    return E_Q_HisHigh(contractNo)


def Q_HisLow(contractNo=''):
    return E_Q_HisLow(contractNo)


def Q_InsideVol(contractNo=''):
    return E_Q_InsideVol(contractNo)


def Q_Last(contractNo=''):
    return E_Q_Last(contractNo)


def Q_LastDate(contractNo=''):
    return E_Q_LastDate(contractNo)


def Q_LastTime(contractNo=''):
    return E_Q_LastTime(contractNo)


def Q_LastVol(contractNo=''):
    return E_Q_LastVol(contractNo)


def Q_Low(contractNo=''):
    return E_Q_Low(contractNo)


def Q_LowLimit(contractNo=''):
    return E_Q_LowLimit(contractNo)


def Q_Open(contractNo=''):
    return E_Q_Open(contractNo)


def Q_OpenInt(contractNo=''):
    return E_Q_OpenInt(contractNo)


def Q_OpenIntFlag(contractNo=''):
    return E_Q_OpenIntFlag(contractNo)


def Q_OutsideVol(contractNo=''):
    return E_Q_OutsideVol(contractNo)


def Q_PreOpenInt(contractNo=''):
    return E_Q_PreOpenInt(contractNo)


def Q_PreSettlePrice(contractNo=''):
    return E_Q_PreSettlePrice(contractNo)


def Q_PriceChg(contractNo=''):
    return E_Q_PriceChg(contractNo)


def Q_PriceChgRadio(contractNo=''):
    return E_Q_PriceChgRadio(contractNo)


def Q_TodayEntryVol(contractNo=''):
    return E_Q_TodayEntryVol(contractNo)


def Q_TodayExitVol(contractNo=''):
    return E_Q_TodayExitVol(contractNo)


def Q_TotalVol(contractNo=''):
    return E_Q_TotalVol(contractNo)


def Q_TurnOver(contractNo=''):
    return E_Q_TurnOver(contractNo)


def Q_UpperLimit(contractNo=''):
    return E_Q_UpperLimit(contractNo)


def Q_TheoryPrice(contractNo=''):
    return E_Q_TheoryPrice(contractNo)


def Q_Sigma(contractNo=''):
    return E_Q_Sigma(contractNo)


def Q_Delta(contractNo=''):
    return E_Q_Delta(contractNo)


def Q_Gamma(contractNo=''):
    return E_Q_Gamma(contractNo)


def Q_Vega(contractNo=''):
    return E_Q_Vega(contractNo)


def Q_Theta(contractNo=''):
    return E_Q_Theta(contractNo)


def Q_Rho(contractNo=''):
    return E_Q_Rho(contractNo)


def QuoteDataExist(contractNo=''):
    return E_QuoteDataExist(contractNo)


def CalcTradeDate(contractNo='', dateTimeStamp=0):
    return E_CalcTradeDate(contractNo, dateTimeStamp)


# 策略状态
def AvgEntryPrice(contractNo='', userNo='', hedge=''):
    return E_AvgEntryPrice(contractNo, userNo, hedge)


def BarsSinceEntry(contractNo='', userNo='', hedge=''):
    return E_BarsSinceEntry(contractNo, userNo, hedge)


def BarsSinceExit(contractNo='', userNo='', hedge=''):
    return E_BarsSinceExit(contractNo, userNo, hedge)


def BarsSinceLastEntry(contractNo='', userNo='', hedge=''):
    return E_BarsSinceLastEntry(contractNo, userNo, hedge)


def BarsSinceLastBuyEntry(contractNo='', userNo='', hedge=''):
    return E_BarsSinceLastBuyEntry(contractNo, userNo, hedge)


def BarsSinceLastSellEntry(contractNo='', userNo='', hedge=''):
    return E_BarsSinceLastSellEntry(contractNo, userNo, hedge)


def BarsSinceToday(contractNo='', kLineType='', kLineValue=0):
    return E_BarsSinceToday(contractNo, kLineType, kLineValue)


def ContractProfit(contractNo='', userNo='', hedge=''):
    return E_ContractProfit(contractNo, userNo, hedge)


def CurrentContracts(contractNo='', userNo='', hedge=''):
    return E_CurrentContracts(contractNo, userNo, hedge)


def BuyPosition(contractNo='', userNo='', hedge=''):
    return E_BuyPosition(contractNo, userNo, hedge)


def SellPosition(contractNo='', userNo='', hedge=''):
    return E_SellPosition(contractNo, userNo, hedge)


def EntryDate(contractNo='', userNo='', hedge=''):
    return E_EntryDate(contractNo, userNo, hedge)


def EntryPrice(contractNo='', userNo='', hedge=''):
    return E_EntryPrice(contractNo, userNo, hedge)


def EntryTime(contractNo='', userNo='', hedge=''):
    return E_EntryTime(contractNo, userNo, hedge)


def ExitDate(contractNo='', userNo='', hedge=''):
    return E_ExitDate(contractNo, userNo, hedge)


def ExitPrice(contractNo='', userNo='', hedge=''):
    return E_ExitPrice(contractNo, userNo, hedge)


def ExitTime(contractNo='', userNo='', hedge=''):
    return E_ExitTime(contractNo, userNo, hedge)


def LastEntryDate(contractNo='', userNo='', hedge=''):
    return E_LastEntryDate(contractNo, userNo, hedge)


def LastEntryPrice(contractNo='', userNo='', hedge=''):
    return E_LastEntryPrice(contractNo, userNo, hedge)


def LastBuyEntryPrice(contractNo='', userNo='', hedge=''):
    return E_LastBuyEntryPrice(contractNo, userNo, hedge)


def LastSellEntryPrice(contractNo='', userNo='', hedge=''):
    return E_LastSellEntryPrice(contractNo, userNo, hedge)


def HighestSinceLastBuyEntry(contractNo='', userNo='', hedge=''):
    return E_HighestSinceLastBuyEntry(contractNo, userNo, hedge)


def HighestSinceLastSellEntry(contractNo='', userNo='', hedge=''):
    return E_HighestSinceLastSellEntry(contractNo, userNo, hedge)


def LowestSinceLastBuyEntry(contractNo='', userNo='', hedge=''):
    return E_LowestSinceLastBuyEntry(contractNo, userNo, hedge)


def LowestSinceLastSellEntry(contractNo='', userNo='', hedge=''):
    return E_LowestSinceLastSellEntry(contractNo, userNo, hedge)


def LastEntryTime(contractNo='', userNo='', hedge=''):
    return E_LastEntryTime(contractNo, userNo, hedge)


def MarketPosition(contractNo='', userNo='', hedge=''):
    return E_MarketPosition(contractNo, userNo, hedge)


def PositionProfit(contractNo='', userNo='', hedge=''):
    return E_PositionProfit(contractNo, userNo, hedge)


def BarsLast(condition):
    return E_BarsLast(condition)


# 策略性能
def Available():
    return E_Available()


def CurrentEquity():
    return E_CurrentEquity()


def FloatProfit(contractNo=''):
    return E_FloatProfit(contractNo)


def GrossLoss():
    return E_GrossLoss()


def GrossProfit():
    return E_GrossProfit()


def Margin(contractNo=''):
    return E_Margin(contractNo)


def NetProfit(contractNo=''):
    return E_NetProfit(contractNo)


def NumEvenTrades():
    return E_NumEvenTrades()


def NumLosTrades():
    return E_NumLosTrades()


def NumWinTrades():
    return E_NumWinTrades()


def NumAllTimes():
    return E_NumAllTimes()


def NumWinTimes():
    return E_NumWinTimes()


def NumLoseTimes():
    return E_NumLoseTimes()


def NumEventTimes():
    return E_NumEventTimes()


def PercentProfit():
    return E_PercentProfit()


def TradeCost(contractNo=''):
    return E_TradeCost(contractNo)


def TotalTrades(contractNo=''):
    return E_TotalTrades(contractNo)


# 账户函数
def A_AccountID():
    return E_A_AccountID()


def A_AllAccountID():
    return E_A_AllAccountID()


def A_GetAllPositionSymbol(userNo=''):
    return E_A_GetAllPositionSymbol(userNo)


def A_Cost(userNo=''):
    return E_A_Cost(userNo)


def A_Assets(userNo=''):
    return E_A_Assets(userNo)


def A_Available(userNo=''):
    return E_A_Available(userNo)


def A_Margin(userNo=''):
    return E_A_Margin(userNo)


def A_ProfitLoss(userNo=''):
    return E_A_ProfitLoss(userNo)


def A_PerProfitLoss(userNo=''):
    return E_A_PerProfitLoss(userNo)


def A_CoverProfit(userNo=''):
    return E_A_CoverProfit(userNo)


def A_PerCoverProfit(userNo=''):
    return E_A_PerCoverProfit(userNo)


def A_TotalFreeze(userNo=''):
    return E_A_TotalFreeze(userNo)


def A_BuyAvgPrice(contractNo='', userNo='', hedge=''):
    return E_A_BuyAvgPrice(contractNo, userNo, hedge)


def A_BuyPosition(contractNo='', userNo='', hedge=''):
    return E_A_BuyPosition(contractNo, userNo, hedge)


def A_BuyPositionCanCover(contractNo='', userNo='', hedge=''):
    return E_A_BuyPositionCanCover(contractNo, userNo, hedge)


def A_BuyProfitLoss(contractNo='', userNo='', hedge=''):
    return E_A_BuyProfitLoss(contractNo, userNo, hedge)


def A_SellAvgPrice(contractNo='', userNo='', hedge=''):
    return E_A_SellAvgPrice(contractNo, userNo, hedge)


def A_SellPosition(contractNo='', userNo='', hedge=''):
    return E_A_SellPosition(contractNo, userNo, hedge)


def A_SellPositionCanCover(contractNo='', userNo='', hedge=''):
    return E_A_SellPositionCanCover(contractNo, userNo, hedge)


def A_SellProfitLoss(contractNo='', userNo='', hedge=''):
    return E_A_SellProfitLoss(contractNo, userNo, hedge)


def A_TotalAvgPrice(contractNo='', userNo='', hedge=''):
    return E_A_TotalAvgPrice(contractNo, userNo, hedge)


def A_TotalPosition(contractNo='', userNo='', hedge=''):
    return E_A_TotalPosition(contractNo, userNo, hedge)


def A_TotalProfitLoss(contractNo='', userNo='', hedge=''):
    return E_A_TotalProfitLoss(contractNo, userNo, hedge)


def A_TodayBuyPosition(contractNo='', userNo='', hedge=''):
    return E_A_TodayBuyPosition(contractNo, userNo, hedge)


def A_TodaySellPosition(contractNo='', userNo='', hedge=''):
    return E_A_TodaySellPosition(contractNo, userNo, hedge)


def A_OrderBuyOrSell(localOrderId):
    return E_A_OrderBuyOrSell('', localOrderId)


def A_OrderEntryOrExit(localOrderId):
    return E_A_OrderEntryOrExit('', localOrderId)


def A_OrderFilledLot(localOrderId):
    return E_A_OrderFilledLot('', localOrderId)


def A_OrderFilledPrice(localOrderId):
    return E_A_OrderFilledPrice('', localOrderId)


def A_OrderFilledList(localOrderId=0):
    return E_A_OrderFilledList('', localOrderId)


def A_OrderLot(localOrderId):
    return E_A_OrderLot('', localOrderId)


def A_OrderPrice(localOrderId):
    return E_A_OrderPrice('', localOrderId)


def A_OrderStatus(localOrderId):
    return E_A_OrderStatus('', localOrderId)


def A_OrderIsClose(localOrderId):
    return E_A_OrderIsClose('', localOrderId)


def A_OrderTime(localOrderId):
    return E_A_OrderTime('', localOrderId)


def A_FirstOrderNo(contractNo='', userNo=''):
    return E_A_FirstOrderNo(contractNo, userNo)


def A_NextOrderNo(localOrderId=0, contractNo='', userNo=''):
    return E_A_NextOrderNo(localOrderId, contractNo, userNo)


def A_LastOrderNo(contractNo='', userNo=''):
    return E_A_LastOrderNo(contractNo, userNo)


def A_FirstQueueOrderNo(contractNo='', userNo=''):
    return E_A_FirstQueueOrderNo(contractNo, userNo)


def A_NextQueueOrderNo(localOrderId=0, contractNo='', userNo=''):
    return E_A_NextQueueOrderNo(localOrderId, contractNo, userNo)


def A_AllQueueOrderNo(contractNo='', userNo=''):
    return E_A_AllQueueOrderNo(contractNo, userNo)


def A_LatestFilledTime(contractNo='', userNo=''):
    return E_A_LatestFilledTime(contractNo, userNo)


def A_AllOrderNo(contractNo='', userNo=''):
    return E_A_AllOrderNo(contractNo, userNo)


def A_OrderContractNo(localOrderId=0, userNo=''):
    return E_A_OrderContractNo(userNo, localOrderId)


def A_SendOrder(orderDirct, entryOrExit, orderQty, orderPrice, contractNo='', userNo='', orderType='2', validType='0',
                hedge='T', triggerType='N', triggerMode='N', triggerCondition='N', triggerPrice=0):
    return E_A_SendOrder(userNo, contractNo, orderDirct, entryOrExit, orderQty, orderPrice, orderType, validType,
                               hedge, triggerType, triggerMode, triggerCondition, triggerPrice)


def A_ModifyOrder(localOrderId, orderQty, orderPrice):
    return E_A_ModifyOrder(localOrderId, orderQty, orderPrice)


def A_DeleteOrder(localOrderId):
    return E_A_DeleteOrder('', localOrderId)


def A_GetOrderNo(localOrderId):
    return E_A_GetOrderNo(localOrderId)


def DeleteAllOrders(contractNo='', userNo=''):
    return E_DeleteAllOrders(contractNo, userNo)


# 策略交易
def Buy(orderQty=0, orderPrice=0, contractNo='', needCover=True, userNo='', hedge=''):
    return E_Buy(orderQty, orderPrice, contractNo, needCover, userNo, hedge)


def BuyToCover(orderQty=0, orderPrice=0, contractNo='', userNo='', coverFlag='A', hedge=''):
    return E_BuyToCover(orderQty, orderPrice, contractNo, userNo, coverFlag, hedge)


def Sell(orderQty=0, orderPrice=0, contractNo='', userNo='', coverFlag='A', hedge=''):
    return E_Sell(orderQty, orderPrice, contractNo, userNo, coverFlag, hedge)


def SellShort(orderQty=0, orderPrice=0, contractNo='', needCover=True, userNo='', hedge=''):
    return E_SellShort(orderQty, orderPrice, contractNo, needCover, userNo, hedge)


def StartTrade():
    return E_StartTrade()


def StopTrade():
    return E_StopTrade()


def IsTradeAllowed():
    return E_IsTradeAllowed()

def UnloadStrategy():
    return E_UnloadStrategy()

def ReloadStrategy():
    return E_ReloadStrategy()

def QuoteSvrState():
    return E_ServerConnectState('Q', '')
    
def TradeSvrState(userNo=''):
    return E_ServerConnectState('T', userNo)

# 枚举函数
def Enum_Buy():
    return E_Enum_Buy()


def Enum_Sell():
    return E_Enum_Sell()


def Enum_Entry():
    return E_Enum_Entry()


def Enum_Exit():
    return E_Enum_Exit()


def Enum_ExitToday():
    return E_Enum_ExitToday()


def Enum_EntryExitIgnore():
    return E_Enum_EntryExitIgnore()


def Enum_Sended():
    return E_Enum_Sended()


def Enum_Accept():
    return E_Enum_Accept()


def Enum_Triggering():
    return E_Enum_Triggering()


def Enum_Active():
    return E_Enum_Active()


def Enum_Queued():
    return E_Enum_Queued()


def Enum_FillPart():
    return E_Enum_FillPart()


def Enum_Filled():
    return E_Enum_Filled()


def Enum_Canceling():
    return E_Enum_Canceling()


def Enum_Modifying():
    return E_Enum_Modifying()


def Enum_Canceled():
    return E_Enum_Canceled()


def Enum_PartCanceled():
    return E_Enum_PartCanceled()


def Enum_Fail():
    return E_Enum_Fail()


def Enum_Suspended():
    return E_Enum_Suspended()


def Enum_Apply():
    return E_Enum_Apply()


def Enum_Period_Tick():
    return E_Enum_Period_Tick()


# def Enum_Period_Dyna():
#     return E_Enum_Period_Dyna()
#
# def Enum_Period_Second():
#     return E_Enum_Period_Second()

def Enum_Period_Min():
    return E_Enum_Period_Min()


# def Enum_Period_Hour():
#     return E_Enum_Period_Hour()

def Enum_Period_Day():
    return E_Enum_Period_Day()


# def Enum_Period_Week():
#     return E_Enum_Period_Week()
#
# def Enum_Period_Month():
#     return E_Enum_Period_Month()
#
# def Enum_Period_Year():
#     return E_Enum_Period_Year()
#
# def Enum_Period_DayX():
#     return E_Enum_Period_DayX()

def RGB_Red():
    return E_RGB_Red()


def RGB_Green():
    return E_RGB_Green()


def RGB_Blue():
    return E_RGB_Blue()


def RGB_Purple():
    return E_RGB_Purple()


def RGB_Gray():
    return E_RGB_Gray()


def RGB_Brown():
    return E_RGB_Brown()


def RGB_Yellow():
    return E_RGB_Yellow()


def Enum_Order_Market():
    return E_Enum_Order_Market()


def Enum_Order_Limit():
    return E_Enum_Order_Limit()


def Enum_Order_MarketStop():
    return E_Enum_Order_MarketStop()


def Enum_Order_LimitStop():
    return E_Enum_Order_LimitStop()


def Enum_Order_Execute():
    return E_Enum_Order_Execute()


def Enum_Order_Abandon():
    return E_Enum_Order_Abandon()


def Enum_Order_Enquiry():
    return E_Enum_Order_Enquiry()


def Enum_Order_Offer():
    return E_Enum_Order_Offer()


def Enum_Order_Iceberg():
    return E_Enum_Order_Iceberg()


def Enum_Order_Ghost():
    return E_Enum_Order_Ghost()


def Enum_Order_Swap():
    return E_Enum_Order_Swap()


def Enum_Order_SpreadApply():
    return E_Enum_Order_SpreadApply()


def Enum_Order_HedgApply():
    return E_Enum_Order_HedgApply()


def Enum_Order_OptionAutoClose():
    return E_Enum_Order_OptionAutoClose()


def Enum_Order_FutureAutoClose():
    return E_Enum_Order_FutureAutoClose()


def Enum_Order_MarketOptionKeep():
    return E_Enum_Order_MarketOptionKeep()


def Enum_GFD():
    return E_Enum_GFD()


def Enum_GTC():
    return E_Enum_GTC()


def Enum_GTD():
    return E_Enum_GTD()


def Enum_IOC():
    return E_Enum_IOC()


def Enum_FOK():
    return E_Enum_FOK()


def Enum_Speculate():
    return E_Enum_Speculate()


def Enum_Hedge():
    return E_Enum_Hedge()


def Enum_Spread():
    return E_Enum_Spread()


def Enum_Market():
    return E_Enum_Market()


def Enum_Data_Close():
    return E_Enum_Data_Close()


def Enum_Data_Open():
    return E_Enum_Data_Open()


def Enum_Data_High():
    return E_Enum_Data_High()


def Enum_Data_Low():
    return E_Enum_Data_Low()


def Enum_Data_Median():
    return E_Enum_Data_Median()


def Enum_Data_Typical():
    return E_Enum_Data_Typical()


def Enum_Data_Weighted():
    return E_Enum_Data_Weighted()


def Enum_Data_Vol():
    return E_Enum_Data_Vol()


def Enum_Data_Opi():
    return E_Enum_Data_Opi()


def Enum_Data_Time():
    return E_Enum_Data_Time()


# 设置函数
def GetConfig():
    return E_GetConfig()


def SetUserNo(*args):
    return E_SetUserNo(args)


def SetBarInterval(contractNo, barType, barInterval, sampleConfig=2000, barDataLen=2000, isTrigger=False):
    return E_SetBarInterval(contractNo, barType, barInterval, sampleConfig, barDataLen, isTrigger)


# def SetSample(sampleType='C', sampleValue=2000):
#     return E_SetSample(sampleType, sampleValue)

def SetInitCapital(capital=10000000):
    return E_SetInitCapital(capital)


def SetMargin(type, value=0, contractNo=''):
    return E_SetMargin(type, value, contractNo)


def SetTradeFee(type, feeType, feeValue, contractNo=''):
    return E_SetTradeFee(type, feeType, feeValue, contractNo)


# def SetTradeMode(inActual, useSample, useReal):
# #     return E_SetTradeMode(inActual, useSample, useReal)

def SetActual():
    return E_SetActual()


def SetOrderWay(type):
    return E_SetOrderWay(type)


def SetTradeDirection(tradeDirection):
    return E_SetTradeDirection(tradeDirection)


def SetMinTradeQuantity(tradeQty=1):
    return E_SetMinTradeQuantity(tradeQty)


def SetHedge(hedge):
    return E_SetHedge(hedge)


def SetSlippage(slippage):
    return E_SetSlippage(slippage)


def SetTriggerType(type, value=None):
    return E_SetTriggerType(type, value)
    
def SetLogLevel(level):
    return E_SetLogLevel(level)

def SetAFunUseForHis():
    return E_SetAFunUseForHis()

def SetWinPoint(winPoint, nPriceType=0, nAddTick=0, contractNo=''):
    return E_SetWinPoint(winPoint, nPriceType, nAddTick, contractNo)


def SetStopPoint(stopPoint, nPriceType=0, nAddTick=0, contractNo=''):
    return E_SetStopPoint(stopPoint, nPriceType, nAddTick, contractNo)


def SetFloatStopPoint(startPoint, stopPoint, nPriceType=0, nAddTick=0, contractNo=''):
    return E_SetFloatStopPoint(startPoint, stopPoint, nPriceType, nAddTick, contractNo)


def SetStopWinKtBlack(op, kt):
    return E_SetStopWinKtBlack(op, kt)


def SubQuote(*args):
    return E_SubQuote(args)


def UnsubQuote(*args):
    return E_UnsubQuote(args)


# 属性函数
def BarInterval():
    return E_BarInterval()


def BarType():
    return E_BarType()


def BidAskSize(contractNo=''):
    return E_BidAskSize(contractNo)


def CanTrade(contractNo=''):
    return E_CanTrade(contractNo)


def ContractUnit(contractNo=''):
    return E_ContractUnit(contractNo)


def ExchangeName(contractNo=''):
    return E_ExchangeName(contractNo)


def ExchangeTime(exchangeNo):
    return E_ExchangeTime(exchangeNo)


def ExchangeStatus(exchangeNo):
    return E_ExchangeStatus(exchangeNo)


def CommodityStatus(commodityNo):
    return E_CommodityStatus(commodityNo)


def ExpiredDate(contractNo=''):
    return E_ExpiredDate(contractNo)


def GetSessionCount(contractNo=''):
    return E_GetSessionCount(contractNo)


def GetSessionEndTime(contractNo='', index=0):
    return E_GetSessionEndTime(contractNo, index)


def GetSessionStartTime(contractNo='', index=0):
    return E_GetSessionStartTime(contractNo, index)


def GetNextTimeInfo(contractNo, timeStr):
    return E_GetNextTimeInfo(contractNo, timeStr)


def TradeSessionBeginTime(contractNo='', tradeDate=0, index=0):
    return E_TradeSessionBeginTime(contractNo, tradeDate, index)


def TradeSessionEndTime(contractNo='', tradeDate=0, index=-1):
    return E_TradeSessionEndTime(contractNo, tradeDate, index)


def CurrentTime():
    return E_CurrentTime()


def CurrentDate():
    return E_CurrentDate()


def TimeDiff(datetime1, datetime2=-1.0):
    return E_TimeDiff(datetime1, datetime2)


def IsInSession(contractNo=''):
    return E_IsInSession(contractNo)


def MarginRatio(contractNo=''):
    return E_MarginRatio(contractNo)


def MaxBarsBack():
    return E_MaxBarsBack()


def MaxSingleTradeSize():
    return E_MaxSingleTradeSize()


def PriceTick(contractNo=''):
    return E_PriceTick(contractNo)


def OptionStyle(contractNo=''):
    return E_OptionStyle(contractNo)


def OptionType(contractNo=''):
    return E_OptionType(contractNo)


def PriceScale(contractNo=''):
    return E_PriceScale(contractNo)


def RelativeSymbol():
    return E_RelativeSymbol()


def StrikePrice():
    return E_StrikePrice()


def Symbol():
    return E_Symbol()


def SymbolName(contractNo=''):
    return E_SymbolName(contractNo)


def SymbolType(contractNo=''):
    return E_SymbolType(contractNo)


def GetTrendContract(contractNo=''):
    return E_GetTrendContract(contractNo)


# 其他函数
def PlotNumeric(name, value, color=0xdd0000, main=True, axis=False, barsback=0):
    return E_PlotNumeric(name, value, color, main, axis, barsback)


def PlotIcon(value, icon=0, main=True, barsback=0):
    return E_PlotIcon(value, icon, main, barsback)


def PlotDot(name, value, icon=0, color=0xdd0000, main=True, barsback=0):
    return E_PlotDot(name, value, icon, color, main, barsback)


def PlotBar(name, vol1, vol2, color=0xdd0000, main=True, filled=True, barsback=0):
    return E_PlotBar(name, vol1, vol2, color, main, filled, barsback)


def PlotText(value, text, color=0x999999, main=True, barsback=0):
    return E_PlotText(value, text, color, main, barsback)


def PlotVertLine(color=0xdd0000, main=True, axis=False, barsback=0):
    return E_PlotVertLine(color, main, axis, barsback)


def PlotPartLine(name, index1, price1, count, price2, color=0xdd0000, main=True, axis=False, width=1):
    return E_PlotPartLine(name, index1, price1, count, price2, color, main, axis, width)


def PlotStickLine(name, price1, price2, color=0xdd0000, main=True, axis=False, barsback=0):
    return E_PlotStickLine(name, price1, price2, color, main, axis, barsback)


def UnPlotText(main=True, barsback=0):
    return E_UnPlotText(main, barsback)


def UnPlotIcon(main=True, barsback=0):
    return E_UnPlotIcon(main, barsback)


def UnPlotVertLine(main=True, barsback=0):
    return E_UnPlotVertLine(main, barsback)


def UnPlotDot(name, main=True, barsback=0):
    return E_UnPlotDot(name, main, barsback)


def UnPlotBar(name, main=True, barsback=0):
    return E_UnPlotBar(name, main, barsback)


def UnPlotNumeric(name, main=True, barsback=0):
    return E_UnPlotNumeric(name, main, barsback)


def UnPlotPartLine(name, index1, count, main=True):
    return E_UnPlotPartLine(name, index1, count, main)


def UnPlotStickLine(name, main=True, barsback=0):
    return E_UnPlotStickLine(name, main, barsback)


def LogDebug(*args):
    return E_LogDebug(args)


def LogInfo(*args):
    return E_LogInfo(args)


def LogWarn(*args):
    return E_LogWarn(args)


def LogError(*args):
    return E_LogError(args)


def SMA(price, period, weight):
    return E_SMA(price, period, weight)


def REF(price, length):
    return E_REF(price, length)


def ParabolicSAR(high, low, afstep, aflimit):
    return E_ParabolicSAR(high, low, afstep, aflimit)


def Highest(price, length):
    return E_Highest(price, length)


def Lowest(price, length):
    return E_Lowest(price, length)


def CountIf(cond, peroid):
    return E_CountIf(cond, peroid)


def CrossOver(price1, price2):
    return E_CrossOver(price1, price2)


def CrossUnder(price1, price2):
    return E_CrossUnder(price1, price2)


def SwingHigh(Price, Length, Instance, Strength):
    return E_SwingHigh(Price, Length, Instance, Strength)


def SwingLow(Price, Length, Instance, Strength):
    return E_SwingLow(Price, Length, Instance, Strength)


def Alert(Info, bBeep=True, level='Signal'):
    return E_Alert(Info, bBeep, level)


def StrategyId():
    return E_StrategyId()