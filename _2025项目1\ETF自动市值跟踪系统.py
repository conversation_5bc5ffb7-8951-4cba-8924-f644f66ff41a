import talib
import math
import copy
import numpy as np
import pandas as pd
from collections import deque
from xtquant.xttrader import XtQuantTrader
from xtquant import xtconstant

g_params['K线基础时间']  = 'M'   # k线基础时间
g_params['K线基础周期']  = 30     # k线周期
g_params['选择显示合约序号']  = 1 # 选择显示合约序号
g_params['FastLength']  = 22    # 快周期
g_params['MidLength']   = 60    # 中周期
g_params['SlowLength']  = 120   # 慢周期
g_params['基准市值']     = 100   # 基准市值(万)
g_params['止盈']  =  2  #市值大于基准市值的2%止盈
g_params['补仓']  = -2  #市值小于基准市值的2%补仓
g_params['疯牛乖离阈值']  =  100  #疯牛乖离阈值
g_params['疯熊乖离阈值']  = -100  #疯熊乖离阈值
g_params['订阅数据长度']  =  20000  #订阅数据长度


g_params['配置文件夹路径'] = "E:\stock_trade"
g_params['配置文件名'] = "配置文件与股票ETF代码.xlsx"
trade_order_filedir = g_params['配置文件夹路径']
trade_config_file   = trade_order_filedir+"\\"+g_params['配置文件名'] 
trade_config_DATA   =pd.read_excel(trade_config_file,sheet_name = 0)
symbol_Id =trade_config_DATA["股票ETF代码"].dropna()
资金权重=trade_config_DATA["资金权重"].dropna()
是否交易=trade_config_DATA["是否交易"].dropna()
初始总权益=trade_config_DATA["初始总权益(万)"].iloc[0]
浮盈减仓启控比例=trade_config_DATA["浮盈减仓启控比例(%)"].iloc[0]
浮亏加仓启控比例=trade_config_DATA["浮亏加仓启控比例(%)"].iloc[0]


class TriggerManager:
    """
    交易触发器管理类，负责处理订单开平仓操作
    """
    def __init__(self):
        # 无需内部状态标记，外部由SQL表状态控制
        pass
    
    def reset_states(self):
        """保留此方法做为兼容接口，但不执行任何操作"""
        pass
    
    # ==================== 历史开仓函数 ====================
    
    def his_trigger_long(self, qty, price, tcode):
        """
        历史多头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
        """
        # 移除状态检查，直接执行开仓
        Buy(qty, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "多单买入开仓价==>", price, "买入数量==>", qty)
        return True
    
    def his_trigger_short(self, qty, price, tcode):
        """
        历史空头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
        """
        # 移除状态检查，直接执行开仓
        SellShort(qty, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "空单卖出开仓价==>", price, "卖出数量==>", qty)
        return True
    
    def his_trigger(self, BK, SK, qty, price, tcode):
        """历史开仓兼容函数"""
        if BK:
            return self.his_trigger_long(qty, price, tcode)
        elif SK:
            return self.his_trigger_short(qty, price, tcode)
        return False
    
    # ==================== 历史平仓函数 ====================
    
    def his_trigger_exit_short(self, clots, price, tcode):
        """
        历史平仓空头持仓
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
        """
        # 只保留必要的持仓检查
        if SellPosition(tcode) <= 0 or clots <= 0:
            return False
            
        _lots = min(clots, SellPosition(tcode))
        BuyToCover(_lots, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "空单买入平仓价==>", price, "买入平仓数量==>", _lots)
        return True
    
    def his_trigger_exit_long(self, clots, price, tcode):
        """
        历史平仓多头持仓
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
        """
        # 只保留必要的持仓检查
        if BuyPosition(tcode) <= 0 or clots <= 0:
            return False
            
        _lots = min(clots, BuyPosition(tcode))
        Sell(_lots, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "多单卖出平仓价==>", price, "卖出平仓数量==>", _lots)
        return True
    
    def his_trigger_Exit(self, BP, SP, long_price, short_price, clots, tcode):
        """历史平仓兼容函数"""
        result = False
        if BP:
            result = self.his_trigger_exit_short(clots, short_price, tcode)
        if SP:
            result = self.his_trigger_exit_long(clots, long_price, tcode) or result
        return result
    
    # ==================== 实时开仓函数 ====================
    
    def tim_trigger_long(self, qty, price, tcode):
        """
        实时多头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
            
        返回:
            (状态, 订单ID)
        """
        upper_limit = Q_UpperLimit(tcode)
        if upper_limit <= 0:
            checked_price = price
        else:
            checked_price = min(price, upper_limit)
            if checked_price != price:
                LogInfo(f"警告: 多头开仓价格 {price} 超过涨停价 {upper_limit}，已自动调整")
        try:
            # 使用 xtquant API 下单：调用 xt_trader.order_stock 接口（请确保 xt_trader 和 acc 已初始化）
            order_id = xt_trader.order_stock(acc, tcode, xtconstant.STOCK_BUY, qty, xtconstant.FIX_PRICE, checked_price, 'ETF自动市值跟踪系统', '多头开仓')
            if order_id:
                LogInfo(f"多头开仓订单已发送，订单号: {order_id}")
                return True, order_id
            else:
                LogInfo("多头开仓订单发送失败")
                return False, None
        except Exception as e:
            LogInfo("实时多头下单异常:", str(e))
            return False, None
    
    def tim_trigger_short(self, qty, price, tcode):
        """
        实时空头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
            
        返回:
            (状态, 订单ID)
        """
        lower_limit = Q_LowLimit(tcode)
        if lower_limit <= 0:
            checked_price = price
        else:
            checked_price = max(price, lower_limit)
            if checked_price != price:
                LogInfo(f"警告: 空头开仓价格 {price} 低于跌停价 {lower_limit}，已自动调整")
        try:
            # 使用 xtquant API 下单：调用 xt_trader.order_stock 接口（请确保 xt_trader 和 acc 已初始化）
            order_id = xt_trader.order_stock(acc, tcode, xtconstant.STOCK_SELL, qty, xtconstant.FIX_PRICE, checked_price, 'ETF自动市值跟踪系统', '空头开仓')
            if order_id:
                LogInfo(f"空头开仓订单已发送，订单号: {order_id}")
                return True, order_id
            else:
                LogInfo("空头开仓订单发送失败")
                return False, None
        except Exception as e:
            LogInfo("实时空头下单异常:", str(e))
            return False, None
    
    def tim_trigger(self, BK, SK, qty, price, tcode):
        """实时开仓兼容函数"""
        if BK:
            return self.tim_trigger_long(qty, price, tcode)
        elif SK:
            return self.tim_trigger_short(qty, price, tcode)
        return None, None
    
    # ==================== 实时平仓函数 ====================
    
    def tim_trigger_exit_short(self, clots, price, tcode):
        """
        实时平仓空头持仓，支持返回多个订单状态
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
            
        返回:
            list: 包含所有订单信息的列表，每项为 (状态, 订单ID, 平仓类型, 平仓手数)
        """
        orders_info = []  # 用于存储所有订单的状态和ID
        
        # 只检查持仓是否存在
        sell_position = A_SellPosition(tcode)
        if sell_position <= 0 or clots <= 0:
            LogInfo(f"合约 {tcode} 无空头持仓或平仓手数为0，跳过平仓")
            return orders_info
            
        _lots = min(clots, sell_position)
        LogInfo(f"准备平仓合约 {tcode} 的空头持仓，持仓量={sell_position}，平仓量={_lots}")
        
        # 价格检查逻辑保持不变
        upper_limit = Q_UpperLimit(tcode)
        if upper_limit <= 0:
            checked_price = price
        else:
            checked_price = min(price, upper_limit)
            if checked_price != price:
                LogInfo(f"警告: 平空头价格 {price} 超过涨停价 {upper_limit}，已自动调整")
        
        # 交易所特殊处理逻辑保持不变
        if ExchangeName(tcode) not in ['SHFE', 'INE']:
            # 普通交易所情况
            retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), _lots, checked_price, tcode)
            orders_info.append((retExit, ExitOrderId, "平仓", _lots))
            LogInfo(f"发送平空头单: {tcode}, 类型=平仓, 数量={_lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
        else:
            # 上期所和能源交易所特殊处理
            lots = _lots
            tlots = A_TodaySellPosition(tcode)
            dlots = lots - tlots
            
            if tlots >= lots:
                # 今仓足够平仓,仅平今仓
                retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_ExitToday(), lots, checked_price, tcode)
                orders_info.append((retExit, ExitOrderId, "平今", lots))
                LogInfo(f"发送平空头单(平今): {tcode}, 数量={lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
            elif tlots > 0:
                # 今仓不够，分别平今仓和昨仓
                # 先平今仓部分
                TretExit, TExitOrderId = A_SendOrder(Enum_Buy(), Enum_ExitToday(), tlots, checked_price, tcode)
                orders_info.append((TretExit, TExitOrderId, "平今", tlots))
                LogInfo(f"发送平空头单(平今部分): {tcode}, 数量={tlots}, 价格={checked_price}, 订单ID={TExitOrderId}, 状态={TretExit}")
                
                # 再平昨仓部分
                YretExit, YExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), int(dlots), checked_price, tcode)
                orders_info.append((YretExit, YExitOrderId, "平昨", int(dlots)))
                LogInfo(f"发送平空头单(平昨部分): {tcode}, 数量={int(dlots)}, 价格={checked_price}, 订单ID={YExitOrderId}, 状态={YretExit}")
            elif tlots == 0:
                # 仅平昨仓
                retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), lots, checked_price, tcode)
                orders_info.append((retExit, ExitOrderId, "平昨", lots))
                LogInfo(f"发送平空头单(平昨): {tcode}, 数量={lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
        
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "空单买入平仓价==>", checked_price, "买入平仓数量==>", _lots)
        
        if any(order[1] for order in orders_info):
            LogInfo(f"空头平仓订单已发送成功")
        else:
            LogInfo(f"所有空头平仓订单发送失败")
        
        return orders_info
    
    def tim_trigger_exit_long(self, clots, price, tcode):
        """
        实时平仓多头持仓
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
            
        返回:
            list: 订单信息列表
        """
        orders_info = []  # 用于存储所有订单的状态和ID
        
        # 只检查持仓是否存在
        buy_position = A_BuyPosition(tcode)
        if buy_position <= 0 or clots <= 0:
            LogInfo(f"合约 {tcode} 无多头持仓或平仓手数为0，跳过平仓")
            return orders_info
            
        _lots = min(clots, buy_position)
        LogInfo(f"准备平仓合约 {tcode} 的多头持仓，持仓量={buy_position}，平仓量={_lots}")
        
        # 价格检查逻辑保持不变
        lower_limit = Q_LowLimit(tcode)
        if lower_limit <= 0:
            checked_price = price
        else:
            checked_price = max(price, lower_limit)
            if checked_price != price:
                LogInfo(f"警告: 平多头价格 {price} 低于跌停价 {lower_limit}，已自动调整")
        
        # 交易所特殊处理逻辑保持不变
        if ExchangeName(tcode) not in ['SHFE', 'INE']:
            # 普通交易所情况
            retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), _lots, checked_price, tcode)
            orders_info.append((retExit, ExitOrderId, "平仓", _lots))
            LogInfo(f"发送平多头单: {tcode}, 类型=平仓, 数量={_lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
        else:
            # 上期所和能源交易所特殊处理
            lots = _lots
            tlots = A_TodayBuyPosition(tcode)
            dlots = lots - tlots
            
            if tlots >= lots:
                # 今仓足够平仓,仅平今仓
                retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_ExitToday(), lots, checked_price, tcode)
                orders_info.append((retExit, ExitOrderId, "平今", lots))
                LogInfo(f"发送平多头单(平今): {tcode}, 数量={lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
            elif tlots > 0:
                # 今仓不够，分别平今仓和昨仓
                # 先平今仓部分
                TretExit, TExitOrderId = A_SendOrder(Enum_Sell(), Enum_ExitToday(), tlots, checked_price, tcode)
                orders_info.append((TretExit, TExitOrderId, "平今", tlots))
                LogInfo(f"发送平多头单(平今部分): {tcode}, 数量={tlots}, 价格={checked_price}, 订单ID={TExitOrderId}, 状态={TretExit}")
                
                # 再平昨仓部分
                YretExit, YExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), int(dlots), checked_price, tcode)
                orders_info.append((YretExit, YExitOrderId, "平昨", int(dlots)))
                LogInfo(f"发送平多头单(平昨部分): {tcode}, 数量={int(dlots)}, 价格={checked_price}, 订单ID={YExitOrderId}, 状态={YretExit}")
            elif tlots == 0:
                # 仅平昨仓
                retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), lots, checked_price, tcode)
                orders_info.append((retExit, ExitOrderId, "平昨", lots))
                LogInfo(f"发送平多头单(平昨): {tcode}, 数量={lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
        
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "多单卖出平仓价==>", checked_price, "卖出平仓数量==>", _lots)
        
        if any(order[1] for order in orders_info):
            LogInfo(f"多头平仓订单已发送成功")
        else:
            LogInfo(f"所有多头平仓订单发送失败")
        
        return orders_info
    
    def tim_trigger_Exit(self, BP, SP, long_price, short_price, clots, tcode):
        """
        实时平仓兼容函数
        
        参数:
            BP: 是否平空仓标志
            SP: 是否平多仓标志
            long_price: 平多头价格 (卖出价)
            short_price: 平空头价格 (买入价)
            clots: 平仓手数
            tcode: 合约代码
            
        返回:
            list: 所有平仓订单的信息列表
        """
        all_orders = []
        
        if BP:
            short_orders = self.tim_trigger_exit_short(clots, short_price, tcode)
            all_orders.extend(short_orders)
            
        if SP:
            long_orders = self.tim_trigger_exit_long(clots, long_price, tcode)
            all_orders.extend(long_orders)
            
        return all_orders

symbol_d=[]
for i in range(len(symbol_Id)):
    symbol_d.append(copy.deepcopy(deque([0]*9,maxlen=9)))
UPSA,DWSA=[0]*len(symbol_Id),[0]*len(symbol_Id)     
BKStatus,SKStatus,BPStatus,SPStatus=[0]*len(symbol_Id),[0]*len(symbol_Id),[0]*len(symbol_Id),[0]*len(symbol_Id)
UPSQ=copy.deepcopy(symbol_d) 
DWSQ=copy.deepcopy(symbol_d) 
k_btime,k_cycle,SetDisplayNo,FastLength,MidLength,SlowLength,BaseMargin,Profit,AddMargin,BullishLimit,BearishLimit=0,0,0,0,0,0,0,0,0,0,0
FinancialWeighting=0
def initialize(context): 
    global g_params,k_btime,k_cycle,SetDisplayNo,FastLength,MidLength,SlowLength,BaseMargin,Profit,AddMargin,BullishLimit,BearishLimit,FinancialWeighting
    k_btime = g_params['K线基础时间'] # k线基础时间取参数
    k_cycle = g_params['K线基础周期'] # k线基础周期取参数
    SetDisplayNo = g_params['选择显示合约序号']-1 # 选择显示合约序号
    FastLength = g_params['FastLength']   # 快周期
    MidLength = g_params['MidLength']     # 中周期
    SlowLength = g_params['SlowLength']   # 慢周期
    BaseMargin = g_params['基准市值']      # 基准市值
    Profit = g_params['止盈']      # 止盈
    AddMargin = g_params['补仓']   # 补仓
    BullishLimit = g_params['疯牛乖离阈值']   # 疯牛乖离阈值
    BearishLimit = g_params['疯熊乖离阈值']   # 疯熊乖离阈值 

    DaySubDataLength = g_params['订阅数据长度']  # 订阅日线数据长度
    SubDataLength = int(DaySubDataLength*8)  # 订阅数据长度
    AluSubDataLength = min(2000,SubDataLength)  # 计算数据长度    
    for i in range(len(symbol_Id)):
        tcode=stock_code_mapping(int(symbol_Id[i]))
        if 是否交易[i]=="是":
            LogInfo("订阅",symbol_Id[i],"的合约"+tcode,"权重",资金权重[i])
            FinancialWeighting+=资金权重[i]
            SetBarInterval(tcode, k_btime, k_cycle,SubDataLength,AluSubDataLength) #订阅交易合约
            SetBarInterval(tcode, 'D', 1 ,DaySubDataLength,AluSubDataLength) #订阅日线数据
    SetTriggerType(1)
    SetTriggerType(5)
    SetOrderWay(1)
    SetActual()
BKS,SKS,BPS,SPS=0,0,0,0
sBARS=deque([0,0],maxlen=3)
VM0,VM1=0,0
LRS,MRS,SRS=0,0,0
trigger_manager = TriggerManager()
def handle_data(context):
    global BKS,SKS,BPS,SPS,VM0,VM1
    HTS=1 if context.strategyStatus()=="C" else 0
    for i in range(len(symbol_Id)):
        if 是否交易[i]=="否":
            continue
        tcode=stock_code_mapping(int(symbol_Id[i]))
        O=Open(tcode, k_btime, k_cycle)
        C = Close(tcode, k_btime, k_cycle)
        CD = Close(tcode, 'D', 1)
        if len(CD) < SlowLength:
            return
        financial_weighting=资金权重[i]/FinancialWeighting
        MarginEquity= Margin(tcode)/(初始总权益*financial_weighting)

        LOTS=math.floor(MarginEquity*financial_weighting/(C[-1]*ContractUnit(tcode)*100+TradeCost(tcode)))*100
        MP=MarketPosition(tcode)
        BKVOL=BuyPosition(tcode)
        SKVOL=SellPosition(tcode)
        A_BKVOL=A_BuyPosition(tcode)
        A_SKVOL=A_SellPosition(tcode)


        FastLine = talib.MA(CD, FastLength) #快线周期均值
        MidLine = talib.MA(CD, MidLength)   #中线周期均值
        SlowLine = talib.MA(CD, SlowLength) #慢线周期均值
        SRS=(C[-1-HTS]/FastLine[-1-HTS]-1)*1000
        MRS=(C[-1-HTS]/MidLine[-1-HTS]-1)*1000
        LRS=(C[-1-HTS]/SlowLine[-1-HTS]-1)*1000
        RSRS=(C[-2-HTS]/FastLine[-2-HTS]-1)*1000
        RMRS=(C[-2-HTS]/MidLine[-2-HTS]-1)*1000
        RLRS=(C[-2-HTS]/SlowLine[-2-HTS]-1)*1000
        if SetDisplayNo==i:
            PlotNumeric("FastLine", FastLine[-1-HTS], 0xFFFFFF)
            PlotNumeric("MidLine", MidLine[-1-HTS], 0x00AAAA)
            PlotNumeric("SlowLine", SlowLine[-1-HTS], 0xFF0000)
            PlotNumeric("短乖离", SRS, 0xFF0000,False, False,0,"乖离指标")
            PlotNumeric("中乖离", MRS, 0x00FF00,False, False,0,"乖离指标")
            PlotNumeric("长乖离", LRS, 0xFFFF00,False, False,0,"乖离指标")
            PlotNumeric("疯牛线", BullishLimit, 0xFF0000,False, False,0,"乖离指标")
            PlotNumeric("疯熊线", BearishLimit, 0x00FF00,False, False,0,"乖离指标")
            PlotNumeric("零轴", 0, 0xFFFFFF,False, False,0,"乖离指标")

        if UPSA[i]<=0 and RSRS<=0 and SRS>0:
            UPSA[i]=1
        if UPSA[i]==1 :
            if SRS<=0:
                UPSA[i]=0
            elif SRS> BullishLimit:
                UPSA[i]=2
        if UPSA[i]==2 and RSRS>=BullishLimit and SRS<BullishLimit:
            UPSA[i]=-1

        if DWSA[i]>=0 and RSRS>=0 and SRS<0:
            DWSA[i]=-1
        if DWSA[i]==-1:
            if SRS>=0:
                DWSA[i]=0
            elif SRS<BearishLimit:
                DWSA[i]=-2
        if DWSA[i]==-2 and RSRS<=BearishLimit and SRS>BearishLimit:
            DWSA[i]=1
        UPSQ[i].append(UPSA[i])
        DWSQ[i].append(DWSA[i])

        if SetDisplayNo==i:
            PlotNumeric("UPSA", UPSA[i], 0xFF0000,False, False,0,"乖离指标2")
            PlotNumeric("DWSA", DWSA[i], 0x00FF00,False, False,0,"乖离指标2")
            PlotNumeric("零轴", 0, 0xFFFFFF,False, False,0,"乖离指标2")
            PlotNumeric("账户市值", CurrentEquity(), 0xFFFF00,True, True,0)

        # 执行下单操作
        BKS0=RLRS<=BearishLimit and LRS>BearishLimit and SRS<0
        SKS0=RLRS>=BullishLimit and LRS<BullishLimit and SRS>0
        BKS1=BullishLimit>LRS>0 and SRS<0 and SRS>MRS
        SKS1=BearishLimit<LRS<0 and SRS>0 and SRS<MRS
        BKS2=BullishLimit>LRS>0 and SRS>0 and SRS>MRS and SRS<BullishLimit/2
        SKS2=BearishLimit<LRS<0 and SRS<0 and SRS<MRS and SRS>BearishLimit/2

        SPS1=UPSA[i]==-1
        BPS1=DWSA[i]== 1

        BK1=BKS1 or BKS2
        SK1=SKS1 or SKS2
        BP1=BPS1 or BKS2
        SP1=SPS1 or SKS2
        if HTS==0:
            # if BK1 and MP==0:
            #     trigger_manager.his_trigger_long(LOTS,C[-1],tcode)
            # if SP1:
            #     trigger_manager.his_trigger_exit_long(LOTS,C[-1],tcode)
            if BKS0 and MP==0:
                BKStatus[i]=1
                trigger_manager.his_trigger_long(LOTS,C[-1],tcode)
            if RSRS>=0 and SRS<0 and BKStatus[i]==1 and MP>0:
                BKStatus[i]=0
                trigger_manager.his_trigger_exit_long(BKVOL,C[-1],tcode)
            if BKStatus[i]==1 and (C[-1]/O[-1]-1)*100>Profit:
                BKStatus[i]=2
            if BKStatus[i]==2 and C[-1]<O[-1]:
                BKStatus[i]=0
                trigger_manager.his_trigger_exit_long(BKVOL,C[-1],tcode)
            # if SKS0 and MP==0:
            #     SKStatus[i]=1
            #     trigger_manager.his_trigger_short(LOTS,C[-1],tcode)
            # if RSRS<=0 and SRS>0 and SKStatus[i]==1 and MP<0:
            #     SKStatus[i]=0
            #     trigger_manager.his_trigger_exit_short(LOTS,C[-1],tcode)
 

        #     if VM0==0 and BKS1:#多头建仓
        #         VM0=BaseMargin*10000
        #         Buy(LOTS,C[-1])
        #         ELOTS=BuyPosition()-LOTS; 

        #     elif SKS1:
        #         SellShort(LOTS, C[-1])
        #     return
        # #进入实时行情，清理掉策略仓
        # if MarketPosition() > 0 and A_BuyPosition(tcode)==0:  
        #     Sell(N, Open()[-1])
        #     LogInfo("进入实时行情，清理掉策略多仓")
        #     return
        # elif MarketPosition() < 0 and A_SellPosition(tcode)==0:
        #     BuyToCover(N, Open()[-1])
        #     LogInfo("进入实时行情，清理掉策略空仓")
        #     return


        # LOTS=math.floor(BaseMargin*10000/(C[-1]*ContractUnit(tcode)*100+FEE))*100    
    
    # #//------------------------实时处理------------------------//

    # # if ExchangeStatus(ExchangeName()) != '3':
    # #     return
    # BKS2 = AvgValue1[-3] <= AvgValue2[-3] and AvgValue1[-2] > AvgValue2[-2]
    # SKS2 = AvgValue1[-3] >= AvgValue2[-3] and AvgValue1[-2] < AvgValue2[-2]   
    # #//------------------------变量赋值------------------------//
    # s_CurrentBar=CurrentBar(tcode, k_btime, k_cycle)
    # sBARS.append(s_CurrentBar)
    # if sBARS[0]>0 and sBARS[1]<sBARS[2]:
    #     if BKS>=1 and A_BuyPosition(tcode)==0: 
    #         BKS=0
    #         SPS=0  
    #     if SKS>=1 and A_SellPosition(tcode)==0: 
    #         SKS=0 
    #         BPS=0
    # if trade_sw>=0:
    #     tim_trigger(BKS2,False,N,20,tcode)
    # if trade_sw<=0:
    #     tim_trigger(False,SKS2,N,20,tcode)
    # if BPS==0:    
    #     tim_trigger_Exit(BKS2,False,20,tcode,N)
    # if SPS==0:    
    #     tim_trigger_Exit(False,SKS2,20,tcode,N)

from typing import Union
def stock_code_mapping(code: Union[int, str]) -> str:
    # 整数处理分支（进一步优化）
    if isinstance(code, int):
        if not (1 <= code <= 999999):
            raise ValueError("输入必须为6位以下正整数")
        
        # 格式化代码字符串（只做一次）
        code_str = f"{code:06d}"
        
        # 快速分类 - 使用整数除法和模运算
        first_digit = code // 100000
        first_two = code // 10000
        first_three = code // 1000
        
        # 沪市股票 (6开头)
        if first_digit == 6:
            if first_three == 688:
                return f"SSE|T|KSHARES|{code_str}"  # 科创板
            elif first_three in {600, 601, 603, 605}:
                return f"SSE|T|ASHARES|{code_str}"      # 沪主板
            
        # 深主板 (0,1,3开头或4-9开头)
        if first_three  in {0, 1, 3}:
            return f"SZSE|T|ASHARES|{code_str}"    # 深主板            
        # 中小板 (002开头)    
        if first_three == 2:
            return f"SZSE|T|SMESHARES|{code_str}"  # 中小板
        # 创业板 (30开头)
        if first_two == 30:
            return f"SZSE|T|CHSHARES|{code_str}"   # 创业板   
        # 深B股 (200开头)
        if first_three == 200:
            return f"SZSE|T|BSHARES|{code_str}"    # 深B股
        # ETF (159开头)
        if first_three == 159:
            return f"SZSE|T|FUNDS|{code_str}"      # ETF
             
        # 基金 (5开头)
        if first_digit == 5:
            return f"SSE|T|FUNDS|{code_str}"       # 沪基金
            
        # REITs (16-18开头)
        if first_two in {16, 18}:
            return f"SZSE|T|FUNDS|{code_str}"      # REITs
            
        # 沪B股 (9开头)
        if first_digit == 9:
            return f"SSE|T|BSHARES|{code_str}"     # 沪B股
            
        # 北交所和新三板
        if first_three in {830, 831, 832, 833, 834, 835, 836, 837, 838, 839, 
                          870, 871, 872, 873, 874, 875, 876, 877, 878, 879, 
                          920, 921, 922, 923, 924, 925, 926, 927, 928, 929}:
            return f"BJSE|T|STOCK|{code_str}"
        if first_three in {400, 430, 830}:
            return f"NEEQ|T|OTC|{code_str}"
            
        return f"UNKNOWN|{code_str}"
    
    # 字符串处理分支（原逻辑）
    elif isinstance(code, str):
        if not (code.isdigit() and len(code) == 6):
            raise ValueError("输入必须为6位数字字符串")

        if code.startswith('688'):
            return f"SSE|T|KSHARES|{code}"
        elif code.startswith(('600','601','603','605')):
            return f"SSE|T|ASHARES|{code}"
        elif code.startswith('5'):
            return f"SSE|T|FUNDS|{code}"
        elif code.startswith('900'):
            return f"SSE|T|BSHARES|{code}"
        elif code.startswith('159'):
            return f"SZSE|T|FUNDS|{code}"
        elif code.startswith(('000','001','003')):
            return f"SZSE|T|ASHARES|{code}"
        elif code.startswith('002'):
            return f"SZSE|T|SMESHARES|{code}"
        elif code.startswith('30'):
            return f"SZSE|T|CHSHARES|{code}"
        elif code.startswith('200'):
            return f"SZSE|T|BSHARES|{code}"
        elif code.startswith(('16','18')):
            return f"SZSE|T|FUNDS|{code}"
        elif code.startswith(('83','87','920')):
            return f"BJSE|T|STOCK|{code}"
        elif code.startswith(('400','430','830')):
            return f"NEEQ|T|OTC|{code}"
        else:
            return f"UNKNOWN|{code}"
    
    else:
        raise TypeError("输入类型必须为int或str")
    
def stock_index_code_mapping(code: Union[int, str]) -> str:
    if isinstance(code, int):
        code_str = f"{code:06d}"
        prefix_three = code // 1000
        if prefix_three == 399:
            return f"SZSE|T|INDEX|{code_str}"
        if prefix_three == 0:
            return f"SSE|T|INDEX|{code_str}"
    elif isinstance(code, str):
        if code.startswith('399'):
            return f"SZSE|T|INDEX|{code}"
        if code.startswith('000'):
            return f"SSE|T|INDEX|{code}"
