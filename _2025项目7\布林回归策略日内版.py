import time
import talib
import numpy as np 
import numba as nb 
import pandas as pd
from collections import deque
g_params['布林周期']  = 20
g_params['布林倍数']  = 3
g_params['止损点数']  = 0        #止损点数
g_params['手动平仓']  =  '开'    #为开时，关闭盘中自动止盈止损和收盘清仓
g_params['订阅数据长度']  =  2000  #订阅数据长度
g_params['交易方向开关']  = 0  #大于0开多，小于0开空，等于0双向开仓
g_params['下单量']  = 1        #下单量
g_params['下单超价跳数']  = 0  #下单超价跳数  
g_params['收盘倒计时分钟数设置'] = 1#距离收盘时间范围不开仓，并清仓    
  
def tim_trigger(BK,SK,qty,itk,tcode):#盘中实时开仓
    global BKS,SKS
    if BK and BKS==0 and (A_BuyPosition(tcode) == 0) :
        iprc = min(Q_AskPrice(tcode) +itk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        A_SendOrder(Enum_Buy(), Enum_Entry(), qty, iprc,tcode) 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单买入开仓价==>",iprc,"买入数量==>",qty)
        BKS=1    
    elif SK and SKS==0 and (A_SellPosition(tcode) == 0):    
        iprc = max(Q_BidPrice(tcode) - itk*PriceTick(tcode), Q_LowLimit(tcode))  # 对盘超价                         
        A_SendOrder(Enum_Sell(), Enum_Entry(), qty, iprc,tcode)   
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单卖出开仓价==>",iprc,"卖出数量==>",qty)
        SKS=1    

def tim_trigger_Exit(BP,SP,otk,tcode,clots):#盘中实时平仓
    global BKS,SKS,BPS,SPS
    if BP and BPS==0 and A_SellPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_SellPosition(tcode))
        prc = min(Q_AskPrice(tcode) +otk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        if ExchangeName(tcode) not in ['SHFE','INE']:    
            retExit, ExitOrderId=A_SendOrder(Enum_Buy(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodaySellPosition(tcode)
            dlots=lots-tlots            
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),lots, prc,tcode) #今仓足够平仓,上期所能交所优先超价全部平今仓    
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Buy(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Buy(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单买入平仓价==>",prc,"买入平仓数量==>",_lots)
        BPS=1  
        if SKS==1:SKS=2      
    elif SP and SPS==0 and A_BuyPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_BuyPosition(tcode))
        prc = max(Q_BidPrice(tcode) - otk*PriceTick(tcode), Q_LowLimit(tcode))
        if ExchangeName(tcode) not in ['SHFE','INE']:
            retExit, ExitOrderId=A_SendOrder(Enum_Sell(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodayBuyPosition(tcode)
            dlots=lots-tlots
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),lots, prc,tcode)   #今仓足够平仓,上期所能交所优先超价全部平今仓  
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Sell(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Sell(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单卖出平仓价==>",prc,"卖出平仓数量==>",_lots)
        SPS=1    
        if BKS==1:BKS=2   

scode,tcode,k_btime,k_cycle,b_period,b_multiplier,stoplost_tick,ManualCloseoutSwitch='','',0,0,0,0,0,0
trade_sw,lots_o1,ovprice_tick,CloseTime=0,0,0,0
def initialize(context): 
    global g_params,scode,tcode,k_btime,k_cycle,b_period,b_multiplier,stoplost_tick,ManualCloseoutSwitch
    global trade_sw,lots_o1,ovprice_tick,CloseTime
    scode   = Symbol()
    tcode   = Symbol()
    b_period = g_params['布林周期']
    b_multiplier = g_params['布林倍数']
    stoplost_tick = g_params['止损点数']
    SubDataLength = g_params['订阅数据长度'] 
    trade_sw=g_params['交易方向开关']    #大于0开多，小于0开空，等于0双向开仓
    lots_o1= g_params['下单量']          #  下单量
    ovprice_tick =g_params['下单超价跳数']    #下单超价跳数
    CloseTime = g_params['收盘倒计时分钟数设置']
    ManualCloseoutSwitch = g_params['手动平仓']!='开'
    AL=b_period*2+1   
    # SetBarInterval(tcode,'T', 1,1) #订阅交易合约
    SetActual()           #设置实盘运行
    SetOrderWay(1)        #设置K线走完后发单
    SetTriggerType(5)     #设置K线触发
# 策略触发事件每次触发时都会执行该函数
_MP,A_MP,BKH,BKL,SKH,SKL=0,0,0,0,0,0
BKS,SKS,BPS,SPS,HH,LL,HS,LS,hh_stop,ll_stop,BKStatus,SKStatus,BARCNT,BARCNT2=0,0,0,0,0,0,0,0,0,0,0,0,0,0
HCS,sBARS,buys,sells=deque([0]*3,maxlen=3),deque([0]*3,maxlen=3),deque([0]*3,maxlen=3),deque([0]*3,maxlen=3)
def handle_data(context):
    global _MP,A_MP,BKH,BKL,SKH,SKL
    global BKS,SKS,BPS,SPS,HH,LL,HS,LS,hh_stop,ll_stop,BKStatus,SKStatus,BARCNT,BARCNT2
    scode   = Symbol()
    tcode   = Symbol()
    H=High( )     # 信号合约最高价格
    L=Low( )     # 信号合约最低价格
    C=Close( )      # 信号合约收盘价格
    CT=Close()   # 交易合约收盘价格
    MINPRICE=PriceTick(tcode)           # 交易合约最小变动价
    
    #if context.strategyStatus() == 'C':
    #    buyprice  = min(Q_AskPrice(tcode)+ovprice_tick*MINPRICE, Q_UpperLimit(tcode))  # 买超价
    #    sellprice = max(Q_BidPrice(tcode)-ovprice_tick*MINPRICE, Q_LowLimit(tcode))    # 卖超价
    #else:
    #    buyprice  = CT[-1]+ovprice_tick*MINPRICE  # 买超价
    #    sellprice = CT[-1]-ovprice_tick*MINPRICE  # 卖超价

    #指数计算指标
    if  len(C)<b_period+3 :
       return
    MID=talib.MA(C,b_period)
    UPPER=MID+b_multiplier*talib.STDDEV(C,b_period)
    LOWER=MID-b_multiplier*talib.STDDEV(C,b_period)

    _VTS=VTS(Time())
    TradeEnbTime=TimeTo_Minutes(_VTS[3])-TimeTo_Minutes(_VTS[2])>CloseTime
    TradeOffTime=CloseTime>=TimeTo_Minutes(_VTS[3])-TimeTo_Minutes(_VTS[2])>0
    ODS=0
    s_CurrentBar=CurrentBar()
    sBARS.append(s_CurrentBar)
    if sBARS[0]>0 and sBARS[1]<sBARS[2]:
        SessionEndFlag=SessionOpenTime(tcode)[-1]<=Time()<SessionCloseTime(tcode)[-1]
        if not SessionEndFlag and TradeEnbTime:
            BARCNT+=1
        else:
            BARCNT=0
        if SessionEndFlag:
            BARCNT2+=1
        else:
            BARCNT2=0
        BKS=0
        SKS=0 
        BPS=0
        SPS=0     
        # LogInfo("sBARS=>",sBARS,"BKS=>",BKS,"SKS=>",SKS)       

    #主连下单，计算止损止盈价格线
    HCS.append(context.strategyStatus())
    # LogInfo('TIME',Time(),'BARCNT=>',BARCNT,"TradeEnbTime=>",TradeEnbTime,"TradeOffTime=>",TradeOffTime,"VTS	=>",_VTS,'BARCNT2=>',BARCNT2)
    HTS=0 #1 if context.strategyStatus()=="C" else 0   
    BK1 =TradeEnbTime and (BARCNT>2 or BARCNT2>1) and L[-1-HTS]<LOWER[-1-HTS]
    SK1 =TradeEnbTime and (BARCNT>2 or BARCNT2>1) and H[-1-HTS]>UPPER[-1-HTS]
    BP1 = C[-1-HTS]<MID[-1-HTS]
    SP1 = C[-1-HTS]>MID[-1-HTS]


    if context.strategyStatus()=="C":
        if _MP!=0:
            if _MP>0:
                _MP=0
                LogInfo(tcode,"从历史阶段进入实盘阶段，执行多单策略仓清理",abs(MC),"手")
                Sell(abs(MC), C[-1])
                ll_stop=0
            elif _MP<0:
                _MP=0
                LogInfo(tcode,"从历史阶段进入实盘阶段，执行空单策略仓清理",abs(MC),"手")
                BuyToCover(abs(MC), C[-1])
                hh_stop=0

        if  ExchangeStatus(ExchangeName(tcode)) in ('1','2','3'): #QuoteSvrState() == 1 and TradeSvrState()==1
            if  BKS==0 and A_MP>=0 and trade_sw>=0 and BK1:
                A_MP+=1
                tim_trigger(True,False,lots_o1,ovprice_tick,tcode);
                BKS=1
                BKStatus=0
                if A_MP==1:
                    BKH=H[-1-HTS]
                    BKL=L[-1-HTS]
                else:
                    BKL=L[-1-HTS]
                ll_stop=(BKH+BKL)/2-stoplost_tick*MINPRICE   
            if SKS==0 and A_MP<=0 and trade_sw<=0 and SK1:
                A_MP-=1
                tim_trigger(False,True,lots_o1,ovprice_tick,tcode);
                SKS=1
                SKStatus=0
                if A_MP==-1:
                    SKH=H[-1-HTS]
                    SKL=L[-1-HTS]
                else:
                    SKL=L[-1-HTS]
                hh_stop=(SKH+SKL)/2+stoplost_tick*MINPRICE    
   
            buys.append(A_BuyPosition(tcode))   
            sells.append(A_SellPosition(tcode)) 

            if  A_BuyPosition(tcode)>0 and ll_stop>0: 
                PlotPartLine("低价线",CurrentBar(),ll_stop,2,ll_stop,0x00ff00)
                if ManualCloseoutSwitch:
                    OutLots=A_BuyPosition(tcode)
                    if Q_Last()>ll_stop:
                        BKS=1
                        LogInfo(tcode,"多单中线止盈",OutLots,"手")
                        tim_trigger_Exit(False,True,ovprice_tick,tcode,OutLots)   
                        ll_stop=0  
                    elif Q_Last()>MID[-1]:        
                        BKS=1
                        LogInfo(tcode,"多单布林中轨止赢",OutLots,"手")
                        tim_trigger_Exit(False,True,ovprice_tick,tcode,OutLots)  
                        ll_stop=0
                    elif  TradeOffTime:
                        BKS=1
                        LogInfo(tcode,"收盘前平多仓",OutLots,"手")
                        tim_trigger_Exit(False,True,ovprice_tick,tcode,OutLots)    
                        ll_stop=0
            if A_SellPosition(tcode)>0 and hh_stop>0: 
                PlotPartLine("高价线",CurrentBar(),hh_stop,2,hh_stop,0xff0000)
                if ManualCloseoutSwitch:
                    OutLots=A_SellPosition(tcode)
                    if Q_Last()<hh_stop:
                        SKS=1
                        LogInfo(tcode,"空单中线止盈",OutLots,"手")
                        tim_trigger_Exit(True,False,ovprice_tick,tcode,OutLots)       
                        hh_stop=0
                    elif Q_Last()<MID[-1]:        
                        SKS=1
                        LogInfo(tcode,"空单布林中轨止赢",OutLots,"手")
                        tim_trigger_Exit(True,False,ovprice_tick,tcode,OutLots)     
                        hh_stop=0
                    elif  TradeOffTime:
                        SKS=1
                        LogInfo(tcode,"收盘前平空仓",OutLots,"手")
                        tim_trigger_Exit(True,False,ovprice_tick,tcode,OutLots)   
                        hh_stop=0
    else:
        if trade_sw>=0 and _MP>=0 and BK1:
            _MP+= 1
            Buy(lots_o1, LOWER[-1-HTS]) 
            BKStatus=0
            if _MP==1:
                BKH=H[-1-HTS]
                BKL=L[-1-HTS]
            else:
                BKL=L[-1-HTS]
            ll_stop=(BKH+BKL)/2-stoplost_tick*MINPRICE

        if trade_sw<=0 and _MP<=0 and SK1: 
            _MP-= 1
            SellShort(lots_o1, UPPER[-1-HTS])
            SKStatus=0
            if _MP==-1:
                SKH=H[-1-HTS]
                SKL=L[-1-HTS]
            else:
                SKH=H[-1-HTS]
            hh_stop=(SKH+SKL)/2+stoplost_tick*MINPRICE

        if _MP>0 and ll_stop>0:
            PlotPartLine("低价线",CurrentBar(),ll_stop,2,ll_stop,0x00ff00)
            MC=BuyPosition(tcode)
            if H[-1]>ll_stop:
                _MP=0
                LogInfo(tcode,"多单中线止盈",MC,"手")
                Sell(MC, ll_stop)
                ll_stop=0
            elif H[-1]>MID[-1]:
                _MP=0
                LogInfo(tcode,"多单布林中轨止赢",MC,"手")
                Sell(MC, MID[-1])
                ll_stop=0
            elif TradeOffTime:
                _MP=0
                LogInfo(tcode,"收盘前平多仓",MC,"手")
                Sell(MC, C[-1])
                ll_stop=0
                
        if _MP<0 and hh_stop>0:
            PlotPartLine("高价线",CurrentBar(),hh_stop,2,hh_stop,0xff0000)
            MC=SellPosition(tcode)
            if L[-1]<hh_stop:
                _MP=0
                LogInfo(tcode,"空单中线止盈",abs(MC),"手")
                BuyToCover(abs(MC), hh_stop)
                hh_stop=0
            elif L[-1]<MID[-1]:
                _MP=0
                LogInfo(tcode,"空单布林中轨止赢",abs(MC),"手")
                BuyToCover(abs(MC), MID[-1])
                hh_stop=0
            elif TradeOffTime:
                _MP=0
                LogInfo(tcode,"收盘前平空仓",abs(MC),"手")
                BuyToCover(abs(MC), C[-1])
                hh_stop=0

    PlotNumeric('MID', MID[-1],0xffffff, True, False,0,"中周期布林指标")
    PlotNumeric('UPPER', UPPER[-1],0xff0000, True, False,0,"中周期布林指标") 
    PlotNumeric('LOWER', LOWER[-1],0x00ff00, True, False,0,"中周期布林指标") 


def floattime_sum(floatin1, floatin2, len_set=12):  # 高精度浮点时间求和（精确到毫秒）
    # 设置浮点数格式，保留len_set位小数
    lensave = f"%0.{len_set}f"
    
    # 格式化浮点数并提取各时间部分
    def extract_time_parts(floatin):
        strfloat = lensave % floatin
        return int(strfloat[2:4]), int(strfloat[4:6]), int(strfloat[6:8]), int(strfloat[8:11])
    
    h1, m1, s1, ms1 = extract_time_parts(floatin1)
    h2, m2, s2, ms2 = extract_time_parts(floatin2)
    
    # 计算总和并处理进位
    total_ms = ms1 + ms2
    ms_carry = total_ms // 1000
    new_ms = total_ms % 1000
    
    total_s = s1 + s2 + ms_carry
    s_carry = total_s // 60
    new_s = total_s % 60
    
    total_m = m1 + m2 + s_carry
    m_carry = total_m // 60
    new_m = total_m % 60
    
    new_h = h1 + h2 + m_carry
    new_h = min(new_h, 99)  # 限制小时数不超过99
    
    # 组合新的浮点时间字符串并转换回浮点数
    new_str_time = f"0.{new_h:02}{new_m:02}{new_s:02}{new_ms:03}"
    return float(new_str_time)

def TimeTo_Minutes(time_in):
    timestr='%0.6f'%time_in
    hsave=int(timestr[2:4])
    msave=int(timestr[4:6])
    tcout=hsave*60+msave
    return tcout

def SessionOpenTime(contractId=''):  # 获取交易时段开盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler:continue
        tlout.append(GetSessionStartTime(contractId, i))  # 获取每个交易时段的开盘时间并加入列表
    return tlout

def SessionCloseTime(contractId=''):  # 获取交易时段收盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler-1:continue
        tlout.append(GetSessionEndTime(contractId, i))  # 获取每个交易时段的收盘时间并加入列表
    return tlout

def VTS(time_in, contractId=''):  # 根据输入时间和合约ID计算交易时段
    RTS, CTS, TSession = [], [], []  # 初始化三个列表，用于存储修正后的时间、收盘时间和交易时段
    opentimet = SessionOpenTime(contractId)  # 获取所有交易时段的开盘时间
    Closetimet = SessionCloseTime(contractId)  # 获取所有交易时段的收盘时间
    
    for open_time, close_time in zip(opentimet, Closetimet):
        if time_in >= open_time:  # 判断输入时间是否在开盘时间之后
            RTS.append(time_in)  # 如果是，加入RTS列表
        else:
            RTS.append(floattime_sum(time_in, 0.24))  # 如果不是，修正时间后加入RTS列表
        
        if close_time >= open_time:  # 判断收盘时间是否在开盘时间之后
            CTS.append(close_time)  # 如果是，加入CTS列表
        else:
            CTS.append(floattime_sum(close_time, 0.24))  # 如果不是，修正时间后加入CTS列表
        
        if open_time <= RTS[-1] <= CTS[-1]:  # 判断修正后的时间是否在交易时段内
            TSession.append(len(RTS) - 1)  # 如果是，加入TSession列表

    if len(TSession) == 1:  # 如果只有一个交易时段
        idx = TSession[0]
        return idx, opentimet[idx], RTS[idx], CTS[idx]  # 返回交易时段和相关时间
    else:
        return -1, time_in, time_in, time_in  # 否则返回默认值
