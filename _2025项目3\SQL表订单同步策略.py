from math import log
import os
import time
import copy
import shutil
import pymysql
import numpy as np
import pandas as pd
import threading
from queue import Queue
from collections import deque

g_params['配置文件夹路径'] = "E:\csv_trade"
g_params['配置文件名'] = "配置文件与订阅合约.xlsx"
g_params['开仓超价跳数'] = 1
g_params['平仓超价跳数'] = 2
g_params['收盘清仓倒计时分钟'] = 5

trade_order_filedir = g_params['配置文件夹路径']
trade_config_file   = trade_order_filedir+"\\"+g_params['配置文件名'] 
trade_config_DATA   =pd.read_excel(trade_config_file,sheet_name = 0)
symbol_Id =trade_config_DATA["使用合约"].dropna().str.upper()
forex_marketId=trade_config_DATA["外盘段映射货币或合约"].dropna().str.upper()
下单量映射倍数=trade_config_DATA["下单量映射倍数"].dropna()
时时价格启用=trade_config_DATA["时时价格启用"].dropna()
平台类型=trade_config_DATA["平台类型"].dropna().str.upper()
注册账号=trade_config_DATA["注册账号"].dropna()
魔术码=trade_config_DATA["魔术码"].dropna()
追单次数=trade_config_DATA["追单次数"].dropna()
追单秒数=trade_config_DATA["追单秒数"].dropna()
隔夜费=trade_config_DATA["隔夜费"].dropna()
手续费=trade_config_DATA["手续费"].dropna()

服务器地址=trade_config_DATA["服务器地址"].iloc[0]
服务器用户名=trade_config_DATA["服务器用户名"].iloc[0]
链接数据库密码=trade_config_DATA["链接数据库密码"].iloc[0]
数据库名称=trade_config_DATA["数据库名称"].iloc[0]
价格表=trade_config_DATA["价格表"].iloc[0]


class MySQLPool:
    def __init__(self, config, min_size=3, max_size=10):
        self.config = config
        self.min_size = min_size
        self.max_size = max_size
        self.pool = Queue(max_size)
        for _ in range(min_size):
            self.pool.put(self.create_connection())

    def create_connection(self):
        conn_str = f"mysql+pymysql://{self.config['user']}:{self.config['password']}@{self.config['host']}/{self.config['database']}?charset={self.config['charset']}"
        return pymysql.connect(**self.config)

    def get_connection(self):
        try:
            return self.pool.get_nowait()
        except:
            return self.create_connection()

    def release_connection(self, conn):
        if self.pool.qsize() < self.max_size:
            self.pool.put(conn)
        else:
            conn.close()

    def close_all(self):
        while not self.pool.empty():
            conn = self.pool.get()
            conn.close()

class TradeDB:
    def __init__(self, db_pool):
        self.db_pool = db_pool

    def execute_query(self, query, params=None, fetch=False):
        conn = self.db_pool.get_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                if fetch:
                    result = cursor.fetchall()
                    return result
                else:
                    conn.commit()
                    return True
        except Exception as e:
            LogInfo(f"❌ 数据库操作失败: {e}")
            conn.rollback()
            return None if fetch else False
        finally:
            self.db_pool.release_connection(conn)

    def get_pending_orders(self, symbol):
        """获取待处理的订单"""
        sql = """
        SELECT * FROM chufakaidanbiao 
        WHERE ahuobi = %s AND bbiaoshi = 0 
        ORDER BY zengjiashijian ASC
        """
        return self.execute_query(sql, (symbol,), fetch=True)

    def update_order_status(self, order_id, status, trade_result=None):
        """更新订单状态"""
        sql = """
        UPDATE chufakaidanbiao 
        SET bbiaoshi = %s, trade_result = %s 
        WHERE adingdanhao = %s
        """
        return self.execute_query(sql, (status, trade_result, order_id))
    
    def close_all_positions(self):
        """获取所有需要平仓的持仓"""
        sql = """
        SELECT * FROM chufakaidanbiao 
        WHERE bbiaoshi = 1
        """
        return self.execute_query(sql, fetch=True)
    
    def update_price(self, table_name, symbol, price, platform_type):
        """更新货币价格"""
        # 首先检查是否存在匹配的记录
        check_sql = f"""
        SELECT IntID FROM {table_name} 
        WHERE huobi = %s AND pingtai = %s
        """
        result = self.execute_query(check_sql, (symbol, platform_type), fetch=True)
        
        if not result:
            LogInfo(f"无法找到匹配的价格记录: 货币={symbol}, 平台={platform_type}")
            return False
        
        # 存在匹配记录，执行更新
        update_sql = f"""
        UPDATE {table_name} 
        SET jiage = %s 
        WHERE huobi = %s AND pingtai = %s
        """
        update_result = self.execute_query(update_sql, (price, symbol, platform_type))
        
        # if update_result:
        #     LogInfo(f"成功更新价格: 货币={symbol}, 价格={price}, 平台={platform_type}")
        # else:
        #     LogInfo(f"价格更新失败: 货币={symbol}, 价格={price}, 平台={platform_type}")
        
        return update_result

    def get_new_orders(self, account, magic_number):
        """获取新的开单信号"""
        sql = """
        SELECT * FROM chufakaidanbiao 
        WHERE abiaoshi = 0 AND kaicangzhuzhanghao = %s AND moshuma = %s
        """
        return self.execute_query(sql, (account, magic_number), fetch=True)

    def update_order_after_open(self, order_id, platform_order_id, open_time, magic_number):
        """开仓后更新订单状态"""
        sql = """
        UPDATE chufakaidanbiao 
        SET adingdanhao = %s, akaidanshijian = %s, abiaoshi = 2 
        WHERE IntID = %s AND moshuma = %s
        """
        return self.execute_query(sql, (platform_order_id, open_time, order_id, magic_number))

    def insert_order_details(self, order_info):
        """
        插入新订单详细信息
        
        参数:
            order_info: 包含订单信息的字典，需要包含以下字段:
                - dingdanhao (订单号)
                - kaidanshijian (开单时间)
                - fangxaing (方向)
                - shoushu (手数)
                - huobi (货币)
                - kaidanjiage (开单价格)
                - pingtaileixing (平台类型)
                - zhushi (注释)
                - moshuma (识别码)
                - geyefei (隔夜费)
                - shouxufei (手续费)
                - yingkuijine (盈亏金额)
                - jingyingkuijine (总盈亏金额)
                - tongyibiaoshi (统一标识)
                - kaicangzhuzhanghao (开仓主账号)
        """
        # 首先检查该订单是否已存在
        check_sql = """
        SELECT dingdanhao FROM xiangxidanxinxibiao 
        WHERE dingdanhao = %s AND moshuma = %s
        """
        result = self.execute_query(check_sql, (order_info['dingdanhao'], order_info['moshuma']), fetch=True)
        
        if result:
            LogInfo(f"订单 {order_info['dingdanhao']} 已存在，将更新信息")
            return self.update_order_profit(
                order_info['dingdanhao'], 
                order_info['moshuma'],
                order_info['geyefei'],
                order_info['shouxufei'],
                order_info['yingkuijine'],
                order_info['jingyingkuijine']
            )
        
        # 订单不存在，执行插入
        insert_sql = """
        INSERT INTO xiangxidanxinxibiao (
            dingdanhao, kaidanshijian, fangxaing, shoushu, huobi, 
            kaidanjiage, pingtaileixing, zhushi, moshuma, geyefei, 
            shouxufei, yingkuijine, jingyingkuijine, tongyibiaoshi, kaicangzhuzhanghao
        ) VALUES (
            %s, %s, %s, %s, %s, 
            %s, %s, %s, %s, %s, 
            %s, %s, %s, %s, %s
        )
        """
        params = (
            order_info['dingdanhao'], order_info['kaidanshijian'], order_info['fangxaing'], 
            order_info['shoushu'], order_info['huobi'], order_info['kaidanjiage'], 
            order_info['pingtaileixing'], order_info['zhushi'], order_info['moshuma'], 
            order_info['geyefei'], order_info['shouxufei'], order_info['yingkuijine'], 
            order_info['jingyingkuijine'], order_info['tongyibiaoshi'], order_info['kaicangzhuzhanghao']
        )
        
        result = self.execute_query(insert_sql, params)
        if result:
            LogInfo(f"成功插入订单 {order_info['dingdanhao']} 的详细信息")
        else:
            LogInfo(f"插入订单 {order_info['dingdanhao']} 的详细信息失败")
        
        return result

    def update_order_profit(self, order_id, magic_number, overnight_fee, commission, profit, total_profit):
        """
        更新订单的盈亏信息
        
        参数:
            order_id: 订单号
            magic_number: 识别码
            overnight_fee: 隔夜费
            commission: 手续费
            profit: 盈亏金额
            total_profit: 总盈亏金额
        """
        update_sql = """
        UPDATE xiangxidanxinxibiao 
        SET geyefei = %s, shouxufei = %s, yingkuijine = %s, jingyingkuijine = %s 
        WHERE dingdanhao = %s AND moshuma = %s
        """
        result = self.execute_query(update_sql, (overnight_fee, commission, profit, total_profit, order_id, magic_number))
        
        if result:
            LogInfo(f"成功更新订单 {order_id} 的盈亏信息")
        else:
            LogInfo(f"更新订单 {order_id} 的盈亏信息失败")
        
        return result

    def get_open_orders(self, account, magic_number, platform_type):
        """获取所有未平仓订单"""
        sql = """
        SELECT * FROM xiangxidanxinxibiao 
        WHERE kaicangzhuzhanghao = %s 
        AND moshuma = %s 
        AND pingtaileixing = %s 
        AND pingcangbiaoshi = 0
        """
        return self.execute_query(sql, (account, magic_number, platform_type), fetch=True)

    def update_closed_order(self, order_id, magic_number):
        """将订单标记为已平仓"""
        sql = """
        UPDATE xiangxidanxinxibiao 
        SET pingcangbiaoshi = 2 
        WHERE tongyibiaoshi = %s AND moshuma = %s
        """
        result = self.execute_query(sql, (order_id, magic_number))
        if result:
            LogInfo(f"订单 {order_id} 已标记为平仓状态2")
        else:
            LogInfo(f"更新订单 {order_id} 平仓状态失败")
        return result

    def get_orders_to_close(self, account, magic_number, platform_type):
        """获取所有需要平仓的订单(pingcangbiaoshi=1或2)"""
        sql = """
        SELECT * FROM xiangxidanxinxibiao 
        WHERE kaicangzhuzhanghao = %s 
        AND moshuma = %s 
        AND pingtaileixing = %s 
        AND (pingcangbiaoshi = 1 OR pingcangbiaoshi = 2)
        ORDER BY IntID ASC
        LIMIT 1
        """
        return self.execute_query(sql, (account, magic_number, platform_type), fetch=True)

    def check_risk_control_account(self, account):
        """查询账号的风控信息是否存在"""
        sql = """
        SELECT * FROM fengkongjine 
        WHERE zhanghao = %s
        """
        return self.execute_query(sql, (account,), fetch=True)

    def insert_risk_control(self, account, platform_type, balance, equity, update_time):
        """插入新的风控信息"""
        sql = """
        INSERT INTO fengkongjine 
        (zhanghao, pingtai, yue, jingzhi, genxinshijian) 
        VALUES (%s, %s, %s, %s, %s)
        """
        result = self.execute_query(sql, (account, platform_type, balance, equity, update_time))
        if result:
            LogInfo(f"成功插入账号 {account} 的风控信息")
        else:
            LogInfo(f"插入账号 {account} 的风控信息失败")
        return result

    def update_risk_control(self, account, balance, equity, update_time):
        """更新账号的风控信息"""
        sql = """
        UPDATE fengkongjine 
        SET yue = %s, jingzhi = %s, genxinshijian = %s 
        WHERE zhanghao = %s
        """
        result = self.execute_query(sql, (balance, equity, update_time, account))
        if result:
            LogInfo(f"成功更新账号 {account} 的风控信息")
        else:
            LogInfo(f"更新账号 {account} 的风控信息失败")
        return result

    def get_chase_orders_info(self, account, magic_number):
        """获取追单信息，用于平仓决策"""
        sql = """
        SELECT d.tongyibiaoshi, d.Ocount, c.zengjiashijian, c.zhuidancishu 
        FROM chufakaidanbiao AS c 
        INNER JOIN  
        (
            SELECT b.tongyibiaoshi, count(*) AS Ocount 
            FROM 
            (
                SELECT * FROM xiangxidanxinxibiao 
                WHERE moshuma = %s AND kaicangzhuzhanghao = %s
            ) AS b
            GROUP BY b.tongyibiaoshi
        ) AS d 
        ON c.adingdanhao = d.tongyibiaoshi
        """
        return self.execute_query(sql, (magic_number, account), fetch=True)

# symbol_d=[]
# for i in range(len(symbol_Id)):
#     symbol_d.append(copy.deepcopy(deque([0]*9,maxlen=9)))
# Tblock=[copy.deepcopy(symbol_d),copy.deepcopy(symbol_d)] 
# 订单状态字典 = {"N" : '无',"0" : '已发送',"1" : '已受理',"2" : '待触发',"3" : '已生效',"4" : '已排队',"5" : '部分成交',"6" : '完全成交',
# "7" : '待撤',"8" : '待改', "9" : '已撤单',"A" : '已撤余单',"B" : '指令失败',"C" : '待审核',"D" : '已挂起',"E" : '已申请',"F" : '无效单',"G" : '部分触发',"H" : '完全触发',"I" : '余单失败',}
CloseTime,itk,otk=0,0,0
DB_CONFIG = None
# 数据库连接池和交易数据库对象
db_pool = None
trade_db = None
def initialize(context): 
    global g_params, DB_CONFIG, CloseTime, db_pool, trade_db, itk, otk
    itk = g_params['开仓超价跳数']
    otk = g_params['平仓超价跳数']
    for i in symbol_Id:
        LogInfo("订阅",i,"合约Tick实时行情数据")
        SetBarInterval(i,'T', 0, 100,isTrigger=True) #订阅信号合约
    # SetBarInterval("SHFE|Z|AU|MAIN",'T', 0, 100) #沪金合约交易时间最长，订阅沪金保证全交易时间可靠触发
    #for i in userNo_Id2:
    #    LogInfo("设置",i,"账户交易")
    # #    SetUserNo(str(i))     
    # SetActual()                      #设置实盘运行
    # SetOrderWay(1)                   #设置实时发单
    # SetTriggerType(1)                #设置即时行情触发
    # SetTriggerType(2)                #设置交易数据触发
    # #SetTriggerType(5)               #设置K线触发
    # #SetTriggerType(6)               #连接状态触发
    # #SetTradeDirection(0)            #设置0双向交易,1多头,2空头

    # # 配置 MySQL 连接参数
    # DB_CONFIG = {
    #     "host": 服务器地址,
    #     "user": 服务器用户名,
    #     "password": 链接数据库密码,
    #     "database": 数据库名称,
    #     "charset": "utf8mb4",
    #     "cursorclass": pymysql.cursors.DictCursor
    # }
    
    # # 创建数据库连接池和交易数据库对象
    # db_pool = MySQLPool(DB_CONFIG)
    # trade_db = TradeDB(db_pool)
    
    # CloseTime = g_params['收盘清仓倒计时分钟']
    # LogInfo("策略初始化完成，数据库连接已建立")

    # # 启动数据库工作线程
    # db_thread = threading.Thread(target=db_worker)
    # db_thread.daemon = True
    # db_thread.start()
    
# 全局队列
pending_orders_queue = Queue()
order_results_queue = Queue()
# 全局订单ID映射
order_id_mapping = {}

# 数据库工作线程
def db_worker():
    while True:
        try:
            # 定期从数据库读取新订单（例如每1秒）
            for symbol in symbol_Id:
                orders = trade_db.get_pending_orders(symbol)
                for order in orders:
                    pending_orders_queue.put(order)
            
            # 处理需要写回数据库的结果
            while not order_results_queue.empty():
                result = order_results_queue.get_nowait()
                trade_db.update_order_status(result['order_id'], result['status'], result['trade_result'])
            
            time.sleep(1)  # 控制查询频率
        except Exception as e:
            LogInfo(f"数据库工作线程异常: {e}")
            time.sleep(5)  # 发生异常时稍等长一些再重试


DateQ,strategyStatusQ=deque([0,0],maxlen=3),deque([0,0],maxlen=3) 
BKS,SKS,BPS,SPS=0,0,0,0
def handle_data(context):
    global g_params, db_pool, trade_db
    # 检查行情数据是否准备好
    HTS=0#1 if context.strategyStatus()=="C" else 0  
    if CurrentBar(symbol_Id[0],'T', 0) == 0:
        return
    # for i in range(len(symbol_Id)):
    #     symbol = symbol_Id[i]
    #     # 获取对应的外盘映射和平台类型
    #     forex_symbol = forex_marketId[i] if i < len(forex_marketId) else ""
    #     platform_type = 平台类型[i] if i < len(平台类型) else ""
    #     current_setting = 时时价格启用[i] if i < len(时时价格启用) else ""
    #     price_update_enabled = (current_setting or current_setting == "TRUE" or current_setting == "1" or current_setting == "YES")
    #     # LogInfo('处理合约:', symbol, '外盘映射:', forex_symbol, '平台类型:', platform_type, '价格更新:', price_update_enabled)
        
    #     # 获取该合约的最新价格
    #     last_priceA = Close(symbol,'T', 0)
    #     if len(last_priceA) == 0:
    #         LogInfo(f"合约 {symbol} 无有效价格，跳过")
    #         continue
    #     last_price = last_priceA[-1] if HTS==1 else Q_Last(symbol)
        
    #     # 如果启用实时价格更新且有外盘映射信息，则更新价格
    #     if price_update_enabled and forex_symbol and platform_type:
    #         try:
    #             # 将价格格式化为字符串，保留适当的小数位数
    #             formatted_price = f"{last_price:.2f}"
    #             # 更新数据库中的价格
    #             update_result = trade_db.update_price(价格表,forex_symbol, formatted_price, platform_type)
    #             # LogInfo(f"更新价格结果: {update_result} - {forex_symbol} 价格 {formatted_price} 平台 {platform_type}")
    #         except Exception as e:
    #             LogInfo(f"更新价格失败: {e}")
        

        
    # #     # 处理订单部分的代码
    # #     # 应根据实际需求完成...

    # # # 处理新的开单信号
    # process_new_orders(context,HTS,itk,otk)
    
    # # 处理活跃订单，更新信息到数据库
    # process_active_orders(context)
    
    # # 检测已平仓订单
    # check_closed_orders(context)

    # # 从数据库中检测并平仓标记为需要平仓的订单
    # close_orders_from_db(context, HTS, otk)

    # # 更新账户风控信息
    # update_risk_control_info(context)
    
    # # 检测追单平仓
    # check_chase_orders(context, HTS, otk)

def execute_order(order, symbol, current_price):
    direction = order['afangxiang']
    lots = order['ashoushu']
    db_order_id = order['adingdanhao']
    
    if direction == 1:  # 买入
        ret, order_id = Buy(lots, current_price, symbol)
    elif direction == 2:  # 卖出
        ret, order_id = SellShort(lots, current_price, symbol)
    
    # 保存映射关系
    if ret:
        order_id_mapping[order_id] = db_order_id
    else:
        # 下单失败，直接更新结果
        result = {
            'order_id': db_order_id,
            'status': -1,
            'trade_result': "下单失败"
        }
        order_results_queue.put(result)

def check_if_near_close(current_time, close_time_minutes):
    """检查是否接近收盘时间"""
    # 这里需要根据实际情况实现判断逻辑
    # 简单示例：假设current_time是0.HHMMSS格式
    # 实际中可能需要考虑不同合约的交易时间等
    
    # 假设实现，实际需根据您的时间格式和交易时间安排调整
    return False

def close_all_positions():
    """平掉所有持仓"""
    for i in range(len(symbol_Id)):
        symbol = symbol_Id[i]
        
        # 平掉多头持仓
        if BuyPosition(symbol) > 0:
            LogInfo(f"清仓多头持仓: {symbol}")
            Sell(BuyPosition(symbol), Q_LastPrice(symbol), symbol)
        
        # 平掉空头持仓
        if SellPosition(symbol) > 0:
            LogInfo(f"清仓空头持仓: {symbol}")
            BuyToCover(SellPosition(symbol), Q_LastPrice(symbol), symbol)

def floattime_sum(floatin1, floatin2, len_set=12):  # 高精度浮点时间求和（精确到毫秒）
    # 设置浮点数格式，保留len_set位小数
    lensave = f"%0.{len_set}f"
    
    # 格式化浮点数并提取各时间部分
    def extract_time_parts(floatin):
        strfloat = lensave % floatin
        return int(strfloat[2:4]), int(strfloat[4:6]), int(strfloat[6:8]), int(strfloat[8:11])
    
    h1, m1, s1, ms1 = extract_time_parts(floatin1)
    h2, m2, s2, ms2 = extract_time_parts(floatin2)
    
    # 计算总和并处理进位
    total_ms = ms1 + ms2
    ms_carry = total_ms // 1000
    new_ms = total_ms % 1000
    
    total_s = s1 + s2 + ms_carry
    s_carry = total_s // 60
    new_s = total_s % 60
    
    total_m = m1 + m2 + s_carry
    m_carry = total_m // 60
    new_m = total_m % 60
    
    new_h = h1 + h2 + m_carry
    new_h = min(new_h, 99)  # 限制小时数不超过99
    
    # 组合新的浮点时间字符串并转换回浮点数
    new_str_time = f"0.{new_h:02}{new_m:02}{new_s:02}{new_ms:03}"
    return float(new_str_time)

def TimeTo_Minutes(time_in):
    timestr='%0.6f'%time_in
    hsave=int(timestr[2:4])
    msave=int(timestr[4:6])
    tcout=hsave*60+msave
    return tcout

def SessionOpenTime(contractId=''):  # 获取交易时段开盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler:continue
        tlout.append(GetSessionStartTime(contractId, i))  # 获取每个交易时段的开盘时间并加入列表
    return tlout

def SessionCloseTime(contractId=''):  # 获取交易时段收盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler-1:continue
        tlout.append(GetSessionEndTime(contractId, i))  # 获取每个交易时段的收盘时间并加入列表
    return tlout

def time_string_to_seconds(time_str):
    """
    Convert a time string in the format 'YYYYMMDDHHMMSSmmm' to the number of seconds
    since the Unix epoch (1970-01-01 00:00:00 UTC).
    
    Parameters:
        time_str (str): Time string in the format 'YYYYMMDDHHMMSSmmm'.
    
    Returns:
        int: Total seconds since the Unix epoch.
    """
    if time_str == "0":
        return 0  # Return 0 or an appropriate default value for historical phases or no real-time data
    
    # Parse the time string
    year = int(time_str[0:4])
    month = int(time_str[4:6])
    day = int(time_str[6:8])
    hour = int(time_str[8:10])
    minute = int(time_str[10:12])
    second = int(time_str[12:14])
    millisecond = int(time_str[14:17])
    
    # Create a datetime object
    dt = datetime(year, month, day, hour, minute, second, millisecond * 1000)
    
    # Calculate the total seconds since the Unix epoch
    epoch = datetime(1970, 1, 1)
    total_seconds = (dt - epoch) // timedelta(seconds=1)  # Use integer division to get whole seconds
    
    return total_seconds

from datetime import datetime, timedelta

def time_int_to_seconds(time_int):
    """
    Convert a time integer in the format YYYYMMDDHHMMSSmmm to the number of seconds
    since the Unix epoch (1970-01-01 00:00:00 UTC).
    
    Parameters:
        time_int (int): Time integer in the format YYYYMMDDHHMMSSmmm.
    
    Returns:
        int: Total seconds since the Unix epoch.
    """
    if time_int == 0:
        return 0  # Return 0 for historical phases or no real-time data
    
    # Extract each component directly using integer arithmetic
    milliseconds = time_int % 1000
    time_int //= 1000
    seconds = time_int % 100
    time_int //= 100
    minutes = time_int % 100
    time_int //= 100
    hours = time_int % 100
    time_int //= 100
    day = time_int % 100
    time_int //= 100
    month = time_int % 100
    year = time_int // 100
    
    # Create a datetime object
    dt = datetime(year, month, day, hours, minutes, seconds, milliseconds * 1000)
    
    # Calculate the total seconds since the Unix epoch
    epoch = datetime(1970, 1, 1)
    total_seconds = (dt - epoch) // timedelta(seconds=1)  # Use integer division to get whole seconds
    
    return total_seconds

def VTS(time_in, contractId=''):  # 根据输入时间和合约ID计算交易时段
    RTS, CTS, TSession = [], [], []  # 初始化三个列表，用于存储修正后的时间、收盘时间和交易时段
    opentimet = SessionOpenTime(contractId)  # 获取所有交易时段的开盘时间
    Closetimet = SessionCloseTime(contractId)  # 获取所有交易时段的收盘时间
    for open_time, close_time in zip(opentimet, Closetimet):
        if time_in > open_time:  # 判断输入时间是否在开盘时间之后
            RTS.append(time_in)  # 如果是，加入RTS列表
        else:
            RTS.append(floattime_sum(time_in, 0.24))  # 如果不是，修正时间后加入RTS列表
        
        if close_time > open_time:  # 判断收盘时间是否在开盘时间之后
            CTS.append(close_time)  # 如果是，加入CTS列表
        else:
            CTS.append(floattime_sum(close_time, 0.24))  # 如果不是，修正时间后加入CTS列表
        
        if open_time < RTS[-1] < CTS[-1]:  # 判断修正后的时间是否在交易时段内
            TSession.append(len(RTS) - 1)  # 如果是，加入TSession列表

    if len(TSession) == 1:  # 如果只有一个交易时段
        idx = TSession[0]
        return idx, opentimet[idx], RTS[idx], CTS[idx]  # 返回交易时段和相关时间
    else:
        return -1, time_in, time_in, time_in  # 否则返回默认值
    
def his_trigger(BK,SK,qty,itk,tcode):#历史开仓
    global BKS,SKS
    if BK and BKS==0:
        iprc = Close(tcode,'T', 0)[-1]+itk*PriceTick(tcode)
        Buy(qty, iprc,tcode) 
        LogInfo(Time(),"->合约==>",tcode,"多单买入开仓价==>",iprc,"买入数量==>",qty)
        # BKS=1    
    elif SK and SKS==0:
        iprc = Close(tcode,'T', 0)[-1]-itk*PriceTick(tcode)
        SellShort(qty, iprc,tcode) 
        LogInfo(Time(),"->合约==>",tcode,"空单卖出开仓价==>",iprc,"卖出数量==>",qty)
        # SKS=1    
def his_trigger_Exit(BP,SP,otk,clots,tcode):#历史平仓
    global BKS,SKS,BPS,SPS
    if BP and BPS==0 and SellPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,SellPosition(tcode))
        prc =Close(tcode,'T', 0)[-1]+otk*PriceTick(tcode)
        BuyToCover(_lots,prc,tcode)
        LogInfo(Time(),"->合约==>",tcode,"空单买入平仓价==>",prc,"买入平仓数量==>",_lots)
        # BPS=1  
        # if SKS==1:SKS=2   
    elif SP and SPS==0 and BuyPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,BuyPosition(tcode))
        prc =Close(tcode,'T', 0)[-1]-otk*PriceTick(tcode)
        Sell(_lots,prc,tcode)
        LogInfo(Time(),"->合约==>",tcode,"多单卖出平仓价==>",prc,"卖出平仓数量==>",_lots)
        # SPS=1  
        # if BKS==1:BKS=2  
def tim_trigger(BK,SK,qty,itk,tcode):#盘中实时开仓
    global BKS,SKS
    if BK and BKS==0 and (A_BuyPosition(tcode) == 0) :
        iprc = min(Q_AskPrice(tcode) +itk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        retEntry,EntryOrderId=A_SendOrder(Enum_Buy(), Enum_Entry(), qty, iprc,tcode) 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单买入开仓价==>",iprc,"买入数量==>",qty)
        # BKS=1    
    elif SK and SKS==0 and (A_SellPosition(tcode) == 0):    
        iprc = max(Q_BidPrice(tcode) - itk*PriceTick(tcode), Q_LowLimit(tcode))  # 对盘超价                         
        retEntry,EntryOrderId=A_SendOrder(Enum_Sell(), Enum_Entry(), qty, iprc,tcode)   
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单卖出开仓价==>",iprc,"卖出数量==>",qty)
        # SKS=1  
    return retEntry,EntryOrderId    #   

def tim_trigger_Exit(BP,SP,otk,clots,tcode):#盘中实时平仓
    global BKS,SKS,BPS,SPS
    if BP and BPS==0 and A_SellPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_SellPosition(tcode))
        prc = min(Q_AskPrice(tcode) +otk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        if ExchangeName(tcode) not in ['SHFE','INE']:    
            retExit, ExitOrderId=A_SendOrder(Enum_Buy(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodaySellPosition(tcode)
            dlots=lots-tlots            
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),lots, prc,tcode) #今仓足够平仓,上期所能交所优先超价全部平今仓    
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Buy(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Buy(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单买入平仓价==>",prc,"买入平仓数量==>",_lots)
        # BPS=1  
        # if SKS==1:SKS=2      
    elif SP and SPS==0 and A_BuyPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_BuyPosition(tcode))
        prc = max(Q_BidPrice(tcode) - otk*PriceTick(tcode), Q_LowLimit(tcode))
        if ExchangeName(tcode) not in ['SHFE','INE']:
            retExit, ExitOrderId=A_SendOrder(Enum_Sell(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodayBuyPosition(tcode)
            dlots=lots-tlots
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),lots, prc,tcode)   #今仓足够平仓,上期所能交所优先超价全部平今仓  
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Sell(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Sell(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单卖出平仓价==>",prc,"卖出平仓数量==>",_lots)
        # SPS=1    
        # if BKS==1:BKS=2       

# 实现获取原始订单ID的函数
def get_original_order_id(platform_order_id):
    """
    从平台订单ID获取原始数据库订单ID
    
    参数:
        platform_order_id: 平台的订单ID
        
    返回:
        原始数据库中的订单ID
    """
    global order_id_mapping
    if platform_order_id in order_id_mapping:
        return order_id_mapping[platform_order_id]
    else:
        LogInfo(f"警告: 找不到订单ID映射: {platform_order_id}")
        return None

# 极智量化平台的订单回调函数（根据平台规范调整函数名）
def OnOrder(context, order):
    """
    订单状态变化的回调函数
    """
    LogInfo(f"订单回调: {order.OrderNo}, 状态: {order.Status}")
    
    # 处理订单状态变化
    if order.Status == "已成交": # 根据平台实际的状态值调整
        if order.OrderNo in order_id_mapping:
            db_order_id = order_id_mapping[order.OrderNo]
            
            result = {
                'order_id': db_order_id,
                'status': 6,  # 假设6表示已成交
                'trade_result': f"成交价: {order.Price}, 成交量: {order.Volume}"
            }
            order_results_queue.put(result)

# 极智量化平台的成交回调函数（根据平台规范调整函数名）
def OnTrade(context, trade):
    """
    成交回调函数
    """
    LogInfo(f"成交回调: 订单号 {trade.OrderNo}, 价格 {trade.Price}, 数量 {trade.Volume}")
    
    if trade.OrderNo in order_id_mapping:
        db_order_id = order_id_mapping[trade.OrderNo]
        
        result = {
            'order_id': db_order_id,
            'status': 6,  # 假设6表示已成交
            'trade_result': f"成交价: {trade.Price}, 成交量: {trade.Volume}"
        }
        order_results_queue.put(result)
def process_new_orders(context,_HTS,_itk,_otk):
    """处理新的开单信号并执行开仓操作"""
    global db_pool, trade_db, 注册账号, 魔术码
    try:
        # 检查注册账号和魔术码是否为空
        if len(注册账号) == 0 or len(魔术码) == 0:
            LogInfo("错误: 注册账号或魔术码未设置")
            return
            
        # 确保二者长度相同
        if len(注册账号) != len(魔术码):
            LogInfo(f"警告: 注册账号数量({len(注册账号)})与魔术码数量({len(魔术码)})不一致")
            
        # 创建一个空列表存储所有查询到的订单
        all_orders = []
        
        # 遍历所有注册账号和魔术码对
        account_count = min(len(注册账号), len(魔术码))
        LogInfo(f"开始查询 {account_count} 个账号的订单信号")
        
        for i in range(account_count):
            account = 注册账号[i]
            magic_number = 魔术码[i]
            
            if not account or not magic_number:
                LogInfo(f"跳过空账号或魔术码: 索引 {i}")
                continue
                
            LogInfo(f"查询账号 {account} 魔术码 {magic_number} 的订单")
            
            # 查询新开单信号
            new_orders = trade_db.get_new_orders(account, magic_number)
            if new_orders:
                LogInfo(f"账号 {account} 发现 {len(new_orders)} 个新开单信号")
                # 将查询结果添加到总订单列表中
                all_orders.extend(new_orders)
        
        # 如果没有找到任何订单，直接返回
        if not all_orders:
            LogInfo("未发现任何新开单信号")
            return
            
        LogInfo(f"总共发现 {len(all_orders)} 个新开单信号，开始处理")
        
        # 处理每个订单
        for order in all_orders:
            try:
                # 提取订单信息
                order_id = order['IntID']  # 序号=0
                symbol = order['ahuobi']   # 货币=2
                direction = order['afangxiang']  # 方向=3
                slots = float(order['ashoushu'])  # 手数=4
                order_account = order['kaicangzhuzhanghao']  # 获取订单关联的账号
                order_magic = order['moshuma']  # 获取订单关联的魔术码
                
                # 检查合约是否存在
                if symbol not in forex_marketId.values:
                    LogInfo(f"警告: 合约 {symbol} 不在订阅列表中，跳过")
                    continue

                LogInfo(f"处理新开单信号:序号 {order_id} 账号 {order_account} 合约 {symbol}, 方向 {direction}, 手数 {slots}")
                
                for i in range(len(symbol_Id)):
                    if forex_marketId[i]==symbol:
                        # 获取最新价格
                        OrderSymbol = symbol_Id[i]
                        lots=int(slots*下单量映射倍数[i])
                        last_priceA = Close(OrderSymbol,'T', 0)
                        if len(last_priceA) == 0:
                            LogInfo(f"{symbol}映射的订单合约 {OrderSymbol} 无有效价格，跳过")
                            continue
                        last_price = last_priceA[-1] if _HTS==1 else Q_Last(OrderSymbol)

                        if last_price <= 0:
                            LogInfo(f"警告: 合约 {symbol} 无有效价格，跳过")
                            continue
                        
                        # 执行开仓
                        platform_order_id = None
                        if direction == "B" or direction == 1:  # 买入
                            if _HTS==1:
                                ret, platform_order_id = tim_trigger(True,False,lots,itk,OrderSymbol)
                            else:
                                ret='h'
                                platform_order_id=999999
                                his_trigger(True,False,lots,itk,OrderSymbol)
                            LogInfo(f"买入开仓: {symbol}映射的订单合约 {OrderSymbol}, 手数: {lots}, 价格: {last_price}, 订单号: {platform_order_id}")
                        elif direction == "S" or direction == 2:  # 卖出
                            if _HTS==1:
                                ret, platform_order_id = tim_trigger(False,True,lots,itk,OrderSymbol)
                            else:
                                ret='h'
                                platform_order_id=999999
                                his_trigger(False,True,lots,itk,OrderSymbol)
                            LogInfo(f"卖出开仓: {symbol}映射的订单合约 {OrderSymbol}, 手数: {lots}, 价格: {last_price}, 订单号: {platform_order_id}")
                        else:
                            LogInfo(f"未知交易方向: {direction}")
                            continue
                        
                        # 检查开仓是否成功
                        if ret and platform_order_id:
                            # 获取当前时间
                            current_time = time.strftime("%Y-%m-%d %H:%M:%S")

                            # 更新数据库记录
                            update_result = trade_db.update_order_after_open(
                                order_id, platform_order_id, current_time, order_magic
                            )

                            if update_result:
                                LogInfo(f"订单状态已更新: ID={order_id}, 平台订单号={platform_order_id}")
                            else:
                                LogInfo(f"订单状态更新失败: ID={order_id}")
                        else:
                            LogInfo(f"开仓失败: {symbol}, 方向: {direction}, 手数: {lots}")
                    
            except Exception as e:
                LogInfo(f"处理订单时发生错误: {e}, 订单ID: {order.get('IntID', 'unknown')}")
                
    except Exception as e:
        LogInfo(f"处理开单信号时发生错误: {e}")

def process_active_orders(context):
    """
    处理当前活跃订单，将新订单添加到数据库并更新已有订单的盈亏信息
    """
    global db_pool, trade_db, 注册账号, 魔术码, 平台类型, 隔夜费, 手续费
    
    try:
        # 检查所有合约的持仓情况
        for i in range(len(symbol_Id)):
            symbol = symbol_Id[i]
            forex_symbol = forex_marketId[i] if i < len(forex_marketId) else ""
            platform_type = 平台类型[i] if i < len(平台类型) else ""
            
            # 获取当前合约的所有持仓订单
            long_orders = A_BuyPosition(symbol)  # 获取多头持仓订单
            short_orders = A_SellPosition(symbol)  # 获取空头持仓订单
            
            all_orders = []
            if long_orders is not None:
                all_orders.extend(long_orders)
            if short_orders is not None:
                all_orders.extend(short_orders)
            
            if not all_orders:
                continue
                
            # 处理每个活跃订单
            for order in all_orders:
                order_id = order.OrderNo
                entry_time = order.InsertTime
                direction = "B" if order.Direction == "BUY" else "S"
                volume = order.Volume
                price = order.Price
                current_price = Q_Last(symbol)
                
                # 计算盈亏
                if direction == "B":
                    point_profit = (current_price - price) * volume
                else:
                    point_profit = (price - current_price) * volume
                    
                # 将点差盈亏转换为金额，这里需要根据您的计算方法调整
                profit = point_profit * PriceTick(symbol) * 10  # 假设每点值为 PriceTick * 10
                
                # 计算手续费和隔夜费
                order_commission = float(手续费[i]) if i < len(手续费) else 0
                order_overnight = float(隔夜费[i]) if i < len(隔夜费) else 0
                
                # 计算净盈亏
                net_profit = profit - order_commission - order_overnight
                
                # 确定此订单应该使用哪个账号和魔术码
                # 这里简单使用第一个，您可能需要根据订单特征匹配正确的账号和魔术码
                account = 注册账号[0] if len(注册账号) > 0 else ""
                magic_number = 魔术码[0] if len(魔术码) > 0 else ""
                
                # 准备订单信息
                order_info = {
                    'dingdanhao': order_id,
                    'kaidanshijian': entry_time,
                    'fangxaing': direction,
                    'shoushu': volume,
                    'huobi': forex_symbol,
                    'kaidanjiage': price,
                    'pingtaileixing': platform_type,
                    'zhushi': f"自动同步订单",
                    'moshuma': magic_number,
                    'geyefei': order_overnight,
                    'shouxufei': order_commission,
                    'yingkuijine': profit,
                    'jingyingkuijine': net_profit,
                    'tongyibiaoshi': order_id,  # 使用订单号作为统一标识
                    'kaicangzhuzhanghao': account
                }
                
                # 插入或更新订单信息
                trade_db.insert_order_details(order_info)
                
    except Exception as e:
        LogInfo(f"处理活跃订单时发生错误: {e}")

def check_closed_orders(context):
    """检测已平仓但数据库中标记为未平仓的订单"""
    global db_pool, trade_db, 注册账号, 魔术码, 平台类型
    
    try:
        # 遍历所有账号、魔术码和平台类型组合
        account_count = min(len(注册账号), len(魔术码), len(平台类型))
        
        for i in range(account_count):
            account = 注册账号[i] if i < len(注册账号) else ""
            magic_number = 魔术码[i] if i < len(魔术码) else ""
            platform = 平台类型[i] if i < len(平台类型) else ""
            
            if not account or not magic_number or not platform:
                continue
                
            # 查询未平仓订单
            open_orders = trade_db.get_open_orders(account, magic_number, platform)
            if not open_orders:
                continue
                
            LogInfo(f"账号 {account} 在平台 {platform} 有 {len(open_orders)} 个未平仓订单")
            
            # 创建当前持仓订单号集合用于快速查找
            active_order_ids = set()
            
            # 获取所有合约的持仓订单号
            for j in range(len(symbol_Id)):
                symbol = symbol_Id[j]
                
                # 获取多头和空头持仓
                long_orders = A_BuyPosition(symbol)
                short_orders = A_SellPosition(symbol)
                
                # 收集所有活跃订单ID
                if long_orders:
                    for order in long_orders:
                        active_order_ids.add(order.OrderNo)
                        
                if short_orders:
                    for order in short_orders:
                        active_order_ids.add(order.OrderNo)
            
            # 检查每个未平仓订单是否还存在持仓
            for order in open_orders:
                order_id = order['tongyibiaoshi']  # 使用统一标识作为订单号
                
                # 如果订单不在活跃订单中，将其标记为已平仓
                if order_id not in active_order_ids:
                    LogInfo(f"发现已平仓订单: {order_id}，但在数据库中标记为未平仓，现在更新")
                    trade_db.update_closed_order(order_id, magic_number)
    
    except Exception as e:
        LogInfo(f"检测平仓订单时发生错误: {e}")

def close_orders_from_db(context, HTS, otk):
    """根据数据库中的标记执行平仓操作"""
    global db_pool, trade_db, 注册账号, 魔术码, 平台类型
    
    try:
        # 遍历所有账号、魔术码和平台类型组合
        account_count = min(len(注册账号), len(魔术码), len(平台类型))
        
        for i in range(account_count):
            account = 注册账号[i] if i < len(注册账号) else ""
            magic_number = 魔术码[i] if i < len(魔术码) else ""
            platform = 平台类型[i] if i < len(平台类型) else ""
            
            if not account or not magic_number or not platform:
                continue
                
            # 查询需要平仓的订单(每次只取一个)
            orders_to_close = trade_db.get_orders_to_close(account, magic_number, platform)
            if not orders_to_close:
                continue
                
            # 一次只处理一个订单
            order = orders_to_close[0]
            order_id = order['tongyibiaoshi']  # 使用统一标识作为订单号
            LogInfo(f"发现需要平仓的订单: {order_id}")
            
            # 获取当前所有活跃订单
            active_orders = {}
            for j in range(len(symbol_Id)):
                symbol = symbol_Id[j]
                forex_symbol = forex_marketId[j] if j < len(forex_marketId) else ""
                
                # 获取多头和空头持仓
                long_orders = A_BuyPosition(symbol)
                short_orders = A_SellPosition(symbol)
                
                # 收集所有活跃订单
                if long_orders:
                    for order_obj in long_orders:
                        active_orders[str(order_obj.OrderNo)] = {
                            'symbol': symbol,
                            'direction': 'BUY',
                            'volume': order_obj.Volume,
                            'forex_symbol': forex_symbol
                        }
                        
                if short_orders:
                    for order_obj in short_orders:
                        active_orders[str(order_obj.OrderNo)] = {
                            'symbol': symbol,
                            'direction': 'SELL',
                            'volume': order_obj.Volume,
                            'forex_symbol': forex_symbol
                        }
            
            try:
                # 检查订单是否存在于活跃订单中
                if str(order_id) not in active_orders:
                    LogInfo(f"订单 {order_id} 不在活跃订单中，可能已被平仓")
                    trade_db.update_closed_order(order_id, magic_number)
                    continue
                
                # 获取订单信息
                active_order = active_orders[str(order_id)]
                symbol = active_order['symbol']
                direction = active_order['direction']
                volume = active_order['volume']
                
                # 执行平仓操作
                if direction == 'BUY':
                    LogInfo(f"平仓多头订单: {order_id}, 合约: {symbol}, 手数: {volume}")
                    if HTS == 1:
                        ret, close_order_id = tim_trigger(False, True, volume, otk, symbol)
                    else:
                        ret = 'h'
                        close_order_id = 999999
                        his_trigger(False, True, volume, otk, symbol)
                else:  # SELL
                    LogInfo(f"平仓空头订单: {order_id}, 合约: {symbol}, 手数: {volume}")
                    if HTS == 1:
                        ret, close_order_id = tim_trigger(True, False, volume, otk, symbol)
                    else:
                        ret = 'h'
                        close_order_id = 999999
                        his_trigger(True, False, volume, otk, symbol)
                
                # 更新订单状态为已平仓
                if ret:
                    trade_db.update_closed_order(order_id, magic_number)
                    LogInfo(f"订单 {order_id} 已成功平仓，更新状态完成")
                else:
                    LogInfo(f"订单 {order_id} 平仓失败")
                    
            except Exception as e:
                LogInfo(f"平仓订单 {order_id} 时发生错误: {e}")
    
    except Exception as e:
        LogInfo(f"从数据库平仓订单时发生错误: {e}")

def update_risk_control_info(context):
    """更新账户风控信息到数据库"""
    global db_pool, trade_db, 注册账号, 平台类型
    
    try:
        # 获取账户信息
        account_info = GetAccountInfo()
        if not account_info:
            LogInfo("无法获取账户信息")
            return
            
        # 获取当前时间
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 遍历所有账号
        for i in range(len(注册账号)):
            account = 注册账号[i]
            platform = 平台类型[i] if i < len(平台类型) else "未知平台"
            
            if not account:
                continue
                
            # 获取账户余额和净值
            balance = account_info.Balance  # 账户余额
            equity = account_info.Equity    # 账户净值
            
            LogInfo(f"账号 {account} 当前余额: {balance}, 净值: {equity}")
            
            # 查询账号是否已存在于风控表中
            existing_record = trade_db.check_risk_control_account(account)
            
            if not existing_record:
                # 账号不存在，插入新记录
                trade_db.insert_risk_control(account, platform, balance, equity, current_time)
            else:
                # 账号已存在，更新记录
                trade_db.update_risk_control(account, balance, equity, current_time)
                
    except Exception as e:
        LogInfo(f"更新风控信息时发生错误: {e}")

def check_chase_orders(context, HTS, otk):
    """检测需要根据追单条件平仓的订单"""
    global db_pool, trade_db, 注册账号, 魔术码, 追单次数, 追单秒数
    
    try:
        # 获取当前北京时间
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        current_timestamp = time.mktime(time.strptime(current_time, "%Y-%m-%d %H:%M:%S"))
        
        # 遍历所有账号和魔术码组合
        account_count = min(len(注册账号), len(魔术码))
        
        for i in range(account_count):
            account = 注册账号[i] if i < len(注册账号) else ""
            magic_number = 魔术码[i] if i < len(魔术码) else ""
            max_chase_count = int(追单次数[i]) if i < len(追单次数) else 3  # 默认追单次数为3
            chase_seconds = int(追单秒数[i]) if i < len(追单秒数) else 300  # 默认追单秒数为300（5分钟）
            
            if not account or not magic_number:
                continue
                
            # 查询追单信息
            chase_orders = trade_db.get_chase_orders_info(account, magic_number)
            if not chase_orders:
                continue
                
            LogInfo(f"检测到 {len(chase_orders)} 个追单记录")
            
            # 获取当前所有活跃订单
            active_orders = {}
            for j in range(len(symbol_Id)):
                symbol = symbol_Id[j]
                
                # 获取多头和空头持仓
                long_orders = A_BuyPosition(symbol)
                short_orders = A_SellPosition(symbol)
                
                # 收集所有活跃订单
                if long_orders:
                    for order_obj in long_orders:
                        active_orders[str(order_obj.OrderNo)] = {
                            'symbol': symbol,
                            'direction': 'BUY',
                            'volume': order_obj.Volume
                        }
                        
                if short_orders:
                    for order_obj in short_orders:
                        active_orders[str(order_obj.OrderNo)] = {
                            'symbol': symbol,
                            'direction': 'SELL',
                            'volume': order_obj.Volume
                        }
            
            # 检查每个追单记录
            for order in chase_orders:
                order_id = order['tongyibiaoshi']
                count = order['Ocount']
                chase_count = order['zhuidancishu']
                
                # 检查增加时间
                if 'zengjiashijian' in order and order['zengjiashijian']:
                    add_time_str = order['zengjiashijian']
                    try:
                        # 解析时间字符串为时间戳
                        add_time = time.mktime(time.strptime(add_time_str, "%Y-%m-%d %H:%M:%S"))
                        time_diff = current_timestamp - add_time
                        
                        LogInfo(f"订单 {order_id} 距离增加时间已过 {time_diff} 秒，追单次数 {chase_count}")
                        
                        # 条件1: 检查时间差是否超过追单秒数
                        if count == 1 and time_diff >= chase_seconds:
                            LogInfo(f"订单 {order_id} 距离增加时间 {time_diff} 秒超过设定的 {chase_seconds} 秒，准备平仓")
                            close_chase_order(order_id, active_orders, magic_number, HTS, otk)
                            continue
                    except Exception as e:
                        LogInfo(f"解析订单 {order_id} 的时间时发生错误: {e}")
                
                # 条件2: 检查追单次数是否超过设定值
                if chase_count is not None and chase_count >= max_chase_count:
                    LogInfo(f"订单 {order_id} 追单次数 {chase_count} 已达到或超过设定的 {max_chase_count} 次，准备平仓")
                    close_chase_order(order_id, active_orders, magic_number, HTS, otk)
    
    except Exception as e:
        LogInfo(f"检测追单平仓时发生错误: {e}")

def close_chase_order(order_id, active_orders, magic_number, HTS, otk):
    """平仓追单订单"""
    global trade_db
    
    try:
        # 检查订单是否存在于活跃订单中
        if str(order_id) not in active_orders:
            LogInfo(f"订单 {order_id} 不在活跃订单中，可能已被平仓")
            trade_db.update_closed_order(order_id, magic_number)
            return
        
        # 获取订单信息
        active_order = active_orders[str(order_id)]
        symbol = active_order['symbol']
        direction = active_order['direction']
        volume = active_order['volume']
        
        # 执行平仓操作
        if direction == 'BUY':
            LogInfo(f"平仓追单多头订单: {order_id}, 合约: {symbol}, 手数: {volume}")
            if HTS == 1:
                ret, close_order_id = tim_trigger(False, True, volume, otk, symbol)
            else:
                ret = 'h'
                close_order_id = 999999
                his_trigger(False, True, volume, otk, symbol)
        else:  # SELL
            LogInfo(f"平仓追单空头订单: {order_id}, 合约: {symbol}, 手数: {volume}")
            if HTS == 1:
                ret, close_order_id = tim_trigger(True, False, volume, otk, symbol)
            else:
                ret = 'h'
                close_order_id = 999999
                his_trigger(True, False, volume, otk, symbol)
        
        # 更新订单状态为已平仓
        if ret:
            trade_db.update_closed_order(order_id, magic_number)
            LogInfo(f"追单订单 {order_id} 已成功平仓，更新状态完成")
        else:
            LogInfo(f"追单订单 {order_id} 平仓失败")
            
    except Exception as e:
        LogInfo(f"平仓追单订单 {order_id} 时发生错误: {e}")

    