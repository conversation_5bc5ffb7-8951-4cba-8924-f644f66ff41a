# 套利交易系统预警策略简化说明

## 变更概述
按照用户要求，对原有策略进行了简化，主要变更如下：

## 主要变更内容

### 1. 取消配置文件读取
- **删除内容**：Excel配置文件读取逻辑
- **原方式**：通过`pd.read_excel()`读取配置文件获取多组套利组合参数
- **新方式**：直接在`g_params`中配置单个套利组合参数

### 2. 单合约处理机制
- **删除内容**：多合约遍历处理的for循环
- **原方式**：使用列表存储多个套利组合，通过循环处理所有组合
- **新方式**：直接处理单个套利组合，去掉循环遍历

### 3. 数据结构简化
- **状态变量**：从列表改为单个变量
  - `UPStatus, DWStatus` 从 `[0]*TotalNumber` 改为 `0`
  - `symbol_d, QMA1, QMA2, QMA3` 等从列表改为单个deque
- **配置参数**：从列表改为单个值
  - `均线一, 均线二, 均线三` 等从列表改为单个数值

### 4. 删除不需要的函数
- `calculate_all_arbitrage_prices()` - 批量计算多个套利组合价格
- `handle_tick_update()` - 处理多个套利组合的Tick更新
- `get_all_first_contracts()` - 获取所有套利组合的第一个合约

### 5. 删除不需要的导入
- `import copy` - 不再需要深拷贝
- `import pandas as pd` - 不再读取Excel文件
- `from collections import defaultdict` - 不再需要默认字典

### 6. 新增CDP指标计算功能
- **新增函数**：`calculate_arbitrage_ohlc()` - 计算套利组合的开高低收价格
- **新增指标**：
  - `PT = REF(High(),1) - REF(Low(),1)` - 前一根K线的振幅
  - `CDP = (REF(High(),1) + REF(Low(),1) + REF(Close(),1))/3` - 前一根K线的中点价位
  - `sAH = CDP + PT` - 阻力位1
  - `sAL = CDP - PT` - 支撑位1
  - `sNH = 2*CDP - Low()` - 阻力位2
  - `sNL = 2*CDP - High()` - 支撑位2
- **数据存储**：新增`high_list`, `low_list`, `open_list`存储套利组合的OHLC数据
- **图表显示**：在图表上显示均线和CDP支撑阻力位

## 新的参数配置方式

```python
# 套利合约配置参数
g_params['套利组合'] = "DCE|F|P|2507+DCE|F|P|2506"  # 套利组合公式
g_params['均线一'] = 20           # 均线一周期
g_params['CDP指标参数'] = 3       # CDP指标参数  
g_params['初预警上突破点数'] = 5   # 初预警上突破点数
g_params['初预警下突破点数'] = 5   # 初预警下突破点数
g_params['正式预警上突破点数'] = 10 # 正式预警上突破点数
g_params['正式预警下突破点数'] = 10 # 正式预警下突破点数

# 音频文件路径配置
g_params['预警音频文件夹路径'] = "D:\\Quant000150v9.5\\Quant\\Strategy\\用户策略"
g_params['预警上突破音频文件'] = "预警上突破.wav"
g_params['预警下突破音频文件'] = "预警下突破.wav"

# 邮件配置参数
g_params['邮件发送'] = '关'        # 开为启用邮件发送，关为不发送
g_params['信息发送邮箱'] = '<EMAIL>'
g_params['邮箱SMTP地址'] = 'smtp.qq.com'
g_params['邮箱端口'] = 465
g_params['邮箱用户名'] = '<EMAIL>'
g_params['邮箱授权码'] = 'anmvczspnxonbheg'
g_params['信息接收邮箱'] = '<EMAIL>'
```

## 技术指标说明

### CDP指标计算公式
```
PT = REF(High(),1) - REF(Low(),1)              # 前一根K线的振幅
CDP = (REF(High(),1) + REF(Low(),1) + REF(Close(),1))/3  # 前一根K线的中点价位
sAH = CDP + PT                                  # 阻力位1
sAL = CDP - PT                                  # 支撑位1
sNH = 2*CDP - Low()                            # 阻力位2 (基于当前低点)
sNL = 2*CDP - High()                           # 支撑位2 (基于当前高点)
```

### 图表显示
- **组合K线**：套利组合的价格K线图
- **均线一**：白色线，根据设置的均线周期计算
- **sAH阻力位1**：红色线，CDP + PT
- **sAL支撑位1**：绿色线，CDP - PT  
- **sNH阻力位2**：橙色线，2*CDP - 当前低点
- **sNL支撑位2**：蓝色线，2*CDP - 当前高点

## 风险控制说明

### 遵循的编程规则
1. **最小变更原则**：只删除多合约处理相关代码，保留核心业务逻辑
2. **功能保持**：单个套利组合的预警功能完全保留
3. **参数化配置**：所有配置通过参数设置，便于调整
4. **新增功能**：加入CDP技术指标分析，增强技术分析能力

### 保留的核心功能
- 套利组合价格计算
- 均线计算与预警逻辑（已注释）
- 邮件发送功能
- 音频预警功能
- K线图表显示
- **新增**：CDP技术指标计算和显示

### 注意事项
1. 使用前需要根据实际需求修改`g_params`中的套利组合公式
2. 邮件配置需要填写真实的邮箱信息
3. 音频文件路径需要确保文件存在
4. 合约代码格式需要符合系统要求
5. **新增**：CDP指标需要足够的历史数据才能准确计算

## 使用方法
1. 修改`g_params['套利组合']`为实际的套利组合公式
2. 调整均线参数和CDP指标参数
3. 配置邮件相关参数（如需要）
4. 设置音频文件路径和文件名
5. 运行策略即可监控单个套利组合并查看CDP技术指标

## 技术架构改进
- **OHLC数据支持**：新增`calculate_arbitrage_ohlc()`函数，支持套利组合的开高低收价格计算
- **数据结构增强**：增加`high_list`, `low_list`, `open_list`存储完整的OHLC数据
- **指标计算模块化**：CDP指标计算逻辑独立，便于后续扩展其他技术指标 