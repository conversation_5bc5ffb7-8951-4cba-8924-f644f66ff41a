import talib


# 策略参数字典
g_params['多空选择'] = 0  #0为多空，1只做多，2只做空
g_params['进场点'] = 1  #1是第一进场点，2是第二进场点
g_params['止损四选一'] = 1  #1是1比1止盈止损，2是跟随K线止损，3是跟随C点止损，4是2比1止盈止损
g_params['止损最大值'] = 200
g_params['止损最小值'] = 20
g_params['时间1开始'] = 0.0
g_params['时间1结束'] = 0.24
g_params['时间2开始'] = 0.0
g_params['时间2结束'] = 0.0
g_params['时间3开始'] = 0.0
g_params['时间3结束'] = 0.0
g_params['每个时间段交易次数'] = 1
g_params['根数N'] = 3
g_params['小数点'] = 0
g_params['下单手数'] = 1
g_params['止损守几根K'] = 3
g_params['大于前几个B点'] = 1

frequency=0
A1,A2=0,0
Barl=[0]
Bars=[0]
G=[]
D=[]

# 策略开始运行时执行该函数一次
def initialize(context): 
    SetAFunUseForHis()

# 策略触发事件每次触发时都会执行该函数
def handle_data(context):
    global A1,A2,Barl,G,D,Bars,ZSB,ZSS,ZSO,ZYO,frequency
    if CurrentBar()<50:return
    A1=High()[-(g_params['根数N']+2)]>=Highest(High(),g_params['根数N'])[-(g_params['根数N']+3)] and\
    High()[-(g_params['根数N']+2)]>=Highest(High(),g_params['根数N'])[-2]
    A2=Low()[-(g_params['根数N']+2)]<=Lowest(Low(),g_params['根数N'])[-(g_params['根数N']+3)] and\
    Low()[-(g_params['根数N']+2)]<=Lowest(Low(),g_params['根数N'])[-2]
    Time1=g_params['时间1开始']<CurrentTime()<g_params['时间1结束']
    Time2=g_params['时间2开始']<CurrentTime()<g_params['时间2结束']
    Time3=g_params['时间3开始']<CurrentTime()<g_params['时间3结束']
    i=2
    x=0
    while True:
        if Close()[-i]>Open()[-i]:
            x+=1
        if x>=g_params['止损守几根K']:
            ZSB=Low()[-i]
            break
        i+=1
    i=2
    x=0
    while True:
        if Close()[-i]<Open()[-i]:
            x+=1
        if x>=g_params['止损守几根K']:
            ZSS=High()[-i]
            break
        i+=1
    if A1:
        if Barl[-1]!=1:
            Bars.append(CurrentBar()-(g_params['根数N']+1))
            Barl.append(1)
            G.append(High()[-(g_params['根数N']+2)])
            if len(G)>=1 and len(D)>=1:
                PlotPartLine('低',Bars[-2],D[-1],Bars[-2]-Bars[-1],G[-1],0xFFFFFF)
                PlotPartLine('低止损',Bars[-2],D[-1],Bars[-2]-Bars[-1],D[-1],RGB_Green())
        elif Barl[-1]==1 and G[-1]<High()[-(g_params['根数N']+2)]:
            Bars[-1]=(CurrentBar()-(g_params['根数N']+1))
            Barl[-1]=1
            G[-1]=High()[-(g_params['根数N']+2)]
            if len(G)>=1 and len(D)>=1:
                PlotPartLine('低',Bars[-2],D[-1],Bars[-2]-Bars[-1],G[-1],0xFFFFFF)
                PlotPartLine('低止损',Bars[-2],D[-1],Bars[-2]-Bars[-1],D[-1],RGB_Green())
    elif A2:
        if Barl[-1]!=2:
            Bars.append(CurrentBar()-(g_params['根数N']+1))
            Barl.append(2)
            D.append(Low()[-(g_params['根数N']+2)])
            if len(G)>=1 and len(D)>=1:
                PlotPartLine('高',Bars[-2],G[-1],Bars[-2]-Bars[-1],D[-1],0xFFFFFF)
                PlotPartLine('高止损',Bars[-2],G[-1],Bars[-2]-Bars[-1],G[-1])
        if Barl[-1]==2 and D[-1]>Low()[-(g_params['根数N']+2)]:
            Bars[-1]=CurrentBar()-(g_params['根数N']+1)
            Barl[-1]=2
            D[-1]=Low()[-(g_params['根数N']+2)]
            if len(G)>=1 and len(D)>=1:
                PlotPartLine('高',Bars[-2],G[-1],Bars[-2]-Bars[-1],D[-1],0xFFFFFF)
                PlotPartLine('高止损',Bars[-2],G[-1],Bars[-2]-Bars[-1],G[-1])
    if len(G)>=2 and len(D)>=2:
        if context.strategyStatus()=="H":
            if Time1==False and Time2==False and Time3==False:frequency=0
            if MarketPosition()>0 and Low()[-1]<D[-1] and g_params['止损四选一']==3:
                Sell(g_params['下单手数'],D[-1] if Open()[-1]>D[-1] else Open()[-1])
            elif MarketPosition()<0 and High()[-1]>G[-1] and g_params['止损四选一']==3:
                BuyToCover(g_params['下单手数'],G[-1] if Open()[-1]<G[-1] else Open()[-1])
            elif MarketPosition()>0 and Low()[-1]<ZSB and g_params['止损四选一']==2:
                Sell(g_params['下单手数'],ZSB if Open()[-1]>ZSB else Open()[-1])
            elif MarketPosition()<0 and High()[-1]>ZSS and g_params['止损四选一']==2:
                BuyToCover(g_params['下单手数'],ZSS if Open()[-1]<ZSS else Open()[-1])
            elif MarketPosition()>0 and Low()[-1]<ZSO and (g_params['止损四选一']==1 or g_params['止损四选一']==4):
                Sell(g_params['下单手数'],ZSO if Open()[-1]>ZSO else Open()[-1])
            elif MarketPosition()<0 and High()[-1]>ZSO and (g_params['止损四选一']==1 or g_params['止损四选一']==4):
                BuyToCover(g_params['下单手数'],ZSO if Open()[-1]<ZSO else Open()[-1])
            elif MarketPosition()>0 and High()[-1]>ZYO and (g_params['止损四选一']==1 or g_params['止损四选一']==4):
                Sell(g_params['下单手数'],ZYO if Open()[-1]<ZYO else Open()[-1])
            elif MarketPosition()<0 and Low()[-1]<ZYO and (g_params['止损四选一']==1 or g_params['止损四选一']==4):
                BuyToCover(g_params['下单手数'],ZYO if Open()[-1]>ZYO else Open()[-1])
            elif MarketPosition()>0 and Time1==False and Time2==False and Time3==False:
                Sell(g_params['下单手数'],Open()[-1])
            elif MarketPosition()<0 and Time1==False and Time2==False and Time3==False:
                BuyToCover(g_params['下单手数'],Open()[-1])
            if g_params['止损四选一']==1 or g_params['止损四选一']==3 or g_params['止损四选一']==4:
                threshold=g_params['止损最大值']>(G[-1]-D[-1])/PriceTick()>g_params['止损最小值']
            elif g_params['止损四选一']==2:
                threshold=True
            if D[-2]<D[-1] and D[-2]<G[-2] and G[-1]>D[-1] and D[-1]<G[-2] and G[-1]>G[-2]  and MarketPosition()>=0 \
            and Low()[-1]<D[-1] and Open()[-1]>D[-1] and Barl[-1]==1 and (g_params['多空选择']==0 or g_params['多空选择']==2)\
            and (Time1 or Time2 or Time3) and frequency<=g_params['每个时间段交易次数'] and g_params['进场点']==1 and threshold:
                SellShort(g_params['下单手数'],D[-1])
                ZSO=D[-1]-PriceTick()+(G[-1]-D[-1])
                ZYO=D[-1]-PriceTick()*2-(G[-1]-D[-1])*(1 if g_params['止损四选一']==1 else 2)
                frequency+=1
            elif G[-2]>G[-1] and G[-2]>D[-2] and D[-1]<G[-1] and G[-1]>D[-2] and D[-1]<D[-2] and MarketPosition()<=0 \
            and High()[-1]>G[-1] and Open()[-1]<G[-1] and Barl[-1]==2 and (g_params['多空选择']==0 or g_params['多空选择']==1)\
            and (Time1 or Time2 or Time3) and frequency<=g_params['每个时间段交易次数'] and g_params['进场点']==1 and threshold:
                Buy(g_params['下单手数'],G[-1])
                ZSO=G[-1]+PriceTick()-(G[-1]-D[-1])
                ZYO=G[-1]+PriceTick()*2+(G[-1]-D[-1])*(1 if g_params['止损四选一']==1 else 2)
                frequency+=1
            elif D[-2]<D[-1] and D[-2]<G[-1] and G[-1]>D[-1] and G[-1]-D[-1]<(G[-1]-D[-2])*(2/3) and G[-1]>=Highest(G,g_params['大于前几个B点'])[-2]\
            and MarketPosition()<=0 and High()[-1]>G[-1] and Open()[-1]<G[-1] and Barl[-1]==2 and (g_params['多空选择']==0 or g_params['多空选择']==1)\
            and (Time1 or Time2 or Time3) and frequency<=g_params['每个时间段交易次数'] and g_params['进场点']==2 and threshold:
                Buy(g_params['下单手数'],G[-1])
                ZSO=G[-1]+PriceTick()-(G[-1]-D[-1])
                ZYO=G[-1]+PriceTick()*2+(G[-1]-D[-1])*(1 if g_params['止损四选一']==1 else 2)
                frequency+=1
            elif G[-2]>G[-1] and G[-2]>D[-1] and D[-1]<G[-1] and G[-1]-D[-1]<(G[-2]-D[-1])*(2/3) and D[-1]<=Lowest(D,g_params['大于前几个B点'])[-2]\
            and MarketPosition()>=0 and Low()[-1]<D[-1] and Open()[-1]>D[-1] and Barl[-1]==1 and (g_params['多空选择']==0 or g_params['多空选择']==2)\
            and (Time1 or Time2 or Time3) and frequency<=g_params['每个时间段交易次数'] and g_params['进场点']==2 and threshold:
                SellShort(g_params['下单手数'],D[-1])
                ZSO=D[-1]-PriceTick()+(G[-1]-D[-1])
                ZYO=D[-1]-PriceTick()*2-(G[-1]-D[-1])*(1 if g_params['止损四选一']==1 else 2)
                frequency+=1
        elif context.strategyStatus()=="C":
            if Time1==False and Time2==False and Time3==False:frequency=0
            if A_TotalPosition()>0 and Q_Last()<D[-1] and g_params['止损四选一']==3:
                Sell(A_BuyPosition(),Q_BidPrice()-PriceTick()*3)
            elif A_TotalPosition()<0 and Q_Last()>G[-1] and g_params['止损四选一']==3:
                BuyToCover(A_SellPosition(),Q_AskPrice()+PriceTick()*3)
            elif A_TotalPosition()>0 and Q_Last()<ZSB and g_params['止损四选一']==2:
                Sell(A_BuyPosition(),Q_BidPrice()-PriceTick()*3)
            elif A_TotalPosition()<0 and Q_Last()>ZSS and g_params['止损四选一']==2:
                BuyToCover(A_SellPosition(),Q_AskPrice()+PriceTick()*3)
            elif A_TotalPosition()>0 and Q_Last()<ZSO and (g_params['止损四选一']==1 or g_params['止损四选一']==4):
                Sell(A_BuyPosition(),Q_BidPrice()-PriceTick()*3)
            elif A_TotalPosition()<0 and Q_Last()>ZSO and (g_params['止损四选一']==1 or g_params['止损四选一']==4):
                BuyToCover(A_SellPosition(),Q_AskPrice()+PriceTick()*3)
            elif A_TotalPosition()>0 and Q_Last()>ZYO and (g_params['止损四选一']==1 or g_params['止损四选一']==4):
                Sell(A_BuyPosition(),Q_BidPrice()-PriceTick()*3)
            elif A_TotalPosition()<0 and Q_Last()<ZYO and (g_params['止损四选一']==1 or g_params['止损四选一']==4):
                BuyToCover(A_SellPosition(),Q_AskPrice()+PriceTick()*3)
            elif A_TotalPosition()>0 and Time1==False and Time2==False and Time3==False:
                Sell(A_BuyPosition(),Q_BidPrice()-PriceTick()*3)
            elif A_TotalPosition()<0 and Time1==False and Time2==False and Time3==False:
                BuyToCover(A_SellPosition(),Q_AskPrice()+PriceTick()*3)
            if g_params['止损四选一']==1 or g_params['止损四选一']==3 or g_params['止损四选一']==4:
                threshold=g_params['止损最大值']>(G[-1]-D[-1])/PriceTick()>g_params['止损最小值']
            elif g_params['止损四选一']==2:
                threshold=True
            if G[-2]>G[-1] and G[-2]>D[-2] and D[-1]<G[-1] and G[-1]>D[-2] and D[-1]<D[-2] and A_TotalPosition()<=0 \
            and Q_Last()>G[-1] and Open()[-1]<G[-1] and Barl[-1]==2 and (g_params['多空选择']==0 or g_params['多空选择']==1)\
            and (Time1 or Time2 or Time3) and frequency<=g_params['每个时间段交易次数'] and g_params['进场点']==1 and threshold:
                Buy(g_params['下单手数'],Q_AskPrice()+PriceTick()*3)
                ZSO=G[-1]+PriceTick()-(G[-1]-D[-1])
                ZYO=G[-1]+PriceTick()*2+(G[-1]-D[-1])*(1 if g_params['止损四选一']==1 else 2)
                frequency+=1
            elif D[-2]<D[-1] and D[-2]<G[-2] and G[-1]>D[-1] and D[-1]<G[-2] and G[-1]>G[-2]  and A_TotalPosition()>=0 \
            and Q_Last()<D[-1] and Open()[-1]>D[-1] and Barl[-1]==1 and (g_params['多空选择']==0 or g_params['多空选择']==2)\
            and (Time1 or Time2 or Time3) and frequency<=g_params['每个时间段交易次数'] and g_params['进场点']==1 and threshold:
                SellShort(g_params['下单手数'],Q_BidPrice()-PriceTick()*3)
                ZSO=D[-1]-PriceTick()+(G[-1]-D[-1])
                ZYO=D[-1]-PriceTick()*2-(G[-1]-D[-1])*(1 if g_params['止损四选一']==1 else 2)
                frequency+=1
            elif D[-2]<D[-1] and D[-2]<G[-1] and G[-1]>D[-1] and G[-1]-D[-1]<(G[-1]-D[-2])*(2/3) and G[-1]>=Highest(G,g_params['大于前几个B点'])[-2]\
            and A_TotalPosition()<=0 and Q_Last()>G[-1] and Open()[-1]<G[-1] and Barl[-1]==2 and (g_params['多空选择']==0 or g_params['多空选择']==1)\
            and (Time1 or Time2 or Time3) and frequency<=g_params['每个时间段交易次数'] and g_params['进场点']==2 and threshold:
                Buy(g_params['下单手数'],Q_AskPrice()+PriceTick()*3)
                ZSO=G[-1]+PriceTick()-(G[-1]-D[-1])
                ZYO=G[-1]+PriceTick()*2+(G[-1]-D[-1])*(1 if g_params['止损四选一']==1 else 2)
                frequency+=1
            elif G[-2]>G[-1] and G[-2]>D[-1] and D[-1]<G[-1] and G[-1]-D[-1]<(G[-2]-D[-1])*(2/3) and D[-1]<=Lowest(D,g_params['大于前几个B点'])[-2]\
            and A_TotalPosition()>=0 and Q_Last()<D[-1] and Open()[-1]>D[-1] and Barl[-1]==1 and (g_params['多空选择']==0 or g_params['多空选择']==2)\
            and (Time1 or Time2 or Time3) and frequency<=g_params['每个时间段交易次数'] and g_params['进场点']==2 and threshold:
                SellShort(g_params['下单手数'],Q_BidPrice()-PriceTick()*3)
                ZSO=D[-1]-PriceTick()+(G[-1]-D[-1])
                ZYO=D[-1]-PriceTick()*2-(G[-1]-D[-1])*(1 if g_params['止损四选一']==1 else 2)
                frequency+=1

# 历史回测阶段结束时执行该函数一次
def hisover_callback(context):
    global frequency
    frequency=0