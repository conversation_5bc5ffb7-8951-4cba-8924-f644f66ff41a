import os
import sys
import time
import math
import copy
import mmap
import talib
import json
import struct
import pickle
import random
import datetime
import traceback
import threading
import subprocess
import tempfile
import numpy as np
import pandas as pd
from threading import Lock
from collections import deque
from typing import Any
g_params: Any  # 声明g_params为动态类型，规避未定义警告
LogInfo : Any  # 声明LogInfo为动态类型，规避未定义警告
LogError: Any  # 声明LogError为动态类型，规避未定义警告
LogWarn: Any  # 声明LogWarn为动态类型，规避未定义警告
SetTriggerType: Any  # 声明SetTriggerType为动态类型，规避未定义警告
SetOrderWay: Any  # 声明SetOrderWay为动态类型，规避未定义警告
SetActual: Any  # 声明SetActual为动态类型，规避未定义警告
SetBarInterval: Any  # 声明SetBarInterval为动态类型，规避未定义警告

class SharedMemoryPathManager:
    """统一管理共享内存路径的类，确保服务端和策略端路径一致"""
    
    @staticmethod
    def get_shared_memory_path(create_dir=True):
        """
        生成共享内存文件路径，各处使用的统一入口
        
        Args:
            create_dir: 是否创建目录
            
        Returns:
            str: 共享内存文件的绝对路径
        """
        # 1. 优先使用环境变量指定的固定路径
        if "QMT_SHARED_MEM_DIR" in os.environ:
            base_dir = os.environ["QMT_SHARED_MEM_DIR"]
        else:
            # 2. 不存在环境变量时，使用项目根目录 - 最关键的统一策略
            # 检测"Strategy"目录，确保在项目根目录结构中正确定位
            try:
                # 从当前文件开始向上查找项目根目录
                current_dir = os.path.dirname(os.path.abspath(__file__))
                strategy_dir = None
                
                # 向上最多查找3层目录
                for _ in range(3):
                    if os.path.basename(current_dir) == "Strategy" or os.path.basename(current_dir).lower() == "strategy":
                        strategy_dir = current_dir
                        break
                    parent = os.path.dirname(current_dir)
                    if parent == current_dir:  # 已到达根目录
                        break
                    current_dir = parent
                
                if strategy_dir:
                    # 在Strategy目录下创建共享目录
                    base_dir = os.path.join(strategy_dir, "qmt_trading_shm")
                else:
                    # 找不到Strategy目录时的备用方案
                    log_message = "无法确定Strategy目录，使用临时目录"
                    if 'logging' in globals():
                        logging.warning(log_message)
                    elif 'LogWarn' in globals():
                        LogWarn(log_message)
                    base_dir = os.path.join(tempfile.gettempdir(), "qmt_trading")
            except Exception as e:
                # 异常情况使用临时目录
                log_message = f"确定目录时出现异常：{str(e)}，使用临时目录"
                if 'logging' in globals():
                    logging.error(log_message)
                elif 'LogError' in globals():
                    LogError(log_message)
                base_dir = os.path.join(tempfile.gettempdir(), "qmt_trading")
        
        # 3. 处理多实例后缀
        suffix = ""
        if "QMT_SHARED_MEM_SUFFIX" in os.environ:
            suffix = f"_{os.environ['QMT_SHARED_MEM_SUFFIX']}"
        
        # 4. 确保目录存在
        if create_dir:
            try:
                os.makedirs(base_dir, exist_ok=True)
            except OSError as e:
                log_message = f"创建共享内存目录失败: {base_dir}, 错误: {str(e)}"
                if 'logging' in globals():
                    logging.error(log_message)
                elif 'LogError' in globals():
                    LogError(log_message)
                # 回退到临时目录
                base_dir = os.path.join(tempfile.gettempdir(), "qmt_trading")
                os.makedirs(base_dir, exist_ok=True)
        
        # 5. 构建最终路径
        file_path = os.path.abspath(os.path.join(base_dir, f"QMT_TRADING_GATEWAY{suffix}.dat"))
        
        # 6. 记录日志
        log_message = f"使用共享内存文件路径: {file_path}"
        if 'logging' in globals():
            logging.info(log_message)
        elif 'LogInfo' in globals():
            LogInfo(log_message)
            
        return file_path
class FileSystemUtils:
    """文件系统工具类"""
    
    @staticmethod
    def get_shared_memory_path():
        """获取共享内存文件路径"""
        # 直接调用统一的路径管理器
        return SharedMemoryPathManager.get_shared_memory_path()

class SmartOrderManager:
    """智能拆单交易管理类（基于StrategyApi实现）"""
    
    def __init__(self, strategy_api, price_limit_percent=0.5, 
                 order_size_ratio=0.3, max_retry=3):
        # 依赖注入策略API
        self.api = strategy_api
        
        # 订单管理参数
        self.price_limit_percent = price_limit_percent
        self.order_size_ratio = order_size_ratio
        self.max_retry = max_retry
        
        # 订单跟踪数据结构
        self.active_orders = {}      # 活跃订单 {order_id: order_info}
        self.completed_orders = {}   # 已完成订单
        self.failed_orders = {}      # 失败订单
        self.lock = threading.Lock()
        
        # 启动监控线程
        self.monitor_running = True
        self.monitor_thread = threading.Thread(target=self._monitor_orders)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()

    def place_order(self, symbol, total_quantity, price, direction):
        """执行智能拆单"""
        symbol = normalize_stock_code(symbol)
        remaining = abs(total_quantity)
        order_ids = []
        
        for attempt in range(self.max_retry):
            # 获取实时盘口数据
            order_size = self._calculate_order_size(symbol, remaining)
            if order_size <= 0: break
            
            # 执行下单
            try:
                if direction == 'BUY':
                    order_id = self.api.place_buy_order(
                        symbol, order_size, price
                    )
                else:
                    order_id = self.api.place_sell_order(
                        symbol, order_size, price
                    )
                
                if order_id:
                    with self.lock:
                        self.active_orders[order_id] = {
                            'symbol': symbol,
                            'total': order_size,
                            'filled': 0,
                            'retries': 0
                        }
                    remaining -= order_size
                    order_ids.append(order_id)
                    
            except Exception as e:
                LogInfo(f"下单异常: {str(e)}")
                time.sleep(1)
            
            if remaining <= 0: break
        
        return order_ids

    def _calculate_order_size(self, symbol, remaining):
        """智能拆单算法"""
        # 获取实时盘口数据
        try:
            from xtquant import xtdata
            tick = xtdata.get_full_tick([symbol])[symbol]
            bid_vol = sum(tick['bidVol'][:5])  # 前五档买量
            ask_vol = sum(tick['askVol'][:5])  # 前五档卖量
            
            # 计算安全下单量
            available_vol = min(bid_vol, ask_vol) * self.order_size_ratio
            order_size = min(int(available_vol), remaining)
            
            # 确保符合最小交易单位
            return max(order_size // 100 * 100, 100)
            
        except Exception as e:
            LogInfo(f"获取盘口数据失败: {str(e)}")
            return remaining  # 降级为全量下单

    def _monitor_orders(self):
        """订单状态监控线程"""
        while self.monitor_running:
            try:
                # 获取最新订单状态
                orders = self.api.get_orders()
                
                with self.lock:
                    # 更新已完成的订单
                    for order_id in list(self.active_orders.keys()):
                        if order_id not in orders:
                            self.completed_orders[order_id] = self.active_orders.pop(order_id)
                            continue
                            
                        # 更新成交数量
                        status = orders[order_id]
                        self.active_orders[order_id]['filled'] = status.get('traded_volume',0)
                        
                        # 处理失败订单
                        if status['order_status'] in ('CANCELED','FAILED'):
                            self.failed_orders[order_id] = self.active_orders.pop(order_id)
                
                time.sleep(3)  # 每3秒更新一次
                
            except Exception as e:
                LogInfo(f"订单监控异常: {str(e)}")
                time.sleep(5)

    def get_order_status(self, order_id):
        """获取订单综合状态"""
        with self.lock:
            if order_id in self.active_orders:
                return {'status': 'ACTIVE', **self.active_orders[order_id]}
            if order_id in self.completed_orders:
                return {'status': 'COMPLETED', **self.completed_orders[order_id]}
            if order_id in self.failed_orders:
                return {'status': 'FAILED', **self.failed_orders[order_id]}
            return {'status': 'UNKNOWN'}

    def shutdown(self):
        """关闭管理器"""
        self.monitor_running = False
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        LogInfo("智能订单管理器已关闭")

    def _check_port_available(self):
        """检查端口是否被占用"""
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            sock.bind(("127.0.0.1", 58609))  # QMT默认端口
            return True
        except OSError:
            LogError("检测到端口冲突(58609)，可能是已有网关进程在运行")
            return False
        finally:
            sock.close()

class GatewayDaemon:
    """网关进程守护类"""
    def __init__(self):
        self.running = False
        self.process = None
        
    def start(self):
        self.running = True
        while self.running:
            if not self._check_alive():
                LogInfo("检测到网关进程离线，尝试重启...")
                self._restart()
            time.sleep(30)
    
    def _check_alive(self):
        return self.process and self.process.poll() is None
        
    def _restart(self):
        if self.process:
            try:
                self.process.terminate()
            except:
                pass
        self.process = _start_gateway_service()
        
    def stop(self):
        self.running = False
        if self.process:
            self.process.terminate()


#************************************************内存共享********************************************************
# 共享内存区域常量定义 
MEM_SIZE = 1024 * 1024  # 1MB共享内存区
HEADER_SIZE = 64        # 头部信息大小
COMMAND_SIZE = 256 * 1024  # 命令区域大小
RESPONSE_SIZE = 256 * 1024  # 响应区域大小
DATA_SIZE = 512 * 1024  # 数据区域大小

# 共享内存区域名称
SHMEM_NAME = "QMT_TRADING_GATEWAY"


# 状态和命令常量
CMD_NONE = 0
CMD_CONNECT = 1
CMD_PLACE_ORDER = 2
CMD_CANCEL_ORDER = 3
CMD_QUERY = 4
CMD_SHUTDOWN = 99

RESP_NONE = 0
RESP_SUCCESS = 1
RESP_FAILURE = 2
RESP_DATA = 3

# 数据区域索引
DATA_ACCOUNT = 0
DATA_POSITIONS = 1
DATA_ORDERS = 2
DATA_TRADES = 3
DATA_STATUS = 4

class SharedMemoryReader:
    """读取共享内存数据的简化类"""
    
    def __init__(self):
        self.lock = threading.RLock()  # 添加锁以同步访问
        self.shmem_file = FileSystemUtils.get_shared_memory_path()
        self.is_initialized = False
        self.file = None  # 初始化文件句柄为None
        self.mm = None    # 初始化内存映射对象为None
        
        try:
            # 检查共享内存文件是否存在
            if not os.path.exists(self.shmem_file):
                logging.warning(f"共享内存文件不存在: {self.shmem_file}")
                return
            
            # 打开共享内存
            self.file = open(self.shmem_file, 'r+b')
            self.mm = mmap.mmap(self.file.fileno(), MEM_SIZE)
            self.is_initialized = True
        except Exception as e:
            logging.error(f"初始化共享内存读取器失败: {e}")
            self.close()  # 确保异常时关闭已打开的资源
        
        # 注册析构函数
        import atexit
        atexit.register(self.close)
    
    def __enter__(self):
        """支持上下文管理器，使用with语句"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出时自动关闭文件"""
        self.close()
        return False  # 不屏蔽异常
    
    def __del__(self):
        """析构函数，关闭共享内存"""
        self.close()

    def close(self):
        """安全关闭所有资源"""
        if hasattr(self, 'lock'):  # 确保锁存在
            with self.lock:  # 使用锁确保线程安全
                # 安全地关闭mmap对象
                if hasattr(self, 'mm') and self.mm:
                    try:
                        self.mm.close()
                    except Exception as e:
                        logging.error(f"关闭内存映射对象失败: {str(e)}")
                    finally:
                        self.mm = None
                
                # 安全地关闭文件
                if hasattr(self, 'file') and self.file:
                    try:
                        self.file.close()
                    except Exception as e:
                        logging.error(f"关闭文件句柄失败: {str(e)}")
                    finally:
                        self.file = None
                
                self.is_initialized = False
        else:  # 没有锁的情况
            # 安全地关闭mmap对象
            if hasattr(self, 'mm') and self.mm:
                try:
                    self.mm.close()
                except Exception:
                    pass
                finally:
                    self.mm = None
            
            # 安全地关闭文件
            if hasattr(self, 'file') and self.file:
                try:
                    self.file.close()
                except Exception:
                    pass
                finally:
                    self.file = None
            
            self.is_initialized = False

    def set_data(self, idx, data):
        """设置数据区域"""
        with self.lock:
            if not self.is_initialized:
                return False
            
            try:
                offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + idx * DATA_SIZE // 5
                self.mm.seek(offset)
                serialized = pickle.dumps(data)
                self.mm.write(serialized[:DATA_SIZE // 5])
                return True
            except Exception as e:
                logging.error(f"设置共享内存数据失败: {e}")
                return False
    
    def get_data(self, idx):
        """获取数据区域"""
        if not self.is_initialized:
            return None
            
        try:
            offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + idx * DATA_SIZE // 5
            self.mm.seek(offset)
            data = self.mm.read(DATA_SIZE // 5)
            try:
                return pickle.loads(data)
            except:
                return None
        except Exception as e:
            logging.error(f"读取共享内存数据失败: {e}")
            return None
    
    def is_gateway_running(self):
        """检查网关是否运行中"""
        if not self.is_initialized:
            return False
            
        try:
            self.mm.seek(16)
            heartbeat = struct.unpack('I', self.mm.read(4))[0]
            current_time = int(time.time())
            # 心跳超过10秒视为网关不在运行
            return current_time - heartbeat < 10
        except:
            return False

    def close(self):
        if hasattr(self, 'mm') and self.mm:
            self.mm.close()
            self.mm = None
        if hasattr(self, 'file') and self.file:
            self.file.close()
            self.file = None
        
#************************************************内存共享********************************************************

class StrategyApi:
    """交易策略API接口"""
    
    def __init__(self):
        # 尝试连接共享内存
        try:
            self.shmem = SharedMemoryReader()
            
            # 如果初始化失败或网关未运行，置为None
            if not self.shmem.is_initialized:
                LogInfo("共享内存初始化失败")
                self.shmem = None
            elif not self.shmem.is_gateway_running():
                LogInfo("交易网关未在运行状态")
                # 保留shmem以便重试，不置None
        except Exception as e:
            LogError(f"连接共享内存异常: {e}")
            self.shmem = None
        
        # 上次响应检查时间
        self.last_response_check = 0
    
    def connect(self, client_path, account_id):
        """连接交易接口"""
        if not self.shmem:
            LogInfo("交易网关未启动")
            return False
            
        # 发送连接命令
        self.shmem.set_command(CMD_CONNECT, {
            "client_path": client_path,
            "account_id": account_id
        })
        
        # 等待响应
        for _ in range(10):  # 最多等待10秒
            resp, data = self.shmem.get_response()
            if resp != RESP_NONE:
                self.shmem.clear_response()
                if resp == RESP_SUCCESS and data and data.get("success"):
                    return True
                break
            time.sleep(1)
                
            return False

    def place_buy_order(self, symbol, quantity, price=0, is_market_order=False, 
                        strategy_name="auto_strategy", remark=""):
        """买入下单"""
        if not self.shmem:
            LogInfo("交易网关未启动")
            return None
            
        from xtquant import xtconstant
        
        # 发送下单命令
        self.shmem.set_command(CMD_PLACE_ORDER, {
            "symbol": symbol,
            "direction": xtconstant.STOCK_BUY,
            "quantity": quantity,
            "price_type": xtconstant.LATEST_PRICE if is_market_order else xtconstant.FIX_PRICE,
            "price": price,
            "strategy_name": strategy_name,
            "remark": remark
        })
        
        # 等待响应
        for _ in range(5):  # 最多等待5秒
            resp, data = self.shmem.get_response()
            if resp != RESP_NONE:
                self.shmem.clear_response()
                if resp == RESP_SUCCESS and data and data.get("success"):
                    return data.get("order_id")
                break
            time.sleep(1)
        
            return None
            
    def place_sell_order(self, symbol, quantity, price=0, is_market_order=False, 
                         strategy_name="auto_strategy", remark=""):
        """卖出下单"""
        if not self.shmem:
            LogInfo("交易网关未启动")
            return None
            
        from xtquant import xtconstant
        
        # 发送下单命令
        self.shmem.set_command(CMD_PLACE_ORDER, {
            "symbol": symbol,
            "direction": xtconstant.STOCK_SELL,
            "quantity": quantity,
            "price_type": xtconstant.LATEST_PRICE if is_market_order else xtconstant.FIX_PRICE,
            "price": price,
            "strategy_name": strategy_name,
            "remark": remark
        })
        
        # 等待响应
        for _ in range(5):  # 最多等待5秒
            resp, data = self.shmem.get_response()
            if resp != RESP_NONE:
                self.shmem.clear_response()
                if resp == RESP_SUCCESS and data and data.get("success"):
                    return data.get("order_id")
                    break
            time.sleep(1)
        
        return None
    
    def cancel_order(self, order_id):
        """撤单"""
        if not self.shmem:
            LogInfo("交易网关未启动")
            return False
        
        # 发送撤单命令
        self.shmem.set_command(CMD_CANCEL_ORDER, {
            "order_id": order_id
        })
        
        # 等待响应
        for _ in range(5):  # 最多等待5秒
            resp, data = self.shmem.get_response()
            if resp != RESP_NONE:
                self.shmem.clear_response()
                if resp == RESP_SUCCESS and data and data.get("success"):
                    return True
                    break
            time.sleep(1)
        
        return False
    
    def get_account(self):
        """获取账户数据"""
        if not self.shmem:
            LogInfo("交易网关未启动")
            return None
    
        # 直接从共享内存获取数据
        return self.shmem.get_data(DATA_ACCOUNT)
    
    def get_positions(self):
        """获取持仓数据"""
        if not self.shmem:
            LogInfo("交易网关未启动")
            return {}
        
        # 直接从共享内存获取数据
        return self.shmem.get_data(DATA_POSITIONS) or {}
    
    def get_orders(self):
        """获取订单数据"""
        if not self.shmem:
            LogInfo("交易网关未启动")
            return {}
            
        # 直接从共享内存获取数据
        return self.shmem.get_data(DATA_ORDERS) or {}
    
    def get_trades(self):
        """获取成交数据"""
        if not self.shmem:
            LogInfo("交易网关未启动")
            return {}
    
        # 直接从共享内存获取数据
        return self.shmem.get_data(DATA_TRADES) or {}
    
    def get_status(self):
        """获取网关状态"""
        if not self.shmem:
            LogInfo("交易网关未启动")
            return {"connected": False}
        
        # 直接从共享内存获取数据
        return self.shmem.get_data(DATA_STATUS) or {"connected": False}
    
    def shutdown(self):
        """关闭网关服务"""
        if not self.shmem:
            LogInfo("交易网关未启动")
            return False
                
        # 发送关闭命令
        self.shmem.set_command(CMD_SHUTDOWN, {})
        
        # 等待响应
        for _ in range(5):  # 最多等待5秒
            resp, data = self.shmem.get_response()
            if resp != RESP_NONE:
                self.shmem.clear_response()
                if resp == RESP_SUCCESS:
                    return True
                break
            time.sleep(1)
        
            return False
    
    def get_data(self, data_type):
        """获取数据的通用方法"""
        if not self.shmem:
            return None
        
        try:
            return self.shmem.get_data(data_type)
        except Exception as e:
            LogError(f"获取数据异常({data_type}): {e}")
            
            # 尝试重新初始化共享内存
            try:
                LogInfo("尝试重新初始化共享内存连接")
                self.shmem = SharedMemoryReader()
                if self.shmem.is_initialized:
                    return self.shmem.get_data(data_type)
            except:
                pass
            
            return None

class StrategyBootstrap:
    """策略启动引导类"""
    
    def __init__(self):
        self.mem_reader = None
        self.service_process = None
        self.panel_process = None
        self.startup_log = []
        self.pid_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "gateway_service.pid")
        self.panel_pid_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "panel_service.pid")
        self.start_lock = threading.Lock()
    
    def ensure_trading_environment(self):
        """确保交易环境已就绪，保证服务进程唯一性"""
        # 使用锁防止并发启动
        with self.start_lock:
            LogInfo("正在初始化交易环境...")
            
            # 1. 检查并确保网关服务运行
            gateway_ok = self._ensure_gateway_running()
            if not gateway_ok:
                LogError("网关服务初始化失败")
                return False
                
            # 2. 检查并确保监控面板运行
            panel_ok = self._ensure_panel_running()
            if not panel_ok:
                LogWarn("监控面板启动失败，但不影响交易执行")
                # 不返回False，因为监控面板失败不应阻止策略运行
            
            LogInfo("交易环境初始化完成")
            return True
    
    def _ensure_gateway_running(self):
        """确保网关服务运行"""
        # 检查现有进程
        existing_pid = self._get_existing_pid()
        if existing_pid:
            LogInfo(f"发现现有网关服务进程 (PID: {existing_pid})")
            
            # 检查进程是否运行且健康
            service_running = self._check_service_running()
            service_healthy = False
            if service_running:
                service_healthy = self._check_service_health()
            
            # 如果进程存在但不健康，或检查失败，终止它
            if not service_healthy:
                LogInfo("服务状态异常，终止现有进程...")
                self._kill_existing_process(existing_pid)
                # 等待进程完全终止
                time.sleep(2)
            else:
                LogInfo("现有服务运行正常，继续使用")
                return True
        
        # 启动新进程
        LogInfo("正在启动新的网关服务进程...")
        if self._start_gateway_service():
            LogInfo("网关服务启动成功")
            
            # 验证服务健康
            service_healthy = False
            for _ in range(10):  # 最多等待10秒
                if self._check_service_running() and self._check_service_health():
                    service_healthy = True
                    break
                time.sleep(1)
            
            if service_healthy:
                return True
            else:
                LogInfo("ERROR: 服务启动后健康检查失败")
                return False
        else:
            LogInfo("ERROR: 无法启动网关服务")
            return False
    
    def _ensure_panel_running(self):
        """确保监控面板运行"""
        # 检查现有进程
        existing_pid = self._get_existing_panel_pid()
        if existing_pid:
            LogInfo(f"发现现有监控面板进程 (PID: {existing_pid})")
            
            # 检查进程是否运行
            panel_running = self._check_panel_running_by_pid(existing_pid)
            
            if panel_running:
                LogInfo("现有监控面板运行正常，继续使用")
                return True
            else:
                LogInfo("监控面板进程不存在，将重新启动")
                # 清理PID文件
                if os.path.exists(self.panel_pid_file):
                    os.remove(self.panel_pid_file)
        
        # 启动新监控面板
        LogInfo("正在启动新的监控面板...")
        if self._start_control_panel():
            LogInfo("监控面板启动成功")
            return True
        else:
            LogInfo("ERROR: 无法启动监控面板")
            return False
    
    def _get_existing_panel_pid(self):
        """获取现有监控面板PID"""
        # 从PID文件读取
        pid_from_file = None
        if os.path.exists(self.panel_pid_file):
            try:
                with open(self.panel_pid_file, 'r') as f:
                    pid_from_file = int(f.read().strip())
                LogInfo(f"从PID文件读取到监控面板进程ID: {pid_from_file}")
            except Exception as e:
                LogInfo(f"读取监控面板PID文件异常: {e}")
        
        # 从进程列表检查
        try:
            import psutil
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if 'server_control_panel.py' in cmdline or 'ServerControlPanel' in cmdline:
                        process_pid = proc.pid
                        LogInfo(f"从进程列表发现监控面板 (PID: {process_pid})")
                        
                        # 如果找到进程但与PID文件不一致，更新PID文件
                        if pid_from_file != process_pid:
                            self._save_panel_pid_file(process_pid)
                        
                        return process_pid
                except:
                    continue
        except Exception as e:
            LogInfo(f"扫描监控面板进程列表异常: {e}")
        
        # 检查PID文件中的进程是否存在
        if pid_from_file:
            try:
                import psutil
                if psutil.pid_exists(pid_from_file):
                    proc = psutil.Process(pid_from_file)
                    cmdline = ' '.join(proc.cmdline())
                    if 'server_control_panel.py' in cmdline or 'ServerControlPanel' in cmdline:
                        return pid_from_file
                # PID存在但不是我们的进程，删除PID文件
                os.remove(self.panel_pid_file)
            except:
                # PID不存在，删除PID文件
                if os.path.exists(self.panel_pid_file):
                    os.remove(self.panel_pid_file)
        
        return None
    
    def _check_panel_running_by_pid(self, pid):
        """通过PID检查监控面板是否运行"""
        try:
            import psutil
            if psutil.pid_exists(pid):
                proc = psutil.Process(pid)
                cmdline = ' '.join(proc.cmdline())
                return 'server_control_panel.py' in cmdline or 'ServerControlPanel' in cmdline
            return False
        except:
            return False
    
    def _save_panel_pid_file(self, pid):
        """保存监控面板PID到文件"""
        try:
            with open(self.panel_pid_file, 'w') as f:
                f.write(str(pid))
        except Exception as e:
            LogInfo(f"保存监控面板PID文件异常: {e}")
    
    def _start_control_panel(self):
        """启动控制面板，使用多种方法尝试确保界面正确显示"""
        if self.panel_process is not None:
            try:
                # 检查进程是否还在运行
                if isinstance(self.panel_process, subprocess.Popen) and self.panel_process.poll() is None:
                    LogInfo("控制面板已经在运行")
                    return
                elif hasattr(self.panel_process, 'is_running') and self.panel_process.is_running():
                    LogInfo("控制面板已经在运行")
                    return
            except:
                # 进程可能已经结束或者状态无法判断，重新启动
                self.panel_process = None

        # 获取Python可执行文件路径
        python_exec = sys.executable
        LogInfo(f"Python可执行文件: {python_exec}")
        
        # 定位控制面板脚本
        panel_path = None
        # 首先检查程序当前目录
        local_panel = os.path.join(os.path.dirname(__file__), "server_control_panel.py")
        if os.path.exists(local_panel):
            panel_path = local_panel
            LogInfo(f"在当前目录找到控制面板: {panel_path}")
        
        # 检查父目录
        if panel_path is None:
            parent_panel = os.path.join(os.path.dirname(os.path.dirname(__file__)), "server_control_panel.py")
            if os.path.exists(parent_panel):
                panel_path = parent_panel
                LogInfo(f"在父目录找到控制面板: {panel_path}")
        
        # 还可以检查其他可能的位置
        if panel_path is None:
            other_locations = [
                os.path.join(os.path.dirname(os.path.dirname(__file__)), "Scripts", "server_control_panel.py"),
                os.path.join(os.path.dirname(os.path.dirname(__file__)), "gateway_service", "server_control_panel.py"),
                # 添加其他可能的位置
            ]
            
            for loc in other_locations:
                if os.path.exists(loc):
                    panel_path = loc
                    LogInfo(f"在其他位置找到控制面板: {panel_path}")
                    break
        
        if panel_path is None:
            LogError("无法找到控制面板脚本 server_control_panel.py")
            return
            
        # 尝试不同的启动方法
        if self.panel_process is None:
            # 方法1: 使用DETACHED_PROCESS实现静默运行但不影响GUI
            try:
                LogInfo("尝试分离进程方式启动控制面板")
                # 使用DETACHED_PROCESS标志，分离进程但不影响GUI显示
                DETACHED_PROCESS = 0x00000008
                
                # 传递正确的环境变量
                env = os.environ.copy()
                if "QMT_SHARED_MEM_DIR" in env:
                    LogInfo(f"传递共享内存路径: {env['QMT_SHARED_MEM_DIR']}")
                    
                self.panel_process = subprocess.Popen(
                    [python_exec, panel_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    stdin=subprocess.PIPE,
                    creationflags=DETACHED_PROCESS,
                    env=env,
                    cwd=os.path.dirname(panel_path)  # 设置工作目录
                )
                
                if self.panel_process and self.panel_process.pid:
                    LogInfo(f"控制面板已启动，PID: {self.panel_process.pid}")
                    # 记录PID
                    self._save_panel_pid_file(self.panel_process.pid)
                    
                    # 等待一段时间检查进程是否仍在运行
                    time.sleep(1)
                    if self.panel_process.poll() is not None:
                        LogInfo(f"进程启动后立即退出，退出码: {self.panel_process.poll()}")
                        stderr = self.panel_process.stderr.read().decode('gbk', errors='replace')
                        if stderr:
                            LogInfo(f"错误输出: {stderr}")
                    else:
                        LogInfo("控制面板进程正在运行")
                        return
                else:
                    LogInfo("未能获取面板进程PID")
            except Exception as e1:
                LogInfo(f"分离进程启动异常: {e1}")
            
            # 备用方法: 使用VBS脚本启动，Windows平台可靠的隐藏控制台方式
            if self.panel_process is None or self.panel_process.poll() is not None:
                try:
                    LogInfo("尝试使用改进的VBS脚本启动控制面板")
                    
                    # 准备路径和环境变量
                    script_dir = os.path.dirname(panel_path)
                    vbs_file = os.path.join(script_dir, "start_panel_env.vbs")
                    bat_file = os.path.join(script_dir, "run_panel_env.bat")
                    
                    # 创建BAT文件，设置环境变量后启动面板
                    with open(bat_file, 'w') as f:
                        f.write('@echo off\n')
                        # 设置所有必要的环境变量
                        if "QMT_SHARED_MEM_DIR" in os.environ:
                            f.write(f'set QMT_SHARED_MEM_DIR={os.environ["QMT_SHARED_MEM_DIR"]}\n')
                        # 可以添加其他环境变量
                        
                        # 启动Python程序
                        f.write(f'"{python_exec}" "{panel_path}"\n')
                    
                    # 创建VBS脚本来隐藏启动BAT文件
                    with open(vbs_file, 'w') as f:
                        f.write(f'Set WshShell = CreateObject("WScript.Shell")\n')
                        f.write(f'WshShell.Run Chr(34) & "{bat_file}" & Chr(34), 0, False\n')
                    
                    LogInfo(f"环境变量设置BAT文件创建成功: {bat_file}")
                    LogInfo(f"启动VBS脚本创建成功: {vbs_file}")
                    
                    # 执行VBS脚本
                    os.system(f'wscript "{vbs_file}"')
                    LogInfo("已执行环境变量增强型VBS脚本启动面板")
                    
                    # 等待一段时间，确保进程启动
                    time.sleep(3)
                    
                    # 成功启动标记
                    self.panel_process = True
                    return
                    
                except Exception as e3:
                    LogInfo(f"环境变量增强型启动异常: {e3}")
            
            
            LogError("所有启动控制面板的方法都失败了")
        else:
            LogInfo("控制面板已经在运行")
    
    def _get_existing_pid(self):
        """获取现有进程PID，从PID文件和进程列表两处检查"""
        # 从PID文件读取
        pid_from_file = None
        if os.path.exists(self.pid_file):
            try:
                with open(self.pid_file, 'r') as f:
                    pid_from_file = int(f.read().strip())
                LogInfo(f"从PID文件读取到进程ID: {pid_from_file}")
            except Exception as e:
                LogInfo(f"读取PID文件异常: {e}")
        
        # 从进程列表检查
        try:
            import psutil
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if 'SmartOrderManagerGateWay.py' in cmdline:
                        process_pid = proc.pid
                        LogInfo(f"从进程列表发现网关服务 (PID: {process_pid})")
                        
                        # 如果找到进程但与PID文件不一致，更新PID文件
                        if pid_from_file != process_pid:
                            self._save_pid_file(process_pid)
                        
                        return process_pid
                except:
                    continue
        except Exception as e:
            LogInfo(f"扫描进程列表异常: {e}")
        
        # 检查PID文件中的进程是否存在
        if pid_from_file:
            try:
                import psutil
                if psutil.pid_exists(pid_from_file):
                    proc = psutil.Process(pid_from_file)
                    cmdline = ' '.join(proc.cmdline())
                    if 'SmartOrderManagerGateWay.py' in cmdline:
                        return pid_from_file
                # PID存在但不是我们的进程，删除PID文件
                os.remove(self.pid_file)
            except:
                # PID不存在，删除PID文件
                if os.path.exists(self.pid_file):
                    os.remove(self.pid_file)
        
        return None
    
    def _kill_existing_process(self, pid):
        """终止现有进程并等待确认"""
        try:
            import psutil
            if psutil.pid_exists(pid):
                LogInfo(f"正在终止进程 PID: {pid}")
                proc = psutil.Process(pid)
                
                # 尝试正常终止
                proc.terminate()
                
                # 等待进程结束，最多等待5秒
                try:
                    proc.wait(timeout=5)
                    LogInfo(f"进程 {pid} 已正常终止")
                except psutil.TimeoutExpired:
                    # 如果超时，强制结束
                    LogInfo(f"进程 {pid} 未响应终止信号，强制结束")
                    proc.kill()
                    proc.wait(timeout=1)
            
            # 清理PID文件
            if os.path.exists(self.pid_file):
                os.remove(self.pid_file)
                
            return True
        except Exception as e:
            LogInfo(f"终止进程异常: {e}")
            traceback.print_exc()
            return False
    
    def _start_gateway_service(self):
        """启动网关服务，确保之前的进程已终止"""
        try:
            # 获取当前Python解释器路径
            python_exec = sys.executable
            
            # 查找网关脚本路径
            script_dir = os.path.dirname(os.path.abspath(__file__))
            gateway_paths = [
                os.path.join(script_dir, "SmartOrderManagerGateWay.py"),
                # os.path.join(script_dir, "gateway_service", "SmartOrderManagerGateWay.py"),
                # os.path.join(script_dir, "..", "gateway_service", "SmartOrderManagerGateWay.py"),
            ]
            gateway_path = None
            for path in gateway_paths:
                if os.path.exists(path):
                    gateway_path = path
                    break
                    
            if not gateway_path:
                LogInfo("ERROR: 找不到网关服务脚本")
                return False
                
            # 创建固定的共享内存路径
            shared_mem_dir = os.path.join(script_dir, "..", "qmt_trading_shm")
            shared_mem_dir = os.path.abspath(shared_mem_dir)  # 转为绝对路径
            os.makedirs(shared_mem_dir, exist_ok=True)
            
            # 设置环境变量 - 同时更新当前进程环境
            env = os.environ.copy()
            env["QMT_SHARED_MEM_DIR"] = shared_mem_dir
            os.environ["QMT_SHARED_MEM_DIR"] = shared_mem_dir  # 更新当前进程环境
            LogInfo(f"设置共享内存目录: {shared_mem_dir}")
            
            # 启动参数配置
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            
            # 启动进程
            self.service_process = subprocess.Popen(
                [python_exec, gateway_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW,
                env=env
            )
            
            # 记录PID
            self._save_pid_file(self.service_process.pid)
            LogInfo(f"网关服务进程已启动 (PID: {self.service_process.pid})")
            
            # 启动监控线程
            threading.Thread(target=self._monitor_process_output, 
                             args=(self.service_process,), 
                             daemon=True).start()
            
            # 等待进程初始化
            time.sleep(3)
            return True
            
        except Exception as e:
            LogInfo(f"启动网关服务失败: {e}")
            traceback.print_exc()
            return False
    
    def _save_pid_file(self, pid):
        """保存PID到文件"""
        try:
            with open(self.pid_file, 'w') as f:
                f.write(str(pid))
        except Exception as e:
            LogInfo(f"保存PID文件异常: {e}")
    
    def _monitor_process_output(self, process):
        """监控进程输出并记录日志"""
        try:
            for line in iter(process.stdout.readline, b''):
                if line:
                    msg = line.decode('GBk', errors='replace').strip()
                    if msg:
                        LogInfo(f"[网关服务] {msg}")
                        
            for line in iter(process.stderr.readline, b''):
                if line:
                    msg = line.decode('GBK', errors='replace').strip()
                    if msg:
                        LogError(f"[网关服务] {msg}")
        except Exception as e:
            LogInfo(f"监控进程输出异常: {e}")
    
    def _check_service_running(self):
        """检查服务进程是否运行"""
        # 使用统一的路径管理器获取路径
        shmem_file = SharedMemoryPathManager.get_shared_memory_path(create_dir=False)
        LogInfo(f"检查服务状态使用路径: {shmem_file}")
        
        if os.path.exists(shmem_file):
            try:
                # 直接打开文件而不依赖SharedMemoryReader类
                with open(shmem_file, 'r+b') as f:
                    mm = mmap.mmap(f.fileno(), MEM_SIZE)
                    mm.seek(16)
                    heartbeat = struct.unpack('I', mm.read(4))[0]
                    current_time = int(time.time())
                    mm.close()
                    # 心跳检查
                    with SharedMemoryReader() as reader:
                        if not reader.is_initialized:
                            LogInfo("无法初始化共享内存读取器")
                            return False

                        # 获取服务状态
                        tracking_dict = reader.get_data(DATA_STATUS) or {}
                        LogInfo(f"跟踪服务状态: {current_time}, 全状态信息:{tracking_dict}")
                    LogInfo(f"当前时间: {current_time}, 上次心跳时间: {heartbeat},时间差值: {current_time-heartbeat}")
                    if current_time - heartbeat < 10:
                        return True
            except Exception as e:
                LogInfo(f"心跳检查异常: {e}")
        
        return False
    
    def _check_service_health(self):
        """检查服务是否健康 - 宽松版本，更详细的诊断"""
        try:
            # 首先使用已实现的心跳检查方法
            if self._check_service_running():
                # 心跳检查通过，获取更详细的健康状态
                LogInfo("心跳检查通过，服务运行中")
                
                # 使用SharedMemoryReader获取具体的状态信息
                with SharedMemoryReader() as reader:
                    if not reader.is_initialized:
                        LogInfo("无法初始化共享内存读取器")
                        return False
                        
                    # 获取服务状态
                    status_data = reader.get_data(DATA_STATUS) or {}
                    
                    # 获取上次健康检查时间和详情
                    last_check = status_data.get("last_heartbeat_check")
                    health_details = status_data.get("health_check_details", {})
                    is_healthy = status_data.get("is_healthy")
                    service_initialized = status_data.get("service_initialized", False)
                    start_time = status_data.get("start_time")
                    
                    # 打印诊断信息
                    LogInfo(f"服务状态: {'健康' if is_healthy else '不健康' if is_healthy is not None else '未知'}")
                    LogInfo(f"上次健康检查时间: {last_check} ")
                    
                    if health_details:
                        LogInfo("健康检查详情:")
                        for key, value in health_details.items():
                            LogInfo(f"  - {key}: {value}")
                    
                    # 如果服务刚启动，给予一定宽限期
                    if service_initialized and start_time:
                        try:
                            from datetime import datetime
                            start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                            now_dt = datetime.now()
                            uptime_seconds = (now_dt - start_dt).total_seconds()
                            
                            # 如果服务启动不到30秒，先认为它是健康的
                            if uptime_seconds < 30 and is_healthy is None:
                                LogInfo("服务刚刚启动，给予宽限期")
                                return True
                        except Exception as e:
                            LogInfo(f"计算服务运行时间异常: {e}")
                    
                    # 宽松检查：如果未明确指出不健康，且能读取状态数据，就认为正常
                    if is_healthy is not False and status_data:
                        return True
                    elif is_healthy is False:
                        # 如果明确不健康，检查具体原因
                        reasons = []
                        if health_details.get("共享内存") != "已初始化":
                            reasons.append("共享内存未初始化")
                        if health_details.get("交易线程") == "异常":
                            reasons.append("交易线程异常")
                        if health_details.get("客户端路径") == "无效":
                            reasons.append("客户端路径无效")
                        if "账户余额" in health_details and "异常" in health_details.get("账户余额"):
                            reasons.append("账户余额异常")
                        
                        if reasons:
                            LogInfo(f"服务不健康，原因: {', '.join(reasons)}")
                        else:
                            LogInfo("服务不健康，但未发现具体原因")
                        
                        return False
                    else:
                        # 默认按最宽松标准：只要有心跳，就认为健康
                        LogInfo("按最宽松标准：有心跳即认为健康")
                        return True
                
            # 如果心跳检查失败，尝试通过SharedMemoryReader再次检查
            with SharedMemoryReader() as reader:
                if not reader.is_initialized:
                    LogInfo("无法初始化共享内存读取器")
                    return False
                    
                # 获取服务状态
                status_data = reader.get_data(DATA_STATUS) or {}
                is_healthy = status_data.get("is_healthy", False)
                
                # 获取连接状态
                connected = status_data.get("connected", False)
                
                return is_healthy and connected
        except Exception as e:
            LogInfo(f"检查服务健康状态时出错: {e}")
            return False
    
    def _restart_gateway_service(self):
        """重启网关服务"""
        # 先停止服务
        if self.service_process:
            try:
                self.service_process.terminate()
                LogInfo("已发送终止信号给网关服务")
                
                # 等待进程结束
                for _ in range(5):
                    if self.service_process.poll() is not None:
                        break
                    time.sleep(0.5)
                    
                # 如果进程仍在运行，强制结束
                if self.service_process.poll() is None:
                    self.service_process.kill()
                    LogInfo("已强制终止网关服务进程")
            except:
                pass
                
        # 等待共享内存释放
        time.sleep(2)
        
        # 重新启动服务
        return self._start_gateway_service()
    
    def _check_panel_running(self):
        """检查控制面板是否运行"""
        try:
            import psutil
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'])
                    if 'server_control_panel.py' in cmdline or 'ServerControlPanel' in cmdline:
                        self.panel_process = proc
                        LogInfo(f"发现控制面板进程 (PID: {proc.info['pid']})")
                        return True
                except:
                    continue
                    
            LogInfo("未找到控制面板进程")
            return False
            
        except Exception as e:
            LogInfo(f"检查控制面板状态时出错: {str(e)}")
            return False
            
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.startup_log.append(log_message)
        LogInfo(log_message)

    def __del__(self):
        """确保资源被清理"""
        try:
            # 清理临时文件
            for pid_file in [self.pid_file, self.panel_pid_file]:
                if os.path.exists(pid_file):
                    os.remove(pid_file)
        except:
            pass

# 策略参数
g_params['K线基础时间']  = 'D'   # k线基础时间
g_params['K线基础周期']  = 1     # k线周期
g_params['选择显示合约序号']  = 1 # 选择显示合约序号

g_params['疯牛乖离阈值']  =  2000  #疯牛乖离阈值
g_params['疯熊乖离阈值']  = -2000  #疯熊乖离阈值

g_params['订阅数据长度']  =  20000  #订阅数据长度
g_params['配置文件夹路径'] = "D:\stock_trade"
g_params['配置文件名'] = "配置文件与股票ETF代码.xlsx"
trade_order_filedir = g_params['配置文件夹路径']
trade_config_file   = trade_order_filedir+"\\"+g_params['配置文件名'] 
trade_config_DATA   =pd.read_excel(trade_config_file,sheet_name = 0)

symbol_Id =trade_config_DATA["股票ETF代码"].dropna()
资金权重=trade_config_DATA["资金权重"].dropna()
是否交易=trade_config_DATA["是否交易"].dropna()
短周期=trade_config_DATA["短周期"].dropna()
长周期=trade_config_DATA["长周期"].dropna()
初始资金百分比 =trade_config_DATA["初始资金百分比"].dropna()
减仓阈值=trade_config_DATA["减仓阈值"].dropna()
加仓阈值=trade_config_DATA["加仓阈值"].dropna()
信息发送邮箱=str(trade_config_DATA["信息发送邮箱"].iloc[0])
邮箱SMTP地址=str(trade_config_DATA["邮箱SMTP地址"].iloc[0])
邮箱端口=str(int(trade_config_DATA["邮箱端口"].iloc[0]))
邮箱用户名=str(trade_config_DATA["邮箱用户名"].iloc[0])
邮箱授权码=str(trade_config_DATA["邮箱授权码"].iloc[0])
信息接收邮箱=list(trade_config_DATA["信息接收邮箱"].dropna())
交易账号=str(int(trade_config_DATA["交易账号"].iloc[0]))
客户端路径=str(trade_config_DATA["客户端路径"].iloc[0])
心跳超时秒数=int(trade_config_DATA["心跳超时秒数"].iloc[0])
最大重连次数=int(trade_config_DATA["最大重连次数"].iloc[0])

symbol_d=[]
for i in range(len(symbol_Id)):
    symbol_d.append(copy.deepcopy(deque([0]*9,maxlen=9)))
TotalNumber=len(symbol_Id)    
UPSA,DWSA=[0]*TotalNumber,[0]*TotalNumber     
BKStatus,SKStatus,BPStatus,SPStatus=[0]*TotalNumber,[0]*TotalNumber,[0]*TotalNumber,[0]*TotalNumber
UPSQ=copy.deepcopy(symbol_d) 
DWSQ=copy.deepcopy(symbol_d) 

k_btime,k_cycle,SetDisplayNo=0,0,0
BullishLimit,BearishLimit=0,0
FinancialWeighting=0

g_api = None             # 策略API实例
g_order_manager = None  # 智能订单管理器实例
bootstrap = None
def initialize(context): 
    global g_api, g_order_manager, bootstrap
    global g_params, k_btime, k_cycle, SetDisplayNo, BullishLimit, BearishLimit, FinancialWeighting
    k_btime = g_params['K线基础时间'] # k线基础时间取参数
    k_cycle = g_params['K线基础周期'] # k线基础周期取参数
    SetDisplayNo = g_params['选择显示合约序号']-1 # 选择显示合约序号
    BullishLimit = g_params['疯牛乖离阈值']         # 疯牛乖离阈值
    BearishLimit = g_params['疯熊乖离阈值']         # 疯熊乖离阈值 

    DaySubDataLength = g_params['订阅数据长度']   # 订阅日线数据长度
    SubDataLength = int(DaySubDataLength)      # 订阅数据长度
    AluSubDataLength = min(1000,SubDataLength)  # 计算数据长度    
    DisplayCode=stock_code_mapping(int(symbol_Id[SetDisplayNo]))
    SetBarInterval(DisplayCode, k_btime, k_cycle,DaySubDataLength,AluSubDataLength) #订阅分钟线数据
    for i in range(len(symbol_Id)):
        tcode=stock_code_mapping(int(symbol_Id[i]))
        if 是否交易[i]=="是":
            LogInfo("订阅",symbol_Id[i],"的合约"+tcode,"资金权重==>",资金权重[i],"短周期==>",短周期[i],"长周期==>",长周期[i],"初始资金百分比==>",初始资金百分比[i],"减仓阈值==>",减仓阈值[i],"加仓阈值==>",加仓阈值[i])
            FinancialWeighting+=资金权重[i]
            if i==SetDisplayNo:
                continue
            SetBarInterval(tcode, k_btime, k_cycle,SubDataLength,AluSubDataLength) #订阅交易合约
            # SetBarInterval(tcode, 'M', 1 ,DaySubDataLength,AluSubDataLength) #订阅分钟线数据      
    LogInfo("交易账号==>",交易账号,"客户端路径==>",客户端路径,"心跳超时秒数==>",心跳超时秒数,"最大重连次数==>",最大重连次数)     
    LogInfo("信息发送邮箱==>",信息发送邮箱,"邮箱SMTP地址==>",邮箱SMTP地址,"邮箱端口==>",邮箱端口,"邮箱用户名==>",邮箱用户名,"信息接收邮箱==>",信息接收邮箱)

    bootstrap = StrategyBootstrap()
    if not bootstrap.ensure_trading_environment():
        LogError("交易环境初始化失败，策略无法正常运行")
        return

    # 创建策略API对象
    g_api = StrategyApi()

    # 创建智能订单管理器
    g_order_manager = SmartOrderManager(
        strategy_api=g_api,
        price_limit_percent=0.5,  # 价格偏离限制
        order_size_ratio=0.3,     # 挂单量比例
        max_retry=3
    )

    SetTriggerType(1)
    SetTriggerType(5)
    # SetTriggerType(3, 1000)
    SetOrderWay(1)
    SetActual()

def handle_data(context):
    global g_api, g_order_manager, bootstrap

    if not bootstrap.ensure_trading_environment():
        LogError("交易环境初始化未完成,两秒后再尝试")
        time.sleep(2)
        return
    # 获取最新的服务状态
    status = g_api.get_status()
    if not status.get("connected"):
        LogError("交易连接断开，当前状态: {}".format(status))
        # 自动发送重连指令到服务端
        try:
            LogInfo("尝试自动重连到交易服务器...")
            
            # 获取客户端路径和账户ID信息
            client_path = 客户端路径  # 全局变量
            account_id = 交易账号     # 全局变量
            
            # 检查网关服务是否运行
            if not _check_gateway_service():
                LogInfo("交易网关服务不在运行状态，尝试重启服务...")
                # 先尝试重启服务
                if _start_gateway_service_with_retry(max_retries=1):
                    LogInfo("交易网关重启成功，等待5秒后尝试重连...")
                    time.sleep(5)  # 等待服务稳定
                else:
                    LogError("交易网关重启失败，无法自动重连")
                    return False
            
            # 尝试重新连接
            reconnect_result = g_api.connect(client_path, account_id)
            if reconnect_result:
                LogInfo("自动重连成功！")
                # 重连成功后更新状态
                new_status = g_api.get_status()
                LogInfo("重连后状态: {}".format(new_status))
                return True
            else:
                LogError("自动重连失败，请检查网络或手动重启交易网关")
                return False
                
        except Exception as e:
            LogError("自动重连过程中发生错误: {}".format(str(e)))
            import traceback
            LogError(traceback.format_exc())
            return False
        
    # 获取账户和持仓信息
    account = g_api.get_account()
    positions = g_api.get_positions()
    LogInfo(f"账户信息: {account}")
    LogInfo(f"持仓信息: {positions}")
    # 执行智能拆单买入
    # order_ids = g_order_manager.place_order(
    #     symbol="510300.SH",
    #     total_quantity=10000,  # 总数量
    #     price=3.800,     # 目标价格
    #     direction='BUY'
    # )
    
    # # 跟踪订单状态
    # for order_id in order_ids:
    #     while True:
    #         status = g_order_manager.get_order_status(order_id)
    #         if status['status'] in ('COMPLETED', 'FAILED'):
    #             LogInfo(f"订单{order_id}最终状态: {status}")
    #             break
    #         time.sleep(1)
    # HTS=1 if context.strategyStatus()=="C" else 0
    # for i in range(len(symbol_Id)):
    #     if 是否交易[i]=="否":
    #         continue
    #     tcode=stock_code_mapping(int(symbol_Id[i]))
    #     # O = Open(tcode, k_btime, k_cycle)
    #     H = High(tcode, k_btime, k_cycle)
    #     L = Low(tcode, k_btime, k_cycle)
    #     C = Close(tcode, k_btime, k_cycle)
    #     CD = Close(tcode, 'D', 1)
    #     if len(CD) < SlowLength:
    #         return
    #     financial_weighting=资金权重[i]/FinancialWeighting
    #     MarginEquity= Margin(tcode)/(初始权益*financial_weighting)
    # global R1_MONEYTOT, MONEY_PERCENTAGE, SHORT_MA, LONG_MA, C_SHORT_MA, C_LONG_MA
    # global AR0, AR1, DIFF, DEA, PW, CNT1, MAXAR, OCNT
    
    # # 初始化资金相关参数
    # if R1_MONEYTOT == 0:
    #     R1_MONEYTOT = context.initial_cash
    # if MONEY_PERCENTAGE == 0:
    #     MONEY_PERCENTAGE = g_params['初始比例']
    
    # # 资金管理部分
    # CHANGE_EQUITY = (NetAsset() / R1_MONEYTOT - 1) * 100
    # if BuyPosition() == 0 and BuyPosition(1) > 0:
    #     if CHANGE_EQUITY > g_params['减仓阈值']:
    #         _MONEY_PERCENTAGE = max(MONEY_PERCENTAGE * (R1_MONEYTOT / NetAsset()), g_params['初始比例'] * 0.618)
    #         MONEY_PERCENTAGE = _MONEY_PERCENTAGE
    #     if CHANGE_EQUITY < -g_params['加仓阈值']:
    #         _MONEY_PERCENTAGE = (MONEY_PERCENTAGE * R1_MONEYTOT) / NetAsset()
    #         MONEY_PERCENTAGE = _MONEY_PERCENTAGE
    #     R1_MONEYTOT = NetAsset()
    
    # # 指标计算区
    # NT = BarIndex()
    # SHORT = g_params['SHORT']
    # LONG = g_params['LONG']
    
    # SHORT_MA[-1] = talib.MA(Close(), SHORT)[-1]
    # LONG_MA[-1] = talib.MA(Close(), LONG)[-1]
    
    # # 当前K线是否为当日第一根K线
    # is_first_bar = Date()[-1] != Date()[-2] if len(Date()) > 1 else True
    # if is_first_bar:
    #     C_SHORT_MA = SHORT_MA[-2] if len(SHORT_MA) > 1 else 0
    #     C_LONG_MA = LONG_MA[-2] if len(LONG_MA) > 1 else 0
    
    # # 价格和技术指标计算
    # M0 = 12
    # M1 = 26
    # PRICE = (High()[-1] + Low()[-1] + Close()[-1]) / 3
    
    # if len(Close()) > M0:
    #     AR0[-1] = (PRICE / Close()[-M0-1] - 1) * 10000
    # else:
    #     AR0[-1] = 0
        
    # if len(Close()) > M1:
    #     AR1[-1] = (PRICE / Close()[-M1-1] - 1) * 10000
    # else:
    #     AR1[-1] = 0
    
    # # MACD相关指标
    # S1 = 12
    # L1 = 26
    # N1 = 9
    
    # price_arr = np.array([(High()[i] + Low()[i] + Close()[i])/3 for i in range(-len(Close()), 0)])
    # if len(price_arr) > L1:
    #     ema_short = talib.EMA(price_arr, S1)
    #     ema_long = talib.EMA(price_arr, L1)
    #     DIFF[-1] = (ema_short[-1] / ema_long[-1] - 1) * 10000
    #     DEA[-1] = talib.EMA(np.array([d for d in DIFF]), N1)[-1]
    #     PW[-1] = 2 * (DIFF[-1] - DEA[-1])
    
    # # 信号计算执行区
    # if len(AR1) < 2:
    #     return
        
    # COND1 = AR1[-1] > 0 and AR1[-2] <= 0
    
    # if COND1:
    #     CNT1 = 1
    # else:
    #     CNT1 = CNT1 + 1 if CNT1 > 0 else 0
    
    # CONDT1 = AR1[-1] > AR1[-2] and C_LONG_MA > LONG_MA[-2] and C_SHORT_MA > SHORT_MA[-2]
    # CONDT2 = AR1[-1] > AR1[-2] and C_SHORT_MA > C_LONG_MA and SHORT_MA[-2] <= LONG_MA[-2] and C_SHORT_MA > SHORT_MA[-2]
    
    # # 检查是否有连续7天收盘价高于开盘价
    # NCONDT1 = False
    # if len(Close()) >= 7:
    #     NCONDT1 = not all(Close()[-i] >= Open()[-i] for i in range(1, 8))
    
    # CONDT = CONDT1 or CONDT2
    
    # # 买入信号
    # if AR1[-1] > 30 and CONDT and NCONDT1 and CNT1 < 10 and Close()[-1] > C_SHORT_MA and PW[-1] > 0:
    #     Buy(1, Close()[-1], needCover=False)
    #     OCNT = 0  # 重置开仓计数
    
    # # 计算AR1的最高值
    # if len(AR1) > CNT1:
    #     MAXAR = max([AR1[-i] for i in range(1, CNT1+1)])
    
    # # 卖出信号
    # if BuyPosition() > 0:
    #     OCNT = OCNT + 1
    #     BK_PRICE = LastEntryPrice() if OCNT == 0 else EntryPrice()
    #     BK_HIGH = HighSinceEntry()
        
    #     # 卖出条件1: AR1最高值大于2000且当前AR1小于500且收盘价小于开盘价
    #     if MAXAR > 2000 and AR1[-1] < 500 and Close()[-1] < Open()[-1]:
    #         Sell(BuyPosition(), Close()[-1])
        
    #     # 卖出条件2: 最大涨幅超过8%且收盘价低于开仓后第3根K线的最低价
    #     if BK_HIGH / BK_PRICE - 1 > 0.08 and OCNT >= 3 and Close()[-1] < min([Low()[-i] for i in range(1, OCNT+4)]):
    #         Sell(BuyPosition(), Close()[-1])
        
    #     # 卖出条件3: MAXAR小于2000且PW下穿0且AR1大于1000且收盘价小于开盘价
    #     if MAXAR < 2000 and PW[-1] < 0 and PW[-2] >= 0 and AR1[-1] > 1000 and Close()[-1] < Open()[-1]:
    #         Sell(BuyPosition(), Close()[-1])
        
    #     # 卖出条件4: AR1下穿-30
    #     if AR1[-1] < -30 and AR1[-2] >= -30:
    #         Sell(BuyPosition(), Close()[-1])
    
    # # 资金比例控制
    # MAXVOL = 1000000000
    # # 设置资金使用比例
    # SetFloatParam(0, MONEY_PERCENTAGE)
    
    # # 绘制指标线
    # PlotNumeric('SHORT_MA', SHORT_MA[-1], 0xff0000)
    # PlotNumeric('LONG_MA', LONG_MA[-1], 0x0000ff)
    # PlotNumeric('AR1', AR1[-1], 0x00ff00)
    # PlotNumeric('PW', PW[-1], 0xff00ff)
    # PlotNumeric('MONEY_PCT', MONEY_PERCENTAGE, 0xffff00)
    # PlotNumeric('PROFIT', NetProfit() - TradeCost(), 0x800080, False)


from typing import Union
def stock_code_mapping(code: Union[int, str]) -> str:
    # 整数处理分支（进一步优化）
    if isinstance(code, int):
        if not (1 <= code <= 999999):
            raise ValueError("输入必须为6位以下正整数")
        
        # 格式化代码字符串（只做一次）
        code_str = f"{code:06d}"
        
        # 快速分类 - 使用整数除法和模运算
        first_digit = code // 100000
        first_two = code // 10000
        first_three = code // 1000
        
        # 沪市股票 (6开头)
        if first_digit == 6:
            if first_three == 688:
                return f"SSE|T|KSHARES|{code_str}"  # 科创板
            elif first_three in {600, 601, 603, 605}:
                return f"SSE|T|ASHARES|{code_str}"      # 沪主板
            
        # 深主板 (0,1,3开头或4-9开头)
        if first_three  in {0, 1, 3}:
            return f"SZSE|T|ASHARES|{code_str}"    # 深主板            
        # 中小板 (002开头)    
        if first_three == 2:
            return f"SZSE|T|SMESHARES|{code_str}"  # 中小板
        # 创业板 (30开头)
        if first_two == 30:
            return f"SZSE|T|CHSHARES|{code_str}"   # 创业板   
        # 深B股 (200开头)
        if first_three == 200:
            return f"SZSE|T|BSHARES|{code_str}"    # 深B股
        # ETF (159开头)
        if first_three == 159:
            return f"SZSE|T|FUNDS|{code_str}"      # ETF
             
        # 基金 (5开头)
        if first_digit == 5:
            return f"SSE|T|FUNDS|{code_str}"       # 沪基金
            
        # REITs (16-18开头)
        if first_two in {16, 18}:
            return f"SZSE|T|FUNDS|{code_str}"      # REITs
            
        # 沪B股 (9开头)
        if first_digit == 9:
            return f"SSE|T|BSHARES|{code_str}"     # 沪B股
            
        # 北交所和新三板
        if first_three in {830, 831, 832, 833, 834, 835, 836, 837, 838, 839, 
                          870, 871, 872, 873, 874, 875, 876, 877, 878, 879, 
                          920, 921, 922, 923, 924, 925, 926, 927, 928, 929}:
            return f"BJSE|T|STOCK|{code_str}"
        if first_three in {400, 430, 830}:
            return f"NEEQ|T|OTC|{code_str}"
            
        return f"UNKNOWN|{code_str}"
    
    # 字符串处理分支（原逻辑）
    elif isinstance(code, str):
        if not (code.isdigit() and len(code) == 6):
            raise ValueError("输入必须为6位数字字符串")

        if code.startswith('688'):
            return f"SSE|T|KSHARES|{code}"
        elif code.startswith(('600','601','603','605')):
            return f"SSE|T|ASHARES|{code}"
        elif code.startswith('5'):
            return f"SSE|T|FUNDS|{code}"
        elif code.startswith('900'):
            return f"SSE|T|BSHARES|{code}"
        elif code.startswith('159'):
            return f"SZSE|T|FUNDS|{code}"
        elif code.startswith(('000','001','003')):
            return f"SZSE|T|ASHARES|{code}"
        elif code.startswith('002'):
            return f"SZSE|T|SMESHARES|{code}"
        elif code.startswith('30'):
            return f"SZSE|T|CHSHARES|{code}"
        elif code.startswith('200'):
            return f"SZSE|T|BSHARES|{code}"
        elif code.startswith(('16','18')):
            return f"SZSE|T|FUNDS|{code}"
        elif code.startswith(('83','87','920')):
            return f"BJSE|T|STOCK|{code}"
        elif code.startswith(('400','430','830')):
            return f"NEEQ|T|OTC|{code}"
        else:
            return f"UNKNOWN|{code}"
    
    else:
        raise TypeError("输入类型必须为int或str")
    
def stock_index_code_mapping(code: Union[int, str]) -> str:
    if isinstance(code, int):
        code_str = f"{code:06d}"
        prefix_three = code // 1000
        if prefix_three == 399:
            return f"SZSE|T|INDEX|{code_str}"
        if prefix_three == 0:
            return f"SSE|T|INDEX|{code_str}"
    elif isinstance(code, str):
        if code.startswith('399'):
            return f"SZSE|T|INDEX|{code}"
        if code.startswith('000'):
            return f"SSE|T|INDEX|{code}"

def normalize_stock_code(code):
    """
    将各种格式的股票代码转换为标准的QMT API格式
    沪市：数字代码.SH（例如：600000.SH）
    深市：数字代码.SZ（例如：000001.SZ）
    北交所：数字代码.BJ（例如：430047.BJ）
    
    参数:
        code: 字符串型股票代码，可以是多种格式（sh600000, SH600000, 600000, 600000.SH, 430047.BJ等）
    
    返回:
        标准化后的股票代码字符串
    """
    # 去除所有空格并转换为大写
    code = str(code).strip().upper()
    
    # 去除任何前缀和后缀，只保留数字部分
    numeric_part = ''.join(filter(str.isdigit, code))
    
    # 判断市场类型
    if any(prefix in code for prefix in ['SH', 'SHSE', 'SSE', 'SH.', '.SH']):
        return f"{numeric_part}.SH"
    elif any(prefix in code for prefix in ['SZ', 'SZSE', 'SZ.', '.SZ']):
        return f"{numeric_part}.SZ"
    elif any(prefix in code for prefix in ['BJ', 'BSE', 'BJSE', 'BJ.', '.BJ']):
        return f"{numeric_part}.BJ"
    else:
        # 根据股票代码规则判断市场类型
        # 沪市：以6开头（主板）、5开头（基金）、7开头（衍生品）
        # 深市：以0开头（主板）、1开头（SME）、2开头（中小板）、3开头（创业板）
        # 北交所：以4、8开头，68开头或82、83、87、88开头的股票代码
        if numeric_part.startswith('6'):
            return f"{numeric_part}.SH"
        elif numeric_part.startswith(('0', '1', '2', '3')):
            return f"{numeric_part}.SZ"
        elif numeric_part.startswith('4') or numeric_part.startswith('8'):
            # 北交所代码通常以4开头（如43开头的新三板精选层挂牌公司）
            # 或8开头（部分北交所特定代码）
            return f"{numeric_part}.BJ"
        elif numeric_part.startswith('68') or numeric_part.startswith(('82', '83', '87', '88')):
            # 特定的北交所其他代码规则
            return f"{numeric_part}.BJ"
        elif numeric_part.startswith('5') or numeric_part.startswith('7'):
            # 上交所基金和衍生品
            return f"{numeric_part}.SH"
        else:
            # 无法确定市场类型，保持原样返回
            return code

def _check_gateway_service():
    try:
        shmem = SharedMemoryReader()
        status = shmem.get_data(DATA_STATUS) or {}
        # 添加默认值处理
        is_healthy = status.get("is_healthy", False)
        # 增强日志输出
        # LogInfo(f"网关检查详情[心跳:{status.get('last_heartbeat','无')} 连接:{status.get('connected',False)} 健康状态:{is_healthy}]")
        return all([
            shmem.is_gateway_running(),
            is_healthy
        ])
    except:
        return False

def _start_gateway_service_with_retry(max_retries=3):
    """带重试机制的服务启动"""
    retry_count = 0
    while retry_count < max_retries and not _check_gateway_service():
        try:
            LogInfo(f"尝试第{retry_count+1}次启动交易网关...")
            if _start_gateway_service():
                LogInfo("交易网关启动指令已发送")
                
                # 等待服务初始化
                for i in range(30):  # 最多等待30秒
                    if _check_gateway_service():
                        LogInfo("交易网关启动成功")
                        return True
                    time.sleep(1)
                LogInfo("交易网关启动超时")
                
        except Exception as e:
            LogInfo(f"启动失败: {str(e)}")
            traceback.LogInfo_exc()
        
        retry_count += 1
        time.sleep(5)  # 失败后等待5秒再重试
    
    if not _check_gateway_service():
        LogError("无法启动交易网关，请检查以下配置：")
        LogError(f"1. 客户端路径是否存在: {os.path.exists(客户端路径)}")
        LogError(f"2. 账户ID是否正确: {交易账号}")
        LogError(f"3. Python执行权限: {os.access(sys.executable, os.X_OK)}")
        return False
    return True

def _start_gateway_service():
    """使用subprocess启动网关服务"""
    try:
        gateway_path = os.path.join(os.path.dirname(__file__), "SmartOrderManagerGateWay.py")
        if not os.path.exists(gateway_path):
            raise FileNotFoundError(f"网关服务文件不存在: {gateway_path}")
        
        # 获取当前Python解释器路径
        python_exec = sys.executable
        # 构造启动命令
        cmd = [python_exec, gateway_path]
        
        # 启动参数配置
        startupinfo = subprocess.STARTUPINFO()
        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
        
        # 在_start_gateway_service()中添加权限检查
        if os.name == 'nt' and os.path.exists(客户端路径):
            # Windows系统检查写入权限
            test_file = os.path.join(客户端路径, 'permission_test.txt')
            try:
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
            except PermissionError:
                LogError("缺少客户端目录写入权限，请用管理员权限运行QMT")
                return False
        
        # 创建独立进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            stdin=subprocess.PIPE,
            creationflags=subprocess.CREATE_NO_WINDOW,  # 隐藏窗口
            cwd=os.path.dirname(__file__),  # 设置工作目录
            env=os.environ.copy()  # 继承环境变量
        )
        # 添加进程监控
        threading.Thread(target=_monitor_gateway_process, args=(process,)).start()
        return True
        
    except Exception as e:
        LogError(f"启动网关服务失败: {str(e)}")
        return False

def _monitor_gateway_process(process):
    """进程状态监控线程"""
    try:
        # 等待10秒初始化
        time.sleep(10)
        
        # 检查进程状态
        if process.poll() is None:
            LogInfo("交易网关进程运行正常 PID:{}".format(process.pid))
            
            # 记录输出日志
            stdout_thread = threading.Thread(target=_log_stream, args=(process.stdout, "INFO"))
            stderr_thread = threading.Thread(target=_log_stream, args=(process.stderr, "ERROR"))
            stdout_thread.start()
            stderr_thread.start()
        else:
            LogError("交易网关进程异常退出 返回码:{}".format(process.returncode))
    except Exception as e:
        LogError("进程监控异常: {}".format(str(e)))

def _log_stream(stream, level="INFO"):
    """实时记录日志输出"""
    try:
        for line in iter(stream.readline, b''):
            msg = line.decode('gbk', errors='replace').strip()
            if msg:
                if level == "INFO":
                    LogInfo("[网关服务] {}".format(msg))
                else:
                    LogError("[网关服务] {}".format(msg))
    except Exception as e:
        LogError("日志记录线程异常: {}".format(str(e)))
