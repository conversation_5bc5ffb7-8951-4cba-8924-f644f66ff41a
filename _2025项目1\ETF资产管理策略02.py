import time
import math
import copy
import talib
import threading
import numpy as np
import pandas as pd
from collections import deque
from queue import Queue, Empty
from xtquant import xtconstant
from xtquant.xttrader import XtQuantTrader
from xtquant.xttype import StockAccount

class TradeConnectionManager:
    """
    交易连接管理类 - 专门负责交易连接和订单处理
    
    功能：
    1. 管理交易账户连接
    2. 处理交易连接中断与重连
    3. 提供下单接口
    4. 发送邮件通知
    """
    def __init__(self, client_path, session_id=None, account_id=None, log_type=0):
        """
        初始化交易连接管理器
        
        参数:
            client_path: 客户端路径，如"D:\\国金QMT交易端模拟\\userdata_mini"
            session_id: 会话编号，如果为None则自动生成
            account_id: 交易账号，如果为None则需要后续设置
            log_type: 日志输出类型，0为print输出(通用环境)，1为LogInfo输出(极星量化环境)
        """
        # 导入必要的库
        import threading
        import datetime
        from xtquant import xttrader, xtconstant, xttype
        
        self.threading = threading
        self.time = time
        self.datetime = datetime
        self.xttrader = xttrader
        self.xtconstant = xtconstant
        self.xttype = xttype  # 直接导入xttype模块
        
        # 日志类型
        self.log_type = log_type
        
        # 设置会话ID（如果未提供则生成）
        if session_id is None:
            # 使用当前时间戳作为会话ID
            session_id = int(datetime.datetime.now().timestamp())
        
        # 交易连接参数
        self.client_path = client_path
        self.session_id = session_id
        self.account_id = account_id
        
        # 交易对象
        self.trader = None
        self.account = None
        
        # 连接状态
        self.connected = False
        self.last_heartbeat = 0
        self.heartbeat_timeout = 30   # 心跳超时秒数
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 3  # 最大重连次数
        
        # 日志相关
        self.log_prefix = "[交易] "
        
        # 邮件相关设置
        self.email_enabled = False
        self.email_sender = ""
        self.email_receivers = []
        self.email_smtp_server = ""
        self.email_smtp_port = 465
        self.email_username = ""
        self.email_password = ""
        
        # 初始化交易对象
        self._init_trader()
        
        # 启动连接监控线程
        self.stop_monitor = False
        self.monitor_thread = threading.Thread(target=self._monitor_connection)
        self.monitor_thread.daemon = True
        
        self._log(f"{self.log_prefix}交易连接管理器初始化完成")
    
    def _log(self, message):
        """
        根据日志类型输出日志
        
        参数:
            message: 日志信息
        """
        if self.log_type == 0:
            # 使用print输出日志(通用环境)
            print(message)
        else:
            # 使用LogInfo输出日志(极星量化环境)
            try:
                LogInfo(message)
            except:
                # 如果LogInfo未定义，回退到print
                print(message)
    
    def _init_trader(self):
        """初始化交易对象"""
        try:
            # 创建XtQuantTrader实例
            self.trader = self.xttrader.XtQuantTrader(self.client_path, self.session_id)
            
            # 创建回调对象并注册
            from xtquant.xttrader import XtQuantTraderCallback
            
            class MyCallback(XtQuantTraderCallback):
                def __init__(self, manager):
                    self.manager = manager
                    
                def on_disconnected(self):
                    self.manager._log("连接断开")
                    
                def on_order_stock_async_response(self, response):
                    self.manager._log(f"异步下单响应: {response.order_id}, 备注: {response.order_remark}")
                    
                def on_stock_order(self, order):
                    self.manager._log(f"委托回报: {order.order_id}, 状态: {order.order_status}")
                    
                def on_stock_trade(self, trade):
                    self.manager._log(f"成交回报: {trade.order_id}, 数量: {trade.traded_volume}")
                    
                def on_order_error(self, error):
                    self.manager._log(f"委托错误: {error.error_msg}")
            
            # 创建并注册回调
            self.callback = MyCallback(self)
            
            # 如果已提供账号，则创建账号对象
            if self.account_id:
                # 直接使用xttype模块
                self.account = self.xttype.StockAccount(self.account_id)
                
            # 注册回调
            if self.trader:
                self.trader.register_callback(self.callback)
                
            self._log(f"{self.log_prefix}交易对象初始化完成")
        except Exception as e:
            self._log(f"{self.log_prefix}交易对象初始化异常: {str(e)}")
    
    def _init_async_engine(self):
        """初始化异步处理引擎"""
        # 任务队列
        self.task_queue = Queue()
        # 订单状态字典
        self.order_status_dict = {}
        # 工作线程
        self.worker_threads = []
        # 线程停止标志
        self.stop_workers = False

        # 启动工作线程
        for i in range(3):  # 创建3个工作线程
            thread = threading.Thread(target=self._order_worker, daemon=True)
            thread.start()
            self.worker_threads.append(thread)

        # 启动订单监控线程
        self.monitor_thread = threading.Thread(target=self._async_monitor_orders, daemon=True)
        self.monitor_thread.start()

    def _order_worker(self):
        """工作线程处理函数，从队列获取任务并执行"""
        while not self.stop_workers:
            try:
                # 获取任务，非阻塞，超时1秒
                try:
                    task = self.task_queue.get(timeout=1)
                except Empty:
                    continue
                
                # 执行任务
                task_type, task_data = task

                if task_type == "BUY":
                    self._async_execute_buy_task(task_data)
                elif task_type == "SELL":
                    self._async_execute_sell_task(task_data)
                elif task_type == "CANCEL":
                    self._async_cancel_order(task_data)

                # 标记任务完成
                self.task_queue.task_done()

            except Exception as e:
                self._log(f"工作线程异常: {str(e)}")
    def set_account(self, account_id, account_type="STOCK"):
        """
        设置交易账号
        
        参数:
            account_id: 交易账号
            account_type: 账号类型，默认为STOCK，可选项有STOCK, CREDIT, FUTURE等
        """
        try:
            self.account_id = account_id
            
            # 根据账号类型创建不同的账号对象
            if account_type == "CREDIT":
                self.account = self.xttype.StockAccount(account_id, self.xtconstant.CREDIT_ACCOUNT)
            elif account_type == "FUTURE":
                self.account = self.xttype.FutureAccount(account_id)
            else:  # 默认为股票账户
                self.account = self.xttype.StockAccount(account_id)
                
            self._log(f"{self.log_prefix}账号设置成功: {account_id}, 类型: {account_type}")
            return True
        except Exception as e:
            self._log(f"{self.log_prefix}账号设置异常: {str(e)}")
            return False
    
    def register_callback(self, callback_obj):
        """
        注册交易回调对象
        
        参数:
            callback_obj: 实现了XtQuantTraderCallback接口的回调对象
        """
        if not self.trader:
            self._log(f"{self.log_prefix}交易对象未初始化，无法注册回调")
            return False
            
        try:
            self.trader.register_callback(callback_obj)
            self._log(f"{self.log_prefix}交易回调注册成功")
            return True
        except Exception as e:
            self._log(f"{self.log_prefix}交易回调注册异常: {str(e)}")
            return False
    
    def start(self):
        """启动交易线程"""
        if not self.trader:
            self._log(f"{self.log_prefix}交易对象未初始化，无法启动")
            return False
            
        try:
            self.trader.start()
            
            # 启动监控线程
            if not self.monitor_thread.is_alive():
                self.monitor_thread.start()
                
            self._log(f"{self.log_prefix}交易线程启动成功")
            return True
        except Exception as e:
            self._log(f"{self.log_prefix}交易线程启动异常: {str(e)}")
            return False
    
    def connect(self):
        """建立交易连接，带重试和更好的错误处理"""
        import os
        import random
        
        if not self.trader:
            self._log(f"{self.log_prefix}交易对象未初始化，无法连接")
            return False
            
        # 检查路径是否存在
        if not os.path.exists(self.client_path):
            self._log(f"{self.log_prefix}客户端路径不存在: {self.client_path}")
            return False
        
        # 如果session_id为None，生成一个带随机数的会话ID以避免冲突
        if self.session_id is None:
            self.session_id = int(time.time()) + random.randint(100, 999)
            self._log(f"{self.log_prefix}使用会话ID: {self.session_id}")
        
        # 尝试多次连接
        for attempt in range(3):
            try:
                self._log(f"{self.log_prefix}尝试连接(第{attempt+1}次)")
                result = self.trader.connect()
                if result == 0:
                    self.connected = True
                    self.last_heartbeat = self.time.time()
                    self.reconnect_attempts = 0
                    self._log(f"{self.log_prefix}交易服务器连接成功")
                    return True
                else:
                    self.connected = False
                    self._log(f"{self.log_prefix}交易服务器连接失败，错误码: {result}")

                    # 如果连接失败，可能是会话ID冲突，尝试更换
                    if attempt < 2:  # 最多尝试3次
                        self.session_id = int(time.time()) + random.randint(100, 999)
                        self._log(f"{self.log_prefix}更换会话ID: {self.session_id}")
                        # 重新初始化交易对象
                        self._init_trader()
                        self.time.sleep(2)  # 等待2秒后重试
            except Exception as e:
                self.connected = False
                self._log(f"{self.log_prefix}交易服务器连接异常: {str(e)}")

                return False
    
    def subscribe(self):
        """订阅交易主推"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法订阅")
            return False
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法订阅")
            return False
            
        try:
            # 订阅交易主推
            result = self.trader.subscribe(self.account)
            
            if result == 0:
                self._log(f"{self.log_prefix}交易主推订阅成功")
                return True
            else:
                self._log(f"{self.log_prefix}交易主推订阅失败，错误码: {result}")
                return False
        except Exception as e:
            self._log(f"{self.log_prefix}交易主推订阅异常: {str(e)}")
            return False
    
    def check_connection_health(self):
        """检查连接健康状态"""
        if not self.connected:
            return False
        
        try:
            # 检查心跳
            current_time = self.time.time()
            if current_time - self.last_heartbeat > self.heartbeat_timeout:
                self._log(f"{self.log_prefix}连接心跳超时")
                return False
            
            # 尝试获取账户信息以验证连接
            try:
                if self.trader and self.account:
                    asset = self.trader.query_stock_asset(self.account)
                    if asset:
                        self.last_heartbeat = current_time
                        return True
                return False
            except:
                return False
                
        except Exception as e:
            self._log(f"{self.log_prefix}检查连接健康状态异常: {str(e)}")
            return False
    
    def _monitor_connection(self):
        """连接监控线程"""
        while not self.stop_monitor:
            try:
                if self.connected and not self.check_connection_health():
                    self._log(f"{self.log_prefix}检测到连接异常，尝试重连")
                    self._reconnect()
                
                self.time.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                self._log(f"{self.log_prefix}连接监控异常: {str(e)}")
                self.time.sleep(5)
    
    def _reconnect(self):
        """重新连接"""
        try:
            if self.reconnect_attempts >= self.max_reconnect_attempts:
                self._log(f"{self.log_prefix}达到最大重连次数，停止重连")
                return False
            
            self.reconnect_attempts += 1
            self._log(f"{self.log_prefix}开始第 {self.reconnect_attempts} 次重连")
            
            # 断开现有连接
            if self.trader:
                try:
                    self.trader.disconnect()
                except:
                    pass
            
            # 初始化交易对象
            self._init_trader()
            
            # 重新连接
            connect_success = self.connect()
            
            # 如果连接成功，则重新订阅
            if connect_success:
                subscribe_success = self.subscribe()
                self._log(f"{self.log_prefix}重连成功，订阅状态: {subscribe_success}")
                self.reconnect_attempts = 0
                return True
            else:
                self._log(f"{self.log_prefix}重连失败")
                return False
                
        except Exception as e:
            self._log(f"{self.log_prefix}重连异常: {str(e)}")
            return False
    
    def order_stock(self, symbol, direction, quantity, price_type, price, 
                   strategy_name="auto_trading", remark=""):
        """
        下单
        
        参数:
            symbol: 证券代码，如"600000.SH"
            direction: 交易方向，使用xtconstant.STOCK_BUY或xtconstant.STOCK_SELL
            quantity: 数量
            price_type: 价格类型，使用xtconstant.FIX_PRICE(限价)或xtconstant.LATEST_PRICE(市价)
            price: 价格，市价单可传0
            strategy_name: 策略名称
            remark: 备注
            
        返回:
            下单成功返回订单ID，失败返回None
        """
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法下单")
            return None
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法下单")
            return None
            
        try:
            # 下单
            self._log(f"{self.log_prefix}准备下单: {symbol}, 方向={direction}, 数量={quantity}, 价格={price}, 类型={price_type}")
            
            order_id = self.trader.order_stock(
                self.account, 
                symbol, 
                direction, 
                quantity, 
                price_type, 
                price, 
                strategy_name, 
                remark
            )
            
            if order_id:
                self._log(f"{self.log_prefix}下单成功，订单ID: {order_id}")
                return order_id
            else:
                self._log(f"{self.log_prefix}下单失败")
                return None
                
        except Exception as e:
            self._log(f"{self.log_prefix}下单异常: {str(e)}")
            return None
    
    def cancel_order(self, order_id):
        """
        异步撤单
        
        参数:
            order_id: 订单ID
            
        返回:
            任务ID
        """
        task_id = f"CANCEL_{order_id}_{int(time.time() * 1000)}"

        # 创建任务数据
        task_data = {
            "task_id": task_id,
            "order_id": order_id,
            "status": "PENDING",
            "created_time": time.time()
        }

        # 添加到任务队列
        self.task_queue.put(("CANCEL", task_data))

        self._log(f"撤单任务已加入队列: {task_id}")
        return task_id

    def liquidate_all_positions(self, price_discount_percent=0, use_market_order=True, remark="策略清仓"):
        """
        异步清仓所有持仓

        参数:
            price_discount_percent: 价格折扣百分比
            use_market_order: 是否使用市价单
            remark: 订单备注
        """
        positions = self.query_positions()
        task_ids = []

        # 先撤销所有未完成订单
        orders = self.query_orders()
        for order in orders:
            if hasattr(order, 'order_status') and order.order_status not in [2, 5]:
                self.cancel_order(order.order_id)

        # 清仓所有持仓
        for position in positions:
            if not hasattr(position, 'stock_code') or not hasattr(position, 'can_use_volume') or position.can_use_volume <= 0:
                continue

            symbol = position.stock_code
            quantity = position.can_use_volume

            # 市价卖出
            task_id = self.place_sell_order(
                symbol=symbol,
                total_quantity=quantity,
                limit_price=None if use_market_order else 0,  # 市价单
                min_quantity=100,
                remark=remark,
                price_discount_percent=price_discount_percent,
                use_market_order=use_market_order
            )
            task_ids.append(task_id)

        return task_ids
    
    def query_asset(self):
        """查询资产"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询资产")
            return None
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询资产")
            return None
            
        try:
            return self.trader.query_stock_asset(self.account)
        except Exception as e:
            self._log(f"{self.log_prefix}查询资产异常: {str(e)}")
            return None
    
    def query_orders(self):
        """查询当日委托"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询委托")
            return []
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询委托")
            return []
            
        try:
            return self.trader.query_stock_orders(self.account)
        except Exception as e:
            self._log(f"{self.log_prefix}查询委托异常: {str(e)}")
            return []
    
    def query_trades(self):
        """查询当日成交"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询成交")
            return []
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询成交")
            return []
            
        try:
            return self.trader.query_stock_trades(self.account)
        except Exception as e:
            self._log(f"{self.log_prefix}查询成交异常: {str(e)}")
            return []
    
    def query_positions(self):
        """查询持仓"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询持仓")
            return []
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询持仓")
            return []
            
        try:
            return self.trader.query_stock_positions(self.account)
        except Exception as e:
            self._log(f"{self.log_prefix}查询持仓异常: {str(e)}")
            return []
    
    def query_position(self, symbol):
        """查询单个持仓"""
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询持仓")
            return None
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询持仓")
            return None
            
        try:
            return self.trader.query_stock_position(self.account, symbol)
        except Exception as e:
            self._log(f"{self.log_prefix}查询持仓异常: {str(e)}")
            return None
    
    def query_order(self, order_id):
        """
        查询订单
        
        参数:
            order_id: 订单ID
            
        返回:
            订单对象，失败返回None
        """
        if not self.trader or not self.account:
            self._log(f"{self.log_prefix}交易对象或账号未初始化，无法查询订单")
            return None
            
        if not self.connected:
            self._log(f"{self.log_prefix}交易连接未建立，无法查询订单")
            return None
            
        try:
            # 使用正确的方法名：query_stock_order，而不是query_order_by_code
            return self.trader.query_stock_order(self.account, order_id)
        except Exception as e:
            self._log(f"{self.log_prefix}查询订单异常: {str(e)}")
            return None
    
    def run_forever(self):
        """阻塞线程，接收交易推送"""
        if not self.trader:
            self._log(f"{self.log_prefix}交易对象未初始化，无法等待推送")
            return
            
        try:
            self.trader.run_forever()
        except Exception as e:
            self._log(f"{self.log_prefix}等待推送异常: {str(e)}")
    
    def shutdown(self):
        """关闭连接管理器"""
        self._log(f"{self.log_prefix}正在关闭交易连接管理器...")
        self.stop_monitor = True
        
        if hasattr(self, 'monitor_thread') and self.monitor_thread.is_alive():
            self.monitor_thread.join(5)
        
        if self.trader:
            try:
                self.trader.disconnect()
            except:
                pass
        
        self._log(f"{self.log_prefix}交易连接管理器已关闭")
    
    def setup_email(self, sender, receivers, smtp_server, smtp_port, username, password):
        """
        设置邮件发送参数
        
        参数:
            sender: 发件人邮箱地址
            receivers: 收件人邮箱地址列表
            smtp_server: SMTP服务器地址
            smtp_port: SMTP服务器端口
            username: 邮箱账号
            password: 邮箱密码
            
        返回:
            设置成功返回True，失败返回False
        """
        try:
            self.email_sender = sender
            self.email_receivers = receivers if isinstance(receivers, list) else [receivers]
            self.email_smtp_server = smtp_server
            self.email_smtp_port = smtp_port
            self.email_username = username
            self.email_password = password
            self.email_enabled = True
            
            self._log(f"{self.log_prefix}邮件设置成功")
            return True
        except Exception as e:
            self._log(f"{self.log_prefix}邮件设置异常: {str(e)}")
            self.email_enabled = False
            return False
    
    def send_email(self, subject, content):
        """
        发送邮件
        
        参数:
            subject: 邮件主题
            content: 邮件内容
            
        返回:
            发送成功返回True，失败返回False
        """
        if not self.email_enabled:
            self._log(f"{self.log_prefix}邮件功能未启用，无法发送邮件")
            return False
            
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            from email.header import Header
            
            # 创建邮件对象
            message = MIMEMultipart()
            message['From'] = self.email_sender
            message['To'] = ','.join(self.email_receivers)
            message['Subject'] = Header(subject, 'utf-8')
            
            # 添加邮件内容
            message.attach(MIMEText(content, 'plain', 'utf-8'))
            
            # 发送邮件
            if self.email_smtp_port == 465:
                # SSL加密方式
                smtp = smtplib.SMTP_SSL(self.email_smtp_server, self.email_smtp_port)
            else:
                # 普通方式
                smtp = smtplib.SMTP(self.email_smtp_server, self.email_smtp_port)
                
            # 登录邮箱
            smtp.login(self.email_username, self.email_password)
            
            # 发送邮件
            smtp.sendmail(self.email_sender, self.email_receivers, message.as_string())
            
            # 关闭连接
            smtp.quit()
            
            self._log(f"{self.log_prefix}邮件发送成功")
            return True
        except Exception as e:
            self._log(f"{self.log_prefix}邮件发送异常: {str(e)}")
            return False


class SmartOrderManager(TradeConnectionManager):
    """
    智能拆单交易管理类，支持买入和卖出的自动拆单执行。
    根据市场挂单量动态调整每笔订单大小，异步跟踪订单状态直到全部完成。
    
    特点:
    - 自动读取盘口挂单量，按比例拆单
    - 分批执行，成交后自动发送后续订单
    - 支持价格偏离和时间窗口限制
    - 完整的订单生命周期跟踪
    - 继承了交易连接管理功能
    - 支持邮件通知订单执行状态
    """
    
    def __init__(self, client_path=None, session_id=None, account_id=None, 
                 price_limit_percent=0, time_limit_seconds=0, order_size_ratio=1/3, 
                 max_retry=3, price_step_percent=0.02, status_check_interval=1, log_type=0,
                 heartbeat_timeout=30, max_reconnect_attempts=3):
        """
        初始化智能拆单管理器
        
        参数:
            client_path: 客户端路径，如"D:\\国金QMT交易端模拟\\userdata_mini"
            session_id: 会话编号，如果为None则自动生成
            account_id: 交易账号，如果为None则需要后续设置
            price_limit_percent: 价格偏离限制百分比，0表示不限制
            time_limit_seconds: 订单超时时间(秒)，0表示不限制
            order_size_ratio: 相对于盘口挂单量的下单比例，默认1/3
            max_retry: 单笔订单最大重试次数
            price_step_percent: 价格调整步长百分比
            status_check_interval: 订单状态检查间隔(秒)
            log_type: 日志输出类型，0为print输出(通用环境)，1为LogInfo输出(极星量化环境)
            heartbeat_timeout: 心跳超时秒数
            max_reconnect_attempts: 最大重连次数
        """
        # 首先调用父类的初始化方法
        super().__init__(
            client_path=client_path,
            session_id=session_id,
            account_id=account_id,
            log_type=log_type
        )
        
        self.heartbeat_timeout = heartbeat_timeout
        self.max_reconnect_attempts = max_reconnect_attempts
        
        # 智能拆单相关参数
        self.price_limit_percent = price_limit_percent
        self.time_limit_seconds = time_limit_seconds
        self.order_size_ratio = order_size_ratio
        self.max_retry = max_retry
        self.price_step_percent = price_step_percent
        self.status_check_interval = status_check_interval
        
        # 订单跟踪相关变量
        self.active_orders = {}      # 活跃订单字典
        self.completed_orders = {}   # 已完成订单字典
        self.failed_orders = {}      # 失败订单字典
        self.order_tasks = {}        # 订单任务跟踪
        self.subscribed_symbols = {}  # 存储已订阅的股票代码
        # 订单状态常量
        self.ORDER_SUBMITTED = "已提交"
        self.ORDER_ACCEPTED = "已接受"
        self.ORDER_FILLED = "已成交"
        self.ORDER_PARTIALLY_FILLED = "部分成交"
        self.ORDER_CANCELLED = "已撤销"
        self.ORDER_REJECTED = "已拒绝"
        self.ORDER_EXPIRED = "已过期"
        
        # 日志相关
        self.log_prefix = "[智能拆单] "
        
        # 创建线程锁
        self.lock = self.threading.Lock()
        
        self._log(f"{self.log_prefix}智能拆单交易管理器已初始化")
        
        # 修复父类初始化问题：直接调用start、connect和subscribe方法
        if client_path and account_id:
            try:
                # 启动交易线程
                self.start()
                
                # 建立交易连接
                connect_result = self.connect()
                self._log(f"{self.log_prefix}交易连接结果: {connect_result}")
                
                # 订阅交易主推
                if connect_result:
                    subscribe_result = self.subscribe()
                    self._log(f"{self.log_prefix}交易订阅结果: {subscribe_result}")
            except Exception as e:
                self._log(f"{self.log_prefix}启动交易服务异常: {str(e)}")
    
        # 初始化异步引擎
        self._init_async_engine()
        
        # 任务字典
        self.tasks = {}

    def _ensure_subscribed(self, symbol):
        """确保股票代码已经订阅"""
        if symbol not in self.subscribed_symbols or not self.subscribed_symbols[symbol]:
            try:
                from xtquant import xtdata
                xtdata.subscribe_whole_quote([symbol])
                self.subscribed_symbols[symbol] = True
                self._log(f"{self.log_prefix}订阅 {symbol} 全推行情成功")
                # 短暂等待数据就绪
                self.time.sleep(0.5)
            except Exception as e:
                self._log(f"{self.log_prefix}订阅 {symbol} 全推行情异常: {str(e)}")
                return False
        return True
    def _log(self, message):
        """
        根据日志类型输出日志
        
        参数:
            message: 日志信息
        """
        if self.log_type == 0:
            # 使用print输出日志(通用环境)
            print(message)
        else:
            # 使用LogInfo输出日志(极星量化环境)
            try:
                LogInfo(message)
            except:
                # 如果LogInfo未定义，回退到print
                print(message)
    
    def _get_upper_limit(self, symbol):
        """
        获取涨停价
        
        参数:
            symbol: 股票代码
            
        返回:
            涨停价，获取失败返回0
        """
        try:
            if self.log_type == 0:
                # 通用环境使用QMT的API
                from xtquant import xtdata
                
                # 使用get_instrument_detail获取证券详情，其中包含涨跌停价格
                detail = xtdata.get_instrument_detail(symbol)
                if detail and "UpStopPrice" in detail:
                    return detail["UpStopPrice"]
                return 0
            else:
                # 极星平台环境使用平台API
                return Q_UpperLimit(symbol)
        except Exception as e:
            self._log(f"{self.log_prefix}获取 {symbol} 涨停价异常: {str(e)}")
            return 0
    
    def _get_lower_limit(self, symbol):
        """
        获取跌停价
        
        参数:
            symbol: 股票代码
            
        返回:
            跌停价，获取失败返回0
        """
        try:
            if self.log_type == 0:
                # 通用环境使用QMT的API
                from xtquant import xtdata
                
                # 使用get_instrument_detail获取证券详情，其中包含涨跌停价格
                detail = xtdata.get_instrument_detail(symbol)
                if detail and "DownStopPrice" in detail:
                    return detail["DownStopPrice"]
                return 0
            else:
                # 极星平台环境使用平台API
                return Q_LowLimit(symbol)
        except Exception as e:
            self._log(f"{self.log_prefix}获取 {symbol} 跌停价异常: {str(e)}")
            return 0
    
    def _get_last_price(self, symbol):
        """
        获取最新价
        
        参数:
            symbol: 股票代码
            
        返回:
            最新价，获取失败返回0
        """
        try:
            if self.log_type == 0:
                # 通用环境使用QMT的API
                from xtquant import xtdata

                # 确保已订阅
                self._ensure_subscribed(symbol)
                
                # 获取最新价
                tick_data = xtdata.get_full_tick([symbol])
                if symbol in tick_data and "lastPrice" in tick_data[symbol]:
                    return tick_data[symbol]["lastPrice"]
                return 0
            else:
                # 极星平台环境使用平台API
                return Q_Last(symbol)
        except Exception as e:
            self._log(f"{self.log_prefix}获取 {symbol} 最新价异常: {str(e)}")
            return 0
    
    def _get_ask1_info(self, symbol):
        """
        获取卖一档价格和量
        
        参数:
            symbol: 股票代码
            
        返回:
            (卖一价, 卖一量)元组，获取失败返回(0, 0)
        """
        try:
            if self.log_type == 0:
                # 通用环境使用QMT的API
                from xtquant import xtdata

                # 确保已订阅
                self._ensure_subscribed(symbol)
                
                # 获取卖一价和卖一量
                tick_data = xtdata.get_full_tick([symbol])
                if symbol in tick_data:
                    ask1_price = tick_data[symbol]["askPrice"][0] if "askPrice" in tick_data[symbol] and len(tick_data[symbol]["askPrice"]) > 0 else 0
                    ask1_volume = tick_data[symbol]["askVol"][0] if "askVol" in tick_data[symbol] and len(tick_data[symbol]["askVol"]) > 0 else 0
                    return ask1_price, ask1_volume
                return 0, 0
            else:
                # 极星平台环境使用平台API
                ask1_price = Q_AskPrice(symbol, 0)  # 卖一价
                ask1_volume = Q_AskVolume(symbol, 0)  # 卖一量
                return ask1_price, ask1_volume
        except Exception as e:
            self._log(f"{self.log_prefix}获取 {symbol} 卖一档信息异常: {str(e)}")
            return 0, 0
    
    def _get_bid1_info(self, symbol):
        """
        获取买一档价格和量
        
        参数:
            symbol: 股票代码
            
        返回:
            (买一价, 买一量)元组，获取失败返回(0, 0)
        """
        try:
            if self.log_type == 0:
                # 通用环境使用QMT的API
                from xtquant import xtdata

                # 确保已订阅
                self._ensure_subscribed(symbol)
                
                # 获取买一价和买一量
                tick_data = xtdata.get_full_tick([symbol])
                if symbol in tick_data:
                    bid1_price = tick_data[symbol]["bidPrice"][0] if "bidPrice" in tick_data[symbol] and len(tick_data[symbol]["bidPrice"]) > 0 else 0
                    bid1_volume = tick_data[symbol]["bidVol"][0] if "bidVol" in tick_data[symbol] and len(tick_data[symbol]["bidVol"]) > 0 else 0
                    return bid1_price, bid1_volume
                return 0, 0
            else:
                # 极星平台环境使用平台API
                bid1_price = Q_BidPrice(symbol, 0)  # 买一价
                bid1_volume = Q_BidVolume(symbol, 0)  # 买一量
                return bid1_price, bid1_volume
        except Exception as e:
            self._log(f"{self.log_prefix}获取 {symbol} 买一档信息异常: {str(e)}")
            return 0, 0
    
    def place_buy_order(self, symbol, total_quantity, limit_price=None, 
                        min_quantity=100, callback=None, **kwargs):
        """
        异步下买单
        
        参数:
            symbol: 股票代码
            total_quantity: 总数量
            limit_price: 限价，如果为None则使用市价单
            min_quantity: 最小下单数量
            callback: 订单状态回调函数
            **kwargs: 其他参数

        返回:
            任务ID
        """
        # 生成任务ID
        task_id = f"BUY_{symbol}_{int(time.time() * 1000)}"

        # 创建任务数据
        task_data = {
            "task_id": task_id,
            "symbol": symbol,
            "total_quantity": total_quantity,
            "limit_price": limit_price,
            "min_quantity": min_quantity,
            "callback": callback,
            "kwargs": kwargs,
            "status": "PENDING",
            "orders": [],
            "created_time": time.time()
        }

        # 添加到任务队列
        self.task_queue.put(("BUY", task_data))

        # 记录任务
        self.tasks[task_id] = task_data

        self._log(f"买入任务已加入队列: {task_id}")
        return task_id
    
    def place_sell_order(self, symbol, total_quantity, limit_price=None, 
                         min_quantity=100, callback=None, **kwargs):
        """
        异步下卖单
        
        参数:
            symbol: 股票代码
            total_quantity: 总数量
            limit_price: 限价，如果为None则使用市价单
            min_quantity: 最小下单数量
            callback: 订单状态回调函数
            **kwargs: 其他参数
        
        返回:
            任务ID
        """
        # 生成任务ID
        task_id = f"SELL_{symbol}_{int(time.time() * 1000)}"
        
        # 创建任务数据
        task_data = {
            "task_id": task_id,
            "symbol": symbol,
            "total_quantity": total_quantity,
            "limit_price": limit_price,
            "min_quantity": min_quantity,
            "callback": callback,
            "kwargs": kwargs,
            "status": "PENDING",
            "orders": [],
            "created_time": time.time()
        }
        
        # 添加到任务队列
        self.task_queue.put(("SELL", task_data))
        
        # 记录任务
        self.tasks[task_id] = task_data
        
        self._log(f"卖出任务已加入队列: {task_id}")
        return task_id
    

    def _async_execute_buy_task(self, task_data):
        """
        异步执行买入任务
        """
        try:
            symbol = task_data["symbol"]
            total_quantity = task_data["total_quantity"]
            limit_price = task_data["limit_price"]
            task_id = task_data["task_id"]

            # 获取当前市场价格和涨停价
            current_price = limit_price if limit_price else self._get_last_price(symbol)
            upper_limit = self._get_upper_limit(symbol)

            # 如果指定了限价并高于涨停，调整限价
            if limit_price and upper_limit > 0 and limit_price > upper_limit:
                self._log(f"指定买入价格 {limit_price} 高于涨停价 {upper_limit}，自动调整为涨停价")
                limit_price = upper_limit

            # 确保交易对象和账户已初始化
            if not self.trader or not self.account:
                self._log(f"交易对象或账户未初始化，无法执行买入任务")
                return

            # 尝试异步下单
            strategy_name = "智能拆单买入"
            order_remark = f"Task_{task_id}"
            
            self._log(f"准备买入 {symbol}，数量: {total_quantity}，价格: {limit_price if limit_price else '市价'}")
            
            order_id = self.trader.order_stock(
                self.account,
                symbol,
                self.xtconstant.STOCK_BUY,
                total_quantity,
                self.xtconstant.FIX_PRICE if limit_price else self.xtconstant.LATEST_PRICE,
                limit_price if limit_price else 0,
                strategy_name,
                order_remark
            )

            if order_id > 0:
                # 记录订单信息
                with self.lock:
                    self.order_status_dict[order_id] = {
                                "task_id": task_id,
                        "status": "SUBMITTED",
                        "callback": task_data.get("callback")
                    }
                    # 更新任务状态
                    if task_id in self.tasks:
                        self.tasks[task_id]["status"] = "SUBMITTED"
                        self.tasks[task_id]["orders"].append(order_id)
                    
                self._log(f"买入订单已提交: {order_id}")
            else:
                self._log(f"买入订单提交失败: {task_id}")
                # 执行回调
                callback = task_data.get("callback")
                if callback:
                    callback(task_id, "FAILED")
                    
        except Exception as e:
            self._log(f"执行买入任务异常: {str(e)}")
            import traceback
            self._log(traceback.format_exc())
    def _async_execute_sell_task(self, task_data):
        """
        异步执行卖出任务
        """
        try:
            symbol = task_data["symbol"]
            total_quantity = task_data["total_quantity"]
            limit_price = task_data["limit_price"]
            task_id = task_data["task_id"]

            # 获取当前市场价格和跌停价
            current_price = limit_price if limit_price else self._get_last_price(symbol)
            lower_limit = self._get_lower_limit(symbol)

            # 如果指定了限价并低于跌停，调整限价
            if limit_price and lower_limit > 0 and limit_price < lower_limit:
                self._log(f"指定卖出价格 {limit_price} 低于跌停价 {lower_limit}，自动调整为跌停价")
                limit_price = lower_limit

            # 确保交易对象和账户已初始化
            if not self.trader or not self.account:
                self._log(f"交易对象或账户未初始化，无法执行卖出任务")
                return

            # 使用同步下单方法替代异步方法
            strategy_name = "智能拆单卖出"
            order_remark = f"Task_{task_id}"
            
            self._log(f"准备卖出 {symbol}，数量: {total_quantity}，价格: {limit_price if limit_price else '市价'}")
            
            order_id = self.trader.order_stock(
                self.account,
                symbol,
                self.xtconstant.STOCK_SELL,
                total_quantity,
                self.xtconstant.FIX_PRICE if limit_price else self.xtconstant.LATEST_PRICE,
                limit_price if limit_price else 0,
                strategy_name,
                order_remark
            )

            if order_id > 0:
                # 记录订单信息
                with self.lock:
                    self.order_status_dict[order_id] = {
                                "task_id": task_id,
                        "status": "SUBMITTED",
                        "callback": task_data.get("callback")
                    }
                    # 更新任务状态
                    if task_id in self.tasks:
                        self.tasks[task_id]["status"] = "SUBMITTED"
                        self.tasks[task_id]["orders"].append(order_id)
                    
                self._log(f"卖出订单已提交: {order_id}")
            else:
                self._log(f"卖出订单提交失败: {task_id}")
                # 执行回调
                callback = task_data.get("callback")
                if callback:
                    callback(task_id, "FAILED")
                
        except Exception as e:
            self._log(f"执行卖出任务异常: {str(e)}")
            import traceback
            self._log(traceback.format_exc())
    def _place_order(self, symbol, direction, quantity, price, **kwargs):
        """
        实际下单操作，调用QMT API
        
        返回订单ID或None
        """
        try:
            # 直接使用继承的方法，不再需要全局变量
            if direction == "BUY":
                order_id = self.order_stock(
                    symbol=symbol,
                    direction=self.xtconstant.STOCK_BUY, 
                    quantity=quantity,
                    price_type=self.xtconstant.FIX_PRICE,
                    price=price, 
                    strategy_name='智能拆单系统',
                    remark='买入'
                )
            else:  # SELL
                order_id = self.order_stock(
                    symbol=symbol,
                    direction=self.xtconstant.STOCK_SELL, 
                    quantity=quantity,
                    price_type=self.xtconstant.FIX_PRICE,
                    price=price, 
                    strategy_name='智能拆单系统',
                    remark='卖出'
                )
            
            return order_id
        except Exception as e:
            self._log(f"{self.log_prefix}下单异常: {str(e)}")
            return None
    
    def _async_monitor_orders(self):
        """异步订单监控线程"""
        while not self.stop_workers:
            try:
                # 获取所有需要监控的订单
                orders_to_monitor = []
                with self.lock:
                    for order_id, order_info in self.order_status_dict.items():
                        if order_info["status"] not in ["COMPLETED", "FAILED", "CANCELLED"]:
                            orders_to_monitor.append((order_id, order_info))
                
                # 批量查询订单状态
                if orders_to_monitor:
                    for order_id, order_info in orders_to_monitor:
                        try:
                            order = self.trader.query_stock_order(self.account, order_id)
                            if order:
                        # 更新订单状态
                                new_status = self._get_order_status(order.order_status)
                                if new_status != order_info["status"]:
                                    with self.lock:
                                        self.order_status_dict[order_id]["status"] = new_status
                                    
                                    # 如果订单完成或失败,执行回调
                                    if new_status in ["COMPLETED", "FAILED", "CANCELLED"]:
                                        if order_info["callback"]:
                                            order_info["callback"](order_info["task_id"], new_status)
                                            
                        except Exception as e:
                            self._log(f"查询订单 {order_id} 状态异常: {str(e)}")
                
                # 使用异步等待
                self.time.sleep(1)  # 每秒检查一次
                
            except Exception as e:
                self._log(f"订单监控异常: {str(e)}")
                self.time.sleep(1)

    def _get_order_status(self, order_status):
        """
        转换订单状态码为状态字符串
        """
        status_map = {
            50: "SUBMITTED",    # 已提交
            51: "ACCEPTED",     # 已接受
            52: "REJECTED",     # 已拒绝
            53: "CANCELLED",    # 已撤销
            54: "FAILED",       # 失败
            55: "COMPLETED"     # 已完成
        }
        return status_map.get(order_status, "UNKNOWN")
    
    def get_position_details(self):
        """
        获取所有持仓的详细信息，返回以合约代码为键的字典
        
        返回字典格式：
        {
            "600000.SH": {
                "symbol": "600000.SH",        # 合约代码
                "symbol_name": "浦发银行",    # 合约名称
                "direction": "LONG",          # 持仓方向：LONG多仓，SHORT空仓
                "total_amount": 1000,         # 总持仓量
                "today_amount": 500,          # 今日买入量
                "history_amount": 500,        # 历史持仓量
                "available_amount": 800,      # 可用持仓量
                "frozen_amount": 200,         # 冻结持仓量
                "position_price": 10.5,       # 持仓价格
                "market_price": 10.6,         # 最新市场价格
                "profit_loss": 100.0,         # 浮动盈亏
                "profit_loss_ratio": 0.01     # 盈亏比例
            },
            ...
        }
        """
        try:
            # 检查连接状态
            if not self.connected:
                self._log(f"{self.log_prefix}交易连接未建立，无法获取持仓信息")
                return {}
            
            # 查询所有持仓
            positions = self.query_positions()
            if not positions:
                return {}
            
            # 以合约代码为键构建持仓字典
            position_dict = {}
            
            for pos in positions:
                symbol = pos.stock_code if hasattr(pos, 'stock_code') else pos.m_strInstrumentID
                
                # 获取合约名称
                symbol_name = ""
                if hasattr(pos, 'stock_name'):
                    symbol_name = pos.stock_name
                elif hasattr(pos, 'm_strInstrumentName'):
                    symbol_name = pos.m_strInstrumentName
                
                # 获取持仓方向
                direction = "LONG"
                if hasattr(pos, 'direction'):
                    # 对于期货和期权，可能有方向区分
                    if pos.direction == 1:  # 假设1为空仓
                        direction = "SHORT"
                
                # 获取总持仓量
                total_amount = 0
                if hasattr(pos, 'volume'):
                    total_amount = pos.volume
                elif hasattr(pos, 'm_nVolume'):
                    total_amount = pos.m_nVolume
                
                # 获取今日持仓量
                today_amount = 0
                if hasattr(pos, 'today_volume'):
                    today_amount = pos.today_volume
                elif hasattr(pos, 'm_nTodayVolume'):
                    today_amount = pos.m_nTodayVolume
                
                # 获取历史持仓量
                history_amount = total_amount - today_amount
                
                # 获取可用持仓量
                available_amount = 0
                if hasattr(pos, 'can_use_volume'):
                    available_amount = pos.can_use_volume
                elif hasattr(pos, 'm_nCanUseVolume'):
                    available_amount = pos.m_nCanUseVolume
                
                # 获取冻结持仓量
                frozen_amount = total_amount - available_amount
                
                # 获取持仓价格
                position_price = 0
                if hasattr(pos, 'avg_price'):  # 优先使用成本价
                    position_price = pos.avg_price
                elif hasattr(pos, 'open_price'):  # 其次使用开仓价
                    position_price = pos.open_price
                elif hasattr(pos, 'm_dOpenPrice'):
                    position_price = pos.m_dOpenPrice
                
                # 获取最新市场价格和计算盈亏
                market_price = self._get_last_price(symbol)
                profit_loss = (market_price - position_price) * total_amount
                profit_loss_ratio = (market_price / position_price - 1) if position_price > 0 else 0
                
                # 构建持仓详情字典
                position_dict[symbol] = {
                    "symbol": symbol,
                    "symbol_name": symbol_name,
                    "direction": direction,
                    "total_amount": total_amount,
                    "today_amount": today_amount,
                    "history_amount": history_amount,
                    "available_amount": available_amount,
                    "frozen_amount": frozen_amount,
                    "position_price": position_price,
                    "market_price": market_price,
                    "profit_loss": profit_loss,
                    "profit_loss_ratio": profit_loss_ratio
                }
            
            return position_dict
            
        except Exception as e:
            self._log(f"{self.log_prefix}获取持仓详情异常: {str(e)}")
            return {}
    
    def cancel_task(self, task_id):
        """
        取消整个任务
        
        参数:
            task_id: 任务ID
            
        返回:
            是否成功
        """
        try:
            with self.lock:
                if task_id not in self.order_tasks:
                    self._log(f"{self.log_prefix}找不到任务 {task_id}")
                    return False
                
                task = self.order_tasks[task_id]
                if task["status"] != "ACTIVE":
                    self._log(f"{self.log_prefix}任务 {task_id} 已不是活跃状态，当前状态: {task['status']}")
                    return False
                
                task["status"] = "CANCELLING"
            
            # 取消所有活跃订单
            cancel_success = True
            for order in task["orders"]:
                order_id = order["order_id"]
                if order_id in self.active_orders:
                    if not self.cancel_order(order_id):
                        cancel_success = False
            
            with self.lock:
                task = self.order_tasks[task_id]
                task["status"] = "CANCELLED"
            
            self._log(f"{self.log_prefix}任务 {task_id} 已取消，成交量: {task['filled_quantity']}, 剩余: {task['remaining_quantity']}")
            
            # 发送邮件通知
            if hasattr(self, 'email_enabled') and self.email_enabled:
                subject = f"任务 {task_id} 已取消"
                content = f"""
                任务ID: {task_id}
                股票代码: {task['symbol']}
                操作类型: {"买入" if task["action"] == "BUY" else "卖出"}
                已成交数量: {task['filled_quantity']}
                未完成数量: {task['remaining_quantity']}
                取消时间: {self.datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                """
                self.send_email(subject, content)
            
            return cancel_success
        except Exception as e:
            self._log(f"{self.log_prefix}取消任务 {task_id} 异常: {str(e)}")
            return False
    
    def get_task_status(self, task_id):
        """
        获取任务状态
        
        参数:
            task_id: 任务ID
            
        返回:
            任务状态信息或None
        """
        with self.lock:
            return self.order_tasks.get(task_id, None)
    
    def get_all_tasks(self):
        """
        获取所有任务信息
        
        返回:
            任务字典的副本
        """
        with self.lock:
            return dict(self.order_tasks)
    
    def shutdown(self):
        """关闭订单管理器"""
        self._log("开始关闭订单管理器...")
        
        # 设置停止标志
        self.stop_workers = True
        
        # 等待所有工作线程结束
        for thread in self.worker_threads:
            if thread.is_alive():
                thread.join(3)
        
        # 等待监控线程结束
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(3)
        
        # 调用父类方法关闭连接
        super().shutdown()
    


# 在策略关闭函数中添加清理代码
def on_strategy_stop():
    global g_order_manager, g_active_orders
    
    # 取消所有活跃订单
    for symbol, task_id in g_active_orders.items():
        if hasattr(g_order_manager, '_log'):
            g_order_manager._log(f"{g_order_manager.log_prefix}策略停止，取消 {symbol} 的订单 {task_id}")
        else:
            try:
                LogInfo(f"策略停止，取消 {symbol} 的订单 {task_id}")
            except:
                print(f"策略停止，取消 {symbol} 的订单 {task_id}")
        g_order_manager.cancel_task(task_id)
    
    # 关闭智能拆单管理器
    if g_order_manager:
        # 发送策略停止邮件通知
        if hasattr(g_order_manager, 'email_enabled') and g_order_manager.email_enabled:
            try:
                g_order_manager.send_email(
                    "交易策略已停止",
                    f"ETF资产管理策略已于 {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 停止运行。"
                )
            except Exception as e:
                try:
                    LogInfo(f"发送策略停止邮件失败: {str(e)}")
                except:
                    print(f"发送策略停止邮件失败: {str(e)}")
        
        g_order_manager.shutdown()
        if hasattr(g_order_manager, '_log'):
            g_order_manager._log(f"{g_order_manager.log_prefix}智能拆单管理器已关闭")
        else:
            try:
                LogInfo("智能拆单管理器已关闭")
            except:
                print("智能拆单管理器已关闭")



# 策略参数
g_params['K线基础时间']  = 'D'   # k线基础时间
g_params['K线基础周期']  = 1     # k线周期
g_params['选择显示合约序号']  = 1 # 选择显示合约序号

g_params['疯牛乖离阈值']  =  2000  #疯牛乖离阈值
g_params['疯熊乖离阈值']  = -2000  #疯熊乖离阈值

g_params['订阅数据长度']  =  20000  #订阅数据长度
g_params['配置文件夹路径'] = "E:\stock_trade"
g_params['配置文件名'] = "配置文件与股票ETF代码.xlsx"
trade_order_filedir = g_params['配置文件夹路径']
trade_config_file   = trade_order_filedir+"\\"+g_params['配置文件名'] 
trade_config_DATA   =pd.read_excel(trade_config_file,sheet_name = 0)

symbol_Id =trade_config_DATA["股票ETF代码"].dropna()
资金权重=trade_config_DATA["资金权重"].dropna()
是否交易=trade_config_DATA["是否交易"].dropna()
短周期=trade_config_DATA["短周期"].dropna()
长周期=trade_config_DATA["长周期"].dropna()
初始资金百分比 =trade_config_DATA["初始资金百分比"].dropna()
减仓阈值=trade_config_DATA["减仓阈值"].dropna()
加仓阈值=trade_config_DATA["加仓阈值"].dropna()
信息发送邮箱=str(trade_config_DATA["信息发送邮箱"].iloc[0])
邮箱SMTP地址=str(trade_config_DATA["邮箱SMTP地址"].iloc[0])
邮箱端口=str(int(trade_config_DATA["邮箱端口"].iloc[0]))
邮箱用户名=str(trade_config_DATA["邮箱用户名"].iloc[0])
邮箱授权码=str(trade_config_DATA["邮箱授权码"].iloc[0])
信息接收邮箱=list(trade_config_DATA["信息接收邮箱"].dropna())
交易账号=str(int(trade_config_DATA["交易账号"].iloc[0]))
客户端路径=str(trade_config_DATA["客户端路径"].iloc[0])
心跳超时秒数=int(trade_config_DATA["心跳超时秒数"].iloc[0])
最大重连次数=int(trade_config_DATA["最大重连次数"].iloc[0])

symbol_d=[]
for i in range(len(symbol_Id)):
    symbol_d.append(copy.deepcopy(deque([0]*9,maxlen=9)))
TotalNumber=len(symbol_Id)    
UPSA,DWSA=[0]*TotalNumber,[0]*TotalNumber     
BKStatus,SKStatus,BPStatus,SPStatus=[0]*TotalNumber,[0]*TotalNumber,[0]*TotalNumber,[0]*TotalNumber
UPSQ=copy.deepcopy(symbol_d) 
DWSQ=copy.deepcopy(symbol_d) 

k_btime,k_cycle,SetDisplayNo=0,0,0
BullishLimit,BearishLimit=0,0
FinancialWeighting=0
def initialize(context): 
    global g_params,k_btime,k_cycle,SetDisplayNo,BullishLimit,BearishLimit,FinancialWeighting,g_order_manager,g_active_orders
    k_btime = g_params['K线基础时间'] # k线基础时间取参数
    k_cycle = g_params['K线基础周期'] # k线基础周期取参数
    SetDisplayNo = g_params['选择显示合约序号']-1 # 选择显示合约序号
    BullishLimit = g_params['疯牛乖离阈值']         # 疯牛乖离阈值
    BearishLimit = g_params['疯熊乖离阈值']         # 疯熊乖离阈值 

    DaySubDataLength = g_params['订阅数据长度']   # 订阅日线数据长度
    SubDataLength = int(DaySubDataLength)      # 订阅数据长度
    AluSubDataLength = min(1000,SubDataLength)  # 计算数据长度    
    DisplayCode=stock_code_mapping(int(symbol_Id[SetDisplayNo]))
    SetBarInterval(DisplayCode, k_btime, k_cycle,DaySubDataLength,AluSubDataLength) #订阅分钟线数据
    for i in range(len(symbol_Id)):
        tcode=stock_code_mapping(int(symbol_Id[i]))
        if 是否交易[i]=="是":
            LogInfo("订阅",symbol_Id[i],"的合约"+tcode,"资金权重==>",资金权重[i],"短周期==>",短周期[i],"长周期==>",长周期[i],"初始资金百分比==>",初始资金百分比[i],"减仓阈值==>",减仓阈值[i],"加仓阈值==>",加仓阈值[i])
            FinancialWeighting+=资金权重[i]
            if i==SetDisplayNo:
                continue
            SetBarInterval(tcode, k_btime, k_cycle,SubDataLength,AluSubDataLength) #订阅交易合约
            # SetBarInterval(tcode, 'M', 1 ,DaySubDataLength,AluSubDataLength) #订阅分钟线数据      
    LogInfo("交易账号==>",交易账号,"客户端路径==>",客户端路径,"心跳超时秒数==>",心跳超时秒数,"最大重连次数==>",最大重连次数)     
    LogInfo("信息发送邮箱==>",信息发送邮箱,"邮箱SMTP地址==>",邮箱SMTP地址,"邮箱端口==>",邮箱端口,"邮箱用户名==>",邮箱用户名,"信息接收邮箱==>",信息接收邮箱)
    # 初始化智能拆单管理器 - SmartOrderManager 在初始化时会自动启动交易服务
    # g_order_manager = SmartOrderManager(
    #     client_path=客户端路径,     # 客户端路径
    #     session_id=None,           # 自动生成
    #     account_id=交易账号,       # 交易账号
    #     price_limit_percent=1,     # 价格偏离限制1%
    #     time_limit_seconds=600,    # 10分钟超时 
    #     order_size_ratio=0.3,      # 相对盘口挂单量的30%
    #     max_retry=3,               # 最大重试3次
    #     price_step_percent=0.02,   # 价格调整步长0.02%
    #     status_check_interval=1,   # 每秒检查一次订单状态
    #     log_type=1,                # 使用LogInfo输出日志
    #     heartbeat_timeout=心跳超时秒数,      # 心跳超时秒数
    #     max_reconnect_attempts=最大重连次数  # 最大重连次数
    # )
    # # 设置邮件
    # g_order_manager.setup_email(
    #     sender=信息发送邮箱,
    #     receivers=信息接收邮箱,
    #     smtp_server=邮箱SMTP地址,
    #     smtp_port=邮箱端口,
    #     username=邮箱用户名,  
    #     password=邮箱授权码
    # )
    # # 初始化活跃订单跟踪字典
    # g_active_orders = {}
    
    SetTriggerType(1)
    SetTriggerType(5)
    SetOrderWay(1)
    SetActual()
    LogInfo("初始化完成",dir(context) )
    initialize_trader_connection()
# def handle_data(context):

#     HTS=1 if context.strategyStatus()=="C" else 0
#     for i in range(len(symbol_Id)):
#         if 是否交易[i]=="否":
#             continue
#         tcode=stock_code_mapping(int(symbol_Id[i]))
#         # O = Open(tcode, k_btime, k_cycle)
#         H = High(tcode, k_btime, k_cycle)
#         L = Low(tcode, k_btime, k_cycle)
#         C = Close(tcode, k_btime, k_cycle)
#         CD = Close(tcode, 'D', 1)
#         if len(CD) < SlowLength:
#             return
#         financial_weighting=资金权重[i]/FinancialWeighting
#         MarginEquity= Margin(tcode)/(初始权益*financial_weighting)
#     global R1_MONEYTOT, MONEY_PERCENTAGE, SHORT_MA, LONG_MA, C_SHORT_MA, C_LONG_MA
#     global AR0, AR1, DIFF, DEA, PW, CNT1, MAXAR, OCNT
    
#     # 初始化资金相关参数
#     if R1_MONEYTOT == 0:
#         R1_MONEYTOT = context.initial_cash
#     if MONEY_PERCENTAGE == 0:
#         MONEY_PERCENTAGE = g_params['初始比例']
    
#     # 资金管理部分
#     CHANGE_EQUITY = (NetAsset() / R1_MONEYTOT - 1) * 100
#     if BuyPosition() == 0 and BuyPosition(1) > 0:
#         if CHANGE_EQUITY > g_params['减仓阈值']:
#             _MONEY_PERCENTAGE = max(MONEY_PERCENTAGE * (R1_MONEYTOT / NetAsset()), g_params['初始比例'] * 0.618)
#             MONEY_PERCENTAGE = _MONEY_PERCENTAGE
#         if CHANGE_EQUITY < -g_params['加仓阈值']:
#             _MONEY_PERCENTAGE = (MONEY_PERCENTAGE * R1_MONEYTOT) / NetAsset()
#             MONEY_PERCENTAGE = _MONEY_PERCENTAGE
#         R1_MONEYTOT = NetAsset()
    
#     # 指标计算区
#     NT = BarIndex()
#     SHORT = g_params['SHORT']
#     LONG = g_params['LONG']
    
#     SHORT_MA[-1] = talib.MA(Close(), SHORT)[-1]
#     LONG_MA[-1] = talib.MA(Close(), LONG)[-1]
    
#     # 当前K线是否为当日第一根K线
#     is_first_bar = Date()[-1] != Date()[-2] if len(Date()) > 1 else True
#     if is_first_bar:
#         C_SHORT_MA = SHORT_MA[-2] if len(SHORT_MA) > 1 else 0
#         C_LONG_MA = LONG_MA[-2] if len(LONG_MA) > 1 else 0
    
#     # 价格和技术指标计算
#     M0 = 12
#     M1 = 26
#     PRICE = (High()[-1] + Low()[-1] + Close()[-1]) / 3
    
#     if len(Close()) > M0:
#         AR0[-1] = (PRICE / Close()[-M0-1] - 1) * 10000
#     else:
#         AR0[-1] = 0
        
#     if len(Close()) > M1:
#         AR1[-1] = (PRICE / Close()[-M1-1] - 1) * 10000
#     else:
#         AR1[-1] = 0
    
#     # MACD相关指标
#     S1 = 12
#     L1 = 26
#     N1 = 9
    
#     price_arr = np.array([(High()[i] + Low()[i] + Close()[i])/3 for i in range(-len(Close()), 0)])
#     if len(price_arr) > L1:
#         ema_short = talib.EMA(price_arr, S1)
#         ema_long = talib.EMA(price_arr, L1)
#         DIFF[-1] = (ema_short[-1] / ema_long[-1] - 1) * 10000
#         DEA[-1] = talib.EMA(np.array([d for d in DIFF]), N1)[-1]
#         PW[-1] = 2 * (DIFF[-1] - DEA[-1])
    
#     # 信号计算执行区
#     if len(AR1) < 2:
#         return
        
#     COND1 = AR1[-1] > 0 and AR1[-2] <= 0
    
#     if COND1:
#         CNT1 = 1
#     else:
#         CNT1 = CNT1 + 1 if CNT1 > 0 else 0
    
#     CONDT1 = AR1[-1] > AR1[-2] and C_LONG_MA > LONG_MA[-2] and C_SHORT_MA > SHORT_MA[-2]
#     CONDT2 = AR1[-1] > AR1[-2] and C_SHORT_MA > C_LONG_MA and SHORT_MA[-2] <= LONG_MA[-2] and C_SHORT_MA > SHORT_MA[-2]
    
#     # 检查是否有连续7天收盘价高于开盘价
#     NCONDT1 = False
#     if len(Close()) >= 7:
#         NCONDT1 = not all(Close()[-i] >= Open()[-i] for i in range(1, 8))
    
#     CONDT = CONDT1 or CONDT2
    
#     # 买入信号
#     if AR1[-1] > 30 and CONDT and NCONDT1 and CNT1 < 10 and Close()[-1] > C_SHORT_MA and PW[-1] > 0:
#         Buy(1, Close()[-1], needCover=False)
#         OCNT = 0  # 重置开仓计数
    
#     # 计算AR1的最高值
#     if len(AR1) > CNT1:
#         MAXAR = max([AR1[-i] for i in range(1, CNT1+1)])
    
#     # 卖出信号
#     if BuyPosition() > 0:
#         OCNT = OCNT + 1
#         BK_PRICE = LastEntryPrice() if OCNT == 0 else EntryPrice()
#         BK_HIGH = HighSinceEntry()
        
#         # 卖出条件1: AR1最高值大于2000且当前AR1小于500且收盘价小于开盘价
#         if MAXAR > 2000 and AR1[-1] < 500 and Close()[-1] < Open()[-1]:
#             Sell(BuyPosition(), Close()[-1])
        
#         # 卖出条件2: 最大涨幅超过8%且收盘价低于开仓后第3根K线的最低价
#         if BK_HIGH / BK_PRICE - 1 > 0.08 and OCNT >= 3 and Close()[-1] < min([Low()[-i] for i in range(1, OCNT+4)]):
#             Sell(BuyPosition(), Close()[-1])
        
#         # 卖出条件3: MAXAR小于2000且PW下穿0且AR1大于1000且收盘价小于开盘价
#         if MAXAR < 2000 and PW[-1] < 0 and PW[-2] >= 0 and AR1[-1] > 1000 and Close()[-1] < Open()[-1]:
#             Sell(BuyPosition(), Close()[-1])
        
#         # 卖出条件4: AR1下穿-30
#         if AR1[-1] < -30 and AR1[-2] >= -30:
#             Sell(BuyPosition(), Close()[-1])
    
#     # 资金比例控制
#     MAXVOL = 1000000000
#     # 设置资金使用比例
#     SetFloatParam(0, MONEY_PERCENTAGE)
    
#     # 绘制指标线
#     PlotNumeric('SHORT_MA', SHORT_MA[-1], 0xff0000)
#     PlotNumeric('LONG_MA', LONG_MA[-1], 0x0000ff)
#     PlotNumeric('AR1', AR1[-1], 0x00ff00)
#     PlotNumeric('PW', PW[-1], 0xff00ff)
#     PlotNumeric('MONEY_PCT', MONEY_PERCENTAGE, 0xffff00)
#     PlotNumeric('PROFIT', NetProfit() - TradeCost(), 0x800080, False)


from typing import Union
def stock_code_mapping(code: Union[int, str]) -> str:
    # 整数处理分支（进一步优化）
    if isinstance(code, int):
        if not (1 <= code <= 999999):
            raise ValueError("输入必须为6位以下正整数")
        
        # 格式化代码字符串（只做一次）
        code_str = f"{code:06d}"
        
        # 快速分类 - 使用整数除法和模运算
        first_digit = code // 100000
        first_two = code // 10000
        first_three = code // 1000
        
        # 沪市股票 (6开头)
        if first_digit == 6:
            if first_three == 688:
                return f"SSE|T|KSHARES|{code_str}"  # 科创板
            elif first_three in {600, 601, 603, 605}:
                return f"SSE|T|ASHARES|{code_str}"      # 沪主板
            
        # 深主板 (0,1,3开头或4-9开头)
        if first_three  in {0, 1, 3}:
            return f"SZSE|T|ASHARES|{code_str}"    # 深主板            
        # 中小板 (002开头)    
        if first_three == 2:
            return f"SZSE|T|SMESHARES|{code_str}"  # 中小板
        # 创业板 (30开头)
        if first_two == 30:
            return f"SZSE|T|CHSHARES|{code_str}"   # 创业板   
        # 深B股 (200开头)
        if first_three == 200:
            return f"SZSE|T|BSHARES|{code_str}"    # 深B股
        # ETF (159开头)
        if first_three == 159:
            return f"SZSE|T|FUNDS|{code_str}"      # ETF
             
        # 基金 (5开头)
        if first_digit == 5:
            return f"SSE|T|FUNDS|{code_str}"       # 沪基金
            
        # REITs (16-18开头)
        if first_two in {16, 18}:
            return f"SZSE|T|FUNDS|{code_str}"      # REITs
            
        # 沪B股 (9开头)
        if first_digit == 9:
            return f"SSE|T|BSHARES|{code_str}"     # 沪B股
            
        # 北交所和新三板
        if first_three in {830, 831, 832, 833, 834, 835, 836, 837, 838, 839, 
                          870, 871, 872, 873, 874, 875, 876, 877, 878, 879, 
                          920, 921, 922, 923, 924, 925, 926, 927, 928, 929}:
            return f"BJSE|T|STOCK|{code_str}"
        if first_three in {400, 430, 830}:
            return f"NEEQ|T|OTC|{code_str}"
            
        return f"UNKNOWN|{code_str}"
    
    # 字符串处理分支（原逻辑）
    elif isinstance(code, str):
        if not (code.isdigit() and len(code) == 6):
            raise ValueError("输入必须为6位数字字符串")

        if code.startswith('688'):
            return f"SSE|T|KSHARES|{code}"
        elif code.startswith(('600','601','603','605')):
            return f"SSE|T|ASHARES|{code}"
        elif code.startswith('5'):
            return f"SSE|T|FUNDS|{code}"
        elif code.startswith('900'):
            return f"SSE|T|BSHARES|{code}"
        elif code.startswith('159'):
            return f"SZSE|T|FUNDS|{code}"
        elif code.startswith(('000','001','003')):
            return f"SZSE|T|ASHARES|{code}"
        elif code.startswith('002'):
            return f"SZSE|T|SMESHARES|{code}"
        elif code.startswith('30'):
            return f"SZSE|T|CHSHARES|{code}"
        elif code.startswith('200'):
            return f"SZSE|T|BSHARES|{code}"
        elif code.startswith(('16','18')):
            return f"SZSE|T|FUNDS|{code}"
        elif code.startswith(('83','87','920')):
            return f"BJSE|T|STOCK|{code}"
        elif code.startswith(('400','430','830')):
            return f"NEEQ|T|OTC|{code}"
        else:
            return f"UNKNOWN|{code}"
    
    else:
        raise TypeError("输入类型必须为int或str")
    
def stock_index_code_mapping(code: Union[int, str]) -> str:
    if isinstance(code, int):
        code_str = f"{code:06d}"
        prefix_three = code // 1000
        if prefix_three == 399:
            return f"SZSE|T|INDEX|{code_str}"
        if prefix_three == 0:
            return f"SSE|T|INDEX|{code_str}"
    elif isinstance(code, str):
        if code.startswith('399'):
            return f"SZSE|T|INDEX|{code}"
        if code.startswith('000'):
            return f"SSE|T|INDEX|{code}"

def normalize_stock_code(code):
    """
    将各种格式的股票代码转换为标准的QMT API格式
    沪市：数字代码.SH（例如：600000.SH）
    深市：数字代码.SZ（例如：000001.SZ）
    北交所：数字代码.BJ（例如：430047.BJ）
    
    参数:
        code: 字符串型股票代码，可以是多种格式（sh600000, SH600000, 600000, 600000.SH, 430047.BJ等）
    
    返回:
        标准化后的股票代码字符串
    """
    # 去除所有空格并转换为大写
    code = str(code).strip().upper()
    
    # 去除任何前缀和后缀，只保留数字部分
    numeric_part = ''.join(filter(str.isdigit, code))
    
    # 判断市场类型
    if any(prefix in code for prefix in ['SH', 'SHSE', 'SSE', 'SH.', '.SH']):
        return f"{numeric_part}.SH"
    elif any(prefix in code for prefix in ['SZ', 'SZSE', 'SZ.', '.SZ']):
        return f"{numeric_part}.SZ"
    elif any(prefix in code for prefix in ['BJ', 'BSE', 'BJSE', 'BJ.', '.BJ']):
        return f"{numeric_part}.BJ"
    else:
        # 根据股票代码规则判断市场类型
        # 沪市：以6开头（主板）、5开头（基金）、7开头（衍生品）
        # 深市：以0开头（主板）、1开头（SME）、2开头（中小板）、3开头（创业板）
        # 北交所：以4、8开头，68开头或82、83、87、88开头的股票代码
        if numeric_part.startswith('6'):
            return f"{numeric_part}.SH"
        elif numeric_part.startswith(('0', '1', '2', '3')):
            return f"{numeric_part}.SZ"
        elif numeric_part.startswith('4') or numeric_part.startswith('8'):
            # 北交所代码通常以4开头（如43开头的新三板精选层挂牌公司）
            # 或8开头（部分北交所特定代码）
            return f"{numeric_part}.BJ"
        elif numeric_part.startswith('68') or numeric_part.startswith(('82', '83', '87', '88')):
            # 特定的北交所其他代码规则
            return f"{numeric_part}.BJ"
        elif numeric_part.startswith('5') or numeric_part.startswith('7'):
            # 上交所基金和衍生品
            return f"{numeric_part}.SH"
        else:
            # 无法确定市场类型，保持原样返回
            return code

def trade_connection_example():
    import time
    import datetime
    from xtquant import xtconstant
    
    # 初始化参数
    client_path = "D:\\国金QMT交易端模拟\\userdata_mini"  # 请替换为实际路径
    session_id = int(datetime.datetime.now().timestamp())  # 使用当前时间戳作为会话ID
    account_id = "********"  # 请替换为实际账号
    
    # 创建交易连接管理器，使用print输出日志(通用环境)
    trade_manager = TradeConnectionManager(
        client_path=client_path,
        session_id=session_id,
        account_id=account_id,
        log_type=0  # 0表示print输出，1表示LogInfo输出
    )
    
    # 配置邮件设置
    trade_manager.setup_email(
        sender="<EMAIL>",
        receivers=["<EMAIL>","<EMAIL>"],
        smtp_server="smtp.qq.com",
        smtp_port=465,
        username="<EMAIL>",  
        password="suvnyawlugejbgeg"
    )
    
    # 启动交易线程
    trade_manager.start()
    
    # 连接交易服务器
    connect_result = trade_manager.connect()
    LogInfo(f"交易连接结果: {connect_result}")
    
    # 订阅交易推送
    subscribe_result = trade_manager.subscribe()
    LogInfo(f"交易订阅结果: {subscribe_result}")
    
    # 查询资产
    asset = trade_manager.query_asset()
    if asset:
        LogInfo(f"可用资金: {asset.cash}")
    
    # 下单示例
    symbol = "510300.SH"
    order_id = trade_manager.order_stock(
        symbol=symbol,
        direction=xtconstant.STOCK_BUY,
        quantity=100,
        price_type=xtconstant.FIX_PRICE,
        price=10.5,
        strategy_name="测试策略",
        remark="测试买入"
    )
    
    if order_id:
        LogInfo(f"下单成功，订单ID: {order_id}")
        
        # 发送邮件通知
        trade_manager.send_email(
            subject="下单通知",
            content=f"已成功下单买入 {symbol} 100股，价格10.5，订单ID: {order_id}"
        )
        
        # 等待一段时间
        time.sleep(2)
        
        # 查询订单状态
        order = trade_manager.query_order(order_id)
        if order:
            LogInfo(f"订单状态: {order.order_status}")
    
    # 关闭连接
    trade_manager.shutdown()

def initialize_trader_connection():
    """尽量简化的连接设置"""
    import time
    from xtquant.xttrader import XtQuantTrader
    from xtquant.xttype import StockAccount
    
    # 使用固定会话ID和路径
    path = "D:\\国金QMT交易端模拟\\userdata_mini"
    session_id = 12345
    account_id = "********"
    
    LogInfo(f"尝试最简化连接: 路径={path}, 会话ID={session_id}")
    
    # 创建基本对象
    trader = XtQuantTrader(path, session_id)
    account = StockAccount(account_id, "STOCK")
    
    # 最小化操作 - 只做必要步骤
    trader.start()
    time.sleep(0.5)  # 短暂等待初始化
    connect_result = trader.connect()
    LogInfo(f"连接结果: {connect_result}")
    
    if connect_result == 0:
        subscribe_result = trader.subscribe(account)
        LogInfo(f"订阅结果: {subscribe_result}")
        return trader, account
    
    return None, None
