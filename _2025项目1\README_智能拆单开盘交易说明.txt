# QMT智能拆单开盘自动交易系统

## 功能介绍

本系统可在A股市场开盘时自动执行智能拆单交易策略，具有以下特点：

1. **开盘自动执行**：准确在每个交易日9:30:00自动启动交易
2. **智能拆单功能**：根据盘口挂单量动态调整每笔委托大小，降低市场冲击
3. **邮件通知**：委托、成交和撤单信息实时发送至指定邮箱
4. **自动判断交易日**：自动识别节假日和周末，只在交易日运行
5. **日志记录**：详细记录交易过程，便于事后分析
6. **健壮性设计**：异常处理和失败重试机制，提高系统可靠性

## 使用方法

### 方法一：使用批处理文件启动（推荐）

1. 双击运行 `创建自动交易启动脚本.bat`
2. 按任意键开始程序
3. 保持窗口运行状态，程序将在交易日开盘时自动执行

### 方法二：直接运行Python脚本

```bash
python auto_trading_at_market_open.py
```

## 执行逻辑

1. 程序启动后，将检查当前是否为交易日
2. 如果是交易日且未到开盘时间，程序将等待至9:30
3. 在开盘时刻（9:30:00）自动执行智能拆单交易
4. 交易完成后等待至下一个交易日

## 日志和通知

- 所有交易记录都将保存到当前目录下的`auto_trading_YYYYMMDD.log`文件中
- 系统会通过邮件发送委托、成交、撤单等重要信息
- 每笔订单的详细拆单记录也会记录在日志中，便于分析

## 参数配置

您可以在`SmartOrderManager2.py`的`smart_order_manager_example`函数中修改以下参数：

- 交易账号ID
- 交易股票代码
- 交易数量
- 是否使用市价单或限价单
- 邮件通知收件人

## 注意事项

1. 使用前请确保QMT交易端已启动并登录
2. 建议使用模拟账号先进行测试
3. 请确保计算机在交易时间不会进入休眠状态
4. 如需修改交易逻辑，请编辑`SmartOrderManager2.py`中的相关函数

## 故障排除

如果程序运行异常，请检查：

1. QMT交易端是否已启动并登录
2. 网络连接是否正常
3. 查看日志文件了解详细错误信息
4. 确保Python环境中已安装所需的依赖库 