import time
import talib
import numpy as np 
import numba as nb 
import pandas as pd
from collections import deque
g_params['信号合约'] = 'DCE|Z|V|MAIN'     # 信号计算的合约品种,一般用指数
g_params['交易合约'] = 'DCE|Z|V|MAIN'      # 交易下单的合约品种,一般用主连
g_params['K线基础时间'] = 'T'     # k线基础时间,tick和秒填“T”,分钟填“M”,日线填“D”
g_params['K线基础周期'] =  20      # k线周期,K线基础时间的倍数,15分钟填写“15”
g_params['中周期k线时间'] = 'M'   # 中周期k线时间,tick和秒填“T”,分钟填“M”,日线填“D”
g_params['中周期k线周期']= 1     # 中周期k线周期,K线基础时间的倍数,30分钟填写“30”
g_params['大周期k线时间'] = 'M'   # 大周期k线时间,tick和秒填“T”,分钟填“M”,日线填“D”
g_params['大周期k线周期']= 1      #大周期k线周期,K线基础时间的倍数,30分钟填写“30”

g_params['交易方向开关']  = 0  #大于0开多，小于0开空，等于0双向开仓
g_params['下单量']  = 1 #下单量
g_params['下单超价跳数']  = 1  #下单超价跳数
g_params['止盈跳数'] = 35     
g_params['止损跳数'] = 1      
g_params['平仓后是否退出策略'] = 2  #大于0时开仓后自动退出策略 
g_params['收盘倒计时分钟数设置'] = 1#距离收盘时间范围不开仓，并清仓    
  

g_params['N']  = 9  # KD N 参数
g_params['M1'] = 3  # KD M1参数
g_params['M2'] = 3  # KD M2参数  
@nb.njit(cache=True)    
def SMA1(price:np.array, period, weight):
    sma = 0.0
    smas= []
    if period <= 0:
        return np.array(smas)
    if weight > period or weight <= 0:
        return np.array(smas)
    append = smas.append    
    for i, p in enumerate(price):
        if np.isnan(p):
            p = 0.0
        if i > 0:
            sma = (sma*(period-weight)+p*weight)/period
            append(sma)
        else:
            sma = p*1.0
            append(sma)
    return np.array(smas)  

def tim_trigger(BK,SK,qty,itk,tcode):#盘中实时开仓
    global BKS,SKS
    if BK and BKS==0 and (A_BuyPosition(tcode) == 0) :
        iprc = min(Q_AskPrice(tcode) +itk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        A_SendOrder(Enum_Buy(), Enum_Entry(), qty, iprc,tcode) 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单买入开仓价==>",iprc,"买入数量==>",qty)
        BKS=1    
    elif SK and SKS==0 and (A_SellPosition(tcode) == 0):    
        iprc = max(Q_BidPrice(tcode) - itk*PriceTick(tcode), Q_LowLimit(tcode))  # 对盘超价                         
        A_SendOrder(Enum_Sell(), Enum_Entry(), qty, iprc,tcode)   
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单卖出开仓价==>",iprc,"卖出数量==>",qty)
        SKS=1    

def tim_trigger_Exit(BP,SP,otk,tcode,clots):#盘中实时平仓
    global BKS,SKS,BPS,SPS
    if BP and BPS==0 and A_SellPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_SellPosition(tcode))
        prc = min(Q_AskPrice(tcode) +otk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        if ExchangeName(tcode) not in ['SHFE','INE']:    
            retExit, ExitOrderId=A_SendOrder(Enum_Buy(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodaySellPosition(tcode)
            dlots=lots-tlots            
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),lots, prc,tcode) #今仓足够平仓,上期所能交所优先超价全部平今仓    
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Buy(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Buy(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单买入平仓价==>",prc,"买入平仓数量==>",_lots)
        BPS=1  
        if SKS==1:SKS=2      
    elif SP and SPS==0 and A_BuyPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_BuyPosition(tcode))
        prc = max(Q_BidPrice(tcode) - otk*PriceTick(tcode), Q_LowLimit(tcode))
        if ExchangeName(tcode) not in ['SHFE','INE']:
            retExit, ExitOrderId=A_SendOrder(Enum_Sell(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodayBuyPosition(tcode)
            dlots=lots-tlots
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),lots, prc,tcode)   #今仓足够平仓,上期所能交所优先超价全部平今仓  
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Sell(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Sell(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单卖出平仓价==>",prc,"卖出平仓数量==>",_lots)
        SPS=1    
        if BKS==1:BKS=2   
订阅数据长度=2000 
scode,tcode,k_btime,k_cycle,m_btime,m_cycle,l_btime,l_cycle,N,M1,M2="","",0,0,0,0,0,0,9,3,3
trade_sw,lots_o1,ovprice_tick,stopwin_tick,stoplost_tick,ops,CloseTime=0,0,0,0,0,0,0
def initialize(context): 
    global g_params,scode,tcode,k_btime,k_cycle,m_btime,m_cycle,l_btime,l_cycle,N,M1,M2
    global trade_sw,lots_o1,ovprice_tick,stopwin_tick,stoplost_tick,ops,CloseTime
    scode   = g_params['信号合约']    # 信号品种取参数
    tcode   = g_params['交易合约']    # 交易品种取参数
    k_btime = g_params['K线基础时间'] # k线基础时间取参数
    k_cycle = g_params['K线基础周期'] # k线基础周期取参数
    m_btime = g_params['中周期k线时间']
    m_cycle = g_params['中周期k线周期']
    l_btime = g_params['大周期k线时间']
    l_cycle = g_params['大周期k线周期']
    N  = g_params['N']        
    M1 = g_params['M1']        
    M2 = g_params['M2']       

    trade_sw=g_params['交易方向开关']    #大于0开多，小于0开空，等于0双向开仓
    lots_o1= g_params['下单量']          #  下单量
    ovprice_tick =g_params['下单超价跳数']    #下单超价跳数
    stopwin_tick =g_params['止盈跳数']
    stoplost_tick=g_params['止损跳数']
    ops = g_params['平仓后是否退出策略']
    CloseTime = g_params['收盘倒计时分钟数设置']
    AL=N*2+M1+M2   
    SetBarInterval(scode, k_btime, k_cycle,订阅数据长度,AL) #订阅信号合约    
    SetBarInterval(tcode, k_btime, k_cycle,订阅数据长度,AL) #订阅交易合约
    SetBarInterval(scode, m_btime, m_cycle,订阅数据长度,AL) #订阅中周期信号合约
    SetBarInterval(scode, l_btime, l_cycle,订阅数据长度,AL) #订阅中周期信号合约
    #SetBarInterval(tcode,'T', 1,1) #订阅交易合约
    SetActual()           #设置实盘运行
    SetOrderWay(1)        #设置K线走完后发单
    SetTriggerType(5)     #设置K线触发
# 策略触发事件每次触发时都会执行该函数
TDS,HCS,sBARS,mBARS,lBARS,buys,sells=0,deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3)
BKS,SKS,BPS,SPS,HH,LL,HS,LS,hh_stop,ll_stop,m_OVO,l_OVO,_MP,_BuyPosition,_SellPosition,_AVP,BT1,ST1=0,0,0,0,0,999999999,0,0,0,0,0,0,0,0,0,0,0,0
def handle_data(context):
    global TDS,BKS,SKS,BPS,SPS,HH,LL,HS,LS,hh_stop,ll_stop,m_OVO,l_OVO,_MP,_BuyPosition,_SellPosition,_AVP,BT1,ST1
    O=Open( scode, k_btime, k_cycle)     # 信号合约开盘价格
    H=High( scode, k_btime, k_cycle)     # 信号合约最高价格
    L=Low(  scode, k_btime, k_cycle)     # 信号合约最低价格
    C=Close(scode, k_btime, k_cycle)      # 信号合约收盘价格

    # mO=Open( scode, m_btime, m_cycle)   # 中周期k线开盘价
    mH=High( scode, m_btime, m_cycle)   # 中周期k线最高价
    mL=Low(  scode, m_btime, m_cycle)   # 中周期k线最低价
    mC=Close(scode, m_btime, m_cycle)   # 中周期k线收盘价

    # lO=Open( scode, l_btime, l_cycle)   # 大周期k线开盘价
    lH=High( scode, l_btime, l_cycle)   # 大周期k线最高价
    lL=Low(  scode, l_btime, l_cycle)   # 大周期k线最低价
    lC=Close(scode, l_btime, l_cycle)   # 大周期k线收盘价

    #OT=Open( tcode, k_btime, k_cycle)   # 交易合约开盘价格
    HT=High( tcode, k_btime, k_cycle)   # 交易合约最高价格
    LT=Low(  tcode, k_btime, k_cycle)   # 交易合约最低价格
    CT=Close(tcode, k_btime, k_cycle)   # 交易合约收盘价格
    MINPRICE=PriceTick(tcode)           # 交易合约最小变动价
    
    #if context.strategyStatus() == 'C':
    #    buyprice  = min(Q_AskPrice(tcode)+ovprice_tick*MINPRICE, Q_UpperLimit(tcode))  # 买超价
    #    sellprice = max(Q_BidPrice(tcode)-ovprice_tick*MINPRICE, Q_LowLimit(tcode))    # 卖超价
    #else:
    #    buyprice  = CT[-1]+ovprice_tick*MINPRICE  # 买超价
    #    sellprice = CT[-1]-ovprice_tick*MINPRICE  # 卖超价
    #KDKDKDKDKDKDKDKDKDKDKDKDKDKDKDKDKDKDKDKDKDKDKD//
    #指数计算指标
    if  len(lC)<N+3 or len(C)<N:
       return
    mHL = talib.MAX(mH,N)-talib.MIN(mL,N)
    mCL = mC-talib.MIN(mL,N)
    np.seterr(divide='ignore', invalid='ignore')
    sRSV = np.where(mHL>0,mCL/mHL*100,100)
    K  = SMA1(sRSV,M1,1)
    DD = SMA1(K,M2,1)
    PW = K-DD

    lHL = talib.MAX(lH,N)-talib.MIN(lL,N)
    lCL = lC-talib.MIN(lL,N)
    np.seterr(divide='ignore', invalid='ignore')
    lRSV = np.where(lHL>0,lCL/lHL*100,100)
    lK  = SMA1(lRSV,M1,1)
    lD = SMA1(lK,M2,1)
    lPW = lK-lD

    ODS=0
    s_CurrentBar=CurrentBar(scode, k_btime, k_cycle)
    sBARS.append(s_CurrentBar)
    if sBARS[0]>0 and sBARS[1]<sBARS[2]:
        if BKS>=1 and A_BuyPosition(tcode)==0: BKS=0
        if SKS>=1 and A_SellPosition(tcode)==0: SKS=0 
        BPS=0
        SPS=0     
        # LogInfo("sBARS=>",sBARS,"BKS=>",BKS,"SKS=>",SKS)       

    m_CurrentBar=CurrentBar(scode, m_btime, m_cycle)
    mBARS.append(m_CurrentBar)
    if mBARS[0]<mBARS[1]==mBARS[2]:
        ODS=1  
        m_OVO=O[-1]   
  
    l_CurrentBar=CurrentBar(scode, l_btime, l_cycle)   
    lBARS.append(l_CurrentBar)
    if lBARS[0]>0 and lBARS[1]<lBARS[2]:
        l_OVO=O[-1]


    #主连下单，计算止损止盈价格线
    MP=MarketPosition(tcode)   #持仓方向
    MC=CurrentContracts(tcode) #持仓手数
    AVP=AvgEntryPrice(tcode)   #EntryPrice(tcode) #持仓均价/第一笔建仓价
    HCS.append(context.strategyStatus())
    _VTS=VTS(Time())
    TradeEnbTime=TimeTo_Minutes(_VTS[3])-TimeTo_Minutes(_VTS[2])>CloseTime
    TradeOffTime=CloseTime>=TimeTo_Minutes(_VTS[3])-TimeTo_Minutes(_VTS[2])>0

    HTS=1 if context.strategyStatus()=="C" else 0   
    BK1 =TradeEnbTime and PW[-1]>0 and lPW[-1]>0 and C[-1-HTS]>H[-2-HTS]
    SK1 =TradeEnbTime and PW[-1]<0 and lPW[-1]<0 and C[-1-HTS]<L[-2-HTS]
    if context.strategyStatus()=="C" and  ExchangeStatus(ExchangeName(tcode)) in ('1','2','3'): #QuoteSvrState() == 1 and TradeSvrState()==1
        if  BKS==0 and trade_sw>=0 and BK1:
            tim_trigger(True,False,lots_o1,ovprice_tick,tcode)
            BKS=1
            ll_stop=LT[-1-HTS]-stoplost_tick*MINPRICE      
        if SKS==0 and trade_sw<=0 and SK1:
            tim_trigger(False,True,lots_o1,ovprice_tick,tcode)
            SKS=1
            hh_stop=HT[-1-HTS]+stoplost_tick*MINPRICE       
        buys.append(A_BuyPosition(tcode))   
        sells.append(A_SellPosition(tcode)) 
        if ops>0:
            if ll_stop>0 and buys[1]-buys[2]==lots_o1:
                BT1+=1
                if BT1>=ops and trade_sw>0:
                    LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"已下多单",BT1,"次",策略多单到达累计交易上限退出策略)     
            if hh_stop>0 and sells[1]-sells[2]==lots_o1:
                ST1+=1
                if ST1>=ops and trade_sw<0:   
                    LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"已下空单",ST1,"次",策略空单到达累计交易上限退出策略)
            if  BT1>=ops and ST1>=ops and trade_sw==0 :
                LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,策略多空单均到达累计交易上限退出策略)    

        if A_BuyPosition(tcode)>0 and ll_stop>0: 
            # if ll_stop==0:
            #     LogInfo(警告警告你有策略启动前的多单老仓未平掉退出策略请平掉老仓再启动策略)
            PlotPartLine("低价线",CurrentBar(),ll_stop,2,ll_stop,0x00ff00)
            if CT[-1]<ll_stop:
                BKS=1
                LogInfo(tcode,"多单止损",lots_o1,"手")
                tim_trigger_Exit(False,True,ovprice_tick,tcode,lots_o1)     
            elif CT[-1]>A_BuyAvgPrice(tcode)+stopwin_tick*MINPRICE:        
                BKS=1
                LogInfo(tcode,"多单止赢",lots_o1,"手")
                tim_trigger_Exit(False,True,ovprice_tick,tcode,lots_o1)  
            elif  TradeOffTime:
                BKS=1
                LogInfo(tcode,"收盘前平多仓",lots_o1,"手")
                tim_trigger_Exit(False,True,ovprice_tick,tcode,lots_o1)      
        if A_SellPosition(tcode)>0 and hh_stop>0: 
            # if hh_stop==0:
            #     LogInfo(警告警告你有策略启动前的空单老仓未平掉退出策略请平掉老仓再启动策略)
            PlotPartLine("高价线",CurrentBar(),hh_stop,2,hh_stop,0xff0000)
            if CT[-1]>hh_stop:
                SKS=1
                LogInfo(tcode,"空单止损",lots_o1,"手")
                tim_trigger_Exit(True,False,ovprice_tick,tcode,lots_o1)       
            elif CT[-1]<A_SellAvgPrice()-stopwin_tick*MINPRICE:        
                SKS=1
                LogInfo(tcode,"空单止赢",lots_o1,"手")
                tim_trigger_Exit(True,False,ovprice_tick,tcode,lots_o1)     
            elif  TradeOffTime:
                SKS=1
                LogInfo(tcode,"收盘前平空仓",lots_o1,"手")
                tim_trigger_Exit(True,False,ovprice_tick,tcode,lots_o1)   
    PlotBar('PW',  PW[-1]+50,50 , RGB_Red() if PW[-1] > 0 else RGB_Green(), False, True,0,"中周期KD指标")     
    PlotNumeric('K', K[-1], RGB_Red()  , False, False,0,"中周期KD指标")
    PlotNumeric('D' , DD[-1] , RGB_Green(), False, False,0,"中周期KD指标") 
    PlotNumeric('80' , 80 , RGB_Yellow(), False, False,0,"中周期KD指标") 
    PlotNumeric('20' , 20 , 0xffffff, False, False,0,"中周期KD指标") 

    PlotBar('lPW',  lPW[-1]+50,50 , RGB_Red() if lPW[-1] > 0 else RGB_Green(), False, True,0,"大周期KD指标")     
    PlotNumeric('lK', lK[-1], RGB_Red()  , False, False,0,"大周期KD指标")
    PlotNumeric('lD' , lD[-1] , RGB_Green(), False, False,0,"大周期KD指标") 
    PlotNumeric('l80' , 80 , RGB_Yellow(), False, False,0,"大周期KD指标") 
    PlotNumeric('l20' , 20 , 0xffffff, False, False,0,"大周期KD指标")


def floattime_sum(floatin1, floatin2, len_set=12):  # 高精度浮点时间求和（精确到毫秒）
    # 设置浮点数格式，保留len_set位小数
    lensave = f"%0.{len_set}f"
    
    # 格式化浮点数并提取各时间部分
    def extract_time_parts(floatin):
        strfloat = lensave % floatin
        return int(strfloat[2:4]), int(strfloat[4:6]), int(strfloat[6:8]), int(strfloat[8:11])
    
    h1, m1, s1, ms1 = extract_time_parts(floatin1)
    h2, m2, s2, ms2 = extract_time_parts(floatin2)
    
    # 计算总和并处理进位
    total_ms = ms1 + ms2
    ms_carry = total_ms // 1000
    new_ms = total_ms % 1000
    
    total_s = s1 + s2 + ms_carry
    s_carry = total_s // 60
    new_s = total_s % 60
    
    total_m = m1 + m2 + s_carry
    m_carry = total_m // 60
    new_m = total_m % 60
    
    new_h = h1 + h2 + m_carry
    new_h = min(new_h, 99)  # 限制小时数不超过99
    
    # 组合新的浮点时间字符串并转换回浮点数
    new_str_time = f"0.{new_h:02}{new_m:02}{new_s:02}{new_ms:03}"
    return float(new_str_time)

def TimeTo_Minutes(time_in):
    timestr='%0.6f'%time_in
    hsave=int(timestr[2:4])
    msave=int(timestr[4:6])
    tcout=hsave*60+msave
    return tcout

def SessionOpenTime(contractId=''):  # 获取交易时段开盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler:continue
        tlout.append(GetSessionStartTime(contractId, i))  # 获取每个交易时段的开盘时间并加入列表
    return tlout

def SessionCloseTime(contractId=''):  # 获取交易时段收盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler-1:continue
        tlout.append(GetSessionEndTime(contractId, i))  # 获取每个交易时段的收盘时间并加入列表
    return tlout

def VTS(time_in, contractId=''):  # 根据输入时间和合约ID计算交易时段
    RTS, CTS, TSession = [], [], []  # 初始化三个列表，用于存储修正后的时间、收盘时间和交易时段
    opentimet = SessionOpenTime(contractId)  # 获取所有交易时段的开盘时间
    Closetimet = SessionCloseTime(contractId)  # 获取所有交易时段的收盘时间
    
    for open_time, close_time in zip(opentimet, Closetimet):
        if time_in >= open_time:  # 判断输入时间是否在开盘时间之后
            RTS.append(time_in)  # 如果是，加入RTS列表
        else:
            RTS.append(floattime_sum(time_in, 0.24))  # 如果不是，修正时间后加入RTS列表
        
        if close_time >= open_time:  # 判断收盘时间是否在开盘时间之后
            CTS.append(close_time)  # 如果是，加入CTS列表
        else:
            CTS.append(floattime_sum(close_time, 0.24))  # 如果不是，修正时间后加入CTS列表
        
        if open_time <= RTS[-1] <= CTS[-1]:  # 判断修正后的时间是否在交易时段内
            TSession.append(len(RTS) - 1)  # 如果是，加入TSession列表

    if len(TSession) == 1:  # 如果只有一个交易时段
        idx = TSession[0]
        return idx, opentimet[idx], RTS[idx], CTS[idx]  # 返回交易时段和相关时间
    else:
        return -1, time_in, time_in, time_in  # 否则返回默认值
