import talib
import math
import copy
import numpy as np
import pandas as pd
from collections import deque
from xtquant.xttrader import XtQuantTrader
from xtquant import xtconstant

g_params['K线基础时间']  = 'D'   # k线基础时间
g_params['K线基础周期']  = 1     # k线周期
g_params['选择显示合约序号']  = 1 # 选择显示合约序号
g_params['FastLength']  = 22    # 快周期
g_params['MidLength']   = 60    # 中周期
g_params['SlowLength']  = 120   # 慢周期
g_params['基准市值']     = 100   # 基准市值(万)
g_params['止盈']  =  2  #市值大于基准市值的2%止盈
g_params['补仓']  = -2  #市值小于基准市值的2%补仓
g_params['疯牛乖离阈值']  =  100  #疯牛乖离阈值
g_params['疯熊乖离阈值']  = -100  #疯熊乖离阈值
g_params['订阅数据长度']  =  20000  #订阅数据长度


g_params['配置文件夹路径'] = "E:\stock_trade"
g_params['配置文件名'] = "配置文件与股票ETF代码.xlsx"
trade_order_filedir = g_params['配置文件夹路径']
trade_config_file   = trade_order_filedir+"\\"+g_params['配置文件名'] 
trade_config_DATA   =pd.read_excel(trade_config_file,sheet_name = 0)
symbol_Id =trade_config_DATA["股票ETF代码"].dropna()
资金权重=trade_config_DATA["资金权重"].dropna()
是否交易=trade_config_DATA["是否交易"].dropna()
初始总权益=trade_config_DATA["初始总权益(万)"].iloc[0]
浮盈减仓启控比例=trade_config_DATA["浮盈减仓启控比例(%)"].iloc[0]
浮亏加仓启控比例=trade_config_DATA["浮亏加仓启控比例(%)"].iloc[0]


class TriggerManager:
    """
    交易触发器管理类，负责处理订单开平仓操作
    """
    def __init__(self):
        # 无需内部状态标记，外部由SQL表状态控制
        pass
    
    def reset_states(self):
        """保留此方法做为兼容接口，但不执行任何操作"""
        pass
    
    # ==================== 历史开仓函数 ====================
    
    def his_trigger_long(self, qty, price, tcode):
        """
        历史多头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
        """
        # 移除状态检查，直接执行开仓
        Buy(qty, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "多单买入开仓价==>", price, "买入数量==>", qty)
        return True
    
    def his_trigger_short(self, qty, price, tcode):
        """
        历史空头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
        """
        # 移除状态检查，直接执行开仓
        SellShort(qty, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "空单卖出开仓价==>", price, "卖出数量==>", qty)
        return True
    
    def his_trigger(self, BK, SK, qty, price, tcode):
        """历史开仓兼容函数"""
        if BK:
            return self.his_trigger_long(qty, price, tcode)
        elif SK:
            return self.his_trigger_short(qty, price, tcode)
        return False
    
    # ==================== 历史平仓函数 ====================
    
    def his_trigger_exit_short(self, clots, price, tcode):
        """
        历史平仓空头持仓
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
        """
        # 只保留必要的持仓检查
        if SellPosition(tcode) <= 0 or clots <= 0:
            return False
            
        _lots = min(clots, SellPosition(tcode))
        BuyToCover(_lots, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "空单买入平仓价==>", price, "买入平仓数量==>", _lots)
        return True
    
    def his_trigger_exit_long(self, clots, price, tcode):
        """
        历史平仓多头持仓
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
        """
        # 只保留必要的持仓检查
        if BuyPosition(tcode) <= 0 or clots <= 0:
            return False
            
        _lots = min(clots, BuyPosition(tcode))
        Sell(_lots, price, tcode)
        LogInfo(Time(), "->合约==>", tcode, "多单卖出平仓价==>", price, "卖出平仓数量==>", _lots)
        return True
    
    def his_trigger_Exit(self, BP, SP, long_price, short_price, clots, tcode):
        """历史平仓兼容函数"""
        result = False
        if BP:
            result = self.his_trigger_exit_short(clots, short_price, tcode)
        if SP:
            result = self.his_trigger_exit_long(clots, long_price, tcode) or result
        return result
    
    # ==================== 实时开仓函数 ====================
    
    def tim_trigger_long(self, qty, price, tcode):
        """
        实时多头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
            
        返回:
            (状态, 订单ID)
        """
        upper_limit = Q_UpperLimit(tcode)
        if upper_limit <= 0:
            checked_price = price
        else:
            checked_price = min(price, upper_limit)
            if checked_price != price:
                LogInfo(f"警告: 多头开仓价格 {price} 超过涨停价 {upper_limit}，已自动调整")
        try:
            # 使用 xtquant API 下单：调用 xt_trader.order_stock 接口（请确保 xt_trader 和 acc 已初始化）
            order_id = xt_trader.order_stock(acc, tcode, xtconstant.STOCK_BUY, qty, xtconstant.FIX_PRICE, checked_price, 'ETF自动市值跟踪系统', '多头开仓')
            if order_id:
                LogInfo(f"多头开仓订单已发送，订单号: {order_id}")
                return True, order_id
            else:
                LogInfo("多头开仓订单发送失败")
                return False, None
        except Exception as e:
            LogInfo("实时多头下单异常:", str(e))
            return False, None
    
    def tim_trigger_short(self, qty, price, tcode):
        """
        实时空头开仓
        
        参数:
            qty: 开仓手数
            price: 开仓价格
            tcode: 合约代码
            
        返回:
            (状态, 订单ID)
        """
        lower_limit = Q_LowLimit(tcode)
        if lower_limit <= 0:
            checked_price = price
        else:
            checked_price = max(price, lower_limit)
            if checked_price != price:
                LogInfo(f"警告: 空头开仓价格 {price} 低于跌停价 {lower_limit}，已自动调整")
        try:
            # 使用 xtquant API 下单：调用 xt_trader.order_stock 接口（请确保 xt_trader 和 acc 已初始化）
            order_id = xt_trader.order_stock(acc, tcode, xtconstant.STOCK_SELL, qty, xtconstant.FIX_PRICE, checked_price, 'ETF自动市值跟踪系统', '空头开仓')
            if order_id:
                LogInfo(f"空头开仓订单已发送，订单号: {order_id}")
                return True, order_id
            else:
                LogInfo("空头开仓订单发送失败")
                return False, None
        except Exception as e:
            LogInfo("实时空头下单异常:", str(e))
            return False, None
    
    def tim_trigger(self, BK, SK, qty, price, tcode):
        """实时开仓兼容函数"""
        if BK:
            return self.tim_trigger_long(qty, price, tcode)
        elif SK:
            return self.tim_trigger_short(qty, price, tcode)
        return None, None
    
    # ==================== 实时平仓函数 ====================
    
    def tim_trigger_exit_short(self, clots, price, tcode):
        """
        实时平仓空头持仓，支持返回多个订单状态
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
            
        返回:
            list: 包含所有订单信息的列表，每项为 (状态, 订单ID, 平仓类型, 平仓手数)
        """
        orders_info = []  # 用于存储所有订单的状态和ID
        
        # 只检查持仓是否存在
        sell_position = A_SellPosition(tcode)
        if sell_position <= 0 or clots <= 0:
            LogInfo(f"合约 {tcode} 无空头持仓或平仓手数为0，跳过平仓")
            return orders_info
            
        _lots = min(clots, sell_position)
        LogInfo(f"准备平仓合约 {tcode} 的空头持仓，持仓量={sell_position}，平仓量={_lots}")
        
        # 价格检查逻辑保持不变
        upper_limit = Q_UpperLimit(tcode)
        if upper_limit <= 0:
            checked_price = price
        else:
            checked_price = min(price, upper_limit)
            if checked_price != price:
                LogInfo(f"警告: 平空头价格 {price} 超过涨停价 {upper_limit}，已自动调整")
        
        # 交易所特殊处理逻辑保持不变
        if ExchangeName(tcode) not in ['SHFE', 'INE']:
            # 普通交易所情况
            retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), _lots, checked_price, tcode)
            orders_info.append((retExit, ExitOrderId, "平仓", _lots))
            LogInfo(f"发送平空头单: {tcode}, 类型=平仓, 数量={_lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
        else:
            # 上期所和能源交易所特殊处理
            lots = _lots
            tlots = A_TodaySellPosition(tcode)
            dlots = lots - tlots
            
            if tlots >= lots:
                # 今仓足够平仓,仅平今仓
                retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_ExitToday(), lots, checked_price, tcode)
                orders_info.append((retExit, ExitOrderId, "平今", lots))
                LogInfo(f"发送平空头单(平今): {tcode}, 数量={lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
            elif tlots > 0:
                # 今仓不够，分别平今仓和昨仓
                # 先平今仓部分
                TretExit, TExitOrderId = A_SendOrder(Enum_Buy(), Enum_ExitToday(), tlots, checked_price, tcode)
                orders_info.append((TretExit, TExitOrderId, "平今", tlots))
                LogInfo(f"发送平空头单(平今部分): {tcode}, 数量={tlots}, 价格={checked_price}, 订单ID={TExitOrderId}, 状态={TretExit}")
                
                # 再平昨仓部分
                YretExit, YExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), int(dlots), checked_price, tcode)
                orders_info.append((YretExit, YExitOrderId, "平昨", int(dlots)))
                LogInfo(f"发送平空头单(平昨部分): {tcode}, 数量={int(dlots)}, 价格={checked_price}, 订单ID={YExitOrderId}, 状态={YretExit}")
            elif tlots == 0:
                # 仅平昨仓
                retExit, ExitOrderId = A_SendOrder(Enum_Buy(), Enum_Exit(), lots, checked_price, tcode)
                orders_info.append((retExit, ExitOrderId, "平昨", lots))
                LogInfo(f"发送平空头单(平昨): {tcode}, 数量={lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
        
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "空单买入平仓价==>", checked_price, "买入平仓数量==>", _lots)
        
        if any(order[1] for order in orders_info):
            LogInfo(f"空头平仓订单已发送成功")
        else:
            LogInfo(f"所有空头平仓订单发送失败")
        
        return orders_info
    
    def tim_trigger_exit_long(self, clots, price, tcode):
        """
        实时平仓多头持仓
        
        参数:
            clots: 平仓手数
            price: 平仓价格
            tcode: 合约代码
            
        返回:
            list: 订单信息列表
        """
        orders_info = []  # 用于存储所有订单的状态和ID
        
        # 只检查持仓是否存在
        buy_position = A_BuyPosition(tcode)
        if buy_position <= 0 or clots <= 0:
            LogInfo(f"合约 {tcode} 无多头持仓或平仓手数为0，跳过平仓")
            return orders_info
            
        _lots = min(clots, buy_position)
        LogInfo(f"准备平仓合约 {tcode} 的多头持仓，持仓量={buy_position}，平仓量={_lots}")
        
        # 价格检查逻辑保持不变
        lower_limit = Q_LowLimit(tcode)
        if lower_limit <= 0:
            checked_price = price
        else:
            checked_price = max(price, lower_limit)
            if checked_price != price:
                LogInfo(f"警告: 平多头价格 {price} 低于跌停价 {lower_limit}，已自动调整")
        
        # 交易所特殊处理逻辑保持不变
        if ExchangeName(tcode) not in ['SHFE', 'INE']:
            # 普通交易所情况
            retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), _lots, checked_price, tcode)
            orders_info.append((retExit, ExitOrderId, "平仓", _lots))
            LogInfo(f"发送平多头单: {tcode}, 类型=平仓, 数量={_lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
        else:
            # 上期所和能源交易所特殊处理
            lots = _lots
            tlots = A_TodayBuyPosition(tcode)
            dlots = lots - tlots
            
            if tlots >= lots:
                # 今仓足够平仓,仅平今仓
                retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_ExitToday(), lots, checked_price, tcode)
                orders_info.append((retExit, ExitOrderId, "平今", lots))
                LogInfo(f"发送平多头单(平今): {tcode}, 数量={lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
            elif tlots > 0:
                # 今仓不够，分别平今仓和昨仓
                # 先平今仓部分
                TretExit, TExitOrderId = A_SendOrder(Enum_Sell(), Enum_ExitToday(), tlots, checked_price, tcode)
                orders_info.append((TretExit, TExitOrderId, "平今", tlots))
                LogInfo(f"发送平多头单(平今部分): {tcode}, 数量={tlots}, 价格={checked_price}, 订单ID={TExitOrderId}, 状态={TretExit}")
                
                # 再平昨仓部分
                YretExit, YExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), int(dlots), checked_price, tcode)
                orders_info.append((YretExit, YExitOrderId, "平昨", int(dlots)))
                LogInfo(f"发送平多头单(平昨部分): {tcode}, 数量={int(dlots)}, 价格={checked_price}, 订单ID={YExitOrderId}, 状态={YretExit}")
            elif tlots == 0:
                # 仅平昨仓
                retExit, ExitOrderId = A_SendOrder(Enum_Sell(), Enum_Exit(), lots, checked_price, tcode)
                orders_info.append((retExit, ExitOrderId, "平昨", lots))
                LogInfo(f"发送平多头单(平昨): {tcode}, 数量={lots}, 价格={checked_price}, 订单ID={ExitOrderId}, 状态={retExit}")
        
        LogInfo(Q_UpdateTime(tcode), "->合约==>", tcode, "多单卖出平仓价==>", checked_price, "卖出平仓数量==>", _lots)
        
        if any(order[1] for order in orders_info):
            LogInfo(f"多头平仓订单已发送成功")
        else:
            LogInfo(f"所有多头平仓订单发送失败")
        
        return orders_info
    
    def tim_trigger_Exit(self, BP, SP, long_price, short_price, clots, tcode):
        """
        实时平仓兼容函数
        
        参数:
            BP: 是否平空仓标志
            SP: 是否平多仓标志
            long_price: 平多头价格 (卖出价)
            short_price: 平空头价格 (买入价)
            clots: 平仓手数
            tcode: 合约代码
            
        返回:
            list: 所有平仓订单的信息列表
        """
        all_orders = []
        
        if BP:
            short_orders = self.tim_trigger_exit_short(clots, short_price, tcode)
            all_orders.extend(short_orders)
            
        if SP:
            long_orders = self.tim_trigger_exit_long(clots, long_price, tcode)
            all_orders.extend(long_orders)
            
        return all_orders

symbol_d=[]
for i in range(len(symbol_Id)):
    symbol_d.append(copy.deepcopy(deque([0]*9,maxlen=9)))
UPSA,DWSA=[0]*len(symbol_Id),[0]*len(symbol_Id)     
BKStatus,SKStatus,BPStatus,SPStatus=[0]*len(symbol_Id),[0]*len(symbol_Id),[0]*len(symbol_Id),[0]*len(symbol_Id)
UPSQ=copy.deepcopy(symbol_d) 
DWSQ=copy.deepcopy(symbol_d) 
k_btime,k_cycle,SetDisplayNo,FastLength,MidLength,SlowLength,BaseMargin,Profit,AddMargin,BullishLimit,BearishLimit=0,0,0,0,0,0,0,0,0,0,0
FinancialWeighting=0

# 在全局变量部分添加
g_order_manager = None  # 智能拆单管理器实例
g_active_orders = {}    # 使用字典跟踪活跃订单，格式为 {symbol: task_id}

def initialize(context): 
    global g_params,k_btime,k_cycle,SetDisplayNo,FastLength,MidLength,SlowLength,BaseMargin,Profit,AddMargin,BullishLimit,BearishLimit,FinancialWeighting, g_order_manager
    k_btime = g_params['K线基础时间'] # k线基础时间取参数
    k_cycle = g_params['K线基础周期'] # k线基础周期取参数
    SetDisplayNo = g_params['选择显示合约序号']-1 # 选择显示合约序号
    FastLength = g_params['FastLength']   # 快周期
    MidLength = g_params['MidLength']     # 中周期
    SlowLength = g_params['SlowLength']   # 慢周期
    BaseMargin = g_params['基准市值']      # 基准市值
    Profit = g_params['止盈']      # 止盈
    AddMargin = g_params['补仓']   # 补仓
    BullishLimit = g_params['疯牛乖离阈值']   # 疯牛乖离阈值
    BearishLimit = g_params['疯熊乖离阈值']   # 疯熊乖离阈值 

    DaySubDataLength = g_params['订阅数据长度']  # 订阅日线数据长度
    SubDataLength = int(DaySubDataLength*8)  # 订阅数据长度
    AluSubDataLength = min(2000,SubDataLength)  # 计算数据长度    
    for i in range(len(symbol_Id)):
        tcode=stock_code_mapping(int(symbol_Id[i]))
        if 是否交易[i]=="是":
            LogInfo("订阅",symbol_Id[i],"的合约"+tcode,"权重",资金权重[i])
            FinancialWeighting+=资金权重[i]
            SetBarInterval(tcode, k_btime, k_cycle,SubDataLength,AluSubDataLength) #订阅交易合约
            SetBarInterval(tcode, 'D', 1 ,DaySubDataLength,AluSubDataLength) #订阅日线数据
    SetTriggerType(1)
    SetTriggerType(5)
    SetOrderWay(1)
    SetActual()
    
    # 初始化智能拆单管理器
    g_order_manager = SmartOrderManager(
        price_limit_percent=1.0,    # 价格偏离不超过1%
        time_limit_seconds=1800,    # 30分钟超时
        order_size_ratio=1/3,       # 每次下单量为挂单量的1/3
        max_retry=5                 # 最大重试5次
    )
    
    LogInfo("智能拆单管理器已初始化")

BKS,SKS,BPS,SPS=0,0,0,0
sBARS=deque([0,0],maxlen=3)
VM0,VM1=0,0
LRS,MRS,SRS=0,0,0
trigger_manager = TriggerManager()
def handle_data(context):
    global BKS,SKS,BPS,SPS,VM0,VM1, g_order_manager, g_active_orders
    HTS=1 if context.strategyStatus()=="C" else 0
    
    # 检查活跃订单状态，清理已完成的订单
    completed_symbols = []
    for symbol, task_id in g_active_orders.items():
        task_status = g_order_manager.get_task_status(task_id)
        if task_status and task_status["status"] not in ["ACTIVE", "CANCELLING"]:
            LogInfo(f"订单 {task_id} 已完成，状态: {task_status['status']}, 成交数量: {task_status['filled_quantity']}")
            completed_symbols.append(symbol)
    
    # 从活跃订单字典中移除已完成的订单
    for symbol in completed_symbols:
        g_active_orders.pop(symbol, None)
    
    # 遍历股票池
    for i in range(len(symbol_Id)):
        if 是否交易[i]=="否":
            continue
        tcode=stock_code_mapping(int(symbol_Id[i]))
        O=Open(tcode, k_btime, k_cycle)
        C = Close(tcode, k_btime, k_cycle)
        CD = Close(tcode, 'D', 1)
        if len(CD) < SlowLength:
            return
        # LogInfo("当前策略状态：",context.strategyStatus(),'合约',tcode,'涨停价',Q_UpperLimit(tcode),'跌停价',Q_LowLimit(tcode))

        financial_weighting=资金权重[i]/FinancialWeighting
        MarginEquity= Margin(tcode)/(初始总权益*financial_weighting)

        LOTS=math.floor(MarginEquity*financial_weighting/(C[-1]*ContractUnit(tcode)*100+TradeCost(tcode)))*100
        MP=MarketPosition(tcode)
        BKVOL=BuyPosition(tcode)
        SKVOL=SellPosition(tcode)
        A_BKVOL=A_BuyPosition(tcode)
        A_SKVOL=A_SellPosition(tcode)


        FastLine = talib.MA(CD, FastLength) #快线周期均值
        MidLine = talib.MA(CD, MidLength)   #中线周期均值
        SlowLine = talib.MA(CD, SlowLength) #慢线周期均值
        SRS=(C[-1-HTS]/FastLine[-1-HTS]-1)*1000
        MRS=(C[-1-HTS]/MidLine[-1-HTS]-1)*1000
        LRS=(C[-1-HTS]/SlowLine[-1-HTS]-1)*1000
        RSRS=(C[-2-HTS]/FastLine[-2-HTS]-1)*1000
        RMRS=(C[-2-HTS]/MidLine[-2-HTS]-1)*1000
        RLRS=(C[-2-HTS]/SlowLine[-2-HTS]-1)*1000
        if SetDisplayNo==i:
            PlotNumeric("FastLine", FastLine[-1-HTS], 0xFFFFFF)
            PlotNumeric("MidLine", MidLine[-1-HTS], 0x00AAAA)
            PlotNumeric("SlowLine", SlowLine[-1-HTS], 0xFF0000)
            PlotNumeric("短乖离", SRS, 0xFF0000,False, False,0,"乖离指标")
            PlotNumeric("中乖离", MRS, 0x00FF00,False, False,0,"乖离指标")
            PlotNumeric("长乖离", LRS, 0xFFFF00,False, False,0,"乖离指标")
            PlotNumeric("疯牛线", BullishLimit, 0xFF0000,False, False,0,"乖离指标")
            PlotNumeric("疯熊线", BearishLimit, 0x00FF00,False, False,0,"乖离指标")
            PlotNumeric("零轴", 0, 0xFFFFFF,False, False,0,"乖离指标")

        if UPSA[i]<=0 and RSRS<=0 and SRS>0:
            UPSA[i]=1
        if UPSA[i]==1 :
            if SRS<=0:
                UPSA[i]=0
            elif SRS> BullishLimit:
                UPSA[i]=2
        if UPSA[i]==2 and RSRS>=BullishLimit and SRS<BullishLimit:
            UPSA[i]=-1

        if DWSA[i]>=0 and RSRS>=0 and SRS<0:
            DWSA[i]=-1
        if DWSA[i]==-1:
            if SRS>=0:
                DWSA[i]=0
            elif SRS<BearishLimit:
                DWSA[i]=-2
        if DWSA[i]==-2 and RSRS<=BearishLimit and SRS>BearishLimit:
            DWSA[i]=1
        UPSQ[i].append(UPSA[i])
        DWSQ[i].append(DWSA[i])

        if SetDisplayNo==i:
            PlotNumeric("UPSA", UPSA[i], 0xFF0000,False, False,0,"乖离指标2")
            PlotNumeric("DWSA", DWSA[i], 0x00FF00,False, False,0,"乖离指标2")
            PlotNumeric("零轴", 0, 0xFFFFFF,False, False,0,"乖离指标2")
            PlotNumeric("账户市值", CurrentEquity(), 0xFFFF00,True, True,0)

        # 执行下单操作
        BKS0=RLRS<=BearishLimit and LRS>BearishLimit and SRS<0
        SKS0=RLRS>=BullishLimit and LRS<BullishLimit and SRS>0
        BKS1=BullishLimit>LRS>0 and SRS<0 and SRS>MRS
        SKS1=BearishLimit<LRS<0 and SRS>0 and SRS<MRS
        BKS2=BullishLimit>LRS>0 and SRS>0 and SRS>MRS and SRS<BullishLimit/2
        SKS2=BearishLimit<LRS<0 and SRS<0 and SRS<MRS and SRS>BearishLimit/2

        SPS1=UPSA[i]==-1
        BPS1=DWSA[i]== 1

        BK1=BKS1 or BKS2
        SK1=SKS1 or SKS2
        BP1=BPS1 or BKS2
        SP1=SPS1 or SKS2
        if HTS==0:
            # if BK1 and MP==0:
            #     trigger_manager.his_trigger_long(LOTS,C[-1],tcode)
            # if SP1:
            #     trigger_manager.his_trigger_exit_long(LOTS,C[-1],tcode)
            if BKS0 and MP==0:
                BKStatus[i]=1
                trigger_manager.his_trigger_long(LOTS,C[-1],tcode)
            if RSRS>=0 and SRS<0 and BKStatus[i]==1 and MP>0:
                BKStatus[i]=0
                trigger_manager.his_trigger_exit_long(BKVOL,C[-1],tcode)
            if BKStatus[i]==1 and (C[-1]/O[-1]-1)*100>Profit:
                BKStatus[i]=2
            if BKStatus[i]==2 and C[-1]<O[-1]:
                BKStatus[i]=0
                trigger_manager.his_trigger_exit_long(BKVOL,C[-1],tcode)
            # if SKS0 and MP==0:
            #     SKStatus[i]=1
            #     trigger_manager.his_trigger_short(LOTS,C[-1],tcode)
            # if RSRS<=0 and SRS>0 and SKStatus[i]==1 and MP<0:
            #     SKStatus[i]=0
            #     trigger_manager.his_trigger_exit_short(LOTS,C[-1],tcode)
 

        #     if VM0==0 and BKS1:#多头建仓
        #         VM0=BaseMargin*10000
        #         Buy(LOTS,C[-1])
        #         ELOTS=BuyPosition()-LOTS; 

        #     elif SKS1:
        #         SellShort(LOTS, C[-1])
        #     return
        # #进入实时行情，清理掉策略仓
        # if MarketPosition() > 0 and A_BuyPosition(tcode)==0:  
        #     Sell(N, Open()[-1])
        #     LogInfo("进入实时行情，清理掉策略多仓")
        #     return
        # elif MarketPosition() < 0 and A_SellPosition(tcode)==0:
        #     BuyToCover(N, Open()[-1])
        #     LogInfo("进入实时行情，清理掉策略空仓")
        #     return


        # LOTS=math.floor(BaseMargin*10000/(C[-1]*ContractUnit(tcode)*100+FEE))*100    
    
    # #//------------------------实时处理------------------------//

    # # if ExchangeStatus(ExchangeName()) != '3':
    # #     return
    # BKS2 = AvgValue1[-3] <= AvgValue2[-3] and AvgValue1[-2] > AvgValue2[-2]
    # SKS2 = AvgValue1[-3] >= AvgValue2[-3] and AvgValue1[-2] < AvgValue2[-2]   
    # #//------------------------变量赋值------------------------//
    # s_CurrentBar=CurrentBar(tcode, k_btime, k_cycle)
    # sBARS.append(s_CurrentBar)
    # if sBARS[0]>0 and sBARS[1]<sBARS[2]:
    #     if BKS>=1 and A_BuyPosition(tcode)==0: 
    #         BKS=0
    #         SPS=0  
    #     if SKS>=1 and A_SellPosition(tcode)==0: 
    #         SKS=0 
    #         BPS=0
    # if trade_sw>=0:
    #     tim_trigger(BKS2,False,N,20,tcode)
    # if trade_sw<=0:
    #     tim_trigger(False,SKS2,N,20,tcode)
    # if BPS==0:    
    #     tim_trigger_Exit(BKS2,False,20,tcode,N)
    # if SPS==0:    
    #     tim_trigger_Exit(False,SKS2,20,tcode,N)

from typing import Union
def stock_code_mapping(code: Union[int, str]) -> str:
    # 整数处理分支（进一步优化）
    if isinstance(code, int):
        if not (1 <= code <= 999999):
            raise ValueError("输入必须为6位以下正整数")
        
        # 格式化代码字符串（只做一次）
        code_str = f"{code:06d}"
        
        # 快速分类 - 使用整数除法和模运算
        first_digit = code // 100000
        first_two = code // 10000
        first_three = code // 1000
        
        # 沪市股票 (6开头)
        if first_digit == 6:
            if first_three == 688:
                return f"SSE|T|KSHARES|{code_str}"  # 科创板
            elif first_three in {600, 601, 603, 605}:
                return f"SSE|T|ASHARES|{code_str}"      # 沪主板
            
        # 深主板 (0,1,3开头或4-9开头)
        if first_three  in {0, 1, 3}:
            return f"SZSE|T|ASHARES|{code_str}"    # 深主板            
        # 中小板 (002开头)    
        if first_three == 2:
            return f"SZSE|T|SMESHARES|{code_str}"  # 中小板
        # 创业板 (30开头)
        if first_two == 30:
            return f"SZSE|T|CHSHARES|{code_str}"   # 创业板   
        # 深B股 (200开头)
        if first_three == 200:
            return f"SZSE|T|BSHARES|{code_str}"    # 深B股
        # ETF (159开头)
        if first_three == 159:
            return f"SZSE|T|FUNDS|{code_str}"      # ETF
             
        # 基金 (5开头)
        if first_digit == 5:
            return f"SSE|T|FUNDS|{code_str}"       # 沪基金
            
        # REITs (16-18开头)
        if first_two in {16, 18}:
            return f"SZSE|T|FUNDS|{code_str}"      # REITs
            
        # 沪B股 (9开头)
        if first_digit == 9:
            return f"SSE|T|BSHARES|{code_str}"     # 沪B股
            
        # 北交所和新三板
        if first_three in {830, 831, 832, 833, 834, 835, 836, 837, 838, 839, 
                          870, 871, 872, 873, 874, 875, 876, 877, 878, 879, 
                          920, 921, 922, 923, 924, 925, 926, 927, 928, 929}:
            return f"BJSE|T|STOCK|{code_str}"
        if first_three in {400, 430, 830}:
            return f"NEEQ|T|OTC|{code_str}"
            
        return f"UNKNOWN|{code_str}"
    
    # 字符串处理分支（原逻辑）
    elif isinstance(code, str):
        if not (code.isdigit() and len(code) == 6):
            raise ValueError("输入必须为6位数字字符串")

        if code.startswith('688'):
            return f"SSE|T|KSHARES|{code}"
        elif code.startswith(('600','601','603','605')):
            return f"SSE|T|ASHARES|{code}"
        elif code.startswith('5'):
            return f"SSE|T|FUNDS|{code}"
        elif code.startswith('900'):
            return f"SSE|T|BSHARES|{code}"
        elif code.startswith('159'):
            return f"SZSE|T|FUNDS|{code}"
        elif code.startswith(('000','001','003')):
            return f"SZSE|T|ASHARES|{code}"
        elif code.startswith('002'):
            return f"SZSE|T|SMESHARES|{code}"
        elif code.startswith('30'):
            return f"SZSE|T|CHSHARES|{code}"
        elif code.startswith('200'):
            return f"SZSE|T|BSHARES|{code}"
        elif code.startswith(('16','18')):
            return f"SZSE|T|FUNDS|{code}"
        elif code.startswith(('83','87','920')):
            return f"BJSE|T|STOCK|{code}"
        elif code.startswith(('400','430','830')):
            return f"NEEQ|T|OTC|{code}"
        else:
            return f"UNKNOWN|{code}"
    
    else:
        raise TypeError("输入类型必须为int或str")
    
def stock_index_code_mapping(code: Union[int, str]) -> str:
    if isinstance(code, int):
        code_str = f"{code:06d}"
        prefix_three = code // 1000
        if prefix_three == 399:
            return f"SZSE|T|INDEX|{code_str}"
        if prefix_three == 0:
            return f"SSE|T|INDEX|{code_str}"
    elif isinstance(code, str):
        if code.startswith('399'):
            return f"SZSE|T|INDEX|{code}"
        if code.startswith('000'):
            return f"SSE|T|INDEX|{code}"

def normalize_stock_code(code):
    """
    将各种格式的股票代码转换为标准的QMT API格式
    沪市：数字代码.SH（例如：600000.SH）
    深市：数字代码.SZ（例如：000001.SZ）
    北交所：数字代码.BJ（例如：430047.BJ）
    
    参数:
        code: 字符串型股票代码，可以是多种格式（sh600000, SH600000, 600000, 600000.SH, 430047.BJ等）
    
    返回:
        标准化后的股票代码字符串
    """
    # 去除所有空格并转换为大写
    code = str(code).strip().upper()
    
    # 去除任何前缀和后缀，只保留数字部分
    numeric_part = ''.join(filter(str.isdigit, code))
    
    # 判断市场类型
    if any(prefix in code for prefix in ['SH', 'SHSE', 'SSE', 'SH.', '.SH']):
        return f"{numeric_part}.SH"
    elif any(prefix in code for prefix in ['SZ', 'SZSE', 'SZ.', '.SZ']):
        return f"{numeric_part}.SZ"
    elif any(prefix in code for prefix in ['BJ', 'BSE', 'BJSE', 'BJ.', '.BJ']):
        return f"{numeric_part}.BJ"
    else:
        # 根据股票代码规则判断市场类型
        # 沪市：以6开头（主板）、5开头（基金）、7开头（衍生品）
        # 深市：以0开头（主板）、1开头（SME）、2开头（中小板）、3开头（创业板）
        # 北交所：以4、8开头，68开头或82、83、87、88开头的股票代码
        if numeric_part.startswith('6'):
            return f"{numeric_part}.SH"
        elif numeric_part.startswith(('0', '1', '2', '3')):
            return f"{numeric_part}.SZ"
        elif numeric_part.startswith('4') or numeric_part.startswith('8'):
            # 北交所代码通常以4开头（如43开头的新三板精选层挂牌公司）
            # 或8开头（部分北交所特定代码）
            return f"{numeric_part}.BJ"
        elif numeric_part.startswith('68') or numeric_part.startswith(('82', '83', '87', '88')):
            # 特定的北交所其他代码规则
            return f"{numeric_part}.BJ"
        elif numeric_part.startswith('5') or numeric_part.startswith('7'):
            # 上交所基金和衍生品
            return f"{numeric_part}.SH"
        else:
            # 无法确定市场类型，保持原样返回
            return code


class SmartOrderManager:
    """
    智能拆单交易管理类，支持买入和卖出的自动拆单执行。
    根据市场挂单量动态调整每笔订单大小，异步跟踪订单状态直到全部完成。
    
    特点:
    - 自动读取盘口挂单量，按比例拆单
    - 分批执行，成交后自动发送后续订单
    - 支持价格偏离和时间窗口限制
    - 完整的订单生命周期跟踪
    """
    
    def __init__(self, price_limit_percent=0, time_limit_seconds=0, order_size_ratio=1/3, 
                 max_retry=3, price_step_percent=0.02, status_check_interval=1):
        """
        初始化智能拆单管理器
        
        参数:
            price_limit_percent: 价格偏离限制百分比，0表示不限制
            time_limit_seconds: 订单超时时间(秒)，0表示不限制
            order_size_ratio: 相对于盘口挂单量的下单比例，默认1/3
            max_retry: 单笔订单最大重试次数
            price_step_percent: 价格调整步长百分比
            status_check_interval: 订单状态检查间隔(秒)
        """
        self.price_limit_percent = price_limit_percent
        self.time_limit_seconds = time_limit_seconds
        self.order_size_ratio = order_size_ratio
        self.max_retry = max_retry
        self.price_step_percent = price_step_percent
        self.status_check_interval = status_check_interval
        
        # 订单跟踪相关变量
        self.active_orders = {}      # 活跃订单字典
        self.completed_orders = {}   # 已完成订单字典
        self.failed_orders = {}      # 失败订单字典
        self.order_tasks = {}        # 订单任务跟踪
        
        # 订单状态常量
        self.ORDER_SUBMITTED = "已提交"
        self.ORDER_ACCEPTED = "已接受"
        self.ORDER_FILLED = "已成交"
        self.ORDER_PARTIALLY_FILLED = "部分成交"
        self.ORDER_CANCELLED = "已撤销"
        self.ORDER_REJECTED = "已拒绝"
        self.ORDER_EXPIRED = "已过期"
        
        # 导入必要的库
        import threading
        import time
        import datetime
        self.threading = threading
        self.time = time
        self.datetime = datetime
        
        # 创建线程锁
        self.lock = threading.Lock()
        
        # 启动订单状态监控线程
        self.stop_monitor = False
        self.monitor_thread = threading.Thread(target=self._monitor_orders)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        LogInfo("智能拆单交易管理器已初始化")
    
    def place_buy_order(self, symbol, total_quantity, limit_price=None, 
                        min_quantity=100, callback=None, **kwargs):
        """
        执行买入订单，自动拆分为多笔小订单
        
        参数:
            symbol: 股票代码
            total_quantity: 总买入数量
            limit_price: 限价，None表示市价
            min_quantity: 最小下单数量，低于此数量将直接一次性下单
            callback: 订单完成后的回调函数
            **kwargs: 其他参数传递给下单API
            
        返回:
            订单任务ID
        """
        # 标准化股票代码
        symbol = normalize_stock_code(symbol)
        
        # 创建订单任务ID
        task_id = f"BUY_{symbol}_{self.datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')}"
        
        # 获取当前市场价格和涨停价
        current_price = limit_price if limit_price else Q_Last(symbol)
        upper_limit = Q_UpperLimit(symbol)
        
        # 如果指定了限价并超过涨停，提前调整限价
        if limit_price and upper_limit > 0 and limit_price > upper_limit:
            LogInfo(f"指定买入价格 {limit_price} 超过涨停价 {upper_limit}，自动调整为涨停价")
            limit_price = upper_limit
        
        # 计算价格限制
        max_price = current_price * (1 + self.price_limit_percent/100) if self.price_limit_percent else float('inf')
        
        # 计算时间限制
        end_time = self.time.time() + self.time_limit_seconds if self.time_limit_seconds else float('inf')
        
        # 创建任务数据结构
        task_data = {
            "task_id": task_id,
            "symbol": symbol,
            "action": "BUY",
            "total_quantity": total_quantity,
            "remaining_quantity": total_quantity,
            "filled_quantity": 0,
            "start_price": current_price,
            "limit_price": limit_price,
            "max_price": max_price,
            "end_time": end_time,
            "min_quantity": min_quantity,
            "status": "ACTIVE",
            "start_time": self.time.time(),
            "orders": [],
            "callback": callback,
            "kwargs": kwargs
        }
        
        # 添加到任务列表
        with self.lock:
            self.order_tasks[task_id] = task_data
        
        # 启动执行线程
        execution_thread = self.threading.Thread(
            target=self._execute_buy_task,
            args=(task_id,)
        )
        execution_thread.daemon = True
        execution_thread.start()
        
        LogInfo(f"已创建买入任务 {task_id}，总数量: {total_quantity}，初始价格: {current_price}")
        return task_id
    
    def place_sell_order(self, symbol, total_quantity, limit_price=None, 
                         min_quantity=100, callback=None, **kwargs):
        """
        执行卖出订单，自动拆分为多笔小订单
        
        参数:
            symbol: 股票代码
            total_quantity: 总卖出数量
            limit_price: 限价，None表示以市价卖出
            min_quantity: 最小下单数量，低于此数量将直接一次性下单
            callback: 订单完成后的回调函数
            **kwargs: 其他参数传递给下单API
            
        返回:
            订单任务ID
        """
        # 标准化股票代码
        symbol = normalize_stock_code(symbol)
        
        # 创建订单任务ID
        task_id = f"SELL_{symbol}_{self.datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')}"
        
        # 获取当前市场价格和跌停价
        current_price = limit_price if limit_price else Q_Last(symbol)
        lower_limit = Q_LowLimit(symbol)
        
        # 如果指定了限价并低于跌停，提前调整限价
        if limit_price and lower_limit > 0 and limit_price < lower_limit:
            LogInfo(f"指定卖出价格 {limit_price} 低于跌停价 {lower_limit}，自动调整为跌停价")
            limit_price = lower_limit
        
        # 计算价格限制
        min_price = current_price * (1 - self.price_limit_percent/100) if self.price_limit_percent else float('-inf')
        
        # 计算时间限制
        end_time = self.time.time() + self.time_limit_seconds if self.time_limit_seconds else float('inf')
        
        # 创建任务数据结构
        task_data = {
            "task_id": task_id,
            "symbol": symbol,
            "action": "SELL",
            "total_quantity": total_quantity,
            "remaining_quantity": total_quantity,
            "filled_quantity": 0,
            "start_price": current_price,
            "limit_price": limit_price,
            "min_price": min_price,
            "end_time": end_time,
            "min_quantity": min_quantity,
            "status": "ACTIVE",
            "start_time": self.time.time(),
            "orders": [],
            "callback": callback,
            "kwargs": kwargs
        }
        
        # 添加到任务列表
        with self.lock:
            self.order_tasks[task_id] = task_data
        
        # 启动执行线程
        execution_thread = self.threading.Thread(
            target=self._execute_sell_task,
            args=(task_id,)
        )
        execution_thread.daemon = True
        execution_thread.start()
        
        LogInfo(f"已创建卖出任务 {task_id}，总数量: {total_quantity}，初始价格: {current_price}")
        return task_id
    
    def _execute_buy_task(self, task_id):
        """执行买入任务的内部方法"""
        task = self.order_tasks.get(task_id)
        if not task:
            LogInfo(f"找不到任务 {task_id}")
            return
        
        symbol = task["symbol"]
        retry_count = 0
        
        while (task["remaining_quantity"] > 0 and 
               task["status"] == "ACTIVE" and 
               self.time.time() < task["end_time"] and
               retry_count < self.max_retry):
            
            # 检查价格是否超出限制
            current_price = Q_Last(symbol)
            if current_price > task["max_price"]:
                LogInfo(f"任务 {task_id} 价格 {current_price} 超出限制 {task['max_price']}，停止执行")
                with self.lock:
                    task["status"] = "PRICE_LIMIT_EXCEEDED"
                break
            
            # 获取涨停价格
            upper_limit = Q_UpperLimit(symbol)
            if upper_limit <= 0:
                LogInfo(f"无法获取 {symbol} 的涨停价，使用当前价格")
                upper_limit = None
            
            # 获取卖一档挂单量
            ask1_price, ask1_volume = self._get_ask1_info(symbol)
            if not ask1_volume or ask1_volume <= 0:
                LogInfo(f"无法获取 {symbol} 的卖一档信息，等待重试")
                self.time.sleep(self.status_check_interval)
                retry_count += 1
                continue
            
            # 计算本次下单量
            batch_size = min(
                int(ask1_volume * self.order_size_ratio),  # 卖一档挂单量的比例
                task["remaining_quantity"]                 # 剩余需要买入的数量
            )
            
            # 确保批量符合最小交易单位(通常是100股)
            batch_size = (batch_size // 100) * 100
            if batch_size < task["min_quantity"]:
                batch_size = min(task["min_quantity"], task["remaining_quantity"])
            
            # 如果剩余很少，一次性买入
            if task["remaining_quantity"] < task["min_quantity"]:
                batch_size = task["remaining_quantity"]
            
            # 如果批量合法，执行买入
            if batch_size > 0:
                # 计算下单价格（限价或卖一价），同时确保不超过涨停价
                if task["limit_price"]:
                    order_price = task["limit_price"]
                else:
                    order_price = ask1_price
                    
                # 检查是否超过涨停价
                if upper_limit and order_price > upper_limit:
                    LogInfo(f"买入价格 {order_price} 超过涨停价 {upper_limit}，已调整为涨停价")
                    order_price = upper_limit
                
                # 调用API买入
                try:
                    LogInfo(f"任务 {task_id} 准备买入 {batch_size} 股 {symbol}，价格 {order_price}")
                    # 使用QMT的API执行买入
                    order_id = self._place_order(
                        symbol=symbol,
                        direction="BUY",
                        quantity=batch_size,
                        price=order_price,
                        **task["kwargs"]
                    )
                    
                    if order_id:
                        LogInfo(f"任务 {task_id} 发送买入订单 {order_id}，数量 {batch_size}，价格 {order_price}")
                        with self.lock:
                            task["orders"].append({
                                "order_id": order_id,
                                "quantity": batch_size,
                                "price": order_price,
                                "status": "SUBMITTED",
                                "filled_quantity": 0,
                                "submit_time": self.time.time()
                            })
                            self.active_orders[order_id] = {
                                "task_id": task_id,
                                "symbol": symbol,
                                "action": "BUY",
                                "quantity": batch_size,
                                "price": order_price,
                                "status": "SUBMITTED"
                            }
                        
                        # 等待订单状态更新
                        self.time.sleep(self.status_check_interval * 2)
                    else:
                        LogInfo(f"任务 {task_id} 买入订单发送失败")
                        retry_count += 1
                except Exception as e:
                    LogInfo(f"任务 {task_id} 买入异常: {str(e)}")
                    retry_count += 1
            
            # 检查任务状态和剩余数量
            with self.lock:
                task = self.order_tasks.get(task_id)
                if not task or task["status"] != "ACTIVE" or task["remaining_quantity"] <= 0:
                    break
            
            self.time.sleep(self.status_check_interval)
        
        # 最终检查任务状态
        with self.lock:
            task = self.order_tasks.get(task_id)
            if task and task["status"] == "ACTIVE":
                if task["remaining_quantity"] <= 0:
                    task["status"] = "COMPLETED"
                    LogInfo(f"任务 {task_id} 已完成，总成交: {task['filled_quantity']}")
                elif self.time.time() >= task["end_time"]:
                    task["status"] = "TIMEOUT"
                    LogInfo(f"任务 {task_id} 超时，剩余: {task['remaining_quantity']}")
                else:
                    task["status"] = "FAILED"
                    LogInfo(f"任务 {task_id} 失败，剩余: {task['remaining_quantity']}")
                
                # 执行回调
                if task["callback"]:
                    try:
                        task["callback"](task_id, task)
                    except Exception as e:
                        LogInfo(f"任务 {task_id} 回调异常: {str(e)}")
    
    def _execute_sell_task(self, task_id):
        """执行卖出任务的内部方法"""
        task = self.order_tasks.get(task_id)
        if not task:
            LogInfo(f"找不到任务 {task_id}")
            return
        
        symbol = task["symbol"]
        retry_count = 0
        
        while (task["remaining_quantity"] > 0 and 
               task["status"] == "ACTIVE" and 
               self.time.time() < task["end_time"] and
               retry_count < self.max_retry):
            
            # 检查价格是否超出限制
            current_price = Q_Last(symbol)
            if current_price < task["min_price"]:
                LogInfo(f"任务 {task_id} 价格 {current_price} 低于限制 {task['min_price']}，停止执行")
                with self.lock:
                    task["status"] = "PRICE_LIMIT_EXCEEDED"
                break
            
            # 获取跌停价格
            lower_limit = Q_LowLimit(symbol)
            if lower_limit <= 0:
                LogInfo(f"无法获取 {symbol} 的跌停价，使用当前价格")
                lower_limit = None
            
            # 获取买一档挂单量
            bid1_price, bid1_volume = self._get_bid1_info(symbol)
            if not bid1_volume or bid1_volume <= 0:
                LogInfo(f"无法获取 {symbol} 的买一档信息，等待重试")
                self.time.sleep(self.status_check_interval)
                retry_count += 1
                continue
            
            # 计算本次下单量
            batch_size = min(
                int(bid1_volume * self.order_size_ratio),  # 买一档挂单量的比例
                task["remaining_quantity"]                 # 剩余需要卖出的数量
            )
            
            # 确保批量符合最小交易单位(通常是100股)
            batch_size = (batch_size // 100) * 100
            if batch_size < task["min_quantity"]:
                batch_size = min(task["min_quantity"], task["remaining_quantity"])
            
            # 如果剩余很少，一次性卖出
            if task["remaining_quantity"] < task["min_quantity"]:
                batch_size = task["remaining_quantity"]
            
            # 如果批量合法，执行卖出
            if batch_size > 0:
                # 计算下单价格（限价或买一价），同时确保不低于跌停价
                if task["limit_price"]:
                    order_price = task["limit_price"]
                else:
                    order_price = bid1_price
                    
                # 检查是否低于跌停价
                if lower_limit and order_price < lower_limit:
                    LogInfo(f"卖出价格 {order_price} 低于跌停价 {lower_limit}，已调整为跌停价")
                    order_price = lower_limit
                
                # 调用API卖出
                try:
                    LogInfo(f"任务 {task_id} 准备卖出 {batch_size} 股 {symbol}，价格 {order_price}")
                    # 使用QMT的API执行卖出
                    order_id = self._place_order(
                        symbol=symbol,
                        direction="SELL",
                        quantity=batch_size,
                        price=order_price,
                        **task["kwargs"]
                    )
                    
                    if order_id:
                        LogInfo(f"任务 {task_id} 发送卖出订单 {order_id}，数量 {batch_size}，价格 {order_price}")
                        with self.lock:
                            task["orders"].append({
                                "order_id": order_id,
                                "quantity": batch_size,
                                "price": order_price,
                                "status": "SUBMITTED",
                                "filled_quantity": 0,
                                "submit_time": self.time.time()
                            })
                            self.active_orders[order_id] = {
                                "task_id": task_id,
                                "symbol": symbol,
                                "action": "SELL",
                                "quantity": batch_size,
                                "price": order_price,
                                "status": "SUBMITTED"
                            }
                        
                        # 等待订单状态更新
                        self.time.sleep(self.status_check_interval * 2)
                    else:
                        LogInfo(f"任务 {task_id} 卖出订单发送失败")
                        retry_count += 1
                except Exception as e:
                    LogInfo(f"任务 {task_id} 卖出异常: {str(e)}")
                    retry_count += 1
            
            # 检查任务状态和剩余数量
            with self.lock:
                task = self.order_tasks.get(task_id)
                if not task or task["status"] != "ACTIVE" or task["remaining_quantity"] <= 0:
                    break
            
            self.time.sleep(self.status_check_interval)
        
        # 最终检查任务状态
        with self.lock:
            task = self.order_tasks.get(task_id)
            if task and task["status"] == "ACTIVE":
                if task["remaining_quantity"] <= 0:
                    task["status"] = "COMPLETED"
                    LogInfo(f"任务 {task_id} 已完成，总成交: {task['filled_quantity']}")
                elif self.time.time() >= task["end_time"]:
                    task["status"] = "TIMEOUT"
                    LogInfo(f"任务 {task_id} 超时，剩余: {task['remaining_quantity']}")
                else:
                    task["status"] = "FAILED"
                    LogInfo(f"任务 {task_id} 失败，剩余: {task['remaining_quantity']}")
                
                # 执行回调
                if task["callback"]:
                    try:
                        task["callback"](task_id, task)
                    except Exception as e:
                        LogInfo(f"任务 {task_id} 回调异常: {str(e)}")
    
    def _get_ask1_info(self, symbol):
        """获取卖一档价格和量"""
        try:
            # 使用QMT API获取盘口数据
            ask1_price = Q_AskPrice(symbol, 0)  # 卖一价
            ask1_volume = Q_AskVolume(symbol, 0)  # 卖一量
            return ask1_price, ask1_volume
        except Exception as e:
            LogInfo(f"获取 {symbol} 卖一档信息异常: {str(e)}")
            return 0, 0
    
    def _get_bid1_info(self, symbol):
        """获取买一档价格和量"""
        try:
            # 使用QMT API获取盘口数据
            bid1_price = Q_BidPrice(symbol, 0)  # 买一价
            bid1_volume = Q_BidVolume(symbol, 0)  # 买一量
            return bid1_price, bid1_volume
        except Exception as e:
            LogInfo(f"获取 {symbol} 买一档信息异常: {str(e)}")
            return 0, 0
    
    def _place_order(self, symbol, direction, quantity, price, **kwargs):
        """
        实际下单操作，调用QMT API
        
        返回订单ID或None
        """
        try:
            # 获取账户ID (从之前的代码中看到使用了acc变量，这里假设它是全局变量)
            global xt_trader, acc
            
            if direction == "BUY":
                order_id = xt_trader.order_stock(
                    acc, symbol, xtconstant.STOCK_BUY, 
                    quantity, xtconstant.FIX_PRICE, price, 
                    '智能拆单系统', '买入'
                )
            else:  # SELL
                order_id = xt_trader.order_stock(
                    acc, symbol, xtconstant.STOCK_SELL, 
                    quantity, xtconstant.FIX_PRICE, price, 
                    '智能拆单系统', '卖出'
                )
            
            return order_id
        except Exception as e:
            LogInfo(f"下单异常: {str(e)}")
            return None
    
    def _monitor_orders(self):
        """
        订单状态监控线程
        持续监控活跃订单的状态变化
        """
        while not self.stop_monitor:
            try:
                # 复制活跃订单列表，避免遍历时修改
                with self.lock:
                    active_orders = list(self.active_orders.items())
                
                for order_id, order_info in active_orders:
                    try:
                        # 获取订单状态
                        order_status = self._get_order_status(order_id)
                        
                        if not order_status:
                            continue
                            
                        task_id = order_info["task_id"]
                        
                        # 更新订单状态
                        with self.lock:
                            if task_id in self.order_tasks and order_id in self.active_orders:
                                task = self.order_tasks[task_id]
                                
                                # 找到对应的订单记录
                                for order in task["orders"]:
                                    if order["order_id"] == order_id:
                                        order["status"] = order_status["status"]
                                        order["filled_quantity"] = order_status["filled_quantity"]
                                        
                                        # 如果订单状态改变
                                        if order_status["status"] != self.active_orders[order_id]["status"]:
                                            self.active_orders[order_id]["status"] = order_status["status"]
                                            
                                            # 处理已成交或部分成交
                                            if order_status["status"] in [self.ORDER_FILLED, self.ORDER_PARTIALLY_FILLED]:
                                                filled_qty = order_status["filled_quantity"] - order.get("reported_filled", 0)
                                                if filled_qty > 0:
                                                    order["reported_filled"] = order_status["filled_quantity"]
                                                    task["filled_quantity"] += filled_qty
                                                    task["remaining_quantity"] -= filled_qty
                                                    LogInfo(f"任务 {task_id} 订单 {order_id} 成交 {filled_qty} 股，剩余 {task['remaining_quantity']} 股")
                                            
                                            # 处理已完成(成交/撤销/拒绝/过期)
                                            if order_status["status"] in [self.ORDER_FILLED, self.ORDER_CANCELLED, self.ORDER_REJECTED, self.ORDER_EXPIRED]:
                                                # 从活跃订单中移除
                                                self.completed_orders[order_id] = self.active_orders.pop(order_id)
                                                self.completed_orders[order_id]["completion_time"] = self.time.time()
                                                LogInfo(f"订单 {order_id} 已完成，状态: {order_status['status']}")
                                        
                                        break
                    except Exception as e:
                        LogInfo(f"监控订单 {order_id} 异常: {str(e)}")
            
            except Exception as e:
                LogInfo(f"订单监控线程异常: {str(e)}")
            
            self.time.sleep(self.status_check_interval)
    
    def _get_order_status(self, order_id):
        """
        获取订单状态
        
        返回订单状态字典或None
        """
        try:
            # 使用QMT API查询订单状态
            global xt_trader, acc
            
            order_info = xt_trader.query_order_by_code(acc, order_id)
            if not order_info:
                return None
            
            # 解析订单状态
            status_map = {
                xtconstant.ORDER_UNREPORTED: self.ORDER_SUBMITTED,
                xtconstant.ORDER_WAIT_REPORTING: self.ORDER_SUBMITTED,
                xtconstant.ORDER_REPORTED: self.ORDER_ACCEPTED,
                xtconstant.ORDER_REPORTED_CANCEL: self.ORDER_CANCELLED,
                xtconstant.ORDER_PARTSUCC_CANCEL: self.ORDER_PARTIALLY_FILLED,
                xtconstant.ORDER_SUCCEEDED: self.ORDER_FILLED,
                xtconstant.ORDER_CANCELED: self.ORDER_CANCELLED,
                xtconstant.ORDER_REJECTED: self.ORDER_REJECTED,
                xtconstant.ORDER_PART_CANCELLED: self.ORDER_PARTIALLY_FILLED
            }
            
            status = status_map.get(order_info.order_status, "UNKNOWN")
            filled_quantity = order_info.traded_quantity
            
            return {
                "status": status,
                "filled_quantity": filled_quantity,
                "avg_price": order_info.traded_price,
                "order_time": order_info.order_time
            }
        except Exception as e:
            LogInfo(f"获取订单 {order_id} 状态异常: {str(e)}")
            return None
    
    def cancel_order(self, order_id):
        """
        取消指定订单
        
        参数:
            order_id: 订单ID
            
        返回:
            是否成功
        """
        try:
            global xt_trader, acc
            success = xt_trader.cancel_order_stock(acc, order_id)
            if success:
                LogInfo(f"订单 {order_id} 已发送撤单请求")
            else:
                LogInfo(f"订单 {order_id} 撤单请求失败")
            return success
        except Exception as e:
            LogInfo(f"撤销订单 {order_id} 异常: {str(e)}")
            return False
    
    def cancel_task(self, task_id):
        """
        取消整个任务
        
        参数:
            task_id: 任务ID
            
        返回:
            是否成功
        """
        try:
            with self.lock:
                if task_id not in self.order_tasks:
                    LogInfo(f"找不到任务 {task_id}")
                    return False
                
                task = self.order_tasks[task_id]
                if task["status"] != "ACTIVE":
                    LogInfo(f"任务 {task_id} 已不是活跃状态，当前状态: {task['status']}")
                    return False
                
                task["status"] = "CANCELLING"
            
            # 取消所有活跃订单
            cancel_success = True
            for order in task["orders"]:
                order_id = order["order_id"]
                if order_id in self.active_orders:
                    if not self.cancel_order(order_id):
                        cancel_success = False
            
            with self.lock:
                task = self.order_tasks[task_id]
                task["status"] = "CANCELLED"
            
            LogInfo(f"任务 {task_id} 已取消，成交量: {task['filled_quantity']}, 剩余: {task['remaining_quantity']}")
            return cancel_success
        except Exception as e:
            LogInfo(f"取消任务 {task_id} 异常: {str(e)}")
            return False
    
    def get_task_status(self, task_id):
        """
        获取任务状态
        
        参数:
            task_id: 任务ID
            
        返回:
            任务状态信息或None
        """
        with self.lock:
            return self.order_tasks.get(task_id, None)
    
    def get_all_tasks(self):
        """
        获取所有任务信息
        
        返回:
            任务字典的副本
        """
        with self.lock:
            return dict(self.order_tasks)
    
    def shutdown(self):
        """关闭管理器"""
        LogInfo("正在关闭智能拆单交易管理器...")
        self.stop_monitor = True
        if hasattr(self, 'monitor_thread') and self.monitor_thread.is_alive():
            self.monitor_thread.join(5)  # 等待最多5秒
        LogInfo("智能拆单交易管理器已关闭")


# 使用示例
def demo_usage():
    # 创建智能拆单管理器实例
    order_manager = SmartOrderManager(
        price_limit_percent=0.5,    # 价格偏离不超过0.5%
        time_limit_seconds=300,     # 5分钟超时
        order_size_ratio=1/3,       # 每次下单量为挂单量的1/3
        max_retry=5                 # 最大重试5次
    )
    
    # 买入示例
    def on_buy_complete(task_id, task_info):
        LogInfo(f"买入任务 {task_id} 完成回调，状态: {task_info['status']}")
    
    buy_task_id = order_manager.place_buy_order(
        symbol="600000",            # 上证浦发银行
        total_quantity=3000,        # 总买入3000股
        limit_price=None,           # 使用市价
        min_quantity=100,           # 最小下单100股
        callback=on_buy_complete    # 完成回调函数
    )
    
    # 卖出示例
    def on_sell_complete(task_id, task_info):
        LogInfo(f"卖出任务 {task_id} 完成回调，状态: {task_info['status']}")
    
    sell_task_id = order_manager.place_sell_order(
        symbol="000001",            # 平安银行
        total_quantity=2000,        # 总卖出2000股
        limit_price=10.50,          # 限价10.50元
        min_quantity=100,           # 最小下单100股
        callback=on_sell_complete   # 完成回调函数
    )
    
    # 查询任务状态
    import time
    time.sleep(5)  # 等待一段时间
    
    buy_status = order_manager.get_task_status(buy_task_id)
    LogInfo(f"买入任务状态: {buy_status['status']}, 已成交: {buy_status['filled_quantity']}")
    
    # 取消任务示例
    order_manager.cancel_task(sell_task_id)
    
    # 关闭管理器
    # order_manager.shutdown()

# 在策略关闭函数中添加清理代码
def on_strategy_stop():
    global g_order_manager, g_active_orders
    
    # 取消所有活跃订单
    for symbol, task_id in g_active_orders.items():
        LogInfo(f"策略停止，取消 {symbol} 的订单 {task_id}")
        g_order_manager.cancel_task(task_id)
    
    # 关闭智能拆单管理器
    if g_order_manager:
        g_order_manager.shutdown()
        LogInfo("智能拆单管理器已关闭")

