{"mcpServers": {"graphiti": {"command": "python", "args": ["mcp_servers/graphiti_server.py"], "description": "Graphiti knowledge graph and memory management service", "env": {"GRAPHITI_LOG_LEVEL": "INFO"}}, "opik": {"command": "python", "args": ["mcp_servers/opik_server.py"], "description": "Opik ML experiment tracking and monitoring service", "env": {"OPIK_LOG_LEVEL": "INFO"}}, "ragie": {"command": "python", "args": ["mcp_servers/ragie_server.py"], "description": "Ragie RAG (Retrieval Augmented Generation) service", "env": {"RAGIE_LOG_LEVEL": "INFO"}}, "jupyter_mcp": {"command": "jupyter-mcp-server", "args": ["start", "--transport", "stdio"], "description": "Jupyter MCP Server for notebook integration", "env": {"JUPYTER_LOG_LEVEL": "INFO"}}}, "mcpSettings": {"timeout": 30000, "retryAttempts": 3, "logLevel": "INFO"}}