{"#CALL": {"body": "#CALL [ , ] AS", "createtime": "20150113", "description": "#CALL [CODE, FORMULA] AS VAR 引用CODE合约的指标FORMULA的数据。\\r\\n\\r\\n注：\\r\\n1、参数CODE支持传入下列指定代码以获取数据：\\r\\nCODE写为文华码或交易代码，即引用指定文华码或交易代码合约的数据\\r\\nCODE写为VIXINDEX，即引用当前合约对应VIX指数的数据\\r\\nCODE写为MAININDEX，即引用当前合约对应主连合约的数据\\r\\nCODE写为WEIGHTINDEX或者#CALL[,指标名]AS VAR 表示自动获取加载合约对应的加权合约。\\r\\n2、FORMULA为引用指标名，VAR为定义变量名（此变量名不能以数字开头）。\\r\\n2、默认只能引用同一周期的数据。\\r\\n3、支持加载到自定义周期中使用。\\r\\n4、\\r\\n该函数支持1分钟数据逐笔回测，即该函数可以和MULTSIG_MIN、CHECKSIG_MIN函数连用；\\r\\n该函数不支持TICK数据逐笔回测，即该函数不可以和MULTSIG、CHECKSIG函数连用。\\r\\n5、一个模型中#IMPORT、#CALL、#CALL_PLUS、#CALL_OTHER总的语句个数不能超过6个；\\r\\n\\r\\n例1：\\r\\nCC:REF(C,1);//定义一个周期前的收盘价\\r\\n保存指标，命名为AA\\r\\n#CALL[1201,AA] AS VAR\\r\\nCC:VAR.CC;//跨合约引用豆粕1501昨天的收盘价\\r\\n\\r\\n例2：\\r\\nCC:REF(C,1);//定义一个周期前的收盘价\\r\\n保存指标，命名为BB\\r\\n#CALL[VIXINDEX,BB] AS VAR\\r\\nCC:VAR.CC;//跨合约引用当前合约对应品种VIX指数一个周期前的收盘价", "explanation": "跨合约引用指标", "markettype": 1, "modifytime": "20240201", "param": "CODE合约名FORMULA引用模型名VAR定义变量名", "tip": "#CALL[CODE,FORMULA]ASVAR引用CODE合约的指标FORMULA的数据", "type": 1}, "#CALL_OTHER": {"body": "#CALL_OTHER [ ] AS", "createtime": "20160216", "description": "#CALL_OTHER [FORMULA] AS VAR 引用当前合约，当前周期的，指标FORMULA的数据\\r\\n\\r\\n注：\\r\\n1、FORMULA为引用指标名，VAR为定义变量名（变量名不能以数字开头）。\\r\\n2、默认只能引用同一周期的数据。\\r\\n3、支持加载到自定义周期中使用。\\r\\n4、默认引用当前合约\\r\\n5、\\r\\n该函数支持1分钟数据逐笔回测，即该函数可以和MULTSIG_MIN、CHECKSIG_MIN函数连用；\\r\\n该函数不支持TICK数据逐笔回测，即该函数不可以和MULTSIG、CHECKSIG函数连用。\\r\\n6、一个模型中#IMPORT、#CALL、#CALL_PLUS、#CALL_OTHER总的语句个数不能超过6个；\\r\\n\\r\\n例1：\\r\\nCC:REF(C,1);//定义一个周期前的收盘价\\r\\n保存指标，命名为AA\\r\\n#CALL_OTHER[AA] AS VAR\\r\\nCC:VAR.CC;//跨指标引用当前合约的一个周期前的收盘价", "explanation": "跨指标引用", "markettype": 1, "modifytime": "", "param": "FORMULA引用模型名VAR定义变量名", "tip": "#CALL_OTHER[FORMULA]ASVAR跨指标引用", "type": 1}, "#CALL_PLUS": {"body": "#CALL_PLUS[ , , , ] AS", "createtime": "20150325", "description": "#CALL_PLUS[CODE,PERIOD,N,FORMULA] AS VAR 引用CODE合约，PERIOD参数为N的周期，指标FORMULA的数据。\\r\\n\\r\\n注：\\r\\n1、参数CODE支持传入下列指定代码以获取数据：\\r\\nCODE写为VIXINDEX，即引用当前合约对应VIX指数的数据\\r\\nCODE写为MAININDEX，即引用当前合约对应主连合约的数据\\r\\nCODE写为WEIGHTINDEX或者CODE位置为空，表示自动获取加载合约对应的加权合约数据。例如：#CALL_PLUS[,DAY,1,AA] AS VAR//自动获取加载合约对应的加权合约一天的AA指标的数值；\\r\\n2、PERIOD为周期，支持如下周期：MIN（分钟周期），HOUR（小时周期），CUSHOUR（自定义小时周期），DAY（日周期），WEEK（一周），MONTH（月周期），QUARTER（一季度），YEAR（年周期）。\\r\\n3、N为具体的参数，N必须为大于等于1的整数，周、季周期，N写入大于1的数，按照1计算。例如：#CALL_PLUS[8600,WEEK,2,FORMULA] AS VAR//默认引用的是一周的指标；\\r\\n4、FORMULA为引用指标名，FORMULA引用指标名可以为字母、汉字或数字命名的指标。\\r\\n5、VAR为定义变量名（此变量名不能以数字开头）。\\r\\n6、该函数支持与1分钟数据为基础数据的信号控制函数连用。\\r\\n7、支持引用自定义周期；\\r\\n如#CALL_PLUS[8600,MIN,2,MACD] AS VAR//引用文华码8600的合约两分钟周期MACD指标数值\\r\\n8、引用常规小时周期使用HOUR，引用自定义小时周期需要使用CUSHOUR。\\r\\n9、不支持加载到秒周期、量能周期。\\r\\n10、该函数可以小周期引用大周期，也可以大周期引用小周期。\\r\\n11、被引用的指标中不能存在引用。\\r\\n12、定义变量名不能与函数名重复。\\r\\n13、一个模型中#IMPORT、#CALL、#CALL_PLUS、#CALL_OTHER总的语句个数不能超过6个。\\r\\n14、使用该函数编写末尾不能编写分号。\\r\\n\\r\\n例1：\\r\\nCC:REF(C,1);//定义一个周期前的收盘价\\r\\n保存指标，命名为AA\\r\\n#CALL_PLUS[8600,DAY,1,AA] AS VAR\\r\\nCC:VAR.CC;//跨周期引用IF加权昨天的收盘价\\r\\n\\r\\n例2：\\r\\nCC:REF(C,1);//定义一个周期前的收盘价\\r\\n保存指标，命名为BB\\r\\n#CALL_PLUS[VIXINDEX,DAY,1,BB] AS VAR\\r\\nCC:VAR.CC;//跨周期引用当前合约对应品种VIX指数昨天的收盘价", "explanation": "跨合约跨周期引用指标", "markettype": 1, "modifytime": "20240201", "param": "CODE合约名FORMULA引用模型名VAR定义变量名", "tip": "#CALL_PLUS[CODE,PERIOD,N,FORMULA]ASVAR引用CODE合约PERIOD参数为N的周期下的指标FORMULA的数据", "type": 1}, "#IMPORT": {"body": "#IMPORT [ , , ] AS", "createtime": "20140924", "description": "#IMPORT [PERIOD,N,FORMULA] AS VAR 引用当前合约，PERIOD参数为N的周期，指标FORMULA的数据。\\r\\n\\r\\n注：\\r\\n1、PERIOD为周期，N为具体的参数，FORMULA为引用指标名，VAR为定义变量名（此变量名不能以数字开头）；\\r\\n2、PERIOD支持如下周期：MIN（分钟周期），HOUR（小时周期），CUSHOUR（自定义小时周期），DAY（日周期），WEEK（一周），MONTH（月周期），QUARTER（一季度），YEAR（年周期）；\\r\\n3、支持引用自定义周期；\\r\\n如#IMPORT [MIN,2,MACD] AS VAR//引用两分钟周期MACD指标数值\\r\\n4、N必须为大于等于1的整数，周、季周期，N写入大于1的数，按照1计算；\\r\\n例如：#IMPORT [WEEK,2,FORMULA] AS VAR//默认引用的是一周的指标；\\r\\n5、引用常规小时周期使用HOUR，引用自定义小时周期需要使用CUSHOUR。\\r\\n6、该函数不支持加载到量能周期使用；\\r\\n7、该函数可以小周期引用大周期，也可以大周期引用小周期；\\r\\n8、被引用的指标中不能存在引用；\\r\\n9、FORMULA引用指标名可以为字母、汉字或数字命名的指标；\\r\\n10、定义变量名不能与函数名重复；\\r\\n11、一个模型中#IMPORT、#CALL、#CALL_PLUS、#CALL_OTHER总的语句个数不能超过6个；\\r\\n12、使用该函数编写末尾不能编写分号。\\r\\n\\r\\n例1：\\r\\nCC:REF(C,1);//定义一个周期前的收盘价\\r\\n保存指标，命名为AA\\r\\n#IMPORT[DAY,1,AA] AS VAR\\r\\nCC:VAR.CC;//跨周期引用昨天的收盘价\\r\\n\\r\\n例2：\\r\\nCC:C;//定义收盘价\\r\\n保存指标，命名为CC\\r\\n#IMPORT[DAY,1,CC] AS VAR\\r\\nCC:=VAR.CC;//跨周期引用日周期上的收盘价\\r\\nCC1:REF(CC,1);\\r\\n//要引用的数据需要写在被引用的指标里，不能写在IMPORT模型中。\\r\\n//例1中的CC指标引用日周期上前一个周期的收盘价，需要在被引用的指标中取一个周期前的收盘价，\\r\\n例2中写在IMPORT模型中则表示取小周期上一个周期前的值\\r\\n\\r\\n例3：\\r\\nCC:=REF(C,1);//定义一个周期前的收盘价\\r\\n保存指标，命名为AA\\r\\n#IMPORT[CUSHOUR,6,AA]AS S\\r\\nCC1:=S.CC;//跨周期引用自定义6小时周期的一个周期前的收盘价\\r\\n#IMPORT[MIN,1,AA]AS R\\r\\nCC2:=R.CC;//跨周期引用自定义1分钟周期的一个周期前的收盘价", "explanation": "跨周期引用指标", "markettype": 1, "modifytime": "20220304", "param": "PERIOD周期N参数FORMULA引用模型名VAR定义变量名", "tip": "#IMPORT[PERIOD,N,FORMULA]ASVAR引用PERIOD参数为N的周期下的指标FORMULA的数据", "type": 1}, "$": {"body": " $ ", "createtime": "", "description": "\" $ \" 简化的跨合约函数，调用其他合约的K线数据。\\r\\n\\r\\n用法：\"CODE$PRICE\"引用CODE合约的PRICE数据，CODE为文华码。\\r\\n\\r\\n注：\\r\\n1、PRICE的位置可以替换为TIME、OPEN、O、HIGH、H、LOW、L、CLOSE、C、OPI、VOL、V、AVPRICE、SETTLE、SCALE\\r\\n2、默认只能引用同一周期的数据。\\r\\n3、CODE的位置不可以为空。\\r\\n4、一个模型中&跨合约、&&跨周期引用语句个数不能超过6个。\\r\\n\\r\\n例1：\\r\\nA:\"1209$CLOSE\";//返回文华码为1209合约的收盘价。\\r\\n\\r\\n例2：\\r\\nA:\"8606$OPI\";//返回文华码为8606合约的持仓量。", "explanation": "引用其他合约的K线数据", "markettype": 1, "modifytime": "", "param": "", "tip": "", "type": 1}, "$ $": {"body": " $ $ ", "createtime": "", "description": "\" $ $ \" 简化的跨周期函数，调用另外一个周期上一根k线的数据。\\r\\n\\r\\n用法：\"MIN$15$PRICE\"引用15分钟K线的PRICE数据，PERIOD为周期类型。PRICE为引用的数据。\\r\\n\\r\\n注：\\r\\n1、PRICE的位置可以替换为TIME、OPEN、O、HIGH、H、LOW、L、CLOSE、C、OPI、VOL、V、AVPRICE、SETTLE、SCALE\\r\\n2、引用的是上一根K线的值。\\r\\n示例 TEST:\"MIN$3$CLOSE\";  //引用3分钟周期K线CLOSE\\r\\n即引用的上一根3分钟K线的CLOSE。\\r\\n3、只支持小周期引用大周期，被引用周期不支持秒周期及自定义周期，支持的被引用周期：1MIN,3MIN,5MIN,10MIN,15MIN,30MIN,1HOUR,2HOUR,3HOUR,4HOUR,DAY,WEEK,MONTH。\\r\\n4、一个模型中&跨合约、&&跨周期引用语句个数不能超过6个。\\r\\n\\r\\n例1：\\r\\nA:\"MIN$5$CLOSE\";//返回上一根5分钟周期K线的收盘价。\\r\\n\\r\\n例2：\\r\\nA:\"HOUR$4$OPI\";//返回上一根4小时周期K线的持仓量。", "explanation": "引用其他周期的K线数据", "markettype": 1, "modifytime": "", "param": "", "tip": "", "type": 1}, "ABS": {"body": "ABS( )", "createtime": "", "description": "ABS(X)：取的X的绝对值。\\r\\n\\r\\n注：\\r\\n1、正数的绝对值是它本身；\\r\\n2、负数的绝对值是它的相反数；\\r\\n3、0的绝对值还是0；\\r\\n\\r\\n例1：\\r\\nABS(-10);//返回10。\\r\\n例2：\\r\\nABS(CLOSE-10);//返回收盘价和的10价差的绝对值。\\r\\n例3：\\r\\nABS(C-O);//当前K线实体长度", "explanation": "绝对值", "markettype": 0, "modifytime": "", "param": "", "tip": "ABS(X),求X的绝对值", "type": 4}, "ACOS": {"body": "ACOS( )", "createtime": "", "description": "ACOS(X)：返回X的反余弦值。\\r\\n\\r\\n注：\\r\\n1、X取值范围[-1，1]。\\r\\n2、若X不在取值范围，返回值为空值。\\r\\n\\r\\n例1：\\r\\nACOS(-1);//求-1的反余弦值；\\r\\n例2：\\r\\nACOS(1);//求1的反余弦值；", "explanation": "反余弦值", "markettype": 0, "modifytime": "", "param": "", "tip": "ACOS(X),求X的反余弦值", "type": 4}, "ACTIVE_ORDER": "ACTIVE_ORDER 对价", "ADD": "ADD 加和", "ADMA": {"body": "ADMA(,,,)", "createtime": "", "description": "ADMA(X,N,P,Q) 考夫曼均值\\r\\n\\r\\n用法：ADMA(X,N,P,Q);求X在N个周期中的，快线频率参数为P，慢线频率参数为Q的考夫曼自适应均值。\\r\\n\\r\\n注：\\r\\n1、X为调用的k线数据（例如高、开、低，收）；N为调用的间隔时间；P为快线频率参数；Q为慢线频率参数。\\r\\n2、当前的K线数不足N根时，函数返回空值。\\r\\n3、N为0或空值的情况下，函数返回空值。\\r\\n\\r\\n算法：\\r\\nADMA(X,N,P,Q)=REF(EMA(X,N),1)+CONSTANT*(X- REF(EMA(X,N),1));\\r\\nCONSTANT是平滑系数，用麦语言函数可以表示为：\\r\\nCONSTANT:=SQUARE((ABS((CLOSE-REF(CLOSE,N))/(SUM(ABS((CLOSE-REF(CLOSE,1))),N))))*(2/(P+1)-2/(Q+1))+2/(Q+1));\\r\\n\\r\\n算法举例：计算C在9周期的，快线频率参数为2，慢线频率参数为30的考夫曼均值。\\r\\n1、确定价格方向：价格方向表示整个时间段中的净价格变化。比如，使用N天的间隔（或N小时），这里N为9\\r\\n2、计算方向移动：DIRECTION:=ABS(CLOSE-REF(CLOSE,9));\\r\\n3、计算波动性：波动性是市场噪音的总数量，计算了时间段内价格变化的总和。\\r\\nVOLATILITY:=SUM(ABS((CLOSE-REF(CLOSE,1))),9);\\r\\n4、确定效率系数：\\r\\nER:=DIRECTION/VOLATILITY;\\r\\n5、计算平滑系数：\\r\\nFASTSC:=2/(2+1);\\r\\nSLOWSC:=2/(30+1);\\r\\nSMOOTH:=ER*(FASTSC-SLOWSC)+SLOWSC;\\r\\nCONSTANT:=SQUARE(SMOOTH);\\r\\n6、计算平滑系数为CONSTANT的自适应均线：\\r\\nAMACLOSE:REF(EMA(C,9),1)+CONSTANT*(C-REF(EMA(C,9),1));", "explanation": "考夫曼均值", "markettype": 0, "modifytime": "", "param": "", "tip": "AMA(X,N,P,Q),考夫曼均值X为调用的k线数据（例如高、开、低，收），N为调用的间隔时间P为快线频率参数，Q为慢线频率参数", "type": 3}, "ALIGN": {"body": "ALIGN", "createtime": "20140528", "description": "设置文字对齐方式（左中右）。\\r\\n\\r\\n用法：DRAWTEXT(COND,PRICE,TEXT),ALIGNX;\\r\\n\\r\\nCOND条件满足时，在PRICE的位置，标注TEXT，文字按照ALIGNX写入的方式对齐。ALIGN0，ALIGN1，ALIGN2，分别表示左对齐，居中对齐，右对齐。\\r\\n\\r\\n例：\\r\\nDRAWTEXT(C>O,H,'涨'),ALIGN1,VALIGN1,FONTSIZE20,COLORGREEN;//在阳线的最高价标注文字“涨”，文字居中对齐，字体大小为20，颜色为绿色。", "explanation": "设置文字对齐方式（左中右）", "markettype": 0, "modifytime": "", "param": "", "tip": "ALIGN0,ALIGN1,ALIGN2,分别表示文字左对齐，居中对齐，右对齐", "type": 8}, "ALIGN0": "ALIGN0 左对齐", "ALIGN1": "ALIGN1 居中对齐", "ALIGN2": "ALIGN2 右对齐", "AND": "", "AS": "AS,与#IMPORT函数连用", "ASIN": {"body": "ASIN( )", "createtime": "", "description": "ASIN(X)：返回X的反正弦值。\\r\\n\\r\\n注：\\r\\n1、X取值范围[-1，1]。\\r\\n2、若X不在取值范围，返回值为空值。\\r\\n\\r\\n例1：\\r\\nASIN(-1);//求-1的反正弦值；\\r\\n例2：\\r\\nASIN(1);//求1的反正弦值；", "explanation": "反正弦值", "markettype": 0, "modifytime": "", "param": "", "tip": "ASIN(X),求X的反正弦值", "type": 4}, "ATAN": {"body": "ATAN( )", "createtime": "", "description": "ATAN(X)：返回X的反正切值。\\r\\n\\r\\n注：X的取值为R（实数集）\\r\\n\\r\\n例1：\\r\\nATAN(-1.75);//求-1.75的反正切值；\\r\\n例2：\\r\\nATAN(1.75);//求1.75的反正切值；", "explanation": "反正切值", "markettype": 0, "modifytime": "", "param": "", "tip": "ATAN(X),求X的反正切值", "type": 4}, "AUTOFILTER": {"body": "AUTOFILTER", "createtime": "", "description": "AUTOFILTER 启用一开一平信号过滤机制。\\r\\n\\r\\n用法：\\r\\n模型中含有AUTOFILTER函数，则启用一开一平信号过滤机制。\\r\\n模型中不写入该函数，则每个指令都有效，支持加减仓。\\r\\n\\r\\n模型的过滤规则：\\r\\n1、连续的同方向指令只有第一个有效，其他的将被过滤；\\r\\n2、交易指令必须先开仓后平仓，一开一平配对出现：\\r\\n出现BK指令，下一个指令只允许出现SP\\SPK指令；\\r\\n出现SK指令，下一个指令只允许出现BP\\BPK指令；\\r\\n出现SP/BP/CLOSEOUT等平仓指令，下一个可以是BK/SK/SPK/BPK指令任一个；\\r\\n反手指令SPK和BPK交叉出现。\\r\\n\\r\\n例：\\r\\nCLOSE>OPEN,BK;\\r\\nCLOSE<OPEN,SP;\\r\\nAUTOFILTER; //启用一开一平信号过滤机制", "explanation": "启用一开一平信号过滤机制", "markettype": 0, "modifytime": "20221208", "param": "", "tip": "AUTOFILTER,启用一开一平信号过滤机制。", "type": 9}, "AUTOFINANCING": {"body": "AUTOFINANCING", "createtime": "********", "description": "AUTOFINANCING 启用按需自动入金方式 \\r\\n\\r\\n用法：模型中含有AUTOFINANCING函数，则启用按需自动入金方式。\\r\\n\\r\\n按需自动入金方式规则：\\r\\n1、模型中含有该函数，回测和模组中资金使用量按需自动入金，参数设置中设置的资金分配量无效。\\r\\n2、首次账户入金为首次开仓所需要的资金，如下次开仓时可用资金不足，则账户再次入金，按所需补齐不足部分资金。\\r\\n3、如模型中不含该函数\\r\\n1）回测时：则按回测参数中设置的资金分配量进行回测\\r\\n2）实盘中：则实盘运行按模组参数中设置的信号计算资金分配量进行计算\\r\\n\\r\\n注：\\r\\n按需自动入金中，回测报告中显示的资金分配量为账户总入金\\r\\n\\r\\n例：\\r\\nMA5:MA(C,5);\\r\\nMA10:MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK(100);\\r\\nCROSSDOWN(MA5,MA10),SP(100);\\r\\nAUTOFINANCING;//启用自动入金方式\\r\\n//该模型加载在股票合约上", "explanation": "启用按需自动入金方式", "markettype": 1, "modifytime": "", "param": "", "tip": "AUTOFINANCING，启用按需自动入金方式", "type": 9}, "AVAILABLE_OPI": {"body": "AVAILABLE_OPI", "createtime": "20151225", "description": "AVAILABLE_OPI 可用股数\\r\\n\\r\\n用法：\\r\\n该函数取值为股票合约的当前可用股数\\r\\n\\r\\n注：\\r\\n1、该函数只支持股票市场\\r\\n2、当日买入手数不计入该函数取值\\r\\n\\r\\n例1\\r\\nMA5:MA(C,5);\\r\\nMA10:MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK(100);\\r\\nAVAILABLE_OPI>0&&CROSSDOWN(MA5,MA10),SP(AVAILABLE_OPI);//当前可用股数大于0，并且5日均线下穿10日均线，卖出全部可用股数", "explanation": "可用股数", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "AVAILABLE_OPI可用股数", "type": 12}, "AVEDEV": {"body": "AVEDEV( , )", "createtime": "2014-04-30", "description": "AVEDEV(X,N)：返回X在N周期内的平均绝对偏差。\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、N为有效值，但当前的k线数不足N根，该函数返回空值；\\r\\n3、N为0时，该函数返回空值；\\r\\n4、N为空值，该函数返回空值；\\r\\n5、N不能为变量\\r\\n\\r\\n算法举例：计算AVEDEV(C,3);在最近一根K线上的值。\\r\\n\\r\\n用麦语言函数可以表示如下：\\r\\n(ABS(C-(C+REF(C,1)+REF(C,2))/3)+ABS(REF(C,1)-(C+REF(C,1)+REF(C,2))/3)+ABS(REF(C,2)-(C+REF(C,1)+REF(C,2))/3))/3;\\r\\n\\r\\n例：\\r\\nAVEDEV(C,5);//返回收盘价在5周期内的平均绝对偏差。\\r\\n//表示5个周期内每个周期的收盘价与5周期收盘价的平均值的差的绝对值的平均值，判断收盘价与其均值的偏离程度", "explanation": "平均绝对偏差", "markettype": 0, "modifytime": "", "param": "", "tip": "AVEDEV(X,N),求X在N周期内的平均绝对偏差", "type": 3}, "AVERAGE": "AVERAGE 均值", "AVPRICE": {"body": "AVPRICE", "createtime": "", "description": "AVPRICE 取得K线图的均价。\\r\\n\\r\\n注：\\r\\n1、表示单根K线内的均价；\\r\\n2、日线周期上收盘后与SETTLE函数一样取得当日的结算价。\\r\\n\\r\\n例1：\\r\\nA:AVPRICE;//定义变量A为均价线；\\r\\n\\r\\n例2：\\r\\nMA5:MA(AVPRICE,5);//定义五个周期均价的平均值;\\r\\n\\r\\n例3：\\r\\nC>MA(AVPRICE,5);//价格大于五个周期均价的平均值则返回1，否则返回0。", "explanation": "取得K线图的均价", "markettype": 0, "modifytime": "", "param": "", "tip": "AVPRICE,取均价", "type": 1}, "BACKGROUNDSTYLE": {"body": "BACKGROUNDSTYLE( )", "createtime": "", "description": "BACKGROUNDSTYLE函数    设置背景的样式。\\r\\n\\r\\n用法：\\r\\nBACKGROUNDSTYLE(i)设置背景的样式。\\r\\ni = 0 或1或2。\\r\\n\\r\\n注：\\r\\n1.\\r\\n0 是保持本身坐标不变。\\r\\n1 是将坐标固定在0到100之间。\\r\\n2 是将坐标以0为中轴的坐标系。\\r\\n2、参数i的选择根据想要显示的指标数据范围而定。\\r\\n3、不支持将该函数直接定义为变量，即不支持下面的写法：A:BACKGROUNDSTYLE(i);\\r\\n\\r\\n例1：\\r\\nMA5:MA(C,5);\\r\\nMA10:MA(C,10);\\r\\nBACKGROUNDSTYLE(0);\\r\\n例2：\\r\\nDIFF : EMA(CLOSE,12) - EMA(CLOSE,26);\\r\\nDEA  : EMA(DIFF,9);\\r\\n2*(DIFF-DEA),COLORSTICK;\\r\\nBACKGROUNDSTYLE(2);", "explanation": "背景的样式", "markettype": 0, "modifytime": "", "param": "i=0或1", "tip": "BACKGROUNDSTYLE(i)设置背景的样式,i=0、1、2", "type": 8}, "BARINTERVAL": {"body": "BARINTERVAL", "createtime": "20200603", "description": "BARINTERVAL 返回数据合约的K线周期数值\\r\\n\\r\\n用法：\\r\\n1、BarInterval返回数据合约的K线周期数值。\\r\\n2、该函数支持在常规周期、自定义周期使用，如加载在常规1小时周期，该函数返回1，加载到自定义2小时周期，该函数返回2。\\r\\n3、加载周期为日线，BarInterval返回1；\\r\\n4、加载周期为量能周期，BarInterval返回设置的成交量容量，如量能（1000），该函数返回1000。\\r\\n\\r\\n例：\\r\\nBARTYPE;\\r\\nBARINTERVAL;", "explanation": "返回数据合约的K线周期数值", "markettype": 0, "modifytime": "", "param": "", "tip": "BARINTERVAL数据合约的K线周期数值。", "type": 1}, "BARPOS": {"body": "BARPOS", "createtime": "", "description": "BARPOS，返回从第一根K线开始到当前的周期数。\\r\\n\\r\\n注：\\r\\n1、BARPOS返回本地已有的K线根数，从本机上存在的数据开始算起。\\r\\n2、本机已有的第一根K线上返回值为1。\\r\\n\\r\\n例1：LLV(L,BARPOS);//求本地已有数据的最小值。\\r\\n\\r\\n例2：IFELSE(BARPOS=1,H,0);//当前K线是本机已有的第一根K线取最高值，否则取0。", "explanation": "取K线的位置", "markettype": 0, "modifytime": "", "param": "", "tip": "BARPOS,取某K线的位置", "type": 7}, "BARSBK": {"body": "BARSBK", "createtime": "20140918", "description": "BARSBK 上一次买开信号位置\\r\\n\\r\\n用法：\\r\\nBARSBK返回上一次买开仓的K线距离当前K线的周期数（不包含出现BK信号的那根K线）\\r\\n取包含BK信号出现的那根K线到当前K线的周期数，则需要在此函数后+1，即BARSBK+1；由于发出BK信号的当根k线BARSBK返回空值，则BARSBK+1在发出BK信号当根k线返回空值。\\r\\n\\r\\n注：\\r\\n1、若当前K线之前无BK信号，则函数返回值为空值\\r\\n2、BK信号固定后BARSBK返回为空值。\\r\\n（1）设置信号执行方式为K线走完确认信号下单\\r\\nBARSBK返回值为上一个BK信号距离当前的K线根数（包含当前K线）\\r\\n（2）设置信号执行方式为出信号立即下单，不复核（例如：在模型中写入MULTSIG或MULTSIG_MIN;）\\r\\n    a.历史信号计算中，出现BK信号的当根K线，BARSBK返回空值\\r\\n    b.加载运行过程中,信号固定后BARSBK返回空值\\r\\n（3）设置信号执行方式为K线走完复核（例如：在模型中写入CHECKSIG(BK,'A',N,'D',0,0);）\\r\\nBARSBK返回值为上一个BK信号距离当前的K线根数（包含当前K线）\\r\\n\\r\\n例：\\r\\n1、BARSBK>10,SP;//上一次买开仓（不包含出现买开信号的那根K线）距离当前K线的周期数大于10，卖平；\\r\\n2、HHV(H,BARSBK+1);//上一次买开仓（包含开仓信号出现的当根k线）到当前的最高价的最大值。\\r\\n当根K线出现BK信号，AA返回为空值，需要返回当根K线上最高价，模型需要修改为:\\r\\nAA:IFELSE(BARSBK>=1,HHV(H,BARSBK+1),H);\\r\\n（1）当根K线出现BK信号，BARSBK返回为空值，不满足BARSBK>=1的条件，则取值为当根K线的最高价H\\r\\n（2）发出BK信号之后K线BARSBK返回买开仓的K线距离当前K线的周期数，满足BARSBK>=1的条件，则取值为HHV(H,BARSBK+1)，即买开仓（包含开仓信号出现的当根k线）到当前的最高价的最大值。\\r\\n修改后如果平仓条件中用到了AA的值，当根K线满足了平仓条件，可以出现平仓信号 \\r\\n3、AA:IFELSE(BARSBK>=1,REF(C,BARSBK),C);//取最近一次买开仓K线的收盘价\\r\\n（1）发出BK信号的当根k线BARSBK返回空值,则当根K线不满足BARSBK>=1的条件，AA返回当根k线的收盘价；\\r\\n（2）发出BK信号之后的k线BARSBK返回买开仓的K线距离当前K线的周期数，则AA返回REF(C,BARSBK)，即开仓k线的收盘价；\\r\\n（3）例：1、2、3三根k线，1 K线为开仓信号的当根k线，则返回当根k线的收盘价，2、3 K线AA返回值为 1 K线的收盘价。", "explanation": "上一次买开信号位置", "markettype": 1, "modifytime": "", "param": "", "tip": "BARSBK，取上一次买开信号位置", "type": 10}, "BARSBP": {"body": "BARSBP", "createtime": "20140918", "description": "BARSBP 上一次买平信号位置\\r\\n\\r\\n用法：\\r\\nBARSBP返回上一次买平仓的K线距离当前K线的周期数（不包含出现BP信号的那根K线）\\r\\n取包含BP信号出现的那根K线到当前K线的周期数，则需要在此函数后+1，即BARSBP+1。由于发出BP信号的当根k线BARSBP返回空值，则BARSBP+1在发出BP信号当根k线返回空值。\\r\\n\\r\\n注：\\r\\n1、若当前K线之前无BP信号，则函数返回值为空值\\r\\n2、BP信号固定后BARSBP返回为空值。\\r\\n（1）设置信号执行方式为K线走完确认信号下单\\r\\nBARSBP返回值为上一个BP信号距离当前的K线根数（包含当前K线）\\r\\n（2）设置信号执行方式为出信号立即下单，不复核（例如：在模型中写入MULTSIG或MULTSIG_MIN;）\\r\\n    a.历史信号计算中，出现BP信号当根K线，BARSBP返回空值\\r\\n    b.加载运行过程中，BP信号当根K线,信号固定后BARSBP返回空值\\r\\n（3）设置信号执行方式为K线走完复核（例如：在模型中写入CHECKSIG(BP,'A',N,'D',0,0);）\\r\\nBARSBP返回值为上一个BP信号距离当前的K线根数（包含当前K线）\\r\\n\\r\\n例： \\r\\n1、BARSBP>10,BK;//上一次买平仓（不包含出现买平信号的那根K线）距离当前K线的周期数大于10，买开。 \\r\\n2、AA:HHV(H,BARSBP+1);//上一次买平仓（包含平仓信号出现的当根k线）到当前的最高价的最大值。 \\r\\n当根K线出现BP信号，AA返回为空值，如果需要返回当根K线上最高价，模型需要修改为:\\r\\nAA:IFELSE(BARSBP>=1,HHV(H,BARSBP+1),H);\\r\\n（1）当根K线出现BP信号，BARSBP返回为空值，不满足BARSBP>=1的条件，则取值为当根K线的最高价H \\r\\n（2）发出BP信号之后K线BARSBP返回买平仓的K线距离当前K线的周期数，满足BARSBP>=1的条件，则取值为HHV(H,BARSBP+1)，即买平仓（包含平仓信号出现的当根k线）到当前的最高价的最大值。\\r\\n3、AA:IFELSE(BARSBP>=1,REF(C,BARSBP),C);//取最近一次买平仓K线的收盘价\\r\\n（1）发出BP信号的当根k线BARSBP返回空值,则当根K线不满足BARSBP>=1的条件，AA返回当根k线的收盘价；\\r\\n（2）发出BP信号之后的k线BARSBP返回买平仓的K线距离当前K线的周期数，则AA返回REF(C,BARSBP)，即平仓k线的收盘价；\\r\\n（3）例：1、2、3三根k线，1 K线为平仓信号的当根k线，则返回当根k线的收盘价，2、3 K线AA返回值为 1 K线的收盘价。", "explanation": "上一次买平信号位置", "markettype": 1, "modifytime": "", "param": "", "tip": "BARSBP，取上一次买平信号位置", "type": 10}, "BARSBUY": {"body": "BARSBUY", "createtime": "20221201", "description": "BARSBUY 上一次买入信号位置\\r\\n\\r\\n用法：\\r\\n1、BARSBUY返回上一次买入的K线距离当前K线的周期数（不包含出现BUY信号的那根K线）\\r\\n2、取包含BUY信号出现的那根K线到当前K线的周期数，则需要在此函数后+1，即BARSBUY+1；由于发出BUY信号的当根k线BARSBUY返回空值，则BARSBUY+1在发出BUY信号当根k线返回空值。\\r\\n\\r\\n注：\\r\\n1、若当前K线之前无BUY信号，则函数返回值为空值。\\r\\n2、该函数支持在股票T+1策略运行池中使用；\\r\\n\\r\\n例：\\r\\n1、BARSBUY>10,SELL;//上一次买入（不包含出现买入信号的那根K线）距离当前K线的周期数大于10，卖出；\\r\\n2、HHV(H,BARSBUY+1);//上一次买入（包含买入信号出现的当根k线）到当前的最高价的最大值。\\r\\n3、AA:IFELSE(BARSBUY>=1,REF(C,BARSBUY),C);//取最近一次买入K线的收盘价\\r\\n（1）发出BUY信号的当根k线BARSBK返回空值,则当根K线不满足BARSBUY>=1的条件，AA返回当根k线的收盘价；\\r\\n（2）发出BUY信号之后的k线BARSBUY返回上一次买入的K线距离当前K线的周期数，则AA返回REF(C,BARSBUY)，即买入k线的收盘价；\\r\\n（3）例：1、2、3三根k线，1 K线为买入信号的当根k线，则返回当根k线的收盘价，2、3 K线AA返回值为 1 K线的收盘价。", "explanation": "上一次买入信号位置", "markettype": 2, "modifytime": "20221205", "param": "", "tip": "BARSBUY 上一次买入信号位置", "type": 10}, "BARSCOUNT": {"body": "BARSCOUNT()", "createtime": "20160111", "description": "BARSCOUNT(COND) 第一个有效周期到当前的周期数。\\r\\n\\r\\n注：\\r\\n1、返回值为COND从第一个有效周期开始计算，到现在为止的周期数。\\r\\n2、条件第一次成立的当根k线上BARSCOUNT(COND)的返回值为0\\r\\n\\r\\n例：\\r\\nBARSCOUNT(MA(C,4));//计算MA(C,4)第一次有返回值到当前的周期数。", "explanation": "第一个有效周期到当前的周期数", "markettype": 0, "modifytime": "", "param": "", "tip": "BARSCOUNT(COND)返回COND第一个有效值的位置到当前的周期数", "type": 2}, "BARSLAST": {"body": "BARSLAST( )", "createtime": "", "description": "BARSLAST(COND)：上一次条件COND成立到当前的周期数\\r\\n\\r\\n注：\\r\\n1、条件成立的当根k线上BARSLAST(COND)的返回值为0\\r\\n\\r\\n例1：\\r\\nBARSLAST(OPEN>CLOSE); //上一根阴线到现在的周期数。\\r\\n例2：\\r\\nN:=BARSLAST(DATE<>REF(DATE,1))+1;//分钟周期，当日k线数。\\r\\n//由于条件成立的当根k线上BARSLAST(COND)的返回值为0，所以“+1”才是当日k线根数。", "explanation": "上一次条件成立位置", "markettype": 0, "modifytime": "", "param": "", "tip": "BARSLAST(X),求上一次条件X满足到现在的周期数", "type": 2}, "BARSLASTCOUNT": {"body": "BARSLASTCOUNT()", "createtime": "", "description": "BARSLASTCOUNT(COND) 从当前周期向前计算，统计连续满足条件的周期数。\\r\\n\\r\\n注：\\r\\n1、返回值为从当前周期计算COND连续不为0的周期数\\r\\n2、条件第一次成立的当根k线上BARSLASTCOUNT(COND)的返回值为1\\r\\n\\r\\n例：\\r\\nBARSLASTCOUNT(CLOSE>OPEN);\\r\\n//计算当根K线在内连续为阳线的周期数", "explanation": "从当前周期向前计算，统计连续满足条件的周期数", "markettype": 0, "modifytime": "", "param": "", "tip": "BARSLASTCOUNT,从当前周期向前计算，统计连续满足条件的周期数", "type": 2}, "BARSSELL": {"body": "BARSSELL", "createtime": "20221201", "description": "BARSSELL 上一次卖出信号位置\\r\\n\\r\\n用法：\\r\\n1、BARSSELL返回上一次卖出的K线距离当前K线的周期数（不包含出现SELL信号的那根K线）\\r\\n2、取包含SELL信号出现的那根K线到当前K线的周期数，则需要在此函数后+1，即BARSSELL+1。由于发出SELL信号的当根k线BARSSELL返回空值，则BARSSELL+1在发出SELL信号当根k线返回空值。\\r\\n\\r\\n注：\\r\\n1、若当前K线之前无SELL信号，则函数返回值为空值\\r\\n2、该函数支持在股票T+1策略运行池中使用\\r\\n\\r\\n例：\\r\\n1、BARSSELL>10,BUY;//上一次卖出（不包含出现卖出信号的那根K线）距离当前K线的周期数大于10，买入。\\r\\n2、AA:HHV(H,BARSSELL+1);//上一次，卖出（包含卖出信号出现的当根k线）到当前的最高价的最大值。 \\r\\n3、AA:IFELSE(BARSSELL>=1,REF(C,BARSSELL),C);//取最近一次卖出K线的收盘价 \\r\\n（1）发出SELL信号的当根k线BARSSELL返回空值,则当根K线不满足BARSSELL>=1的条件，AA返回当根k线的收盘价；\\r\\n（2）发出SELL信号之后的k线BARSSELL返回卖出的K线距离当前K线的周期数，则AA返回REF(C,BARSSELL)，即卖出k线的收盘价；\\r\\n（3）1、2、3三根k线，1 K线为卖出信号的当根k线，则返回当根k线的收盘价，2、3 K线AA返回值为 1 K线的收盘价", "explanation": "上一次卖出信号位置", "markettype": 2, "modifytime": "20221205", "param": "", "tip": "BARSSELL 上一次卖出信号位置", "type": 10}, "BARSSINCE": {"body": "BARSSINCE()", "createtime": "", "description": "BARSSINCE(COND) 第一个条件成立到当前的周期数。\\r\\n\\r\\n注：\\r\\n1、返回值为COND第一次成立到当前的周期数\\r\\n2、条件第一次成立的当根k线上BARSSINCE(COND)的返回值为0\\r\\n\\r\\n例：\\r\\nBARSSINCE(CLOSE>OPEN);\\r\\n//统计第一次满足阳线这个条件的K线到现在的周期数", "explanation": "第一个条件成立到当前的周期数", "markettype": 0, "modifytime": "", "param": "", "tip": "BARSSINCE,第一个条件成立到当前的周期数", "type": 2}, "BARSSINCEN": {"body": "BARSSINCEN", "createtime": "20160927", "description": "BARSSINCEN(COND,N) 统计N周期内第一次条件成立到当前的周期数\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、当N为有效值，但当前的k线数不足N根，按照实际的根数计算； \\r\\n3、若N为0返回无效值；\\r\\n4、N可以为变量\\r\\n\\r\\n例：\\r\\nN:=BARSLAST(DATE<>REF(DATE,1))+1;//分钟周期，当日K线数。\\r\\nBARSSINCEN(ISUP,N);//统计N周期内第一次满足阳线到当前的周期数", "explanation": "统计N周期内第一次条件成立到当前的周期数", "markettype": 0, "modifytime": "", "param": "", "tip": "BARSSINCEN统计N周期内第一次条件成立到当前的周期数", "type": 2}, "BARSSK": {"body": "BARSSK", "createtime": "20140918", "description": "BARSSK 上一次卖开信号位置\\r\\n\\r\\n用法：\\r\\nBARSSK返回上一次卖开仓的K线距离当前K线的周期数（不包含出现SK信号的那根K线）\\r\\n取包含SK信号出现的那根K线到当前K线的周期数，需要在此函数后+1，即BARSSK+1；由于发出SK信号的当根k线BARSSK返回空值，则BARSSK+1在发出SK信号当根k线返回空值。\\r\\n\\r\\n注：\\r\\n1、若当前K线之前无SK信号，则函数返回值为空值\\r\\n2、SK信号固定后BARSSK返回为空值。\\r\\n（1）设置信号执行方式为K线走完确认信号下单\\r\\nBARSSK返回值为上一个SK信号距离当前的K线根数（包含当前K线）\\r\\n（2）设置信号执行方式为出信号立即下单，不复核（例如：在模型中写入MULTSIG或MULTSIG_MIN;）\\r\\n    a.历史信号计算中，出现SK信号当根K线，BARSSK返回空值\\r\\n    b.加载运行过程中，SK信号当根K线,信号固定后BARSSK返回空值\\r\\n（3）设置信号执行方式为K线走完复核（例如：在模型中写入CHECKSIG(SK,'A',N,'D',0,0);）\\r\\nBARSSK返回值为上一个SK信号距离当前的K线根数（包含当前K线）\\r\\n\\r\\n例：\\r\\n1、BARSSK>10,BP;//上一次卖开仓（不包含出现买开信号的那根K线）距离当前K线的周期数大于10，买平；\\r\\n2、LLV(L,BARSSK+1);//上一次卖开仓（包含开仓信号出现的当根k线）到当前的最低价的最小值。\\r\\n当根K线出现SK信号，AA返回为空值，如果需要返回当根K线上最低价，模型需要修改为:\\r\\nAA:IFELSE(BARSSK>=1,LLV(L,BARSSK+1),L);\\r\\n（1）当根K线出现SK信号，BARSSK返回为空值，不满足BARSSK>=1的条件，则取值为当根K线的最低价L\\r\\n（2）发出SK信号之后K线SARSBK返回卖开仓的K线距离当前K线的周期数，满足BARSSK>=1的条件，则取值为LLV(L,BARSSK+1)，即卖开仓（包含开仓信号出现的当根k线）到当前的最低价的最小值。\\r\\n修改后如果平仓条件中用到了AA的值，当根K线满足了平仓条件，可以出现平仓信号。\\r\\n3、AA:IFELSE(BARSSK>=1,REF(C,BARSSK),C);//取最近一次卖开仓K线的收盘价\\r\\n（1）发出SK信号的当根k线BARSSK返回空值,则当根K线不满足BARSSK>=1的条件，AA返回当根k线的收盘价；\\r\\n（2）发出SK信号之后的k线BARSSK返回卖开仓的K线距离当前K线的周期数，则AA返回REF(C,BARSSK)，即开仓k线的收盘价；\\r\\n（3）例：1、2、3三根k线，1K线为开仓信号的当根k线，则返回当根k线的收盘价，2、3K线AA返回值为1K线的收盘价。", "explanation": "上一次卖开信号位置", "markettype": 1, "modifytime": "", "param": "", "tip": "BARSSK，取上一次卖开信号位置", "type": 10}, "BARSSP": {"body": "BARSSP", "createtime": "20140918", "description": "BARSSP 上一次卖平信号位置\\r\\n\\r\\n用法：\\r\\nBARSSP返回上一次卖平仓的K线距离当前K线的周期数（不包含出现SP信号的那根K线）\\r\\n取包含SP信号出现的那根K线到当前K线的周期数，则需要在此函数后+1，即BARSSP+1。由于发出SP信号的当根k线BARSSP返回空值，则BARSSP+1在发出SP信号当根k线返回空值。\\r\\n\\r\\n注：\\r\\n1、若当前K线之前无SP信号，则函数返回值为空值\\r\\n2、SP信号固定后BARSSP返回为空值。\\r\\n（1）设置信号执行方式为K线走完确认信号下单\\r\\nBARSBP返回值为上一个BP信号距离当前的K线根数（包含当前K线）\\r\\n（2）设置信号执行方式为出信号立即下单，不复核（例如：在模型中写入MULTSIG或MULTSIG_MIN;）\\r\\n    a.历史信号计算中，出现SP信号当根K线，BARSSP返回空值\\r\\n    b.加载运行过程中，SP信号当根K线,信号固定后BARSSP返回空值\\r\\n（3）设置信号执行方式为K线走完复核（例如：在模型中写入CHECKSIG(SP,'A',N,'D',0,0);）\\r\\nBARSSP返回值为上一个SP信号距离当前的K线根数（包含当前K线）\\r\\n\\r\\n例：\\r\\n1、BARSSP>10,BK;//上一次卖平仓（不包含出现卖平信号的那根K线）距离当前K线的周期数大于10，买开。\\r\\n2、AA:HHV(H,BARSSP+1);//上一次，卖平仓（包含平仓信号出现的当根k线）到当前的最高价的最大值。 \\r\\n当根K线出现SP信号，AA返回为空值，如果需要返回当根K线上最高价，模型需要修改为:\\r\\nAA:IFELSE(BARSSP>=1,HHV(H,BARSSP+1),H); \\r\\n（1）当根K线出现SP信号，BARSSP返回为空值，不满足BARSSP>=1的条件，则取值为当根K线的最高价H \\r\\n（2）发出SP信号之后K线BARSSP返回买平仓的K线距离当前K线的周期数，满足BARSSP>=1的条件，则取值为HHV(H,BARSSP+1)，即卖平仓（包含平仓信号出现的当根k线）到当前的最高价的最大值。\\r\\n3、AA:IFELSE(BARSSP>=1,REF(C,BARSSP),C);//取最近一次卖平仓K线的收盘价 \\r\\n（1）发出SP信号的当根k线BARSSP返回空值,则当根K线不满足BARSSP>=1的条件，AA返回当根k线的收盘价；\\r\\n（2）发出SP信号之后的k线BARSSP返回卖平仓的K线距离当前K线的周期数，则AA返回REF(C,BARSSP)，即平仓k线的收盘价；\\r\\n（3）1、2、3三根k线，1 K线为平仓信号的当根k线，则返回当根k线的收盘价，2、3 K线AA返回值为 1 K线的收盘价", "explanation": "上一次卖平信号位置", "markettype": 1, "modifytime": "", "param": "", "tip": "BARSSP，取上一次卖平信号位置", "type": 10}, "BARSTATUS": {"body": "BARSTATUS", "createtime": "", "description": "BARSTATUS 返回当前周期的位置状态。\\r\\n\\r\\n注：\\r\\n1、该函数返回1表示当前周期是第一个周期，返回2表示是最后一个周期，返回0表示当前周期处于中间位置\\r\\n2、该函数仅支持在绘图函数中使用。\\r\\n\\r\\n例:\\r\\nDRAWNUMBER(BARSTATUS=1,HIGH,OPEN,0,COLORRED);//如果当前K线是第一个周期，则在最高价位置红色显示开盘价。", "explanation": "返回当前周期的位置状态", "markettype": 0, "modifytime": "", "param": "", "tip": "BARSTATUS返回当前周期的位置状态。1表示当前周期是第一个周期，2表示是最后一个周期，0表示当前周期处于中间位置。", "type": 8}, "BARTYPE": {"body": "BARTYPE", "createtime": "20200603", "description": "BARTYPE 数据合约的K线周期类型值\\r\\n\\r\\n用法：\\r\\n返回值如下：\\r\\n0 日周期\\r\\n1 分钟周期\\r\\n2 小时周期  \\r\\n3 量能周期\\r\\n4 周线\\r\\n5 月线\\r\\n6 季线\\r\\n7 年线\\r\\n8 秒周期\\r\\n\\r\\n例：\\r\\nBARTYPE;\\r\\nBARINTERVAL;", "explanation": "数据合约的K线周期类型值", "markettype": 0, "modifytime": "", "param": "", "tip": "BARTYPE数据合约的K线周期类型值。", "type": 1}, "BEGIN": "", "BETWEEN": {"body": "BETWEEN( , , )", "createtime": "", "description": "BETWEEN(X,Y,Z) 表示X是否处于Y和Z之间，成立返回1(Yes)，否则返回0(No)。\\r\\n\\r\\n注：\\r\\n1、其中若X=Y、X=Z、或X=Y且Y=Z时函数返回值为1(Yse)。\\r\\n\\r\\n例1：\\r\\nBETWEEN(CLOSE,MA5,MA10); //表示收盘价介于5日均线与10日均线之间。", "explanation": "介于", "markettype": 0, "modifytime": "", "param": "", "tip": "BETWEEN(A,B,C),A处于B和C之间时取1(Yes)，否则取0(No)", "type": 5}, "BK": "BK 买开仓", "BKHIGH": {"body": "BKHIGH", "createtime": "20140718", "description": "返回数据合约买开仓以来的最高价\\r\\n用法：\\r\\nBKHIGH返回数据合约最近一次模型买开位置到当前的最高价。\\r\\n1、不同信号执行方式，其返回值分别为：\\r\\n（1）信号执行方式为K线走完确认信号下单\\r\\na.历史信号计算中，BK(BPK)信号之后的K线返回委托以来的数据合约行情的最高价\\r\\nb.加载运行过程中，BK(BPK)信号当根K线返回上个BK(BPK)信号发出以来的数据合约行情最高价，BK之后的K线返回委托以来的数据合约行情最高价\\r\\n（2）信号执行方式选择K线走完复核（例如：在模型中写入CHECKSIG(BK,'A',0,'D',0,0);）\\r\\n从BK(BPK)信号发出时开始统计数据合约行情的最高价；信号消失，返回上次买开以来的数据合约行情的最高价，信号确认存在，返回当根K线记录的数据合约行情的最高价\\r\\n注：BK信号发出后，中间出了信号消失，从最后一次信号出现开始统计数据合约最高价\\r\\n（3）信号执行方式选择不进行信号复核（例如：在模型中写入MULTSIG或MULTSIG_MIN;）\\r\\nBK(BPK)信号的当根K线返回从信号发出到K线走完时数据合约行情的最高价；BK(BPK)信号之后的K线返回信号发出以来数据合约行情的最高价。\\r\\n2、主连合约使用换月移仓函数，主力合约切换后，从新的主力合约第一根K线开盘价重新开始统计\\r\\n3、模组重新初始化后，数据合约和交易合约相同，则BKGIGH返回初始化后的最高价与初始化弹出框中填入的持仓价相比较后较大的数值；数据合约与交易合约不同时，则BKHIGH返回初始化后的最高价与初始化弹出框中填入的数据合约信号价相比较后较大的数值。\\r\\n\\r\\n例：\\r\\nC>O,BK;\\r\\nC>BKPRICE&&C<BKHIGH-5*MINPRICE,SP;\\r\\nAUTOFILTER;//最新价低于买开仓以来的数据合约最高价5个点，止盈平仓。", "explanation": "返回数据合约买开仓以来的最高价", "markettype": 1, "modifytime": "20220525", "param": "", "tip": "BKHIGH,返回数据合约买开仓以来的最高价", "type": 10}, "BKLOW": {"body": "BKLOW", "createtime": "20140718", "description": "返回数据合约买开仓以来的最低价\\r\\n用法：\\r\\nBKLOW返回数据合约最近一次模型买开位置到当前的最低价。\\r\\n1、不同信号执行方式，其返回值分别为：\\r\\n（1）K线走完确认信号下单\\r\\na.历史信号计算中，BK(BPK)信号之后的K线返回委托以来的数据合约行情的最低价\\r\\nb.加载运行过程中，BK(BPK)信号当根K线返回上个BK(BPK)信号发出以来的数据合约行情最低价，BK之后的K线返回委托以来的数据合约行情最低价\\r\\n（2）信号执行方式选择K线走完复核（例如：在模型中写入CHECKSIG(BK,'A',0,'D',0,0);）\\r\\n从BK(BPK)信号发出时行情时开始统计数据合约行情的最低价；信号消失，返回上次买开以来的数据合约行情的最低价，信号确认存在，返回当根K线记录的数据合约行情的最低价\\r\\n注：BK信号发出后，中间出了信号消失，从最后一次信号出现开始统计数据合约最低价\\r\\n（3）信号执行方式选择不进行信号复核（例如：在模型中写入MULTSIG或MULTSIG_MIN;）\\r\\nBK(BPK)信号的当根K线返回的从信号发出到K线走完时数据合约行情的最低价；BK(BPK)信号之后的K线返回信号发出以来数据合约行情的最低价。\\r\\n2、主连合约使用换月移仓函数，主力合约切换后，从新的主力合约第一根K线开盘价重新开始统计\\r\\n3、模组重新初始化后，数据合约和交易合约相同，则BKLOW返回初始化后的最低价与初始化弹出框中填入的持仓价相比较后较小的数值；数据合约与交易合约不同时，则BKLOW返回初始化后的最低价与初始化弹出框中填入的数据合约信号价相比较后较小的数值。\\r\\n\\r\\n例：\\r\\nC>O,BK;\\r\\nC>BKLOW+5*MINPRICE,SP;\\r\\nAUTOFILTER;//最新价高于买开仓以来数据合约的最低价5个点，平仓。", "explanation": "返回数据合约买开仓以来的最低价", "markettype": 1, "modifytime": "20220525", "param": "", "tip": "BKLOW,返回数据合约买开仓以来的最低价", "type": 10}, "BKPRICE": {"body": "BKPRICE", "createtime": "20140718", "description": "BKPRICE 返回数据合约最近一次买开信号价位。\\r\\n\\r\\n用法：\\r\\nBKPRICE 返回数据合约最近一次买开信号发出时的行情的最新价。\\r\\n\\r\\n注：\\r\\n1、当数据合约和交易合约相同时BKPRICE值和BKPRICE1值相等。\\r\\n2、当模型存在连续多个开仓信号(加仓)的情况下，该函数返回的是最近一次开仓信号的价格,而不是开仓均价。\\r\\n3、不同信号执行方式，其返回值分别为：\\r\\n（1）信号执行方式为不进行信号复核\\r\\na.历史回测：BKPRICE返回信号发出时的数据合约行情最新价\\r\\nb.模组运行：BKPRICE返回信号发出时的数据合约行情最新价\\r\\n（2）信号执行方式选择K线走完确认信号下单\\r\\na.历史回测：BKPRICE返回信号发出时数据合约当根K线的收盘价\\r\\nb.模组运行：BKPRICE返回信号发出时数据合约当根K线的收盘价\\r\\n（3）信号执行方式设置为K线走完进行信号复核\\r\\na.历史回测：BKPRICE返回信号发出时数据合约当根K线的收盘价\\r\\nb.模组运行：复核前，返回上一次BK信号当根K线数据合约的行情最新价；复核后，返回本次BK信号当根K线数据合约的行情最新价\\r\\n4、模组头寸同步后，BKPRICE的值不变，仍然返回上一次买开信号时数据合约行情的最新价。\\r\\n5、模组重新初始化后，数据合约和交易合约相同，则BKPRICE返回为初始化弹出框中填入的持仓价格；数据合约与交易合约不同时，则BKPRICE返回初始化弹出框中填入的数据合约信号价。\\r\\n6、加载在主连合约上，使用了换月移仓函数，主力换月后BKPRCIE取值为新的主力合约的第一根K线的开盘价\\r\\n\\r\\n例:\\r\\nBKPRICE-CLOSE>60 && BKPRICE>0 && BKVOL>0, SP;//如果买开价位比当前价位高出60,且多头持仓存在，卖平仓。", "explanation": "返回数据合约最近一次买开信号价位", "markettype": 1, "modifytime": "", "param": "", "tip": "BKPRICE，返回数据合约最近一次买开信号价位", "type": 10}, "BKPRICE1": {"body": "BKPRICE1", "createtime": "", "description": "BKPRICE1 返回交易合约最近一次买开信号价位。\\r\\n\\r\\n用法：\\r\\nBKPRICE1：返回交易合约最近一次买开信号发出时的行情的最新价。\\r\\n\\r\\n注：\\r\\n1、当数据合约和交易合约相同时BKPRICE值和BKPRICE1值相等。\\r\\n2、当数据合约和交易合约不同时，不同信号执行方式，其返回值分别为：\\r\\n（1）信号执行方式为不进行信号复核\\r\\na.历史回测：BKPRICE1返回信号发出时的交易合约行情最新价\\r\\nb.模组运行：BKPRICE1返回信号发出时的交易合约行情最新价\\r\\n（2）信号执行方式选择K线走完确认信号下单\\r\\na.历史回测：BKPRICE1返回信号发出时交易合约当根K线的收盘价\\r\\nb.模组运行：BKPRICE1返回信号发出时交易合约当根K线的收盘价\\r\\n（3）信号执行方式设置为K线走完进行信号复核\\r\\na.历史回测：BKPRICE1返回信号发出时交易合约当根K线的收盘价\\r\\nb.模组运行：复核前，返回上一次BK信号当根K线交易合约的行情最新价；复核后，返回本次BK信号当根K线交易合约的行情最新价\\r\\n3、模组头寸同步后，BKPRICE1的值不变，仍然返回上一次买开信号时交易合约行情的最新价；模组重新初始化后，BKPRICE1返回为初始化弹出框中填入的持仓价格。\\r\\n4、加载在加权/主连合约上，使用了换月移仓函数，主力换月后BKPRCIE1取值为新的主力合约的第一根K线的开盘价", "explanation": "返回交易合约最近一次买开信号价位", "markettype": 1, "modifytime": "", "param": "", "tip": "BKPRICE1，返回交易合约最近一次买开信号价位", "type": 10}, "BKPRICEAV": {"body": "BKPRICEAV", "createtime": "20151214", "description": "BKPRICEAV 返回数据合约多头开仓均价。\\r\\n\\r\\n用法：\\r\\nBKPRICEAV 返回数据合约多头开仓均价。\\r\\n\\r\\n注：\\r\\n1、过滤模型：\\r\\n（1）开仓信号后，未出平仓信号时：BKPRICEAV取值和BKPRICE取值相同。\\r\\n（2）平仓信号后：BKPRICEAV返回值为0。\\r\\n2、加减仓模型：\\r\\n（1）持仓不为0时：BKPRICEAV返回数据合约理论持仓的开仓均价。\\r\\n（2）加减仓模型持仓为0时：BKPRICEAV返回值为0。\\r\\n3、该函数在模组运行和回测中都读取的是模组理论持仓的开仓均价，非实际持仓开仓均价。\\r\\n4、模组重新初始化后，数据合约和交易合约相同，则BKPRICEAV计算取初始化弹出框中填入的持仓价格；数据合约与交易合约不同时，则BKPRICEAV计算取初始化弹出框中填入的数据合约信号价。\\r\\n\\r\\n注：\\r\\n该函数的计算考虑滑点。\\r\\n\\r\\n例：\\r\\nCLOSE-BKPRICEAV>60,SP(BKVOL);//当前价位比多头开仓均价高出60,平掉所有多头持仓", "explanation": "返回数据合约多头开仓均价", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "BKPRICEAV返回数据合约多头开仓均价", "type": 12}, "BKPRICEAV1": {"body": "BKPRICEAV1", "createtime": "20160419", "description": "BKPRICEAV1 返回交易合约多头开仓均价\\r\\n\\r\\n用法：\\r\\nBKPRICEAV1 返回交易合约多头开仓均价\\r\\n\\r\\n注：\\r\\n1、当模型存在连续多个开仓信号(加仓)的情况下，该函数返回的是交易合约开仓均价。\\r\\n2、当数据合约和交易合约相同时BKPRICEAV值和BKPRICEAV1值相等。\\r\\n3、过滤模型：\\r\\n（1）开仓信号后，未出平仓信号时：BKPRICEAV1取值和BKPRICE1取值相同。\\r\\n（2）平仓信号后：BKPRICEAV1返回值为0。\\r\\n4、加减仓模型：\\r\\n（1）持仓不为0时：BKPRICEAV1返回交易合约理论持仓的开仓均价。\\r\\n（2）加减仓模型持仓为0时：BKPRICEAV返回值为0。\\r\\n\\r\\n注：\\r\\n该函数的计算考虑滑点。\\r\\n\\r\\n例：\\r\\nCLOSE-BKPRICEAV1>60,SP(BKVOL);//当前价位比交易合约多头开仓均价高出60,平掉所有多头持仓", "explanation": "返回交易合约多头开仓均价", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "BKPRICEAV1交易合约多头开仓均价", "type": 12}, "BKVOL": {"body": "BKVOL", "createtime": "20140918", "description": "买开信号手数\\r\\n用法：\\r\\nBKVOL返回模型当前的多头理论持仓。\\r\\n1、加载运行：\\r\\n（1）模组初始化后，BKVOL仍然返回根据信号下单手数计算的理论持仓，不受账户持仓的影响。\\r\\n（2）模组运行中手动调仓，头寸同步修改持仓，BKVOL返回值不变，仍然返回根据信号下单手数计算的理论持仓。\\r\\n（3）页面盒子运行中，BKVOL不受资金情况的限制，按照信号显示开仓手数。\\r\\n2、回测、模组运行中：\\r\\n（1）如果资金不够开仓，开仓手数为0，BKVOL返回值为0。\\r\\n（2）BK（BPK）信号出现并且确认固定后，BKVOL的取值增加开仓手数的数值；SP（SPK）信号出现并且确认固定后，BKVOL的取值减少平仓手数的数值。\\r\\n\\r\\n例：\\r\\nBKVOL=0&&C>O,BK(1);//多头理论持仓为0并且收盘价大于开盘价时，买开一手\\r\\nBKVOL>=1&&H>HV(H,5),BK(2); //多头持仓大于等于1，并且当根K线的最高价大于前面5个周期中最高价中最大值时，加仓2手\\r\\nBKVOL>0&&L<REF(L,5),SP(BKVOL); //多头持仓大于0，并且当根K线的最低价小于5个周期前K线的最低价时，卖平所有多头持仓", "explanation": "买开信号手数", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "BKVOL返回模型当前的多头理论持仓", "type": 12}, "BKVOL2": {"body": "BKVOL2", "createtime": "20150513", "description": "买开信号手数\\r\\n用法：\\r\\nBKVOL2返回模型当前的多头持仓。\\r\\n1、加载运行：\\r\\n（1）模组初始化后，BKVOL2返回的理论持仓仍然延续，返回模型信号手数，不受账户持仓的影响。\\r\\n（2）页面盒子和模组加载中，BKVOL2不受资金情况的限制，按照信号显示开仓手数。\\r\\n（3）模组运行过程中BK（BPK）信号出现并且确认固定后，BKVOL2的取值增加开仓手数的数值；SP（SPK）信号出现并且确认固定后，BKVOL2的取值减少平仓手数的数值。\\r\\n2、回测：\\r\\n（1）BKVOL2不受资金情况的限制，按照信号显示开仓手数。\\r\\n（2）BK（BPK）信号出现并且确认固定后，BKVOL2的取值增加开仓手数的数值；SP（SPK）信号出现并且确认固定后，BKVOL2的取值减少平仓手数的数值。\\r\\n\\r\\n例：\\r\\nBKVOL2=0&&C>O,BK(1);//多头持仓为0并且收盘价大于开盘价时，买开一手\\r\\nBKVOL2>=1&&H>HV(H,5),BK(2); //多头持仓大于等于1，并且当根K线的最高价大于前面5个周期中最高价中最大值时，加仓2手\\r\\nBKVOL2>0&&L<REF(L,5),SP(BKVOL2); //多头持仓大于0，并且当根K线的最低价小于5个周期前K线的最低价时，卖平所有多头持仓", "explanation": "买开信号手数", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "BKVOL2模组多头持仓", "type": 12}, "BOLD": "BOLD,加粗", "BP": "BP 买平仓", "BPK": "BPK 买平后买开新仓", "BUY": "BUY 买入", "BUYPRICE": {"body": "BUYPRICE", "createtime": "20221201", "description": "BUYPRICE 返回最近一次买入信号所在K线的收盘价。\\r\\n\\r\\n用法：\\r\\nBUYPRICE 返回最近一次买入信号所在K线的收盘价。\\r\\n\\r\\n注：\\r\\n1、该函数支持在股票T+1策略运行池中使用。\\r\\n\\r\\n例:\\r\\nBUYPRICE-CLOSE>60 && BUYPRICE>0 && BUYVOL>0, SELL;//如果买入价位比当前价位高出60,且多头持仓存在，卖出持仓。", "explanation": "返回最近一次买入信号所在K线的收盘价。", "markettype": 2, "modifytime": "20221205", "param": "", "tip": "BUYPRICE  返回最近一次买入信号所在K线的收盘价。", "type": 10}, "C": "C 收盘价", "CANCEL_ORDER": "CANCEL_ORDER 启用自动撤单并终止下单", "CEILING": {"body": "CEILING( , )", "createtime": "", "description": "CEILING(X,Y) 返回指定实数(X)在沿绝对值增大的方向上第一个能整除基数(Y)的值。\\r\\n\\r\\n注：\\r\\n1、如果X和Y符号相同，则对值按远离0的方向进行舍入。\\r\\n2、X和Y符号不同的情况下：\\r\\n（1）如果X为负，Y为正，则对值按朝向0的方向进行向上舍入。\\r\\n（2）如果X为正，Y为负，CEILING返回无效值。\\r\\n3、X、Y均可以为变量。\\r\\n4、若X、Y中任意一个为空值，则该函数返回空值。\\r\\n\\r\\n例1：\\r\\nCEILING(2.1,1);//求得3。\\r\\n例2：\\r\\nCEILING(-8.8,-2);//求得-10。\\r\\n例3：\\r\\nCEILING(CLOSE*1.01,1);//求收盘价的1.01倍向上取整\\r\\n例4：\\r\\nCEILING(-7,2);//求得-6。\\r\\n例5：\\r\\nCEILING(8,-2);//返回无效值。", "explanation": "向上舍入", "markettype": 0, "modifytime": "", "param": "", "tip": "CEILING(X,Y)返回指定实数(X)在沿绝对值增大的方向上第一个能整除基数(Y)的值。", "type": 4}, "CHECKSIG": {"body": "CHECKSIG( , , , , ,)", "createtime": "20150109", "description": "CHECKSIG 设置信号确认与复核的指令价方式（TICK逐笔回测，可设置回测精度）\\r\\n\\r\\n用法：\\r\\nCHECKSIG(SIG,MODE1,TIME1,MODE2,TIME2,INTERVAL);\\r\\n1、当INTERVAL不为0时，INTERVAL数据时间间隔，每隔INTERVAL秒计算一次信号，SIG为信号,MODE1为信号确认方式,TIME1信号确认时间乘数,MODE2信号复核方式,TIME2信号复核时间乘数。\\r\\n（例：INTERVAL为10，豆粕合约开盘第一根K线21：00：09为第一次计算模型，21：00：19为第二次计算模型...）\\r\\n2、当INTERVAL为0时，每笔TICK计算一次信号，SIG为信号,MODE1为信号确认方式,TIME1信号确认时间,MODE2信号复核方式,TIME2信号复核时间。\\r\\n3、通过调整INTERVAL参数，模型可设置不同数据快照频率进行回测。\\r\\n\\r\\n注：\\r\\n1、该函数支持在期货月份合约和股票上运行。\\r\\n2、写了这个函数以后，模型会按照指令价方式运行。\\r\\n3、SIG位置为交易指令，交易指令包括BK\\SK\\BP\\SP\\BPK\\SPK。\\r\\n4、MODE1位置为信号确认方式，有A和B两种：\\r\\nA：MODE1为'A'时\\r\\n  1）当INTERVAL不为0时，出信号后第TIME1个数据时间间隔确认信号下单\\r\\n  2）当INTERVAL为0时，出信号TIME1秒后确认信号下单\\r\\nB：MODE1为'B'时\\r\\n  1）当INTERVAL不为0时，K线走完前TIME1个时间间隔确认信号下单\\r\\n  2）当INTERVAL为0时，K线走完前TIME1秒确认信号下单\\r\\n  3）TIME1=0为K线走完确认信号下单\\r\\n  4）TIME1不为0时，该函数不支持加载到日线以上的周期中使用\\r\\n5、MODE2位置为信号复核方式，有C，D，E和F四种：\\r\\nC：MODE2为'C'时\\r\\n  1）当INTERVAL不为0时，下单后第TIME2个数据时间间隔进行信号复核\\r\\n  2）当INTERVAL为0时，下单TIME2秒后进行信号复核，TIME2=0为不复核\\r\\nD：MODE2为'D'时\\r\\n  1）当INTERVAL不为0时，K线走完前TIME2个时间间隔进行信号复核\\r\\n  2）当INTERVAL为0时，K线走完前TIME2秒进行信号复核\\r\\n  3）TIME2=0为K线走完复核\\r\\n  4）TIME2不为0时，该函数不支持加载到日线以上的周期中使用\\r\\nE：每一个以小节为结束时间的K线提前复核，其他非小节时间结束的K线为K线走完复核。(小节包括：商品合约10:15-10:30休盘、11:30-13:30休市、21:00-23:00(或23:30或1:00或2:30)期间夜盘小节、收盘前最后一根k线；股指合约11:30-13:00休市以及收盘前最后一根k线)\\r\\n  1）当INTERVAL不为0时，提前TIME2个时间间隔进行信号复核\\r\\n  2）当INTERVAL为0时，提前TIME2秒进行信号复核\\r\\n  3）TIME2=0为K线走完复核\\r\\n  4）TIME2不为0时，该函数不支持加载到日线以上的周期中使用\\r\\nF：每天以收盘时间为结束时间的K线提前复核，其他不以收盘时间为结束时间的K线为K线走完复核\\r\\n  1）当INTERVAL不为0时，提前TIME2个时间间隔进行信号复核\\r\\n  2）当INTERVAL为0时，提前TIME2秒进行信号复核\\r\\n  3）TIME2=0为K线走完复核\\r\\n  4）TIME2不为0时，该函数不支持加载到日线以上的周期中使用\\r\\n6、INTERVAL代表数据时间间隔\\r\\n  1）支持0、3、5、10四个值，不支持变量。\\r\\n  2）3、5、10分别代表用每隔3秒、5秒、10秒，计算一次模型\\r\\n  3）参数为3、5、10 ，回测速度可提升3-10倍，回测精度稍差\\r\\n  4）参数为0的时 为每笔TICK计算一次模型\\r\\n  5）一个模型中只能写入一个INTERVAL值\\r\\n7、模型中写入该函数，一根K线只能有一个信号。\\r\\n8、CHECKSIG、MULTSIG、MULTSIG_MIN、CHECKSIG_MIN函数不能同时出现在一个模型中\\r\\n9、该函数只允许在模组中使用，不支持加载到盒子。\\r\\n10、未使用该函数的指令，默认的信号执行方式为K线走完确认信号下单。\\r\\n11、该函数不支持加载到量能周期使用,例如：量能周期出信号TIME1个数据时间间隔下单， K线走完前TIME2个数据时间间隔复核之类的都不支持\\r\\n12、如果用该函数设置了信号复核，复核时产生了信号消失，会进行信号消失处理。信号消失的处理方式：\\r\\n还没有成交时的信号消失处理-撤单\\r\\nBK、SK信号消失处理-平仓\\r\\nBPK、SPK信号消失处理-平仓+恢复建仓\\r\\nBP、SP信号消失处理-恢复建仓\\r\\n13、不支持与TRADE_OTHER、#CALL、#CALL_OTHER、#CALL_PLUS函数同时使用。\\r\\n\\r\\n例：\\r\\nC>O,BK;\\r\\nC<O,SP;\\r\\nCHECKSIG(BK,'A',5,'D',0,3);//设置BK信号，出信号后第5个数据时间间隔确认下单（例如21:00:02满足条件，出现信号后第5次计算信号，即21:00：17时确认信号下单），K线走完复核。每隔3秒计算一次信号。\\r\\nCHECKSIG(SP,'A',0,'C',10,3);//设置SP信号，根据数据时间间隔计算出信号后立即下单（例如21:00:02满足条件，出现信号后立即下单），下单后第10个数据时间间隔复核（例如21:00：17时确认信号下单，则确认下单后第10次计算模型，即21:00:47进行信号复核）。每隔3秒计算一次信号。\\r\\nAUTOFILTER;", "explanation": "设置信号确认与复核的指令价方式（TICK逐笔回测，可设置回测精度）", "markettype": 1, "modifytime": "20240325", "param": "", "tip": "CHECKSIG(SIG,MODE1,TIME1,MODE2,TIME2,INTERVAL);设置信号确认与复核的指令价方式（TICK逐笔回测，可设置回测精度）SIG为信号,MODE1为下单方式,TIME1下单确认时间,MODE2复核方式,TIME2复核确认时间,INTERVAL代表数据时间间隔", "type": 13}, "CHECKSIG_MIN": {"body": "CHECKSIG_MIN( , , , ,)", "createtime": "20141125", "description": "CHECKSIG_MIN 设置信号确认与复核的指令价方式（逐分钟回测）\\r\\n\\r\\n用法：\\r\\nCHECKSIG_MIN(SIG,MODE1,TIME1,MODE2,TIME2);SIG为信号,MODE1为信号确认方式,TIME1信号确认时间,MODE2信号复核方式,TIME2信号复核时间。\\r\\n\\r\\n注：\\r\\n1、写了这个函数以后，模型会按照指令价方式运行。\\r\\n2、使用该函数时，基础数据为1分钟数据。(TIME1 TIME2不支持小数)\\r\\n3、该函数不支持加载在15分钟以下周期使用\\r\\n4、SIG位置为交易指令，交易指令包括BK\\SK\\BP\\SP\\BPK\\SPK。\\r\\n5、MODE1位置为信号确认方式，有A和B两种：\\r\\nA：出信号TIME1分钟确认信号下单。TIME1>0为出信号TIME1分钟确认信号下单，TIME1=0为出信号立即下单。\\r\\nB：K线走完前TIME1分钟确认信号下单。TIME1>0为K线走完前TIME1分钟确认信号下单(不支持加载在日线以上周期)，TIME1=0为K线走完确认信号下单\\r\\n6、MODE2位置为信号复核方式，有C，D，E和F四种：\\r\\nC：下单后TIME2分钟进行信号复核。TIME2>0为下单后TIME2分钟进行信号复核，TIME2=0为不复核。\\r\\nD：K线走完前TIME2分钟进行信号复核。TIME2>0为K线走完前TIME2分钟进行信号复核(不支持加载在日线以上周期)，TIME2=0为K线走完复核。\\r\\nE：每一个以小节为结束时间的K线提前复核。(小节包括：商品合约10:15-10:30休盘、11:30-13:30休市、21:00-23:00(或23:30或1:00或2:30)期间夜盘小节、收盘前最后一根k线；股指合约11:30-13:00休市以及收盘前最后一根k线)TIME2>0为每一个以小节为结束时间的K线提前TIME2分钟进行信号复核(不支持加载在日线以上周期)，TIME2=0为K线走完复核。其他非小节时间结束的K线为K线走完复核。\\r\\nF：以收盘时间为结束时间的K线为提前TIME2分钟复核。TIME2>0为以收盘时间为结束时间的K线提前TIME2分钟进行信号复核(不支持加载在日线以上周期)，TIME2=0为K线走完复核。其他非收盘时间结束的K线为K线走完复核。\\r\\n7、模型中写入该函数，一根K线只能有一个信号。\\r\\n8、MULTSIG、MULTSIG_MIN、CHECKSIG和CHECKSIG_MIN函数不能同时出现在一个模型中\\r\\n9、该函数只允许在模组中使用，不支持加载到盒子。\\r\\n10、未使用该函数的指令，默认的信号执行方式为K线走完确认信号下单。\\r\\n11、参数TIME1、TIME2非0时，该函数不支持加载到日线以上的周期中使用。\\r\\n12、如果用该函数设置了信号复核，复核时产生了信号消失，会进行信号消失处理。信号消失的处理方式：\\r\\n还没有成交时的信号消失处理-撤单\\r\\nBK、SK信号消失处理-平仓\\r\\nBPK、SPK信号消失处理-平仓+恢复建仓\\r\\nBP、SP信号消失处理-恢复建仓\\r\\n\\r\\n几种典型的信号复核确认方式对应的写法举例：\\r\\nCHECKSIG_MIN(SIG,'A',0,'D',0);//出信号立即下单，K线走完复核\\r\\nCHECKSIG_MIN(SIG,'A',N,'D',0);//出信号N分钟确认信号下单，K线走完复核\\r\\nCHECKSIG_MIN(SIG,'A',N,'C',0);//出信号N分钟确认信号下单，不进行复核\\r\\nCHECKSIG_MIN(SIG,'B',N,'D',0);//K线走完前N分钟确认信号下单，K线走完复核\\r\\nCHECKSIG_MIN(SIG,'B',N,'C',0);//K线走完前N分钟确认信号下单，不复核\\r\\nCHECKSIG_MIN(SIG,'B',0,'C',N);//K线走完确认信号下单\\r\\nCHECKSIG_MIN(SIG,'B',0,'D',0);//K线走完确认信号下单\\r\\nCHECKSIG_MIN(SIG,'A',0,'C',0);//出信号立即下单，不复核\\r\\nCHECKSIG_MIN(SIG,'A',0,'F',10);//出信号立即下单，收盘前最后一根K线提前10分钟进行复核。\\r\\n\\r\\n例：\\r\\nC>O,BK;\\r\\nC<O,SP;\\r\\nCHECKSIG_MIN(BK,'A',5,'D',0);//设置BK信号，出信号5分钟后确认下单，K线走完复核。\\r\\nCHECKSIG_MIN(SP,'A',0,'C',10);//设置SP信号，出信号立即下单，下单后10分钟复核。\\r\\nAUTOFILTER;", "explanation": "设置信号确认与复核的指令价方式（逐分钟回测）", "markettype": 1, "modifytime": "20220106", "param": "", "tip": "CHECKSIG_MIN(SIG,MODE1,TIME1,MODE2,TIME2);设置信号确认与复核的指令价方式（逐分钟回测）SIG为信号,MODE1为下单方式,TIME1下单确认时间,MODE2复核方式,TIME2复核确认时间", "type": 13}, "CIRCLEDOT": {"body": "CIRCLEDOT", "createtime": "********", "description": "画小圆点线。\\r\\n用法：\\r\\nCIRCLEDOT 画小圆点线。\\r\\n\\r\\n注：\\r\\n1、该函数支持设置颜色。\\r\\n2、不支持将函数定义为变量，即不支持下面的写法：A:CIRCLEDOT；\\r\\n\\r\\n例：MA5:MA(C,5),CIRCLEDOT,COLORCYAN;//用小圆点线画5周期均线，圆点线显示为青色。", "explanation": "小圆点线", "markettype": 0, "modifytime": "", "param": "", "tip": "", "type": 8}, "CJLVOL": {"body": "CJLVOL( )", "createtime": "********", "description": "CJLVOL(N) 绘制CJL成交量柱线\\r\\n\\r\\n用法：当参数N为零时，则相当于VOL,VOLUMESTICK;当参数N不为零时，成交量柱线的宽度和颜色和竹线保持一致。\\r\\n\\r\\n注：\\r\\n该函数用于系统CJL指标 \\r\\n\\r\\n例：\\r\\nCJLVOL(0);//绘制CJL成交量红绿柱线", "explanation": "绘制CJL成交量柱线", "markettype": 0, "modifytime": "", "param": "", "tip": "CJLVOL(N)，绘制CJL成交量柱线", "type": 8}, "CLOSE": {"body": "CLOSE", "createtime": "", "description": "CLOSE 取得K线图的收盘价。\\r\\n\\r\\n注：\\r\\n1、当盘中k线没有走完的时候，取得最新价。\\r\\n2、可简写为C。\\r\\n\\r\\n例1：\\r\\nA:CLOSE;//定义变量A为收盘价（盘中k线没有走完的时候A为最新价）。\\r\\n例2：\\r\\nMA5:=MA(C,5);//定义收盘价的5周期均线（C为CLOSE简写）。\\r\\n例3：\\r\\nA:=REF(C,1);//取得前一根k线的收盘价。", "explanation": "取得K线图的收盘价", "markettype": 0, "modifytime": "", "param": "", "tip": "CLOSE,取收盘(最新)价", "type": 1}, "CLOSEKLINE": {"body": "CLOSEKLINE( , )", "createtime": "20141218", "description": "CLOSEKLINE(TYPE,N) 设置K线走完前N秒，确认信号下单，K线走完进行复核，N的取值范围为1-30\\r\\n\\r\\n用法：\\r\\nTYPE=0，小节及交易日结束前N秒确认信号下单，其他时间K线走完确认信号下单\\r\\nTYPE=1，夜盘结束及日盘结束前N秒确认信号下单，其他时间K线走完确认信号下单\\r\\nTYPE=2，每一根K线提前N秒确认信号下单\\r\\n\\r\\n注：\\r\\n1、提前N秒下单不支持历史回测，仅支持盘中运行\\r\\n（1）历史回测：信号按照收盘价计算。\\r\\n（2）模组运行：历史信号按照收盘价计算；盘中运行根据TYPE设置提前N秒下单，K线走完复核。\\r\\n（3）盒子运行：历史信号按照收盘价计算；盘中运行根据TYPE设置提前N秒下单，信号消失不处理。\\r\\n2、K线走完复核机制说明\\r\\n（1）提前N秒有信号并确认下单，K线走完信号消失，下根K线开盘后做信号消失处理\\r\\n（2）提前N秒没有信号，K线走完时复核有信号，下根K线开盘立即下单\\r\\n3、CLOSEKLINE与CLOSEOUT同时使用的模型，不管参数怎么设置，均按照提前60秒下单执行。\\r\\n4、数据合约是股票指数，交易日结束前的最后一根k线提前时间为N秒+3分钟。\\r\\n5、如果设置小节提前下单，小节时间和k线走完时间重合时，此设置才起作用。例如：沪金合约，15分钟周期上，小节时间的信号提前N秒下单；但日线周期上，小节时不会提前下单。\\r\\n6、该函数不支持加载到量能周期和日线以上的周期中使用。\\r\\n7、该函数不支持与CHECKSIG/CHECKSIG_MIN/MULTSIG/MULTSIG_MIN一起使用。\\r\\n\\r\\n例：\\r\\nC>HV(H,4),BK;//价格大于前四个周期高点开多仓\\r\\nC<MA(C,5),SP;//价格小于5周期均线，平多仓\\r\\nCLOSEKLINE(1,9);//设置夜盘结束及日盘结束前9秒确认信号下单\\r\\nAUTOFILTER;", "explanation": "设置K线提前N(1-30)秒走完", "markettype": 1, "modifytime": "20240320", "param": "", "tip": "CLOSEKLINE(TYPE,N),设置K线提前N(1-30)秒走完TYPE=0，代表每小节和收盘前最后一根K线提前N秒走完；\r\nTYPE=1，代表夜盘结束及日盘结束前最后一根K线提前N秒走完；TYPE=2，代表每一根K线提前N秒走完；N是时间（秒数）", "type": 13}, "CLOSEMINUTE": {"body": "CLOSEMINUTE", "createtime": "20141031", "description": "CLOSEMINUTE，返回K线距离收盘前的分钟数。\\r\\n\\r\\n注：\\r\\n1、该函数只能用于收盘价模型，返回当根K线开始时间距离交易日结束的分钟数。\\r\\n2、该函数与CLOSEOUT指令同时使用时：\\r\\nK线走完前：返回K线当前时间距离交易日结束的分钟数；\\r\\n历史K线：返回K线结束时间距离交易日结束的分钟数。\\r\\n3、该函数需要在分钟，小时周期使用；不支持在TICK周期，秒周期，量能周期，日线及以上周期使用。\\r\\n4、该函数的返回值包含小结和午休的时间。\\r\\n5、CLOSEMINUTE返回的是交易所的时间，不是本机的时间。\\r\\n6、对于夜盘合约，夜盘收盘不是当日收盘，15点收盘才算作当日收盘。\\r\\n7、CLOSEMINUTE在合约最后交易日，返回实际收盘时间。\\r\\n8、该函数不支持和CLOSESEC同时使用。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5))&&CLOSEMINUTE>5,BK;//收盘价上穿五周期均线，开仓，收盘前5分钟内不开仓\\r\\nCLOSEMINUTE<=5,CLOSEOUT;//收盘前5分钟清仓\\r\\nAUTOFILTER;", "explanation": "距收盘前时间", "markettype": 0, "modifytime": "20230707", "param": "", "tip": "CLOSEMINUTE,返回K线距离闭市前的时间（单位：分钟），方便闭市前及时平仓", "type": 7}, "CLOSEMINUTE1": {"body": "CLOSEMINUTE1", "createtime": "20141031", "description": "CLOSEMINUTE1，返回距离收盘前的分钟数。\\r\\n\\r\\n注：\\r\\n1、该函数只能用于指令价模型。\\r\\n2、\\r\\n历史K线：该函数返回K线结束时间距离收盘的分钟数。\\r\\n盘中：该函数返回K线当前时间距离收盘的分钟数。\\r\\n3、该函数需要在分钟，小时，日线周期使用；不支持在TICK周期，秒周期，量能周期，周线及以上周期使用。\\r\\n4、该函数返回值包含小结和午休的时间。\\r\\n5、CLOSEMINUTE1返回的是交易所的时间，不是本机的时间。\\r\\n6、对于夜盘合约，夜盘收盘不是当日收盘，15点收盘才算作当日收盘。\\r\\n7、CLOSEMINUTE1在合约最后交易日，返回实际收盘时间。\\r\\n8、CLOSEMINUTE1加载到主力合约上，主力换月和合约最后交易日在同一天，则按照最后交易日的收盘时间计算，主力换月和合约最后交易日不在同一天，那么按照正常的非最后交易日进行计算。\\r\\n9、该函数不支持和CLOSESEC1同时使用。\\r\\n10、CLOSEMINUTE1与逐分钟回测函数CHECKSIG_MIN 、MULTSIG_MIN连用，想实现日内闭式前及时平仓，CLOSEMINUTE1的参数取值需大于1，即CLOSEMINUTE1需<=大于1 的值。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK;//最新价上穿五周期均线，买开\\r\\nMULTSIG(0,0,1,0);//使用TICK数据回测，出信号立即下单，不复核\\r\\nCLOSEMINUTE1<=1,CLOSEOUT;//收盘前1分钟，清仓\\r\\nAUTOFILTER;", "explanation": "距收盘前时间", "markettype": 1, "modifytime": "20230707", "param": "", "tip": "CLOSEMINUTE1,返回当前时间距离闭市前的时间（单位：分钟），方便闭市前及时平仓", "type": 7}, "CLOSEMINUTEEVERY": {"body": "CLOSEMINUTEEVERY()", "createtime": "20160927", "description": "CLOSEMINUTEEVERY(N)，返回K线距离小节结束的分钟数。\\r\\n\\r\\n用法：\\r\\nCLOSEMINUTEEVERY(N),返回距离第N个小节结束的分钟数。N为参数，表示第几个小节（交易时间段）\\r\\n以沪金合约为例。N=1,第一小节为21：00到次日2点30;N=2，第二小节为上午9:00-10：15；N=3,第三小节为上午10：30到11:30；N=4,第四小节为13:00-15:00\\r\\n\\r\\n注：\\r\\n1、该函数只能用于收盘价模型，返回当根K线开始时间距离小节结束的分钟数。\\r\\n2、该函数与CLOSEOUT指令同时使用时：\\r\\nK线走完前：返回K线当前时间距离小节结束的分钟数。\\r\\n历史K线：返回K线结束时间距离小节结束的分钟数。\\r\\n3、该函数需要在分钟，小时周期使用；不支持在TICK周期，秒周期，量能周期，日线及以上周期使用。\\r\\n4、该函数的返回值包含小结和午休的时间。\\r\\n5、CLOSEMINUTEEVERY返回的是交易所的时间，不是本机的时间。\\r\\n6、对于夜盘合约，第一个小节时间指加载合约的夜盘交易时间段。\\r\\n7、该函数不支持在跨周期、跨合约中作为被引用指标使用。\\r\\n8、该函数不支持和CLOSESECEVERY同时使用。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK;//收盘价上穿五周期均线，开仓\\r\\nCLOSEMINUTEEVERY(1)<=10,CLOSEOUT;//第一个小节结束前10分钟，清仓\\r\\nAUTOFILTER;", "explanation": "距小节结束时间", "markettype": 1, "modifytime": "20220119", "param": "", "tip": "CLOSEMINUTEEVERY(N),返回K线距离每个小节结束的时间（单位：分钟），N为第几个小节，方便小节前及时平仓", "type": 7}, "CLOSEMINUTEEVERY1": {"body": "CLOSEMINUTEEVERY1()", "createtime": "20160927", "description": "CLOSEMINUTEEVERY1(N),返回距离小节结束的分钟数\\r\\n\\r\\n用法：\\r\\nCLOSEMINUTEEVERY1(N),返回距离第N个小节结束的分钟数。N为参数，表示第几个小节（交易时间段）\\r\\n以沪金合约为例。N=1,第一小节为21：00到次日2点30;N=2，第二小节为上午9:00-10：15；N=3,第三小节为上午10：30到11:30；N=4,第四小节为13:00-15:00\\r\\n\\r\\n注：\\r\\n1、该函数只能用于指令价模型。\\r\\n2、\\r\\n历史K线：该函数返回K线结束时间距离小节结束的分钟数。\\r\\n盘中：该函数返回K线当前时间距离小节结束的分钟数。\\r\\n3、该函数需要在分钟，小时，日线周期使用；不支持在TICK周期，秒周期，量能周期，周线及以上周期使用。\\r\\n4、CLOSEMINUTEEVERY1返回的是交易所的时间，不是本机的时间。\\r\\n5、对于夜盘合约，第一个小节时间指加载合约的夜盘交易时间段\\r\\n6、该函数不支持和CLOSESECEVERY1同时使用。\\r\\n7、CLOSEMINUTEEVERY1与逐分钟回测函数CHECKSIG_MIN 、MULTSIG_MIN连用，想实现小节结束前及时平仓，CLOSEMINUTEEVERY1的取值需大于1，即CLOSEMINUTEEVERY1(N)需<=大于1 的值。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK;//最新价上穿五周期均线，买开\\r\\nMULTSIG(0,0,1,0);//使用TICK数据回测，出信号立即下单，不复核\\r\\nCLOSEMINUTEEVERY1(2)<=1,SP;//第二小节结束前平仓\\r\\nAUTOFILTER;", "explanation": "距小节结束时间", "markettype": 1, "modifytime": "20221206", "param": "", "tip": "CLOSEMINUTEEVERY1(N),返回当前时间距离每个小节结束的时间（单位：分钟），N为第几个小节，方便小节前及时平仓", "type": 7}, "CLOSEOUT": "CLOSEOUT，清仓指令，平掉所有方向的持仓", "CLOSESEC": {"body": "CLOSESEC", "createtime": "20141031", "description": "CLOSESEC，返回K线开始时间距离收盘前的秒数。\\r\\n\\r\\n注：\\r\\n1、该函数只能用于收盘价模型。\\r\\n2、该函数返回当根K线开始时间距离收盘的秒数。\\r\\n3、该函数需要加载到秒周期使用；不支持在TICK周期，量能周期，分钟及以上周期使用。\\r\\n4、该函数的返回值包含小结和午休的时间。\\r\\n5、CLOSESEC返回的是交易所的时间，不是本机的时间。\\r\\n6、对于夜盘合约，夜盘收盘不是当日收盘，15点收盘才算作当日收盘。\\r\\n7、CLOSESEC在合约最后交易日，返回实际收盘时间。\\r\\n8、CLOSESEC加载到主力合约上，主力换月和合约最后交易日在同一天，则按照最后交易日的收盘时间计算，主力换月和合约最后交易日不在同一天，那么按照正常的非最后交易日进行计算。\\r\\n9、该函数不支持和CLOSEMINUTE同时使用。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5))&&CLOSESEC>15,BK;//十五秒周期上，收盘价上穿五周期均线，开仓，当天最后一根K线不交易\\r\\nCLOSESEC<=15,SP;//15秒周期上，最后一根K线尾盘平仓\\r\\nCLOSEKLINE(1,5);//收盘前最后一根K线提前5秒判断K线走完\\r\\nAUTOFILTER;", "explanation": "距收盘前时间（秒数）", "markettype": 1, "modifytime": "20230707", "param": "", "tip": "CLOSESEC,返回K线开始时间距离闭市前的时间（单位：秒），方便闭市前及时平仓", "type": 7}, "CLOSESEC1": {"body": "CLOSESEC1", "createtime": "20141031", "description": "CLOSESEC1，返回距离收盘前的秒数。\\r\\n\\r\\n注：\\r\\n1、该函数只能用于指令价模型。\\r\\n2、\\r\\n历史K线：该函数返回K线结束时间距离收盘的秒数。\\r\\n盘中：该函数返回K线当前时间距离收盘的秒数。\\r\\n3、该函数不支持在TICK周期，量能周期使用。\\r\\n4、该函数返回值包含小结和午休的时间。\\r\\n5、CLOSESEC1返回的是交易所的时间，不是本机的时间。\\r\\n6、对于夜盘合约，夜盘收盘不是当日收盘，15点收盘才算作当日收盘。\\r\\n7、CLOSESEC1在合约最后交易日，返回实际收盘时间。\\r\\n8、CLOSESEC1加载到主力合约上，主力换月和合约最后交易日在同一天，则按照最后交易日的收盘时间计算，主力换月和合约最后交易日不在同一天，那么按照正常的非最后交易日进行计算。\\r\\n9、该函数不支持和CLOSEMINUTE1同时使用。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK;//最新价上穿五周期均线，买开\\r\\nMULTSIG(0,0,1,0);//使用TICK数据回测，出信号立即下单，不复核\\r\\nCLOSESEC1<=5,CLOSEOUT;//收盘前五秒钟，清仓。\\r\\nAUTOFILTER;", "explanation": "距收盘前时间（秒数）", "markettype": 1, "modifytime": "20230707", "param": "", "tip": "CLOSESEC1,返回当前时间距离闭市前的时间（单位：秒），方便闭市前及时平仓", "type": 7}, "CLOSESECEVERY": {"body": "CLOSESECEVERY()", "createtime": "20160927", "description": "CLOSESECEVERY(N) 返回K线开始时间距离小节结束的秒数\\r\\n\\r\\n用法：\\r\\nCLOSESECEVERY(N)，返回K线开始时间距离第N个小节结束的秒数。N为参数，表示第几个小节（交易时间段）\\r\\n以沪金合约为例。N=1,第一小节为21：00到次日2点30;N=2，第二小节为上午9:00-10：15；N=3,第三小节为上午10：30到11:30；N=4,第四小节为13:00-15:00\\r\\n\\r\\n注：\\r\\n1、该函数只能用于收盘价模型。\\r\\n2、该函数返回当根K线开始时间距离小节结束的秒数。\\r\\n3、该函数需要加载到秒周期使用；不支持在TICK周期，量能周期，分钟及以上周期使用。\\r\\n4、CLOSESECEVERY返回的是交易所的时间，不是本机的时间。\\r\\n5、对于夜盘合约，第一个小节时间指加载合约的夜盘交易时间段\\r\\n6、该函数不支持和CLOSEMINUTEEVERY同时使用。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK;//十五秒周期上，收盘价上穿五周期均线，开仓\\r\\nCLOSESECEVERY(1)<=15,SP;//15秒周期上，第一小节结束前平仓\\r\\nCLOSEKLINE(1,5);//以收盘时间为结束时间的K线提前5秒判断K线走完\\r\\nAUTOFILTER;", "explanation": "距小节结束时间（秒）", "markettype": 1, "modifytime": "", "param": "", "tip": "CLOSESECEVERY(N),返回K线开始时间距离每个小节结束的时间（单位：秒），N为第几个小节，方便小节前及时平仓", "type": 7}, "CLOSESECEVERY1": {"body": "CLOSESECEVERY1()", "createtime": "20160927", "description": "CLOSESECEVERY1(N) 返回距离小节结束前的秒数\\r\\n\\r\\n用法：\\r\\nCLOSESECEVERY1(N),返回距离第N个小节结束的秒数。N为参数，表示第几个小节（交易时间段）\\r\\n以沪金合约为例。N=1,第一小节为21：00到次日2点30;N=2，第二小节为上午9:00-10：15；N=3,第三小节为上午10：30到11:30；N=4,第四小节为13:00-15:00\\r\\n\\r\\n注：\\r\\n1、该函数只能用于指令价模型。\\r\\n2、\\r\\n历史K线：该函数返回K线结束时间距离小节结束的秒数。\\r\\n盘中：该函数返回K线当前时间距离小节结束的秒数。\\r\\n3、该函数不支持在TICK周期，量能周期使用。\\r\\n4、CLOSESECEVERY1返回的是交易所的时间，不是本机的时间。\\r\\n5、对于夜盘合约，第一个小节时间指加载合约的夜盘交易时间段\\r\\n6、该函数不支持和CLOSEMINUTEEVERY1同时使用。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK;//最新价上穿五周期均线，买开\\r\\nMULTSIG(0,0,1,0);//使用TICK数据回测，出信号立即下单，不复核\\r\\nCLOSESECEVERY1(1)<=5,CLOSEOUT;//第一小节结束前五秒钟，清仓。\\r\\nAUTOFILTER;", "explanation": "距小节结束时间（秒）", "markettype": 1, "modifytime": "", "param": "", "tip": "CLOSESECEVERY1(N),返回当前时间距离每个小节结束的时间（单位：秒），N为第几个小节，方便小节前及时平仓", "type": 7}, "CMPETITV_ORDER": "CMPETITV_ORDER 超价", "CODELIKE": {"body": "CODELIKE('')", "createtime": "20161216", "description": "CODELIKE('') 模糊股票代码函数。\\r\\n\\r\\n用法：\\r\\nCODELIKE('600') 判断股票代码是否以600开头。是返回1（YES）,不是返回0（NO）。\\r\\n\\r\\n注：\\r\\n1、判断的内容用单引号标注。 \\r\\n2、该函数不支持美股、港股。\\r\\n\\r\\n例：\\r\\nC>O&&CODELIKE('300')=1;//最后一根K线为阳线并且代码以300开头（创业版）。", "explanation": "模糊股票代码函数", "markettype": 0, "modifytime": "", "param": "", "tip": "CODELIKE('')模糊股票代码函数。CODELIKE('600')判断股票代码是否以600开头。是返回1（YES）,不是返回0（NO）。", "type": 5}, "COEFFICIENTR": {"body": "COEFFICIENTR( , , )", "createtime": "20150525", "description": "COEFFICIENTR(X,Y,N) 求X、Y在N个周期内的皮尔森相关系数。\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、N为有效值，但当前的k线数不足N根，该函数返回空值。\\r\\n3、N为0时，该函数返回空值。\\r\\n4、N为空值，该函数返回空值。\\r\\n5、N可以为变量。\\r\\n\\r\\n算法举例：计算COEFFICIENTR(O,C,3);在最近一根K线上的值。\\r\\n用麦语言函数可以表示如下：\\r\\n(((O-MA(O,3))*(C-MA(C,3))+(REF(O,1)-MA(O,3))*(REF(C,1)-MA(C,3))+(REF(O,2)-MA(O,3))*(REF(C,2)-MA(C,3))) /(STD(O,3)*STD(C,3)))/(3-1);\\r\\n\\r\\n例：\\r\\nCC: C;//定义文华商品的收盘价\\r\\n保存指标，命名为AA\\r\\n加载豆粕合约的指标为：\\r\\n#CALL[7186,AA]  AS  VAR\\r\\nC1:VAR.CC;//跨合约引用文华商品的收盘价\\r\\nCOEFFICIENTR(C1,C,10);//求文华商品和豆粕在10个周期内的皮尔森相关系数。\\r\\n//皮尔森相关系数是衡量两个随机变量之间的相关程度的指标", "explanation": "皮尔森相关系数", "markettype": 0, "modifytime": "", "param": "", "tip": "COEFFICIENTR(X,Y,N)求X、Y在N个周期内的皮尔森相关系数", "type": 3}, "COLORBLACK": "COLORBLACK 黑色", "COLORBLUE": "COLORBLUE 蓝色", "COLORCYAN": "COLORCYAN 青色", "COLORGRAY": "COLORGRAY 灰色", "COLORGREEN": "COLORGREEN 绿色", "COLORLIGHTBLUE": "COLORLIGHTBLUE 浅蓝色", "COLORLIGHTGREEN": "COLORLIGHTGREEN 浅绿色", "COLORLIGHTGREY": "COLORLIGHTGREY 浅灰色", "COLORLIGHTRED": "COLORLIGHTRED 浅红色", "COLORMAGENTA": "COLORMAGENTA 紫红色", "COLORRED": "COLORRED 红色", "COLORSTICK": {"body": "COLORSTICK", "createtime": "", "description": "COLORSTICK 画柱线。\\r\\n\\r\\n用法：X,COLORSTICK;画柱线，柱高为X的值，X大于0为红色柱线，X小于0为青色柱线。\\r\\n\\r\\n注：不支持将该函数定义为变量，即不支持下面的写法：A:COLORSTICK；\\r\\n\\r\\n例：\\r\\nC-O,COLORSTICK;//画柱线，阳线时画红色向上柱线，阴线时画青色的向下柱线。", "explanation": "画柱线", "markettype": 0, "modifytime": "", "param": "", "tip": "COLORSTICK画柱线，大于0为红色，小于0为青色", "type": 8}, "COLORWHITE": "COLORWHITE 白色", "COLORYELLOW": "COLORYELLOW 黄色", "CONDBARS": {"body": "CONDBARS(,)", "createtime": "", "description": "CONDBARS(A,B);取得最近的满足A、B条件的k线间周期数\\r\\n注意：\\r\\n1、该函数返回周期数不包含最后满足条件的K线\\r\\n2、距离当前K线最近的满足的条件为B条件，则该函数返回值为最后一次满足A条件的K线到满足B条件的K线的周期数（A条件满足后的第一次满足B条件的K线)\\r\\n距离当前K线最近的满足的条件为A条件，则该函数返回值为最后一次满足B条件的K线到满足A条件的K线的周期数（B条件满足后的第一次满足A条件的K线)\\r\\n\\r\\n例1：\\r\\nMA5:=MA(C,5);//5周期均线\\r\\nMA10:=MA(C,10);//10周期均线\\r\\nCONDBARS(CROSSUP(MA5,MA10),CROSSDOWN(MA5,MA10));//最近一次满足5周期均线上穿10周期均线与5周期均线下穿10周期均线之间的周期数", "explanation": "取得最近满足A,B条件的K线间周期数", "markettype": 0, "modifytime": "", "param": "", "tip": "CONDBARS(A,B),取得最近满足A、B条件的k线间周期数", "type": 2}, "COS": {"body": "COS( )", "createtime": "", "description": "COS(X)：返回X的余弦值。\\r\\n\\r\\n注：\\r\\n1、X的取值为R（实数集）\\r\\n2、值域为[-1，1]\\r\\n\\r\\n例1：\\r\\nCOS(-1.57);//返回-1.57的余弦值\\r\\n例2：\\r\\nCOS(1.57);//返回1.57的余弦值", "explanation": "余弦", "markettype": 0, "modifytime": "", "param": "", "tip": "COS(X),求X的余弦值", "type": 4}, "COST": {"body": "COST( )", "createtime": "20161216", "description": "COST(X) 成本分布情况。\\r\\n用法:\\r\\n COST(X) 表示X%获利盘的价格,即有X%的持仓成本在该价格下，其余(100-X)%的持仓成本在该价格以上，是套牢盘。 \\r\\n例如COST(1);返回10.5表示1%获利盘的价格是10.5。\\r\\n\\r\\n注：\\r\\n1、X的取值范围为(0-100)（0、100返回无效值），并且X可以为变量。\\r\\n2、该函数仅对日线分析周期有效。\\r\\n\\r\\n算法：\\r\\n 根据获利盘和套牢盘的比例求得价格。", "explanation": "成本分布情况", "markettype": 0, "modifytime": "", "param": "", "tip": "COST(X)成本分布情况。", "type": 2}, "COUNT": {"body": "COUNT( , )", "createtime": "", "description": "COUNT(COND,N)：统计N周期中满足COND条件的周期数。\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、若N为0则从第一个有效值算起；\\r\\n3、当N为有效值，但当前的k线数不足N根，从第一根统计到当前周期。\\r\\n4、N 为空值时返回值为空值 。\\r\\n5、N可以为变量\\r\\n\\r\\n例1：\\r\\nN:=BARSLAST(DATE<>REF(DATE,1))+1;//分钟周期，当日k线数。\\r\\nM:COUNT(ISUP,N);//统计分钟周期上开盘以来阳线的根数。\\r\\n例2：\\r\\nMA5:=MA(C,5);//定义5周期均线\\r\\nMA10:=MA(C,10);//定义10周期均线\\r\\nM:COUNT(CROSSUP(MA5,MA10),0);//统计从申请到的行情数据以来到当前这段时间内，5周期均线上穿10周期均线的次数。", "explanation": "统计总数", "markettype": 0, "modifytime": "", "param": "N为周期", "tip": "COUNT(X,N),统计N周期中满足X条件的周期数。若N为0则从第一个周期开始", "type": 2}, "COUNTGROUPSIG": {"body": "COUNTGROUPSIG( , , )", "createtime": "20191025", "description": "COUNTGROUPSIG(X,N,'group'); 统计N周期内，分组为group的X信号的数量\\r\\n\\r\\n用法：\\r\\nX可以为BK、SK、SP、BP、SPK、BPK、CLOSEOUT、STOP\\r\\n\\r\\n注：\\r\\n1、统计周期时，\\r\\n（1）包含当前k线； \\r\\n（2）若N为0则从第一个有效值算起；\\r\\n（3）当N为有效值，但当前的k线数不足N根，从第一根统计到当前周期。\\r\\n（4）N 为空值时返回值为空值 。\\r\\n（5）N可以为变量\\r\\n2、统计信号时：\\r\\n（1）信号执行方式选择为K线走完确认信号或者K线走完复核（例如：在模型中写入CHECKSIG(SIG,'A',0,'D',0,0);），不包含当根K线上未固定的信号，即返回已经固定的信号个数\\r\\n（2）信号执行方式选择为不进行信号复核（例如：在模型中写入MULTSIG或MULTSIG_MIN;），包含当根K线上信号发出并且固定后的信号\\r\\n3、由BPK指令产生的BK信号按BPK信号处理，SPK指令产生的SK信号同理。\\r\\n\\r\\n例：\\r\\nN:=BARSLAST(DATE<>REF(DATE,1))+1;\\r\\nBKN:=COUNTGROUPSIG(BK,N,'A');\\r\\nMA5:=MA(C,5);\\r\\nBKN=0&&C>MA5,BK('A',1);//当日内日未出现过BK信号并且最新价大于5周期均线，则买开仓", "explanation": "统计N周期内，分组为group的X信号的数量", "markettype": 1, "modifytime": "20211229", "param": "", "tip": "COUNTGROUPSIG(X,N,'group');统计N周期内,分组为group的X信号的数量X可以为BK、SK、SP、BP、SPK、BPK、CLOSEOUT、STOP", "type": 10}, "COUNTSIG": {"body": "COUNTSIG(,)", "createtime": "20140303", "description": "COUNTSIG(X,N); 统计N周期内，X信号的数量\\r\\n\\r\\n用法：\\r\\nX可以为BK、SK、SP、BP、SPK、BPK、CLOSEOUT、STOP\\r\\n\\r\\n注：\\r\\n1、统计周期时，\\r\\n（1）包含当前k线； \\r\\n（2）若N为0则从第一个有效值算起；\\r\\n（3）当N为有效值，但当前的k线数不足N根，从第一根统计到当前周期。\\r\\n（4）N 为空值时返回值为空值 。\\r\\n（5）N可以为变量\\r\\n2、统计信号时：\\r\\n（1）信号执行方式选择为K线走完确认信号或者K线走完复核（例如：在模型中写入CHECKSIG(SIG,'A',0,'D',0,0);），不包含当根K线上未固定的信号，即返回已经固定的信号个数\\r\\n（2）信号执行方式选择为不进行信号复核（例如：在模型中写入MULTSIG或MULTSIG_MIN;），包含当根K线上信号发出并且固定后的信号\\r\\n3、由BPK指令产生的BK信号按BPK信号处理，SPK指令产生的SK信号同理。\\r\\n\\r\\n例：\\r\\nN:=BARSLAST(DATE<>REF(DATE,1))+1;\\r\\nBKN:=COUNTSIG(BK,N);\\r\\nMA5:=MA(C,5);\\r\\nBKN=0&&C>MA5,BK;//当日内日未出现过BK信号并且最新价大于5周期均线，则买开仓", "explanation": "统计N周期内，X信号的数量", "markettype": 1, "modifytime": "20211229", "param": "", "tip": "COUNTSIG(X,N);统计N周期内,X信号的数量X可以为BK、SK、SP、BP、SPK、BPK、CLOSEOUT、STOP", "type": 10}, "COVAR": {"body": "COVAR( , , )", "createtime": "20150525", "description": "COVAR(X,Y,N) 求X、Y在N个周期内的协方差。\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、N为有效值，但当前的k线数不足N根，该函数返回空值。\\r\\n3、N为0时，该函数返回空值。\\r\\n4、N为空值，该函数返回空值。\\r\\n5、N可以为变量。\\r\\n\\r\\n算法举例：计算COVAR(O,C,3);在最近一根K线上的值。\\r\\n用麦语言函数可以表示如下：\\r\\n(((O-MA(O,3))*(C-MA(C,3))+(REF(O,1)-MA(O,3))*(REF(C,1)-MA(C,3))+(REF(O,2)-MA(O,3))*(REF(C,2)-MA(C,3))) )/3;\\r\\n\\r\\n例：\\r\\nCC: C;//定义文华商品的收盘价\\r\\n保存指标，命名为AA\\r\\n加载豆粕合约的指标为：\\r\\n#CALL[7186,AA]  AS  VAR\\r\\nC1:VAR.CC;//跨合约引用文华商品的收盘价\\r\\nCOVAR(C1,C,10);//求文华商品和豆粕在10个周期内的协方差。\\r\\n//两个不同变量之间的方差就是协方差，如果两个变量的变化趋势一致，那么两个变量之间的协方差就是正值；如果两个变量的变化趋势相反，那么两个变量之间的协方差就是负值。", "explanation": "协方差", "markettype": 0, "modifytime": "", "param": "", "tip": "COVAR(X,Y,N)求X、Y在N个周期内的协方差", "type": 3}, "CROSS": {"body": "CROSS( , )", "createtime": "", "description": "CROSS(A,B) 表示A从下方向上穿过B，成立返回1(Yes)，否则返回0(No)\\r\\n\\r\\n注：\\r\\n1、满足穿越的条件必须上根k线满足A<=B，当根k线满足A>B才被认定为穿越。\\r\\n\\r\\n例1：\\r\\nCROSS(CLOSE,MA(CLOSE,5));//表示收盘线从下方向上穿过5周期均线", "explanation": "交叉函数", "markettype": 0, "modifytime": "", "param": "", "tip": "CROSS(A,B),A从下方向上穿过B时取1(Yes)，否则取0(No)", "type": 5}, "CROSS2": {"body": "CROSS2( , , )", "createtime": "2014-08-07", "description": "CROSS2(A,B,N) 表示N个周期内当A从下方向上穿B偶数次。\\r\\n\\r\\n注：\\r\\n1、若N为0，则从第一个有效的值开始算。\\r\\n2、当N为有效值，但当前的k线数不足N根，或者N空值的情况下，代表不成立，该函数返回0\\r\\n3、N可以为变量\\r\\n\\r\\n例1：\\r\\nMA5:=MA(C,5);\\r\\nCROSS2(C,MA5,10) 返回值为1(Yes)，表示当前周期是10个周期内(包含当前周期)收盘价从下方向上穿过5周期均线的第偶数次；返回值为0(No)，表示当前周期不是10个周期内(包含当前周期)收盘价从下方向上穿过5周期均线的第偶数次", "explanation": "二次交叉函数", "markettype": 0, "modifytime": "", "param": "", "tip": "CROSS2(A,B,N),表示N个周期内当A从下方向上穿过B的次数为偶数次偶数次时返回1(Yes)，否则返回0(No)", "type": 5}, "CROSSDOT": {"body": "CROSSDOT", "createtime": "********", "description": "小圆圈线。\\r\\n用法：\\r\\nCROSSDOT 画小圆圈线。\\r\\n\\r\\n注：\\r\\n1、该函数支持设置颜色。\\r\\n2、不支持将函数定义为变量，即不支持下面的写法：A:CROSSDOT；\\r\\n\\r\\n例：MA5:MA(C,5),CROSSDOT,COLORCYAN;//用小圆圈线画5周期均线，圆圈线显示为青色。", "explanation": "小圆圈线", "markettype": 0, "modifytime": "", "param": "", "tip": "", "type": 8}, "CROSSDOWN": {"body": "CROSSDOWN( , )", "createtime": "", "description": "CROSSDOWN(A,B)：表示当A从上方向下穿B，成立返回1(Yes)，否则返回0(No)\\r\\n\\r\\n注：\\r\\n1、CROSSDOWN(A,B)等同于CROSS(B,A)，CROSSDOWN(A,B)编写更利于理解\\r\\n\\r\\n例1：\\r\\nMA5:=MA(C,5);\\r\\nMA10:=MA(C,10);\\r\\nCROSSDOWN(MA5,MA10),SK;//MA5下穿MA10卖开仓\\r\\n//CROSSDOWN(MA5,MA10),SK;  与  CROSSDOWN(MA5,MA10)=1,SK;表达同等意义", "explanation": "向下穿越", "markettype": 0, "modifytime": "", "param": "", "tip": "CROSSDOWN(A,B),表示当A从上方向下穿过B时返回1(Yes)，否则返回0(No)", "type": 5}, "CROSSUP": {"body": "CROSSUP( , )", "createtime": "", "description": "CROSSUP(A,B) 表当A从下方向上穿过B，成立返回1(Yes)，否则返回0(No)\\r\\n\\r\\n注：\\r\\n1、CROSSUP(A,B)等同于CROSS(A,B)，CROSSUP(A,B)编写更利于理解。\\r\\n\\r\\n例1：\\r\\nMA5:=MA(C,5);\\r\\nMA10:=MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;//MA5上穿MA10，买开仓。\\r\\n//CROSSUP(MA5,MA10),BK; 与  CROSSUP(MA5,MA10)=1,BK;表达同等意义", "explanation": "向上穿越", "markettype": 0, "modifytime": "", "param": "", "tip": "CROSSUP(A,B),表示当A从下方向上穿过B时返回1(Yes)，否则返回0(No)", "type": 5}, "CUBE": {"body": "CUBE( )", "createtime": "", "description": "CUBE(X)：返回X的三次方。\\r\\n\\r\\n例1：\\r\\nCUBE(4);//求4的立方。", "explanation": "立方函数", "markettype": 0, "modifytime": "", "param": "", "tip": "CUBE(X),求X的三次方", "type": 4}, "CURRENTDATE": {"body": "CURRENTDATE", "createtime": "20181120", "description": "CURRENTDATE 返回当前的年月日。\\r\\n\\r\\n注：\\r\\n1、该日期是从1900年开始的日期，2000年以后的日期显示为1YYMMDD的形式，2000年以前的日期显示为YYDDMM的形式。例如2014年1月1日将返回1140101。\\r\\n2、该函数返回的是计算当时的日期。\\r\\n3、该函数返回的时间为本机时间。\\r\\n\\r\\n例：\\r\\nA:CURRENTDATE;//返回的是现在的本机时间。", "explanation": "返回当前的年月日", "markettype": 0, "modifytime": "", "param": "", "tip": "CURRENTDATE返回当前的年月日", "type": 7}, "CURRENTTIME": {"body": "CURRENTTIME", "createtime": "20181120", "description": "CURRENTTIME 返回当前的时分秒。\\r\\n\\r\\n注：\\r\\n1、返回值取值范围为0至235959。返回为HHMMSS的形式。\\r\\n2、该函数返回的是计算当时的时间。\\r\\n3、该函数返回的时间为本机时间。\\r\\n\\r\\n例：\\r\\nA:CURRENTTIME;//返回的是现在的本机时间。", "explanation": "返回当前的时分秒", "markettype": 0, "modifytime": "", "param": "", "tip": "CURRENTTIME返回当前的时分秒", "type": 7}, "CUSTOM_DAY": "CUSTOM_DAY 自定义日", "CUSTOM_HOUR": "CUSTOM_HOUR 自定义小时", "CUSTOM_MIN": "CUSTOM_MIN 自定义分钟", "CUSTOM_SEC": "CUSTOM_SEC 自定义秒", "DASH": {"body": "DASH", "createtime": "20141121", "description": "画虚线。\\r\\n用法：\\r\\nDASH 画虚线。\\r\\n\\r\\n注：\\r\\n1、该函数支持设置颜色。\\r\\n2、不支持将函数定义为变量，即不支持下面的写法：A:DASH；\\r\\n\\r\\n例：MA5:MA(C,5),DASH,COLORCYAN;//用虚线画5周期均线，显示为青色。", "explanation": "画虚线", "markettype": 0, "modifytime": "", "param": "", "tip": "DASH,画虚线", "type": 8}, "DASHDOT": {"body": "DASHDOT", "createtime": "20141121", "description": "画点虚线。\\r\\n用法：\\r\\nDASHDOT 画点虚线。\\r\\n\\r\\n注：\\r\\n1、该函数支持设置颜色。\\r\\n2、不支持将函数定义为变量，即不支持下面的写法：A:DASHDOT；\\r\\n\\r\\n例：MA5:MA(C,5),DASHDOT,COLORCYAN;//用点虚线画5周期均线，显示为青色。", "explanation": "画点虚线", "markettype": 0, "modifytime": "", "param": "", "tip": "DASHDOT,画点虚线", "type": 8}, "DASHDOTDOT": {"body": "DASHDOTDOT", "createtime": "20141121", "description": "画双点虚线。\\r\\n用法：\\r\\nDASHDOTDOT 画双点虚线。\\r\\n\\r\\n注：\\r\\n1、该函数支持设置颜色。\\r\\n2、不支持将函数定义为变量，即不支持下面的写法：A:DASHDOTDOT；\\r\\n\\r\\n例：MA5:MA(C,5),DASHDOTDOT,COLORCYAN;//用双点虚线画5周期均线，显示为青色。", "explanation": "画双点虚线", "markettype": 0, "modifytime": "", "param": "", "tip": "DASHDOTDOT,画双点虚线", "type": 8}, "DATE": {"body": "DATE", "createtime": "", "description": "DATE,返回某周期的日期数。\\r\\n \\r\\n注：\\r\\n1：DATE的取值范围为700101-331231(即1970年1月1日—2033年12月31日)。\\r\\n2：DATE返回六位数字，YYMMDD，\\r\\n3：DATE支持上海夜盘的使用，例如：2013年7月8日 21:00夜盘开盘，DATE返回值即为130709，返回的为收盘时对应的日期 ,即数据所属的交易的日期（周五周六晚上的数据返回的日期为下周一的日期）\\r\\n\\r\\n例1：\\r\\nBARSLAST(DATE<>REF(DATE,1))+1;//当天开盘以来共有多少根K线。\\r\\n例2：\\r\\nAA:DATE=130507&&TIME=1037;\\r\\nHH:VALUEWHEN(AA=1,H);// 取201305071037分钟位置，同时取201305071037分钟k线位置最高价", "explanation": "取得某周期的日期数", "markettype": 0, "modifytime": "", "param": "", "tip": "DATE,取某周期的日期数（700101-331231）", "type": 7}, "DATE1": {"body": "DATE1", "createtime": "20181120", "description": "DATE1：返回某周期的日期数。\\r\\n\\r\\n注：\\r\\n1、DATE1的取值范围为700101-1331231(即1970年1月1日-2033年12月31日)。\\r\\n2、DATE1在2000年以前返回六位数字，YYMMDD，在2000年以后返回1YYMMDD的形式。\\r\\n3、DATE1支持上海夜盘的使用，例如：2013年7月8日 21:00夜盘开盘，DATE1返回值即为1130709，返回的为收盘时对应的日期 ,即数据所属的交易的日期（周五周六晚上的数据返回的日期为下周一的日期）\\r\\n\\r\\n例1：\\r\\nA:=BARSLAST(DATE1<>REF(DATE1,1))+1;//定义变量A为当天开盘以来共有多少根K线。\\r\\n\\r\\n例2：\\r\\nAA:DATE1=1130507&&TIME=1037;\\r\\nHH:VALUEWHEN(AA=1，H);// 取201305071037分钟位置，同时取201305071037分钟k线位置最高价", "explanation": "返回某周期的日期数", "markettype": 0, "modifytime": "", "param": "", "tip": "DATE1返回某周期的日期数", "type": 7}, "DAY": {"body": "DAY", "createtime": "", "description": "DAY,返回某一周期的日数。\\r\\n\\r\\n注：\\r\\nDAY取值范围为1-31。\\r\\n\\r\\n例1：\\r\\nDAY=3&&TIME=0915，BK;//当日起为3日，时间为9点15分时，买开。\\r\\n例2：\\r\\nN:BARSLAST(DATE<>REF(DATE,1))+1;\\r\\nCC:IFELSE(DAY=1,VALUEWHEN(N=1,O),0);//当日期为1时，取开盘价，否则取值为0.", "explanation": "取得某周期的日数", "markettype": 0, "modifytime": "", "param": "", "tip": "DAY,取某周期的日数（1-31）", "type": 7}, "DAYBARPOS": {"body": "DAYBARPOS", "createtime": "20160401", "description": "DAYBARPOS：返回当根k线是当天的第几根k线\\r\\n\\r\\n注：\\r\\n该函数返回当根k线是当天的第几根k线，日以上周期返回空值\\r\\n\\r\\n例：\\r\\nVALUEWHEN(DAYBARPOS=1,C);//取当天第一根K线的收盘价", "explanation": "当根k线为当天第几根k线", "markettype": 0, "modifytime": "", "param": "", "tip": "DAYBARPOS当根k线为当天第几根k线", "type": 2}, "DAYSTOEXPIRED": {"body": "DAYSTOEXPIRED()", "createtime": "20160426", "description": "DAYSTOEXPIRED(CODE) 期货合约距最后交易日的天数。\\r\\n\\r\\n用法：DAYSTOEXPIRED(CODE);取得合约的到期剩余天数。CODE为文华码。\\r\\n\\r\\n注：\\r\\n1、该函数返回期货合约距最后交易日的天数，其中包括最后交易日。\\r\\n2、该函数只支持应用在日线及以下周期使用，在日周期以上的周期该函数返回值为0。\\r\\n\r3、CODE位置：\\r\\n\r   写入''时默认取当前合约。\\r\\n\r   写入主连合约，返回对应的主力合约距最后交易日的天数。\\r\\n   写入连续合约，返回对应的月份合约距最后交易日的天数。\\r\\n\r   写入加权合约，返回值为0。\\r\\n\r4、该函数不支持在外盘主连合约上使用。\r\\r\\n\\r\\n例1：\\r\\nA:DAYSTOEXPIRED('');//A返回当前加载合约的到期剩余天数。\\r\\n\\r\\n例2：\\r\\nA:=DAYSTOEXPIRED('')=1&&CLOSEMINUTE=5;//定义变量A为最后交易日收盘前五分钟。", "explanation": "期货合约距最后交易日的天数", "markettype": 0, "modifytime": "20220425", "param": "", "tip": "DAYSTOEXPIRED(CODE)期货合约距最后交易日的天数,CODE为文华码", "type": 7}, "DAYTRADE": {"body": "DAYTRADE", "createtime": "20141225", "description": "DAYTRADE 日内交易函数。\\r\\n\\r\\n用法：\\r\\nDAYTRADE 模型中写入该函数，信号和资金每天重新初始化进行计算，与历史割裂。\\r\\n\\r\\n注：\\r\\n1、该函数适用于小时、分钟以下周期，不支持日、自定义N日、周、月、季、年周期。\\r\\n2、回测报告的出金/入金为日内的出金/入金的和。\\r\\n3、模型中不能同时使用DAYTRADE1\\DAYTRADE\\WEEKTRADE\\WEEKTRADE1\\MONTHTRADE\\QUARTERTRADE\\YEARTRADE函数。\\r\\n4、（1）历史回测中，当日K线走完持仓大于0，会对持仓进行全清处理。\\r\\n   （2）模组运行中，当日K线走完持仓大于0，信号和资金会重新初始化进行计算，但不会自动对持仓进行全清处理，需要在模型中编写实现全清。\\r\\n\\r\\n例：\\r\\nMA5^^MA(C,5);\\r\\nMA10^^MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;//5周期均线上穿10周期均线，买开仓\\r\\nCROSSDOWN(MA5,MA10),SK;//5周期均线下穿10周期均线，卖开仓\\r\\nC<BKPRICE-10*MINPRICE,SP;//亏损10点平多\\r\\nC>SKPRICE+10*MINPRICE,BP;//亏损10点平空\\r\\nCLOSEMINUTE<=1,CLOSEOUT;//收盘前一分钟，清仓。\\r\\nAUTOFILTER;//过滤模型\\r\\nDAYTRADE;//只用日内数据进行计算", "explanation": "日内交易函数", "markettype": 1, "modifytime": "", "param": "", "tip": "DAYTRADE,日内交易函数", "type": 9}, "DAYTRADE1": {"body": "DAYTRADE1", "createtime": "20141225", "description": "DAYTRADE1 日内交易函数。\\r\\n\\r\\n用法：\\r\\nDAYTRADE1 模型中写入该函数，信号和资金每天重新初始化进行计算，与历史割裂，并且每一个函数只使用当日K线数据进行计算，历史数据不参与计算。\\r\\n\\r\\n注：\\r\\n1、该函数适用于小时、分钟以下周期，不支持日、自定义N日、周、月、季、年周期。\\r\\n2、回测报告的出金/入金为日内的出金/入金的和。\\r\\n3、不同函数对当天数据的引用不同，使用时需注意函数用法，如：\\r\\nMA(X,N)函数N的取值：当天如果k线小于N根，则返回空值。如果k线为大于等于N根，则取N。\\r\\nHHV(X,N)函数N的取值：当天如果k线小于N根，则返回实际根数，如果k线为大于等于N根，则取N。\\r\\n4、模型中不能同时使用DAYTRADE1\\DAYTRADE\\WEEKTRADE\\WEEKTRADE1\\MONTHTRADE\\QUARTERTRADE\\YEARTRADE函数。\\r\\n5、（1）历史回测中，当日K线走完持仓大于0，会对持仓进行全清处理。\\r\\n   （2）模组运行中，当日K线走完持仓大于0，信号和资金会重新初始化进行计算，但不会自动对持仓进行全清处理，需要在模型中编写实现全清。\\r\\n\\r\\n例：\\r\\nMA5^^MA(C,5);\\r\\nMA10^^MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;//5周期均线上穿10周期均线，买开仓\\r\\nCROSSDOWN(MA5,MA10),SK;//5周期均线下穿10周期均线，卖开仓\\r\\nC<BKPRICE-10*MINPRICE,SP;//亏损10点平多\\r\\nC>SKPRICE+10*MINPRICE,BP;//亏损10点平空\\r\\nCLOSEMINUTE<=1,CLOSEOUT;//收盘前一分钟，清仓。\\r\\nAUTOFILTER;//过滤模型\\r\\nDAYTRADE1;//只用日内数据进行计算", "explanation": "日内交易函数", "markettype": 1, "modifytime": "", "param": "", "tip": "DAYTRADE1,日内交易函数", "type": 9}, "DEVSQ": {"body": "DEVSQ( , )", "createtime": "2014-04-30", "description": "DEVSQ(X,N)： 计算数据X的N个周期的数据偏差平方和。\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、N为有效值，但当前的k线数不足N根，该函数返回空值；\\r\\n3、N为0时，该函数返回空值；\\r\\n4、N为空值，该函数返回空值；\\r\\n5、N不支持为变量\\r\\n\\r\\n算法举例：计算DEVSQ(C,3);在最近一根K线上的值。\\r\\n\\r\\n用麦语言函数可以表示如下：\\r\\nSQUARE(C-(C+REF(C,1)+REF(C,2))/3)+SQUARE(REF(C,1)-(C+REF(C,1)+REF(C,2))/3)+SQUARE(REF(C,2)-(C+REF(C,1)+REF(C,2))/3);\\r\\n\\r\\n例：\\r\\nDEVSQ(C,5);计算数据收盘价5个周期的数据偏差平方和。\\r\\n//表示收盘价与收盘价均值偏差分别平方之后求和，DEVSQ(C,5)表示5个周期的收盘价与收盘价均值偏差分别平方之后求和。", "explanation": "取得数据偏差平方和", "markettype": 0, "modifytime": "", "param": "N为周期", "tip": "DEVSQ(X,N),求X的N个周期的数据偏差平方和", "type": 3}, "DIVERGENCE": {"body": "DIVERGENCE(,,,,)", "createtime": "", "description": "DIVERGENCE(X1,X2,S,L,HL);变量X1与X2在指定周期内是否发生背离\\r\\n用法：\\r\\nS: 设置转折点两边需要的周期数，取值应小于L的四分之一;S不可以为变量； \\r\\nL: 计算的总的范围的周期数；L不可以为变量。\\r\\nHL: 可以取值为1和-1\\r\\n1表示根据X1的峰值判断背离情况；\\r\\nX1在L周期内波峰取值创了新高，但X2在X1峰值对应的取值没有创新高，熊背离，或称顶背离 \\r\\n-1表示计算波谷点，\\r\\nX1在L周期内波谷取值创了新低，但X2在X1波谷对应的取值没有创新低，牛背离，或称底背离；\\r\\n\\r\\n例1：\\r\\nMA10:MA(C,10);\\r\\nDIVERGENCE(C,MA10,2,20,1);//在20个周期内，收盘价与10周期均线存在顶背离\\r\\n说明：收盘价峰值的判断标准--收盘价大于前2个周期的收盘价，并且大于后2个周期的收盘价，认为为收盘价的峰值；\\r\\n即在当根K线前面的20个周期（不包含当根K线）内，收盘价存在两个这样的峰值，且峰值创了新高，但是在两个峰值对应K线取到的10周期均线的值未创新高", "explanation": "变量X1与X2在指定周期内是否发生背离", "markettype": 0, "modifytime": "", "param": "", "tip": "DIVERGENCE(X1,X2,S,L,HL);变量X1与X2在指定周期内是否发生背离S:设置转折点两边需要的周期数；L:计算的总的范围的周期数HL为1，表示顶背离，HL为-1，表示底背离", "type": 5}, "DIVIDEND": {"body": "DIVIDEND()", "createtime": "20161027", "description": "DIVIDEND(N) 返回之前第N次派息的每股派息数量。\\r\\n\\r\\n用法：\\r\\n1、该函数返回值为之前第N次派息（不包含最近一次派息）的每股派息数量\\r\\n2、该函数返回值为每股派息数量，需要在发布的派息金额的基础上除以股数。\\r\\n（例如：每10股，红利5.15元，则每股派息数量为5.15/10=0.515）\\r\\n\\r\\n注：\\r\\n1、当N为0时，返回最近一次的每股派息数量；\\r\\n2、当N为有效值，但本地数据范围内不足N次派息时，返回无效值；\\r\\n3、若N为无效值时，返回无效值；\\r\\n4、N可以为变量；\\r\\n5、该函数只支持加载在国内股票日线及日线以下周期使用。", "explanation": "返回之前第N次派息的每股派息数量", "markettype": 0, "modifytime": "", "param": "", "tip": "DIVIDEND(N)返回之前第N次派息的每股派息数量", "type": 15}, "DIVIDENDBARS": {"body": "DIVIDENDBARS()", "createtime": "20161027", "description": "DIVIDENDBARS(N) 返回从之前第N个派息日到当前的周期数。\\r\\n\\r\\n用法：\\r\\n返回从之前第N个派息日（不包含最近一次派息日）到当前的周期数；若当前K线为之前的N个派息日，返回值为0\\r\\n\\r\\n注：\\r\\n1、当N为0时，返回从最近的一个派息日到当前的周期数；\\r\\n2、当N为有效值，但本地数据范围内不足N次派息时，返回无效值；\\r\\n3、若N为无效值时，返回无效值；\\r\\n4、N可以为变量；\\r\\n5、该函数只支持加载在国内股票日线及日线以下周期使用。", "explanation": "返回从之前第N个派息日到当前的周期数", "markettype": 0, "modifytime": "", "param": "", "tip": "DIVIDENDBARS(N)返回从之前第N次派息到当前的周期数", "type": 15}, "DMA": {"body": "DMA( , )", "createtime": "", "description": "DMA(X,A)：求X的动态移动平均，其中A必须小于1大于0。\\r\\n注：\\r\\n1、A可以为变量\\r\\n2、如果A<=0或者A>=1，返回无效值\\r\\n\\r\\n计算公式：DMA(X,A)=REF(DMA(X,A),1)*(1-A)+X*A\\r\\n\\r\\n例1：\\r\\nDMA3:=DMA(C,0.3);//计算结果为REF(DMA3,1)*(1-0.3)+C*0.3", "explanation": "动态移动平均", "markettype": 0, "modifytime": "", "param": "A必须小于1大于0", "tip": "DMA(X,A),求X的动态移动平均。A必须小于1大于0", "type": 2}, "DOT": {"body": "DOT", "createtime": "", "description": "画点线。\\r\\n用法：\\r\\nDOT 画点线。\\r\\n注：\\r\\n不支持将该函数直接定义为变量，即不支持下面的写法：A:DOT;\\r\\n例：MA5:MA(C,5),DOT;用点线画5日均线。", "explanation": "画点线", "markettype": 0, "modifytime": "", "param": "", "tip": "DOT,画点线", "type": 8}, "DRAWBARLINE": {"body": "DRAWBARLINE(,,,)", "createtime": "20140418", "description": "DRAWBARLINE(H1,O1,L1,C1);绘制BAR线（美国线）\\r\\n用法：\\r\\n在L1到H1之间绘制柱线，在O1位置绘制左侧横线，在C1位置绘制右侧横线。\\r\\n注：\\r\\n不支持将该函数直接定义为变量，即不支持下面的写法：\\r\\nA:DRAWBARLINE(H1,O1,L1,C1);\\r\\n\\r\\n例： \\r\\nDRAWBARLINE(H,O,L,C); //在最高价和最低价之间绘制BAR线，在开盘价位置绘制左侧横线，在收盘价位置绘制右侧横线。", "explanation": "绘制BAR线（美国线）", "markettype": 0, "modifytime": "", "param": "", "tip": "DRAWBARLINE(H1,O1,L1,C1);在L1到H1之间绘制柱线，在O1位置绘制左侧横线，在C1位置绘制右侧横线", "type": 8}, "DRAWBKBMP": {"body": "DRAWBKBMP( , )", "createtime": "20140410", "description": "DRAWBKBMP(COND,IMAGE) 设置背景图片。\\r\\n\\r\\n用法：\\r\\nDRAWBKBMP(COND,IMAGE);\\r\\n当最后一根K线满足COND条件时，将图片IMAGE设置为背景。\\r\\n\\r\\n注：\\r\\n1、IMAGE指定的图片必须位于程序安装目录的Formula\\Image目录下(Image文件夹需要用户自己建立)\\r\\n2、图片格式必须为.BMP格式。\\r\\n3、不支持将函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWBKBMP(COND,IMAGE);\\r\\n\\r\\n例1：\\r\\nDRAWBKBMP(CLOSE>OPEN,'壁纸20140410112435');//当最后一根K线为阳线时，将Formula\\Image目录下的壁纸20140410112435图片设置为背景。", "explanation": "设置背景图片", "markettype": 0, "modifytime": "", "param": "", "tip": "DRAWBKBMP(COND,IMAGE);设置背景图片", "type": 8}, "DRAWBMP": {"body": "DRAWBMP( , )", "createtime": "20140515", "description": "输出图片。\\r\\n\\r\\n用法：\\r\\nDRAWBMP(COND,DATA,IMAGE);\\r\\n当满足COND条件时，在DATA位置，输出图片IMAGE。\\r\\n\\r\\n注：\\r\\n1、IMAGE指定的图片必须位于程序安装目录的Formula\\Image目录下(Image文件夹需要用户自己建立)\\r\\n2、图片格式必须为.BMP格式\\r\\n3、图片路径同时支持写.BMP后缀和不写后缀两种形式\\r\\n4、不支持将函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWBMP(COND,DATA,IMAGE);\\r\\n5、输出的图片不能过大，否则会影响显示速度。\\r\\n\\r\\n例1：\\r\\nDRAWBMP(CLOSE>OPEN,H,'壁纸20140410112435.BMP');//当K线为阳线时，在K线最高价位置显示Formula\\Image目录下的壁纸20140410112435图片。", "explanation": "输出图片", "markettype": 0, "modifytime": "", "param": "", "tip": "DRAWBMP(COND,DATA,IMAGE);满足条件COND时，输出图片IMAGE", "type": 8}, "DRAWCOLORKLINE": {"body": "DRAWCOLORKLINE", "createtime": "20160401", "description": "DRAWCOLORKLINE 满足Cond条件绘制K线。\\r\\n\\r\\n用法：\\r\\nDRAWCOLORKLINE(Cond,Color,Empty);\\r\\n满足Cond条件时，按照Color颜色绘制K线，根据Empty标志判断是空心还是实心，不满足条件时不绘制K线。COLOR代表颜色，Empty非0为空心。\\r\\n\\r\\n注：\\r\\n不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWCOLORKLINE(Cond,Color,Empty);\\r\\n\\r\\n例：\\r\\nDRAWCOLORKLINE(C>O,COLORBLUE,0);//收盘价大于开盘价，用蓝色绘制实心K线。", "explanation": "绘制K线", "markettype": 0, "modifytime": "20211228", "param": "", "tip": "DRAWCOLORKLINE(Cond,Color,Empty);绘制K线", "type": 8}, "DRAWCOLORLINE": {"body": "DRAWCOLORLINE(,,,)", "createtime": "20140620", "description": "DRAWCOLORLINE(COND,DATA,COLOR1,COLOR2);根据条件画相应颜色的线\\r\\n\\r\\n用法：当满足COND时，DATA为COLOR1颜色的线，不满足COND时，DATA为COLOR2颜色的线\\r\\n\\r\\n注：\\r\\n1、不支持将该函数直接定义为变量，即不支持下面的写法：\\r\\nA:DRAWCOLORLINE（COND,DATA,COLOR1,COLOR2）;\\r\\n2、该函数支持在函数后设置线型（LINETHICK1 - LINETHICK7、POINTDOT、DOT），即支持下面的写法：\\r\\nDRAWCOLORLINE(COND,DATA,COLOR1,COLOR2),LINETHICK;\\r\\n\\r\\n例1： \\r\\nMA1:=MA(C,5);\\r\\nDRAWCOLORLINE(MA1>REF(MA1,1),MA1,COLORRED,COLORGREEN); //如果当根5日均线的值大于前一根5日均线的值，MA1画红线，否则画绿线", "explanation": "根据条件画相应颜色的线", "markettype": 0, "modifytime": "20211228", "param": "", "tip": "DRAWCOLORLINE（COND,DATA,COLOR1,COLOR2）;根据条件画相应颜色的线当满足COND时，DATA为COLOR1颜色的线，不满足COND时，DATA为COLOR2颜色的线", "type": 8}, "DRAWCOLUMNCHART": {"body": "DRAWCOLUMNCHART( , , )", "createtime": "", "description": "DRAWCOLUMNCHART 画柱形图。\\r\\n\\r\\n用法：\\r\\nDRAWCOLUMNCHART(X,C1,C2);\\r\\nX表示柱高，C1条件满足时从0轴向上画柱，不满足时从0轴向下画柱，C2条件满足时柱为红色，不满足时柱为青色\\r\\n\\r\\n注：\\r\\n1、C1、C2是判断条件。\\r\\n2、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWCOLUMNCHART(X,C1,C2);\\r\\n3、该函数画图为独立坐标显示，0轴为画面中央。\\r\\n例1：\\r\\nDRAWCOLUMNCHART(10,C>O,C>O);//满足收阳条件从0轴向上10个高度画红色柱，不满足条件从0轴向下10个高度画青色柱。", "explanation": "画双向柱形图", "markettype": 0, "modifytime": "", "param": "", "tip": "DRAWCOLUMNCHART(X,C1,C2)，X表示柱高,C1判断柱的方向,C2判断柱的颜色C1条件满足时从0轴向上画柱，不满足时从0轴向下画柱，C2条件满足时柱为红色，不满足时柱为青色", "type": 8}, "DRAWGBK": {"body": "DRAWGBK(,,,)", "createtime": "20140411", "description": "DRAWGBK(COND,C1,C2,D) 设置渐变背景色。\\r\\n\\r\\n用法：\\r\\nDRAWGBK(COND,C1,C2,D);\\r\\n当最后一根K线满足COND条件时，以C1至C2的渐变色填充背景。\\r\\n\\r\\n注：\\r\\n1、C1,C2可以用颜色函数定义，即可以支持下面的写法：\\r\\nDRAWGBK(CLOSE>0,COLORRED,COLORGREEN,0);//用户可以在插入-插入颜色中选择\\r\\n2、C1，C2也支持直接使用自定义颜色，即支持下面的写法：\\r\\nDRAWGBK(CLOSE>0,RGB(0,255,255),RGB(128,128,255),0);//用户可以在插入-插入颜色中选择\\r\\n3、D指定渐变方向，0表示从左到右，1表示从上到下。\\r\\n4、不支持将函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWGBK(COND,C1,C2,D);\\r\\n\\r\\n例1：\\r\\nDRAWGBK(CLOSE>OPEN,COLORRED,COLORGREEN,1);//当最后一根K线为阳线时，将背景设置为从上到下，红色到绿色的渐变。", "explanation": "设置渐变背景色", "markettype": 0, "modifytime": "", "param": "", "tip": "DRAWGBK(COND,C1,C2,D);以C1至C2的渐变色填充背景,D指定渐变方向，0表示从左到右，1表示从上到下", "type": 8}, "DRAWGBK1": {"body": "DRAWGBK1(,)", "createtime": "20151102", "description": "DRAWGBK1(COND,COLOR) 设置满足条件K线的背景颜色。\\r\\n\\r\\n用法：\\r\\nDRAWGBK1(COND,COLOR);\\r\\n当条件COND成立时，以K线宽度、COLOR颜色填充背景区域，高度为整个显示区域的最高到最低。\\r\\n\\r\\n注：\\r\\n1、COLOR可以用颜色函数定义，即可以支持下面的写法：\\r\\nDRAWGBK1(C>O,COLORRED);//用户可以在颜色中选择\\r\\n2、COLOR也支持直接使用自定义颜色，即支持下面的写法：\\r\\nDRAWGBK1(C>O,RGB(252,209,218));//用户可以在颜色中选择\\r\\n\\r\\n例1：\\r\\nMA5:=MA(C,5);\\r\\nDRAWGBK1(C>MA5,COLORRED);//表示在收盘价大于5周期均线的k线对应背景颜色设置为红色。", "explanation": "设置满足条件K线的背景颜色", "markettype": 0, "modifytime": "", "param": "", "tip": "DRAWGBK1(COND,COLOR)当条件COND成立时，以K线宽度、COLOR颜色填充背景区域，高度为整个显示区域的最高到最低", "type": 8}, "DRAWICON": {"body": "DRAWICON( , , )", "createtime": "20140731", "description": "DRAWICON：绘制小图标。\\r\\n\\r\\n用法：\\r\\nDRAWICON(COND,PRICE,ICON);\\r\\n当COND条件满足时,在PRICE位置画图标ICON。\\r\\n\\r\\n注：\\r\\n1、该函数可以指定位置PRICE标注图标ICON\\r\\n2、ICON位置可以写成'ICON'的形式，也可以写为数字的形式，即DRAWICON(COND,PRICE,'ICO1');等价于DRAWICON(COND,PRICE,1);\\r\\n3、该函数可以用ALIGN，VALIGN设置图标的对齐方式。\\r\\n4、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWICON(COND,PRICE,ICON);\\r\\n\\r\\n例1：\\r\\nDRAWICON(CLOSE<OPEN,LOW,'ICO1'),ALIGN2,VALIGN2;//在阴线的最低价上画出图标ICO1。图标右下对齐。\\r\\n写完“DRAWICON(CLOSE<OPEN,LOW,” 以后，点击插入图标按钮，再单击选中的图标插入到函数中，图标用'ICO1'~'ICO165'（或1~165）表示。\\r\\n\\r\\n例2：\\r\\nMA5:=MA(C,5);\\r\\nDRAWICON(C>MA5,MA5,2),ALIGN0,VALIGN0;//表示在收盘价大于5周期均线的k线对应的MA5数值位置上画出图标ICO2，图标左上对齐。\\r\\n写完“DRAWICON(C>MA5,MA5,” 以后，点击插入图标按钮，再单击选中的图标插入到函数中，图标用ICO1~ICO165（或1~165）表示。", "explanation": "画图标", "markettype": 0, "modifytime": "", "param": ",ICON图标用'ICO1'~'ICO105'表示", "tip": "DRAWICON(COND,PRICE,ICON),当条件COND满足时,在PRICE位置画图标ICONICON图标用'ICO1'~'ICO105'表示", "type": 8}, "DRAWKLINE": {"body": "DRAWKLINE( , , , , )", "createtime": "20140310", "description": "DRAWKLINE 自定义K线颜色、实空心及宽度绘制K线。\\r\\n\\r\\n用法：\\r\\nDRAWKLINE(WidthRatio,COLOR1,EMPTY1,COLOR2,EMPTY2);\\r\\n按照宽度比例WidthRatio画线，阳线以COLOR1和EMPTY1判断，阴线以COLOR2和EMPTY2判断。WidthRadio从0到1，COLOR1、COLOR2代表颜色，Empty非0为空心。\\r\\n\\r\\n注：\\r\\n不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWKLINE(WidthRatio,COLOR1,EMPTY1,COLOR2,EMPTY2);\\r\\n\\r\\n例1：\\r\\nDRAWKLINE(0.75,COLORRED,1,COLORCYAN,0);//绘制K线宽度比例为0.75,阳线为红色空心，阴线为青色实心。\\r\\n例2：\\r\\nDRAWKLINE(0.5,COLORYELLOW,0,COLORBLUE,1);//绘制K线宽度比例为0.5,阳线为黄色实心，阴线为蓝色空心。", "explanation": "绘制K线", "markettype": 0, "modifytime": "20211230", "param": "WidthRadio从0到1，COLOR1、COLOR2代表颜色，Empty非0为空心", "tip": "DRAWKLINE(Width<PERSON>ati<PERSON>,COLOR1,EMPTY1,COLOR2,EMPTY2)按照宽度比例WidthRatio画线（WidthRadio从0到1），阳线以COLOR1和EMPTY1判断阴线以COLOR2和EMPTY2判断。（COLOR1、COLOR2代表颜色，Empty非0为空心）", "type": 8}, "DRAWKLINE1": {"body": "DRAWKLINE1( , , , )", "createtime": "********", "description": "DRAWKLINE1 自定义价格绘制K线。\\r\\n\\r\\n用法：\\r\\nDRAWKLINE1(H1,O1,L1,C1);\\r\\n1、以H1为最高价，L1为最低价，O1为开盘价，C1为收盘价绘制K线。\\r\\n2、参数写好后，系统会根据写入的高开低收绘制K线，阳线显示为红色空心，阴线显示为青色实心，不支持修改颜色。\\r\\n\\r\\n注：\\r\\n1、写入的参数要符合逻辑，即H1大于等于O1，L1，C1；L1小于等于H1，O1，C1。\\r\\n2、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWKLINE1(H1,O1,L1,C1);\\r\\n\\r\\n例：\\r\\nO1:=REF(C,1);\\r\\nL1:=MIN(L,REF(C,1));\\r\\nH1:=MAX(H,REF(C,1));\\r\\nDRAWKLINE1(H1,O1,L1,C);//以昨日收盘价作为开盘价绘制K线\r", "explanation": "绘制K线", "markettype": 0, "modifytime": "20211228", "param": "", "tip": "DRAWKLINE1(H1,O1,L1,C1)以H1为最高价，L1为最低价，O1为开盘价，C1为收盘价绘制K线", "type": 8}, "DRAWKLINE2": {"body": "DRAWKLINE2( , , , , )", "createtime": "20141203", "description": "DRAWKLINE2 绘制盘整区间K线，盘整区间以外k线自定义。\\r\\n\\r\\n用法：\\r\\nDRAWKLINE2(SET,COLOR1,EMPTY1,COLOR2,EMPTY2);\\r\\n根据SET的设置绘制盘整K线，其他K线阳线以COLOR1和EMPTY1判断，阴线以COLOR2和EMPTY2判断。\\r\\nCOLOR1、COLOR2代表颜色，Empty 0为实心非0为空心。\\r\\n（1）SET为1时，对盘整K线进行特殊处理，并在K线区域显示成交量柱状线；黑色背景下，盘整时K线显示为黄色；白色背景下，盘整时K线显示为蓝色。\\r\\n（2）SET为0时，不特殊处理盘整K线，并且不显示成交量柱状线，K线根据COLOR和EMPTY的设置显示，用法同DRAWKLINE函数。\\r\\n\\r\\n注：\\r\\n1、绘制盘整K线的区间，根据函数PANZHENG进行计算；\\r\\n2、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWKLINE2(SET,COLOR1,EMPTY1,COLOR2,EMPTY2);\\r\\n\\r\\n例：\\r\\nDRAWKLINE2(1,COLORRED,1,COLORCYAN,0);//处于盘整状态下的K线显示为黄色，非盘整K线阳线为红色空心，阴线为青色实心。", "explanation": "绘制K线", "markettype": 0, "modifytime": "20211230", "param": "WidthRadio从0到1，COLOR1、COLOR2代表颜色，Empty非0为空心", "tip": "DRAWKLINE2(SET,COLOR1,EMPTY1,COLOR2,EMPTY2)绘制K线，黑色背景下，盘整时K线显示为黄色；白色背景下，盘整时K线显示为蓝色", "type": 8}, "DRAWLASTBARICON": {"body": "DRAWLASTBARICON( , )", "createtime": "", "description": "DRAWLASTBARICON ：在最后一根k线绘制图标。\\r\\n\\r\\n用法：\\r\\nDRAWLASTBARICON(PRICE,ICON);\\r\\n最后一根k线,在PRICE位置画图标ICON。\\r\\n\\r\\n注：\\r\\n1、该函数可以指定位置PRICE标注图标ICON\\r\\n2、ICON位置可以写成'ICON'的形式，也可以写为数字的形式，即DRAWLASTBARICON(PRICE,'ICO1');等价于DRAWLASTBARICON(PRICE,1);\\r\\n3、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWLASTBARICON(PRICE,ICON);\\r\\n4、该函数可以用ALIGN，VALIGN设置图标的对齐方式。\\r\\n\\r\\n例1：\\r\\nDRAWLASTBARICON(LOW,'ICO1');//在最后一根k线最低价上画出图标ICON1。\\r\\n写完“DRAWLASTBARICON(LOW,” 以后，点击插入图标按钮，再单击选中的图标插入到函数中，图标用'ICO1'~'ICO165'（或1~165）表示。\\r\\n\\r\\n例2：\\r\\nMA5:=MA(C,5);\\r\\nDRAWLASTBARICON(MA5,2);//表示在最后一根k线对应的MA5数值位置上画出图标ICON2。\\r\\n写完“DRAWLASTBARICON(MA5,” 以后，点击插入图标按钮，再单击选中的图标插入到函数中，图标用ICO1~ICO165（或1~165）表示。", "explanation": "在最后一根k线绘制图标", "markettype": 0, "modifytime": "", "param": "", "tip": "DRAWLASTBARICON(PRICE,ICON)最后一根k线,在PRICE位置画图标ICON", "type": 8}, "DRAWLASTBARLINE": {"body": "DRAWLASTBARLINE(,,,,,,)", "createtime": "", "description": "DRAWLASTBARLINE 最后一根k线满足条件偏移周期绘制直线段\\r\\n\\r\\n用法：\\r\\nDRAWLASTBARLINE(C1,P1,X1,C2,P2,X2,EXP);\\r\\n最后一根k线满足条件C1时向左偏移X1个周期及最后一根k线满足条件C2时向左偏移X2个周期从P1向P2画线。EXP为0表示画线不延伸，EXP不为0表示画线延伸。\\r\\n\\r\\n注：\\r\\n1、画线连接的是最后一根k线满足C1条件向左偏移X1个周期的P1位置，和最后一根k线满足C2条件向左偏移X2个周期的P2位置。\\r\\n2、EXP为0，画线不延伸，即画线段；EXP不为0，画线延伸，即画射线\\r\\n3、X1,X2支持变量\\r\\n4、该函数支持在函数后设置颜色、线型（LINETHICK1 - LINETHICK7、POINTDOT、DOT），即支持下面的两种写法：\\r\\nDRAWLASTBARLINE(C1,P1,X1,C2,P2,X2,EXP),LINETHICK,COLOR;\\r\\nDRAWLASTBARLINE(C1,P1,X1,C2,P2,X2,EXP),COLOR,LINETHICK;\\r\\n5、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWLASTBARLINE(C1,P1,X1,C2,P2,X2,EXP);\\r\\n\\r\\n例1:\\r\\nDRAWLASTBARLINE(ISDOWN,REF(H,4),4,ISDOWN,REF(L,4),4,0);//最后一根k线满足阴线，倒数第五根k线从最高价绘制到最低价，画线不延伸。\\r\\n例2：\\r\\nDRAWLASTBARLINE(COD2,REF(L,2),2,COD1,REF(H,1),1,0),COLORRED;//最后一根k线满足COD2时向左偏移2个周期的最低价和最后一根k线满足COD1条件向左偏移个1周期的最高价画红色的线，画线不延伸。", "explanation": "最后一根k线满足条件偏移周期画线", "markettype": 0, "modifytime": "", "param": "", "tip": "DRAWLASTBARLINE(C1,P1,X1,C2,P2,X2,EXP);最后一根k线满足条件C1时向左偏移X1个周期及最后一根k线满足条件C2时向左偏移X2个周期从P1向P2画线。EXP为0表示画线不延伸，EXP不为0表示画线延伸", "type": 8}, "DRAWLASTBARNUMBER": {"body": "DRAWLASTBARNUMBER( , , ,)", "createtime": "", "description": "DRAWLASTBARNUMBER：在最后一根k线输出数值。\\r\\n\\r\\n用法：\\r\\nDRAWLASTBARNUMBER(DATA,NUMBER,PRECISION,COLOR); \\r\\n最后一根k线在DATA位置写数字NUMBER。PRECISION为精度（小数点后有几位数字）。COLOR为颜色。\\r\\n\\r\\n注：\\r\\n该函数支持在函数后设置文字的大小和文字对齐方式。即支持下面的写法：\\r\\nDRAWLASTBARNUMBER(DATA,NUMBER,PRECISION,COLOR),ALIGN,VALIGN;\\r\\n\\r\\n例1：\\r\\nDRAWLASTBARNUMBER(HIGH,(CLOSE-OPEN)/OPEN*100,2,COLORRED);//最后一根k线在最高价位置红色显示涨幅数值(相对开盘价的百分比，精确2位小数)。\\r\\n例2：\\r\\nDRAWLASTBARNUMBER(L,REF(C,1),2,COLORRED),ALIGN0,VALIGN0;//表示最后一根k线的最低价处以红色显示昨收盘价数值(精确2位小数)，标注文字居左，居上对齐。", "explanation": "在最后一根k线输出数值", "markettype": 0, "modifytime": "", "param": "", "tip": "DRAWLASTBARNUMBER(DATA,NUMBER,PRECISION,COLOR);最后一根k线,在DATA位置写数字NUMBER", "type": 8}, "DRAWLASTBARTEXT": {"body": "DRAWLASTBARTEXT( , )", "createtime": "", "description": "DRAWLASTBARTEXT：在最后一根k线显示文字。\\r\\n\\r\\n用法：\\r\\nDRAWLASTBARTEXT(PRICE,TEXT);\\r\\n最后一根k线,在PRICE位置书写文字TEXT。\\r\\n\\r\\n注：\\r\\n1、显示的汉字用单引号标注\\r\\n2、可以设置文字显示的对齐方式，字体大小以及文字的颜色，即支持下面的写法：\\r\\nDRAWLASTBARTEXT(PRICE,TEXT),COLOR,ALIGN,VALIGN;\\r\\n\\r\\n例1：\\r\\nDRAWLASTBARTEXT(LOW,'注');//最后一根k线，在最低价上写\"注\"字。\\r\\n例2：\\r\\nDRAWLASTBARTEXT(LOW,'低'),ALIGN0,FONTSIZE16,COLORRED;//在最后一根k线，在最低价写\"低\"字，文字左对齐，字体大小为16，文字颜色为红色。", "explanation": "在最后一根k线显示文字", "markettype": 0, "modifytime": "", "param": "", "tip": "DRAWLASTBARTEXT(PRICE,TEXT)最后一根k线,在PRICE位置书写文字TEXT", "type": 8}, "DRAWLINE": {"body": "DRAWLINE( , , , , )", "createtime": "20140515", "description": "DRAWLINE 绘制直线段。\\r\\n\\r\\n用法：\\r\\nDRAWLINE(C1,P1,C2,P2,COLOR);\\r\\n满足条件C1时及C2时从P1向P2画线。颜色为COLOR。\\r\\n\\r\\n注：\\r\\n1、画线所在的k线须C1、C2同时满足。\\r\\n2、绘制的直线段是在满足的k线上从P1到P2位置画COLOR颜色的线段。\\r\\n3、该函数支持在函数后设置线型（LINETHICK1 - LINETHICK7、POINTDOT、DOT），即支持下面的写法：\\r\\nDRAWLINE(C1,P1,C2,P2,COLOR),LINETHICK;\\r\\n4、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWLINE(C1,P1,C2,P2,COLOR);\\r\\n\\r\\n例1：\\r\\nMA5:=MA(C,5);\\r\\nMA10:=MA(C,10);\\r\\nDRAWLINE(MA10<CLOSE,OPEN,MA5>CLOSE,CLOSE,COLORCYAN);//表示当收盘价大于10日均线并且小于5日均线时，从开盘价画青色直线到收盘价。\\r\\n例2：\\r\\nDRAWLINE(ISUP,C,ISUP,H,COLORRED),LINETHICK7;//表示当前k线收阳时，从收盘价价画红色直线到最高价，线型粗细为7。", "explanation": "画线", "markettype": 0, "modifytime": "", "param": "COLOR为颜色", "tip": "DRAWLINE(C1,P1,C2,P2,COLOR)满足条件C1时及C2时从P1向P2画线", "type": 8}, "DRAWLINE1": {"body": "DRAWLINE1( , , , , )", "createtime": "20140911", "description": "DRAWLINE1 绘制直线段。\\r\\n\\r\\n用法：\\r\\nDRAWLINE1(C1,P1,C2,P2,EXP);\\r\\n满足条件C1时及该K线后最近一个满足C2时从P1向P2画线。EXP为0表示画线不延伸，EXP不为0表示画线延伸。\\r\\n\\r\\n注：\\r\\n1、画线连接的是满足C1条件的K线的P1位置，和该K线后最近一个满足C2条件的K线的P2位置。\\r\\n2、EXP为0，画线不延伸，即画线段；EXP不为0，画线延伸，即画射线\\r\\n3、该函数支持在函数后设置颜色、线型（LINETHICK1 - LINETHICK7、POINTDOT、DOT），即支持下面的两种写法：\\r\\nDRAWLINE1(C1,P1,C2,P2,EXP),LINETHICK,COLOR;\\r\\nDRAWLINE1(C1,P1,C2,P2,EXP),COLOR,LINETHICK;\\r\\n4、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWLINE1(C1,P1,C2,P2,EXP);\\r\\n\\r\\n例：\\r\\nDRAWLINE1(ISUP,H,ISDOWN,L,0),COLORBLUE,LINETHICK7;//表示在阳线的最高价处到距离该阳线最近的一根阴线的最低价处画线，画线不延伸，画线颜色为蓝色，线型粗细为7。", "explanation": "画线", "markettype": 0, "modifytime": "", "param": "", "tip": "DRAWLINE1(C1,P1,C2,P2,EXP)满足条件C1时及该K线后最近一个满足C2时从P1向P2画线。EXP为画线0不延伸，EXP不为0画线延伸", "type": 8}, "DRAWLINE2": {"body": "DRAWLINE2( , , , , )", "createtime": "20151215", "description": "DRAWLINE2 绘制直线段。\\r\\n\\r\\n用法：\\r\\nDRAWLINE2(C1,P1,C2,P2,EXP);\\r\\n满足条件C1时及之后最后一次满足C2时从P1向P2画线。EXP为画线0不延伸，EXP不为0画线延伸。\\r\\n\\r\\n注：\\r\\n1、画线连接的是满足C1条件的K线的P1位置，和该K线后最后一个满足C2条件的K线的P2位置。\\r\\n2、EXP为0，画线不延伸，即画线段；EXP不为0，画线延伸，即画射线\\r\\n3、该函数支持在函数后设置颜色、线型（LINETHICK1 - LINETHICK7、POINTDOT、DOT），即支持下面的两种写法：\\r\\nDRAWLINE2(C1,P1,C2,P2,EXP),LINETHICK,COLOR;\\r\\nDRAWLINE2(C1,P1,C2,P2,EXP),COLOR,LINETHICK;\\r\\n4、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWLINE2(C1,P1,C2,P2,EXP);\\r\\n\\r\\n例：\\r\\nDRAWLINE2(ISUP,H,ISDOWN,L,0),COLORBLUE,LINETHICK7;//表示在阳线的最高价处到距离该阳线之后连续出现阴线中最远的一根阴线的最低价处画线，画线不延伸，画线颜色为蓝色，线型粗细为7。", "explanation": "画线", "markettype": 0, "modifytime": "", "param": "", "tip": "DRAWLINE2(C1,P1,C2,P2,EXP)满足条件C1时及之后最后一次满足C2时从P1向P2画线。EXP为画线0不延伸，EXP不为0画线延伸", "type": 8}, "DRAWLINE3": {"body": "DRAWLINE3(,,,,,,)", "createtime": "20160520", "description": "DRAWLINE3 偏移周期绘制直线段\\r\\n\\r\\n用法：\\r\\nDRAWLINE3(C1,P1,X1,C2,P2,X2,EXP);\\r\\n满足条件C1时向左偏移X1个周期及满足条件C2时向左偏移X2个周期从P1向P2画线。EXP为0表示画线不延伸，EXP不为0表示画线延伸。\\r\\n\\r\\n注：\\r\\n1、画线连接的是满足C1条件的K线向左偏移X1个周期的P1位置，和满足C1条件后最近一个满足C2条件的k线向左偏移X2个周期的P2位置。\\r\\n2、EXP为0，画线不延伸，即画线段；EXP不为0，画线延伸，即画射线\\r\\n3、X1,X2支持变量\\r\\n4、该函数支持在函数后设置颜色、线型（LINETHICK1 - LINETHICK7、POINTDOT、DOT），即支持下面的两种写法：\\r\\nDRAWLINE3(C1,P1,X1,C2,P2,X2,EXP),LINETHICK,COLOR;\\r\\nDRAWLINE3(C1,P1,X1,C2,P2,X2,EXP),COLOR,LINETHICK;\\r\\n5、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWLINE3(C1,P1,X1,C2,P2,X2,EXP);\\r\\n\\r\\n例1：\\r\\nDRAWLINE3(ISUP,REF(H,3),3,ISDOWN,REF(L,3),3,0),COLORBLUE,LINETHICK7;//表示在阳线向左偏移3个周期的最高价处到距离该阳线最近的一根阴线向左偏移3个周期的最低价处画线，画线不延伸，画线颜色为蓝色，线型粗细为7。\\r\\n\\r\\n例2：\\r\\nCOD1:=C<REF(C,2)&&REF(C,2)>REF(C,4);\\r\\nCOD2:=C>REF(C,2)&&REF(C,2)<REF(C,4);\\r\\nDRAWLINE3(COD1,REF(H,2),2,COD2,REF(L,2),2,0),COLORGREEN;//满足COD1时向左偏移2个周期的最高价到满足COD2时向左偏移个2周期的最低价画绿色的线，画线不延伸\\r\\nDRAWLINE3(COD2,REF(L,2),2,COD1,REF(H,2),2,0),COLORRED;//满足COD2时向左偏移2个周期的最低价到满足COD1时向左偏移个2周期的最高价画红色的线，画线不延伸", "explanation": "偏移周期画线", "markettype": 0, "modifytime": "", "param": "", "tip": "DRAWLINE3(C1,P1,X1,C2,P2,X2,EXP);满足条件C1时向左偏移X1个周期及满足条件C2时向左偏移X2个周期从P1向P2画线。EXP为0表示画线不延伸，EXP不为0表示画线延伸", "type": 8}, "DRAWNUMBER": {"body": "DRAWNUMBER( , , , , )", "createtime": "20140603", "description": "DRAWNUMBER：输出数值。\\r\\n\\r\\n用法：\\r\\nDRAWNUMBER(COND,DATA,NUMBER,PRECISION,COLOR); \\r\\n当条件满足时在DATA位置写数字NUMBER。PRECISION为精度（小数点后有几位数字）。COLOR为颜色。\\r\\n\\r\\n注：\\r\\n1、该函数可以用ALIGN，VALIGN设置文字的对齐方式。\\r\\n2、可以用FONTSIZE设置文字显示的字体大小。\\r\\n\\r\\n例1：\\r\\nDRAWNUMBER(CLOSE/OPEN>1.08,HIGH,(CLOSE-OPEN)/OPEN*100,2,COLORRED);//表示当日涨幅大于8%时在最高价位置红色显示涨幅数值(相对开盘价的百分比，精确2位小数)。\\r\\n例2：\\r\\nDRAWNUMBER(DATE<>REF(DATE,1),L,REF(C,1),2,COLORRED),ALIGN0,VALIGN0;//表示在当天第一根k线的最低价处以红色显示昨收盘价数值(精确2位小数)，标注文字居左，居上对齐。", "explanation": "写数字", "markettype": 0, "modifytime": "", "param": "PRECISION为精度（小数点后有几位数字）。COLOR为颜色", "tip": "DRAWNUMBER(COND,DATA,NUMBER,PRECISION,COLOR)当条件COND满足时在DATA位置写数字NUMBERPRECISION为精度（小数点后有几位数字）。COLOR为颜色", "type": 8}, "DRAWNUMBER1": {"body": "DRAWNUMBER1( , , , )", "createtime": "20140603", "description": "DRAWNUMBER1：输出数值。\\r\\n\\r\\n用法：\\r\\nDRAWNUMBER1(COND,DATA,NUMBER,PRECISION); \\r\\n当条件满足时在DATA位置写数字NUMBER。PRECISION为精度（小数点后有几位数字）。\\r\\n\\r\\n注：\\r\\n该函数支持在函数后设置文字的颜色、文字的大小和文字对齐方式。即支持下面的写法：\\r\\nDRAWNUMBER1(COND,DATA,NUMBER,PRECISION),COLOR,ALIGN,VALIGN;\\r\\n\\r\\n例1：\\r\\nDRAWNUMBER1(CLOSE/OPEN>1.08,HIGH,(CLOSE-OPEN)/OPEN*100,2),COLORRED;//表示当日涨幅大于8%时在最高价位置红色显示涨幅数值(相对开盘价的百分比，精确2位小数)。\\r\\n\\r\\n例2：\\r\\nDRAWNUMBER1(DATE<>REF(DATE,1),L,REF(C,1),2),COLORRED,ALIGN0,VALIGN0;//表示在当天第一根k线的最低价处以红色显示昨收盘价数值(精确2位小数)，标注文字居左，居上对齐。", "explanation": "写数字", "markettype": 0, "modifytime": "", "param": "PRECISION为精度（小数点后有几位数字）", "tip": "DRAWNUMBER1(COND,DATA,NUMBER,PRECISION)当条件满足时在DATA位置写数字NUMBER", "type": 8}, "DRAWSHIFTNUMBER": {"body": "DRAWSHIFTNUMBER( , , , , , , )", "createtime": "20200317", "description": "DRAWSHIFTNUMBER 输出数值\\r\\n\\r\\n用法：DRAWSHIFTNUMBER(COND,DATA,NUMBER,PRECISION,COLOR,DIRECTION,X);当条件满足时在DATA位置写数字NUMBER。PRECISION为精度（小数点后有几位数字）。COLOR为颜色。DIRECTION 为偏移的方向： 0 左 1右 , X 为偏移的K线根数。\\r\\n\\r\\n注：\\r\\n1、该函数可以用ALIGN，VALIGN设置文字的对齐方式。\\r\\n2、可以用FONTSIZE设置文字显示的字体大小。\\r\\n\\r\\n例1：\\r\\nDRAWSHIFTNUMBER(CLOSE/OPEN>1.08,HIGH,(CLOSE-OPEN)/OPEN*100,2,COLORRED,0,1);//表示当日涨幅大于8%时在最高价位置红色显示涨幅数值向左偏移一根k线(相对开盘价的百分比，精确2位小数)。\\r\\n\\r\\n例2：\\r\\nDRAWSHIFTNUMBER(DATE<>REF(DATE,1),L,REF(C,1),2,COLORRED,1,1),ALIGN0,VALIGN0;//表示在当天第一根k线的最低价处以红色显示昨收盘价数值(精确2位小数)向右偏移一根k线，标注文字居左，居上对齐。", "explanation": "输出数值", "markettype": 0, "modifytime": "", "param": "", "tip": "DRAWSHIFTNUMBER(COND,DATA,NUMBER,,PRECISION,COLOR,DIRECTION,X);当条件满足时在DATA位置写数字NUMBER。PRECISION为精度（小数点后有几位数字）。COLOR为颜色。DIRECTION为偏移的方向：0左1右,X为偏移的K线根数。", "type": 8}, "DRAWSL": {"body": "DRAWSL( , , , , , )", "createtime": "20140515", "description": "DRAWSL 绘制直线（段）。\\r\\n\\r\\n用法：\\r\\nDRAWSL(COND,DATA,SLOPE,LEN,EXPAND,COLOR);\\r\\n当条件COND满足时，在DATA数据处以每个周期相差SLOPE个价位作为斜率画LEN个周期长的线段。\\r\\nEXPAND为画线延长方式0:不延伸；1:向左延伸；2:向右延伸；3:双向延伸。\\r\\n\\r\\n注：\\r\\n1、每根k线与每根k线（每个周期）的纵向高度差为SLOPE。\\r\\n2、当SLOPE为0时,画的是水平线。\\r\\n3、该函数支持在函数后设置线型（LINETHICK1 - LINETHICK7、POINTDOT、DOT），即支持下面的写法：\\r\\nDRAWSL(COND,DATA,SLOPE,LEN,EXPAND,COLOR),LINETHICK;\\r\\n4、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWSL(COND,DATA,SLOPE,LEN,EXPAND,COLOR);\\r\\n\\r\\n例1：\\r\\nDRAWSL(C>O,H,0,2,0,COLORYELLOW);//表示当前k线为阳线时，从最高价开始画长度为2个周期的水平线，颜色为黄色。\\r\\n\\r\\n例2：\\r\\nDRAWSL(LOW=LLV(LOW,50),LOW,5,3,2,COLORRED),LINETHICK5;//表示当前最低价等于50周期内的最小值时，从当前最低价开始以每隔5个点的斜率画长度为3个周期向右延伸的斜线，颜色为红色，线型粗细为5。", "explanation": "画线（段）", "markettype": 0, "modifytime": "", "param": "EXPAND为画线延长方式0:不延伸；1:向左延伸；2:向右延伸；3:双向延伸。COLOR为颜色", "tip": "DRAWSL(COND,DATA,SLOPE,LEN,EXPAND,COLOR),当条件满足时，在DATA数据处以每个周期相差SLOPE个价位作为斜率画LEN个周期长的线段", "type": 8}, "DRAWSL1": {"body": "DRAWSL1( , , , , )", "createtime": "20140604", "description": "DRAWSL1 绘制直线（段）。\\r\\n\\r\\n用法：\\r\\nDRAWSL1(COND,DATA,SLOPE,LEN,EXPAND);\\r\\n当COND条件满足时，在DATA数据处以每个周期相差SLOPE个价位作为斜率画LEN个周期长的线段。\\r\\nEXPAND为延伸方向：0为向右，1为向左，2为双向。\\r\\n\\r\\n注：\\r\\n1、每根k线与每根k线（每个周期）的纵向高度差为SLOPE。\\r\\n2、当SLOPE为0时,画的是水平线,LEN为负值或0则直线无限延伸。\\r\\n3、该函数支持在函数后设置颜色、线型（LINETHICK1 - LINETHICK7、POINTDOT、DOT），即支持下面的两种写法：\\r\\nDRAWSL1(COND,DATA,SLOPE,LEN,EXPAND),LINETHICK,COLOR;\\r\\nDRAWSL1(COND,DATA,SLOPE,LEN,EXPAND),COLOR,LINETHICK;\\r\\n4、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:DRAWSL1(COND,DATA,SLOPE,LEN,EXPAND);\\r\\n\\r\\n例1：\\r\\nDRAWSL1(C>O,H,0,2,0),COLORYELLOW;//表示当前k线为阳线时，从最高价开始画长度为2个周期的水平线，颜色为黄色。\\r\\n\\r\\n例2：\\r\\nDRAWSL1(LOW=LLV(LOW,50),LOW,5,3,1),COLORRED,LINETHICK5;//表示当前最低价等于50周期内的最小值时，从当前最低价开始以每隔5个点的斜率画长度为3个周期向左延伸的斜线，颜色为红色，线型粗细为5。", "explanation": "画线（段）", "markettype": 0, "modifytime": "", "param": "EXPAND为画线延长方式0:不延伸；1:向左延伸；2:向右延伸；3:双向延伸", "tip": "DRAWSL1(COND,DATA,SLOPE,LEN,EXPAND),当条件满足时，在DATA数据处以每个周期相差SLOPE个价位作为斜率画LEN个周期长的线段", "type": 8}, "DRAWTEXT": {"body": "DRAWTEXT( , , )", "createtime": "20140409", "description": "DRAWTEXT：显示文字。\\r\\n\\r\\n用法：\\r\\n1、DRAWTEXT(COND,PRICE,TEXT);\\r\\n当COND条件满足时,在PRICE位置书写文字TEXT。\\r\\n2、DRAWTEXT(COND,PRICE,TEXT,COLOR);\\r\\n当COND条件满足时,在PRICE位置书写文字TEXT,文字颜色为COLOR。\\r\\n\\r\\n注：\\r\\n1、显示的汉字用单引号标注\\r\\n2、该函数可以用ALIGN，VALIGN设置文字的对齐方式。\\r\\n3、该函数可以用FONTSIZE设置文字显示的字体大小\\r\\n4、该函数可以用COLOR设置文字的颜色，即该函数支持如下写法：DRAWTEXT(COND,PRICE,TEXT),COLOR;\\r\\n\\r\\n例1：\\r\\nDRAWTEXT(CLOSE<OPEN&&REF(CLOSE,1)<REF(OPEN,1)&&REF(VOL,1)*1.1<VOL,LOW,'注');// \\r\\n表示连续两日收阴并且成交量比前一日至少多10%时，在最低价上写\"注\"字。\\r\\n例2：\\r\\nDRAWTEXT(L<=LLV(L,10),LOW,'新低'),ALIGN0,FONTSIZE16,COLORRED;//表示当根k线创10周期新低时，在最低价写\"新低\"字，文字左对齐，字体大小为16，文字颜色为红色。", "explanation": "显示文字", "markettype": 0, "modifytime": "", "param": "", "tip": "DRAWTEXT(COND,PRICE,TEXT),当COND条件满足时,在PRICE位置书写文字TEXT", "type": 8}, "DRAWVALID": {"body": "DRAWVALID()", "createtime": "20151216", "description": "DRAWVALID 连接数据的有效值画折线\\r\\n\\r\\n用法：\\r\\nDRAWVALID(DATA);连接DATA中的有效值画折线\\r\\n注：无效值指的是空值，该函数连接K线图中各个非空值的点\\r\\n\\r\\n例1：DRAWVALID(IFELSE(C>O,H,NULL));//连接K线图中所有阳线的最高价", "explanation": "连接数据的有效值画折线", "markettype": 1, "modifytime": "20220627", "param": "", "tip": "DRAWVALID(DATA);连接DATA中的有效值画折线", "type": 8}, "DUALVOLUME": {"body": "DUALVOLUME( )", "createtime": "2014-04-18", "description": "DUALVOLUME 多空量函数\\r\\n\\r\\n该函数有两种用法：\\r\\n1、DUALVOLUME('M')：括号中填写M，则函数返回一定周期内的（主动买量-主动卖量）的平均数值。\\r\\n2、DUALVOLUME('N')：括号中填写N，则函数返回K线图上主动买量-主动卖量的差。\\r\\n\\r\\n注：\\r\\n1、用法1：“一定周期”由参数P的数值决定，如果不定义P，默认为5周期。P不能直接定义，需要在参数列表中定义。\\r\\n2、主动买量比例和主动卖量比例相等或者一边是100%，不画柱。\\r\\n3、在日、周、月周期上考虑交割信息（即交割后，重新挂牌，要重新计算）。\\r\\n4、在日线下以周期例如1分钟、3分钟不跨日计算（即新的交易日的第一根开始重新计算）。\\r\\n5、指数没有主动买和主动卖的概念，所以该函数在指数合约日线周期的比例是根据该指数的所有合约计算的；并且指数合约日线以下周期不支持该函数。\\r\\n\\r\\n例1：\\r\\nM:=DUALVOLUME('M');//5周期（主动买量-主动卖量）的平均数值。\\r\\nN:=DUALVOLUME('N');//主动买量-主动卖量的差\\r\\nDRAWCOLUMNCHART(N,SCALE>=0.5,M>=0);\\r\\n//当主动买大于主动卖的时候，向上画柱高为N的红柱。反之向下画柱高为N的绿柱\\r\\n\\r\\n例2：\\r\\n//在参数列表中定义P的缺省值为10。\\r\\nM:=DUALVOLUME('M');//10周期（主动买量-主动卖量）的平均数值。\\r\\nN:=DUALVOLUME('N');//主动买量-主动卖量的差\\r\\nDRAWCOLUMNCHART(N,SCALE>=0.5,M>=0);\\r\\n//当主动买大于主动卖的时候，向上画柱高为N的红柱。反之向下画柱高为N的绿柱", "explanation": "多空量函数", "markettype": 0, "modifytime": "", "param": "", "tip": "DUALVOLUME('M'),返回值代表一段时间内的（主动买-主动卖）的平均数值DUALVOLUME('N'),返回值代表主动买-主动卖的量差", "type": 1}, "ELSE": "", "EMA": {"body": "EMA( , )", "createtime": "2014-05-16", "description": "EMA(X,N)：求N周期X值的指数加权移动平均（平滑移动平均）。\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、对距离当前较近的k线赋予了较大的权重。\\r\\n3、当N为有效值，但当前的k线数不足N根，按N根计算。\\r\\n4、N为0或空值时返回值为空值。\\r\\n5、N可以为变量\\r\\n\\r\\nEMA(X,N)=2*X/(N+1)+(N-1)*REF(EMA(X,N),1)/(N+1)\\r\\n\\r\\n例1：\\r\\nEMA10:=EMA(C,10);//求收盘价10周期指数加权移动平均值", "explanation": "指数加权移动平均", "markettype": 0, "modifytime": "", "param": "", "tip": "EMA(X,N),求X的N日指数加权移动平均值", "type": 2}, "EMA2": {"body": "EMA2( , )", "createtime": "2014-05-16", "description": "EMA2(X,N);//求N周期X值的线性加权移动平均(也称WMA)\\r\\n\\r\\nEMA2(X,N)=[N*X0+(N-1)*X1+(N-2)*X2+...+1*X(N-1)]/[N+(N-1)+(N-2)+...+1],X0表示本周期值，X1表示上一周期值 \\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、当N为有效值，但当前的k线数不足N根，返回值为空值。\\r\\n3、N为0或空值时返回值为空值。\\r\\n4、N可以为变量\\r\\n\\r\\n例1：\\r\\nEMA2(H,5);//求最高价在5个周期的线性加权移动平均值。", "explanation": "线性加权移动平均", "markettype": 0, "modifytime": "", "param": "", "tip": "EMA2(X,N),求X的N个周期的线性加权平均值", "type": 2}, "EMAWH": {"body": "EMAWH( , )", "createtime": "2014-05-16", "description": "EMAWH(C,N)，指数加权移动平均，也叫平滑移动平均，采用指数加权方法，对距离当前较近的K线赋予了较大的权重。\\r\\n注：\\r\\n1、当N为有效值，当前的k线数不足N根时，或者前面周期的取值仍作用于当前周期时，EMAWH返回值为空值\\r\\n因为EMAWH计算公式中着重考虑了当周期的权重，所以当周期较长，前面的周期取值对当前的影响越小，EMAWH从前面数据对当前周期不再影响时的取值开始显示，所以即使选择的数据起始时间不同，当前已经显示的K线的EMAWH的取值也不会发生变化\\r\\n2、当N为0或空值时返回值均为空值\\r\\n3、N不能为变量\\r\\n\\r\\nEMAWH(C,N)=2*C/(N+1)+(N-1)*REF(EMAWH(C,N),1)/(N+1)\\r\\n\\r\\n注：\\r\\nEMAWH用法同EMA(C,N)", "explanation": "指数加权移动平均", "markettype": 0, "modifytime": "", "param": "", "tip": "EMAWH（X,N),求X的N日指数加权移动平均值", "type": 2}, "END": "", "ENTRYSIG_PLACE": {"body": "ENTRYSIG_PLACE()", "createtime": "20180402", "description": "ENTRYSIG_PLACE(N) 取一次完整交易中第N个开仓信号所在K线的位置。\\r\\n\\r\\n用法：ENTRYSIG_PLACE(N) 取一次完整交易中第N个开仓信号所在K线的位置。如果没有开仓信号，则该函数返回空值。\\r\\n\\r\\n注：\\r\\n1、开仓信号有：BK,SK,BPK,SPK\\r\\n2、从开仓到持仓为0被视为一次完整交易。\\r\\n3、一次完整交易中开仓信号个数小于N时，该函数返回空值。\\r\\n4、K线位置是指当前K线到指定开仓信号所在K线的根数。\\r\\n5、N为0或空值时，该函数返回空值。\\r\\n6、参数N不支持为变量。\\r\\n\\r\\n例：\\r\\nENTRYSIG_PLACE(3)=5&&BKVOL>0,SP(BKVOL);//如果第3个开仓信号所在K线距离当前K线有5根K线，并且多头持仓大于0，卖平仓", "explanation": "取指定开仓信号的K线位置", "markettype": 1, "modifytime": "20221107", "param": "", "tip": "ENTRYSIG_PLACE(N)取一次完整交易第N个开仓信号距离当前K线的位置。", "type": 10}, "ENTRYSIG_PRICE": {"body": "ENTRYSIG_PRICE()", "createtime": "20180402", "description": "ENTRYSIG_PRICE(N) 取一次完整交易中第N个开仓信号的价格。\\r\\n\\r\\n用法：ENTRYSIG_PRICE(N) 取一次完整交易中第N个开仓信号的价格。如果没有开仓信号，则该函数返回空值。\\r\\n\\r\\n注：\\r\\n1、开仓信号有：BK,SK,BPK,SPK\\r\\n2、从开仓到持仓为0被视为一次完整交易。\\r\\n3、一次完整交易中开仓信号个数小于N时，该函数返回空值。\\r\\n4、N为0或空值时，该函数返回空值。\\r\\n5、参数N不支持为变量。\\r\\n6、该函数的计算包含滑点\\r\\n7、收盘价模型：在指定信号的当根K线函数的取值不会发生变化；\\r\\n   指令价模型：在指定信号的当根K线返回当次交易第N个开仓信号的价格。\\r\\n\\r\\n例：\\r\\nENTRYSIG_PRICE(3)=3000&&BKVOL>0,SP(BKVOL);//如果第3个固定的开仓信号的开仓价位为3000，并且多头持仓大于0，卖平仓", "explanation": "取指定开仓信号的价格", "markettype": 1, "modifytime": "20221107", "param": "", "tip": "ENTRYSIG_PRICE(N)取一次完整交易第N个开仓信号的价格。", "type": 10}, "ENTRYSIG_VOL": {"body": "ENTRYSIG_VOL()", "createtime": "20180726", "description": "ENTRYSIG_VOL(N) 取一次完整交易中第N个开仓信号的信号手数。\\r\\n\\r\\n用法：ENTRYSIG_VOL(N) 取一次完整交易中第N个开仓信号的信号手数。如果没有开仓信号，则该函数返回空值。\\r\\n\\r\\n注：\\r\\n1、开仓信号有：BK,SK,BPK,SPK\\r\\n2、从开仓到持仓为0被视为一次完整交易。\\r\\n3、一次完整交易中开仓信号个数小于N时，该函数返回空值。\\r\\n4、N为0或空值时，该函数返回空值。\\r\\n5、参数N不支持为变量。\\r\\n6、收盘价模型：在指定信号的当根K线函数的取值不会发生变化；\\r\\n   指令价模型：在指定信号的当根K线返回当次交易第N个开仓信号的信号手数。\\r\\n\\r\\n例：\\r\\nENTRYSIG_PRICE(3)=3000&&ENTRYSIG_VOL(3)>2,SP(BKVOL);//如果第3个固定的开仓信号的开仓价位为3000，并且第3个固定的开仓信号的信号手数大于2，卖平仓", "explanation": "取指定开仓信号的信号手数", "markettype": 1, "modifytime": "20221107", "param": "", "tip": "ENTRYSIG_VOL(N)取一次完整交易第N个开仓信号的信号手数。", "type": 10}, "EVERY": {"body": "EVERY( , )", "createtime": "", "description": "EVERY(COND,N)，判断N周期内，是否一直满足COND条件。若满足函数返回值为1，不满足函数返回值为0；\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、若N是有效数值，但前面没有那么多K线,或者N为空值，代表条件不满足，函数返回值为0。\\r\\n3、N可以是变量\\r\\n\\r\\n例1：\\r\\nEVERY(CLOSE>OPEN,5);//表示5个周期内一直是阳线\\r\\n例2：\\r\\nMA5:=MA(C,5);//定义5周期均线\\r\\nMA10:=MA(C,10);//定义10周期均线\\r\\nEVERY(MA5>MA10,4),BK;//4个周期内MA5都大于MA10，则买开仓。\\r\\n//EVERY(MA5>MA10,4),BK;   与   EVERY(MA5>MA10,4)=1,BK;    表达同等意义", "explanation": "判断是否持续满足", "markettype": 0, "modifytime": "", "param": "", "tip": "EVERY(X,N),判断过去一定周期N内，是否一直满足条件X如果一直满足返回1，否则返回0", "type": 5}, "EXIST": {"body": "EXIST( , )", "createtime": "", "description": "EXIST(COND,N) 判断N个周期内是否有满足COND的条件\\r\\n\\r\\n注:\\r\\n1、N包含当前k线。\\r\\n2、N可以是变量。\\r\\n3、若N是有效数值，但前面没有那么多K线，按实际周期数计算\\r\\n\\r\\n例1：\\r\\nEXIST(CLOSE>REF(HIGH,1),10);表示10个周期中是否存在收盘价大于前一个周期的最高价，存在返回1，不存在则返回0.\\r\\n例2：\\r\\nN:=BARSLAST(DATE<>REF(DATE,1))+1;\\r\\nEXIST(C>MA(C,5),N);// 表示当天是否有满足收盘价大于5周期均线的k线，存在返回1，不存在返回0", "explanation": "判断是否存在满足", "markettype": 0, "modifytime": "", "param": "", "tip": "EXIST(X,N),判断过去周期N内，是否有满足条件X如果有满足X条件的K线，返回1；如果没有满足X条件的K线，则返回0", "type": 5}, "EXITSIG_PLACE": {"body": "EXITSIG_PLACE()", "createtime": "20180402", "description": "EXITSIG_PLACE(N) 取一次完整交易中第N个平仓信号所在K线的位置。\\r\\n\\r\\n用法：EXITSIG_PLACE(N) 取一次完整交易中第N个平仓信号所在K线的位置。如果没有平仓信号，则该函数返回空值。\\r\\n\\r\\n注：\\r\\n1、平仓信号有：BP,SP,CLOSEOUT,STOP\\r\\n2、从开仓到持仓为0被视为一次完整交易。\\r\\n3、平仓信号个数小于N时，该函数返回空值。\\r\\n4、K线位置是指当前K线到指定平仓信号所在K线的根数。\\r\\n5、N为0或空值时，该函数返回空值。\\r\\n6、参数N不支持为变量。\\r\\n\\r\\n例：\\r\\nEXITSIG_PLACE(3)=5&&BKVOL<=0,BK(2);//如果第3个平仓信号所在K线距离当前K线有5根K线，并且没有多头持仓，买开仓2手", "explanation": "取指定平仓信号的K线位置", "markettype": 1, "modifytime": "20221107", "param": "", "tip": "EXITSIG_PLACE(N)取一次完整交易第N个平仓信号距离当前K线的位置。", "type": 10}, "EXITSIG_PRICE": {"body": "EXITSIG_PRICE()", "createtime": "20180402", "description": "EXITSIG_PRICE(N) 取一次完整交易中第N个平仓信号的价格。\\r\\n\\r\\n用法：EXITSIG_PRICE(N) 取一次完整交易中第N个平仓信号的价格。如果没有平仓信号，则该函数返回空值。\\r\\n\\r\\n注：\\r\\n1、平仓信号有：BP,SP,CLOSEOUT,STOP\\r\\n2、从开仓到持仓为0被视为一次完整交易。\\r\\n3、一次完整交易中平仓信号个数小于N时，该函数返回空值。\\r\\n4、N为0或空值时，该函数返回0。\\r\\n5、参数N不支持为变量。\\r\\n6、该函数的计算包含滑点\\r\\n7、收盘价模型：在指定信号的当根K线函数的取值不会发生变化；\\r\\n   指令价模型：在指定信号的当根K线返回当次交易第N个平仓信号的价格。\\r\\n\\r\\n例：\\r\\nEXITSIG_PRICE(3)=3000&&BKVOL>0,SP(BKVOL);//如果第3个固定的平仓信号的平仓价位为3000，并且多头持仓大于0，卖平仓", "explanation": "取指定平仓信号的价格", "markettype": 1, "modifytime": "20221107", "param": "", "tip": "EXITSIG_PRICE(N)取一次完整交易第N个平仓信号的价格。", "type": 10}, "EXITSIG_VOL": {"body": "EXITSIG_VOL()", "createtime": "20180726", "description": "EXITSIG_VOL(N) 取一次完整交易中第N个平仓信号的信号手数。\\r\\n\\r\\n用法：EXITSIG_VOL(N) 取一次完整交易中第N个平仓信号的信号手数。如果没有平仓信号，则该函数返回空值。\\r\\n\\r\\n注：\\r\\n1、平仓信号有：BP,SP,CLOSEOUT,STOP\\r\\n2、从开仓到持仓为0被视为一次完整交易。\\r\\n3、一次完整交易中平仓信号个数小于N时，该函数返回空值。\\r\\n4、N为0或空值时，该函数返回0。\\r\\n5、参数N不支持为变量。\\r\\n6、收盘价模型：在指定信号的当根K线函数的取值不会发生变化；\\r\\n   指令价模型：在指定信号的当根K线返回当次交易第N个平仓信号的信号手数。\\r\\n\\r\\n例：\\r\\nEXITSIG_PRICE(3)=3000&&EXITSIG_VOL(3)>2,BK(2);//如果第3个固定的平仓信号的平仓价位为3000，并且第3个固定的平仓信号的信号手数大于2，买开仓2手", "explanation": "取指定平仓信号的信号手数", "markettype": 1, "modifytime": "20221107", "param": "", "tip": "EXITSIG_VOL(N)取一次完整交易第N个平仓信号的信号手数。", "type": 10}, "EXP": {"body": "EXP( )", "createtime": "", "description": "EXP(X)：求e的X次幂。\\r\\n\\r\\n例1：\\r\\nC*EXP(0.01);//求收盘价乘以e的0.01次幂", "explanation": "指数", "markettype": 0, "modifytime": "", "param": "", "tip": "EXP(X),求e的X次幂", "type": 4}, "EXPIREDATE": {"body": "EXPIREDATE()", "createtime": "20181120", "description": "EXPIREDATE(CODE) 返回期货合约的最后交易日。\\r\\n\\r\\n用法：EXPIREDATE(CODE);取得合约的最后交易日。CODE为文华码。\\r\\n\\r\\n注：\\r\\n1、该函数返回期货合约的最后交易日，返回YYMMDD的形式。\\r\\n2、该函数只支持应用在日线及以下周期使用，在日周期以上的周期该函数返回值为0。\\r\\n\r3、CODE位置：\\r\\n\r   写入''时默认取当前合约。\\r\\n\r   写入主连合约，返回对应的主力合约的最后交易日\r 。\\r\\n   写入月份合约，返回对应的月份合约的最后交易日。\\r\\n\r   写入加权合约，返回值为0。\\r\\n\r4、该函数不支持在外盘主连合约上使用。\\r\\n\\r\\n例：\\r\\nEXPIREDATE('');//加载到IF1406上返回值为140620。", "explanation": "返回期货合约的最后交易日", "markettype": 0, "modifytime": "20230707", "param": "", "tip": "TIME0求当前周期自该日0点以来的秒数EXPIREDATE(CODE)返回期货合约的最后交易日,CODE为文华码", "type": 7}, "FEE": {"body": "FEE", "createtime": "2021-04-13", "description": "FEE手续费\\r\\n\\r\\n用法：FEE返回当前合约的手续费，用于模型中资金、手数相关计算。\\r\\n\\r\\n注：\\r\\n1、主图加载、回测中，FEE取值为回测参数中，对手续费的设置\\r\\n2、模组运行时，FEE取值为模组加载时单元参数中手续费的设置\\r\\n3、当交易品种手续费为按手数收取，返回值为手续费数值\\r\\n4、当交易品种手续费按比例收取,返回值为手续费比例，数值为小数\\r\\n\\r\\n例：\\r\\nK:=MONEYTOT*0.2/(C*MARGIN*UNIT+FEE); //理论权益的20%可以开仓的手数（此写法适用于按固定手数收取手续费的合约）", "explanation": "手续费", "markettype": 1, "modifytime": "20230216", "param": "", "tip": "FEE返回当前合约的手续费", "type": 12}, "FILLRGN": {"body": "FILLRGN( , , , )", "createtime": "20140603", "description": "FILLRGN 条件满足时，填充某一区域。\\r\\n\\r\\n用法：\\r\\nFILLRGN(COND, DATA1, DATA2, COLOR); \\r\\n当条件COND满足时，以颜色COLOR填充DATA1及DATA2之间形成的区域。\\r\\n\\r\\n注：\\r\\n不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:FILLRGN(COND,DATA1,DATA2,COLOR);\\r\\n\\r\\n例1：\\r\\nFILLRGN(ISUP,10,20,COLORRED);//当K线为阳线时，填充10到20之间的区域。\\r\\n例2：\\r\\nMA5:MA(C,5);\\r\\nMA10:MA(C,10);\\r\\nFILLRGN(MA5>MA10,MA5,MA10,COLORRED);//表示MA5>MA10时以红色填充MA5和MA10之间的区域。", "explanation": "填充函数", "markettype": 0, "modifytime": "", "param": "COLOR为颜色", "tip": "FILLRGN(COND,DATA1,DATA2,COLOR),当条件满足时，以颜色COLOR填充DATA1及DATA2之间形成的区域", "type": 8}, "FILLRGN1": {"body": "FILLRGN1( , , )", "createtime": "20140603", "description": "FILLRGN1 条件满足时，填充某一区域。\\r\\n\\r\\n用法：\\r\\nFILLRGN1(COND, DATA1, DATA2); \\r\\n当条件COND满足时，填充DATA1及DATA2之间形成的区域。\\r\\n\\r\\n注：\\r\\n1、该函数支持在函数后设置颜色，即支持下面的写法：\\r\\nFILLRGN1(COND, DATA1, DATA2), COLOR;\\r\\n2、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:FILLRGN1(COND,DATA1,DATA2);\\r\\n\\r\\n例1：\\r\\nFILLRGN1(ISUP,10,20),COLORRED;//K线为阳线时，用红色填充10到20之间的区域。\\r\\n\\r\\n例2：\\r\\nMA5:MA(C,5);\\r\\nMA10:MA(C,10);\\r\\nFILLRGN1(MA5>MA10,MA5,MA10),COLORRED;//表示MA5>MA10时以红色填充MA5和MA10之间的区域。", "explanation": "填充函数", "markettype": 0, "modifytime": "", "param": "", "tip": "FILLRGN1(COND,DATA1,DATA2),当条件满足时，填充DATA1及DATA2之间的区域", "type": 8}, "FILTER": {"body": "FILTER( , )", "createtime": "", "description": "FILTER(COND,N) 当COND条件成立，将其后N周期内的数据设置为0.\\r\\n\\r\\n注：\\r\\n1、N为空值，返回空值。\\r\\n2、N不能为变量\\r\\n\\r\\n例1：\\r\\nFILTER(CLOSE>OPEN,3);// 查找阳线，3天内再次出现的阳线不被记录在内", "explanation": "过滤", "markettype": 0, "modifytime": "", "param": "", "tip": "FILTER(COND,N)当COND条件成立时，将其后N周期内的数据设置为0.", "type": 5}, "FINANCE_DATA": {"body": "FINANCE_DATA('')", "createtime": "20161216", "description": "FINANCE_DATA('') 取某一股票合约的财务数据。\\r\\n\\r\\n用法：\\r\\nFINANCE_DATA('每股收益');//加载后返回当前股票合约财务数据中每股收益的数据值。\\r\\n\\r\\n注：\\r\\n1、该函数只支持加载在股票合约上，在期货合约上返回空值。\\r\\n2、该函数仅支持国内股票，不支持美国股票和香港股票。\\r\\n\\r\\n例：\\r\\nFINANCE_DATA('每股收益');//返回当前股票合约财务数据中每股收益的数据值。\\r\\n\\r\\n//其中'每股收益'可以替换为以下\\r\\n'每股净资产'\\r\\n'净资产收益率(摊薄)%'\\r\\n'净资产收益率(加权)%'\\r\\n'每股经营现金'\\r\\n'每股公积金'\\r\\n'每股未分配'\\r\\n'股东权益比%'\\r\\n'净利润同比%'\\r\\n'主营收入同比%'\\r\\n'年每股收益'\\r\\n'销售毛利率%'\\r\\n'资产负债率%'\\r\\n'营业总收入'\\r\\n'营业利润'\\r\\n'投资收益'\\r\\n'营业外收支'\\r\\n'利润总额'\\r\\n'净利润'\\r\\n'归母净利润'\\r\\n'未分配利润'\\r\\n'调整每股净资产'\\r\\n'总资产'\\r\\n'流动资产'\\r\\n'固定资产'\\r\\n'无形资产'\\r\\n'流动负债'\\r\\n'长期负债'\\r\\n'总负债'\\r\\n'股东权益'\\r\\n'资本公积金'\\r\\n'经营现金流量'\\r\\n'投资现金流量'\\r\\n'筹资现金流量'\\r\\n'现金增加额'\\r\\n'总股本'\\r\\n'无限售股合计'\\r\\n'A股'\\r\\n'B股'\\r\\n'流通股本'\\r\\n'流通H股'\\r\\n'其他流通股'\\r\\n'自由流通股'\\r\\n'限售股合计'\\r\\n'国家持股'\\r\\n'国有法人股'\\r\\n'境内法人股'\\r\\n'境内自然人股'\\r\\n'其他发起人股'\\r\\n'募集法人股'\\r\\n'境外法人股'\\r\\n'境外自然人股'\\r\\n", "explanation": "取某一股票合约的财务数据", "markettype": 0, "modifytime": "20230209", "param": "", "tip": "FINANCE_DATA('')取某一股票合约的财务数据。", "type": 15}, "FLOOR": {"body": "FLOOR( )", "createtime": "", "description": "FLOOR(A)：向数值减小方向舍入。\\r\\n\\r\\n注：\\r\\nFLOOR(A)返回沿A数值减小方向最接近的整数，若A为整数，则返回值为A。\\r\\n\\r\\n例1：\\r\\nFLOOR(2.1);//返回值为2；\\r\\n例2：\\r\\nFLOOR(-8.8);//返回值为-9；\\r\\n例3：\\r\\nFLOOR(5);//返回值为5；\\r\\n例4：\\r\\nIFELSE(C-INTPART(C)>=0.5,CEILING(C,1),FLOOR(C));//对收盘价四舍五入后取整数部分。", "explanation": "向下舍入", "markettype": 0, "modifytime": "", "param": "", "tip": "FLOOR(A),取沿A数值减小方向最接近的整数", "type": 4}, "FONTSIZE": {"body": "FONTSIZE", "createtime": "20140421", "description": "设置字体大小。\\r\\n用法：\\r\\nFONTSIZEX，X为8至72，表示字体由小到大。\\r\\n注：\\r\\n不支持将该函数直接定义为变量，即不支持下面的写法：A:FONTSIZE9;\\r\\n\\r\\n例1： \\r\\nDRAWTEXT(C>O,H,'阳线'),FONTSIZE10;//收盘价大于开盘价，则在K线最高价处以10的字体大小标注阳线。\\r\\n例2： \\r\\nDRAWTEXT(C<O,L,'阴线'),ALIGN0,VALIGN2,FONTSIZE30;//收盘价小于开盘价，则在K线最低价处以30的字体大小，居左居上标注阴线。", "explanation": "设置字体大小", "markettype": 0, "modifytime": "", "param": "", "tip": "", "type": 8}, "FONTSIZE10": "FONTSIZE10 字体大小为10", "FONTSIZE11": "FONTSIZE11 字体大小为11", "FONTSIZE12": "FONTSIZE12 字体大小为12", "FONTSIZE13": "FONTSIZE13 字体大小为13", "FONTSIZE14": "FONTSIZE14 字体大小为14", "FONTSIZE15": "FONTSIZE15 字体大小为15", "FONTSIZE16": "FONTSIZE16 字体大小为16", "FONTSIZE17": "FONTSIZE17 字体大小为17", "FONTSIZE18": "FONTSIZE18 字体大小为18", "FONTSIZE19": "FONTSIZE19 字体大小为19", "FONTSIZE20": "FONTSIZE20 字体大小为20", "FONTSIZE21": "FONTSIZE21 字体大小为21", "FONTSIZE22": "FONTSIZE22 字体大小为22", "FONTSIZE23": "FONTSIZE23 字体大小为23", "FONTSIZE24": "FONTSIZE24 字体大小为24", "FONTSIZE25": "FONTSIZE25 字体大小为25", "FONTSIZE26": "FONTSIZE26 字体大小为26", "FONTSIZE27": "FONTSIZE27 字体大小为27", "FONTSIZE28": "FONTSIZE28 字体大小为28", "FONTSIZE29": "FONTSIZE29 字体大小为29", "FONTSIZE30": "FONTSIZE30 字体大小为30", "FONTSIZE31": "FONTSIZE31 字体大小为31", "FONTSIZE32": "FONTSIZE32 字体大小为32", "FONTSIZE33": "FONTSIZE33 字体大小为33", "FONTSIZE34": "FONTSIZE34 字体大小为34", "FONTSIZE35": "FONTSIZE35 字体大小为35", "FONTSIZE36": "FONTSIZE36 字体大小为36", "FONTSIZE37": "FONTSIZE37 字体大小为37", "FONTSIZE38": "FONTSIZE38 字体大小为38", "FONTSIZE39": "FONTSIZE39 字体大小为39", "FONTSIZE40": "FONTSIZE40 字体大小为40", "FONTSIZE41": "FONTSIZE41 字体大小为41", "FONTSIZE42": "FONTSIZE42 字体大小为42", "FONTSIZE43": "FONTSIZE43 字体大小为43", "FONTSIZE44": "FONTSIZE44 字体大小为44", "FONTSIZE45": "FONTSIZE45 字体大小为45", "FONTSIZE46": "FONTSIZE46 字体大小为46", "FONTSIZE47": "FONTSIZE47 字体大小为47", "FONTSIZE48": "FONTSIZE48 字体大小为48", "FONTSIZE49": "FONTSIZE49 字体大小为49", "FONTSIZE50": "FONTSIZE50 字体大小为50", "FONTSIZE51": "FONTSIZE51 字体大小为51", "FONTSIZE52": "FONTSIZE52 字体大小为52", "FONTSIZE53": "FONTSIZE53 字体大小为53", "FONTSIZE54": "FONTSIZE54 字体大小为54", "FONTSIZE55": "FONTSIZE55 字体大小为55", "FONTSIZE56": "FONTSIZE56 字体大小为56", "FONTSIZE57": "FONTSIZE57 字体大小为57", "FONTSIZE58": "FONTSIZE58 字体大小为58", "FONTSIZE59": "FONTSIZE59 字体大小为59", "FONTSIZE60": "FONTSIZE60 字体大小为60", "FONTSIZE61": "FONTSIZE61 字体大小为61", "FONTSIZE62": "FONTSIZE62 字体大小为62", "FONTSIZE63": "FONTSIZE63 字体大小为63", "FONTSIZE64": "FONTSIZE64 字体大小为64", "FONTSIZE65": "FONTSIZE65 字体大小为65", "FONTSIZE66": "FONTSIZE66 字体大小为66", "FONTSIZE67": "FONTSIZE67 字体大小为67", "FONTSIZE68": "FONTSIZE68 字体大小为68", "FONTSIZE69": "FONTSIZE69 字体大小为69", "FONTSIZE70": "FONTSIZE70 字体大小为70", "FONTSIZE71": "FONTSIZE71 字体大小为71", "FONTSIZE72": "FONTSIZE72 字体大小为72", "FONTSIZE8": "FONTSIZE8 字体大小为8", "FONTSIZE9": "FONTSIZE9 字体大小为9", "FORCAST": {"body": "FORCAST( , )", "createtime": "2014-04-30", "description": "FORCAST(X,N)：为X的N周期线性回归预测值。\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、N为有效值，但当前的k线数不足N根，该函数返回空值；\\r\\n3、N为0时，该函数返回空值；\\r\\n4、N为空值，该函数返回空值；\\r\\n5、N可以是变量\\r\\n\\r\\n算法举例：用最小平方法计算FORCAST(C,3)在最近一根K线上的值\\r\\n1、建立一元线性方程：y=a+b*i+m\\r\\n2、y的估计值：y(i)^=a+b*i\\r\\n3、求残差：m^=y(i)-y(i)^=y(i)-a-b*i\\r\\n4、误差平方和：\\r\\nQ=m(1)*m(1)+...+m(3)*m(3)=[y(1)-a-b*1]*[y(1)-a-b*1]+...+[y(3)-a-b*3]*[y(3)-a-b*3]\\r\\n5、对线性方程中的参数a,b求一阶偏导:\\r\\n2*{[y(1)-a-b*1]+...+[y(3)-a-b*3]}*(-1)=0\\r\\n2*[y(1)-a-b*1]*(-1)+...+[y(3)-a-b*3]*(-3)=0\\r\\n6、联立以上两个公式，解出a,b的值：\\r\\na=(y(1)+y(2)+y(3))/3-b(i(1)+i(2)+i(3))/3\\r\\nb=(y(1)*i(1)+y(2)*i(2)+y(3)*i(3)-(3*((i(1)+i(2)+i(3))/3)*((y(1)+y(2)+y(3))/3))/((i(1)^2+i(2)^2+i(3)^2)-3*((i(1)+i(2)+i(3))/3)^2)\\r\\n7、将a，b，i值带入1，求出y值\\r\\n\\r\\n以上公式用麦语言函数可以表示如下：\\r\\nBB:(3*C+2*REF(C,1)+REF(C,2)-(3*((1+2+3)/3)*MA(C,3)))/((SQUARE(1)+SQUARE(2)+SQUARE(3))-3*SQUARE((1+2+3)/3));\\r\\nAA:MA(C,3)-BB*(1+2+3)/3;\\r\\nYY:AA+BB*3;\\r\\n\\r\\n例:\\r\\nFORCAST(CLOSE,5);//表示求5周期线性回归预测值", "explanation": "线性回归值", "markettype": 0, "modifytime": "", "param": "N为周期数", "tip": "FORCAST(X,N),求X的N周期线性回归预测值", "type": 3}, "FUNCTION_ORDER": {"NUM": 394, "function1": "ABS", "function10": "AVEDEV", "function100": "DRAWLASTBARLINE", "function101": "DRAWLASTBARNUMBER", "function102": "DRAWLASTBARTEXT", "function103": "DRAWLINE", "function104": "DRAWLINE1", "function105": "DRAWLINE2", "function106": "DRAWLINE3", "function107": "DRAWNUMBER", "function108": "DRAWNUMBER1", "function109": "DRAWSHIFTNUMBER", "function11": "AVPRICE", "function110": "DRAWSL", "function111": "DRAWSL1", "function112": "DRAWTEXT", "function113": "DRAWVALID", "function114": "DUALVOLUME", "function115": "EMA", "function116": "EMA2", "function117": "EMAWH", "function118": "ENTRYSIG_PLACE", "function119": "ENTRYSIG_PRICE", "function12": "BACKGROUNDSTYLE", "function120": "ENTRYSIG_VOL", "function121": "EVERY", "function122": "EXIST", "function123": "EXITSIG_PLACE", "function124": "EXITSIG_PRICE", "function125": "EXITSIG_VOL", "function126": "EXP", "function127": "EXPIREDATE", "function128": "FEE", "function129": "FILLRGN", "function13": "BARINTERVAL", "function130": "FILLRGN1", "function131": "FILTER", "function132": "FINANCE_DATA", "function133": "FLOOR", "function134": "FONTSIZE", "function135": "FORCAST", "function136": "GROUP", "function137": "GROUPBKPRICE", "function138": "GROUPBKVOL", "function139": "GROUPSKPRICE", "function14": "BARPOS", "function140": "GROUPSKVOL", "function141": "HARMEAN", "function142": "HASTRADEDATA", "function143": "HHV", "function144": "HHVBARS", "function145": "HIGH", "function146": "HISEXPDATE", "function147": "HISEXPDAYS", "function148": "HOLLOW", "function149": "HOUR", "function15": "BARSBK", "function150": "HV", "function151": "ICON", "function152": "IDLE", "function153": "IF", "function154": "IFELSE", "function155": "IMPLIEDVOLATILITY", "function156": "INITMONEY", "function157": "INTPART", "function158": "ISCONTRACT", "function159": "ISDELIVERYDAY", "function16": "BARSBP", "function160": "ISDOWN", "function161": "ISEQUAL", "function162": "ISLASTBAR", "function163": "ISLASTBK", "function164": "ISLASTBP", "function165": "ISLASTBPK", "function166": "ISLASTBUY", "function167": "ISLASTCLOSEOUT", "function168": "ISLASTKLINE", "function169": "ISLASTSELL", "function17": "BARSBUY", "function170": "ISLASTSK", "function171": "ISLASTSP", "function172": "ISLASTSPK", "function173": "ISLASTSTOP", "function174": "ISMAINCONTRACT", "function175": "ISMONTHEND", "function176": "ISNEARHOLIDAY", "function177": "ISNULL", "function178": "ISRECORDDAY", "function179": "ISTIMETOKLINEEND", "function18": "BARSCOUNT", "function180": "ISUP", "function181": "ISWEEKEND", "function182": "K_STATE", "function183": "K_STATE1", "function184": "K_STATE2", "function185": "K_STATE3", "function186": "K_STATE4", "function187": "KLINESIG", "function188": "KLINESTART", "function189": "KTEXT", "function19": "BARSLAST", "function190": "KURTOSIS", "function191": "LAST", "function192": "LASTOFFSETPROFIT", "function193": "LASTSIG", "function194": "LASTSIGGROUP", "function195": "LINETHICK", "function196": "LLV", "function197": "LLVBARS", "function198": "LN", "function199": "LOG", "function2": "ACOS", "function20": "BARSLASTCOUNT", "function200": "LOG10", "function201": "LONGCROSS", "function202": "LOOP1", "function203": "LOOP2", "function204": "LOW", "function205": "LV", "function206": "MA", "function207": "MARGIN", "function208": "MAX", "function209": "MAX1", "function21": "BARSSELL", "function210": "MAXBKVOL", "function211": "MAXSKVOL", "function212": "MEDIAN", "function213": "MEDIAN1", "function214": "MIN", "function215": "MIN1", "function216": "MINPRICE", "function217": "MINPRICE1", "function218": "MINPRICED", "function219": "MINUTE", "function22": "BARSSINCE", "function220": "MOD", "function221": "MODE", "function222": "MONEY", "function223": "MONEYRATIO", "function224": "MONEYTOT", "function225": "MONTH", "function226": "MONTHTRADE", "function227": "MONTHTRADE1", "function228": "MULTSIG", "function229": "MULTSIG_MIN", "function23": "BARSSINCEN", "function230": "MV", "function231": "MYVOL", "function232": "NAMELIKE", "function233": "NEWHBARS", "function234": "NEWHBARS1", "function235": "NEWLBARS", "function236": "NEWLBARS1", "function237": "NODRAW", "function238": "NORMPDF", "function239": "NOT", "function24": "BARSSK", "function240": "NOTEXT", "function241": "NULL", "function242": "NUMPOW", "function243": "OFFSETPROFIT", "function244": "OFFSETPROFIT1", "function245": "OPEN", "function246": "OPENMINUTE", "function247": "OPENMINUTE1", "function248": "OPENSEC", "function249": "OPENSEC1", "function25": "BARSSP", "function250": "OPI", "function251": "PANZHENG", "function252": "PARTLINE", "function253": "PARTLINE1", "function254": "PCRATE", "function255": "PCRATETREND", "function256": "PERCENTILE", "function257": "PERIOD", "function258": "PLAYSOUND", "function259": "POINTDOT", "function26": "BARSTATUS", "function260": "POLYLINE", "function261": "POLYLINE1", "function262": "POW", "function263": "PRECIS", "function264": "PRECISION", "function265": "PRICEPRECISION", "function266": "PRICEPRECISION1", "function267": "PROFIT", "function268": "QUARTER", "function269": "QUARTERTRADE", "function27": "BARTYPE", "function270": "QUARTERTRADE1", "function271": "RAND", "function272": "RANGE", "function273": "RAWDATA", "function274": "REF", "function275": "REFLINE", "function276": "REFLINE1", "function277": "REFSIG_PLACE", "function278": "REFSIG_PRICE", "function279": "REFSIG_PRICE1", "function28": "BETWEEN", "function280": "REFSIG_PRICE2", "function281": "REFSIG_VOL", "function282": "REFWH", "function283": "REVERSE", "function284": "ROUND", "function285": "SAR", "function286": "SAR1", "function287": "SCALE", "function288": "SEEK", "function289": "SELECT", "function29": "BKHIGH", "function290": "SETDEALPERCENT", "function291": "SETEXPIREDATE", "function292": "SETMOVEOPIPRICE", "function293": "SETQUOTACCOUNT", "function294": "SETSIGPRICE", "function295": "SETSIGPRICETYPE", "function296": "SETSTYLECOLOR", "function297": "SETTLE", "function298": "SETTRADEACCOUNT", "function299": "SGN", "function3": "ADMA", "function30": "BKLOW", "function300": "SIGNUM", "function301": "SIGVOL", "function302": "SIN", "function303": "SKEWNESS", "function304": "SKHIGH", "function305": "SKLOW", "function306": "SKPRICE", "function307": "SKPRICE1", "function308": "SKPRICEAV", "function309": "SKPRICEAV1", "function31": "BKPRICE", "function310": "SKVOL", "function311": "SKVOL2", "function312": "SLOPE", "function313": "SMA", "function314": "SMMA", "function315": "SOLID", "function316": "SORT", "function317": "SORTPOS", "function318": "SOUND", "function319": "SPLIT", "function32": "BKPRICE1", "function320": "SPLITBARS", "function321": "SQRT", "function322": "SQUARE", "function323": "STD", "function324": "STDP", "function325": "STICK", "function326": "STICKLINE", "function327": "STICKLINE1", "function328": "STKTYPE", "function329": "STOCKDIVD", "function33": "BKPRICEAV", "function330": "SUM", "function331": "SUMBARS", "function332": "T_CLOSE", "function333": "T_MAX", "function334": "T_PLUS", "function335": "T0TOTIME", "function336": "TAN", "function337": "TAVLOSS", "function338": "TAVWIN", "function339": "TAVWINLOSS", "function34": "BKPRICEAV1", "function340": "TIME", "function341": "TIME0", "function342": "TIMETOT0", "function343": "TMAXLOSS", "function344": "TMAXSEQLOSS", "function345": "TMAXSEQWIN", "function346": "TMAXWIN", "function347": "TNUMSEQLOSS", "function348": "TNUMSEQWIN", "function349": "TODAYDEUCETIMES", "function35": "BKVOL", "function350": "TODAYLOSSTIMES", "function351": "TODAYWINTIMES", "function352": "TPROFIT_REF", "function353": "TRACING_ORDER", "function354": "TRADE_AGAIN", "function355": "TRADE_OTHER", "function356": "TRADE_REF", "function357": "TRADE_SMOOTHING", "function358": "TREND", "function359": "TRMA", "function36": "BKVOL2", "function360": "TSEQLOSS", "function361": "TSEQWIN", "function362": "TSMA", "function363": "UNIT", "function364": "UNIT1", "function365": "UNITLIMIT", "function366": "VALIGN", "function367": "VALUEWHEN", "function368": "VAR", "function369": "VARP", "function37": "BUYPRICE", "function370": "VERTLINE", "function371": "VERTLINE1", "function372": "VOL", "function373": "VOLATILITY", "function374": "VOLMARGIN", "function375": "VOLSTICK", "function376": "VOLTICK", "function377": "VOLTIME", "function378": "VOLUMESTICK", "function379": "WEEKDAY", "function38": "CEILING", "function380": "WEEKTRADE", "function381": "WEEKTRADE1", "function382": "WINNER", "function383": "WORD", "function384": "YCLOSE", "function385": "YEAR", "function386": "YEARTRADE", "function387": "YEARTRADE1", "function388": "YSETTLE", "function389": "#CALL", "function39": "CHECKSIG", "function390": "#CALL_OTHER", "function391": "#CALL_PLUS", "function392": "#IMPORT", "function393": "$", "function394": "$ $", "function4": "ALIGN", "function40": "CHECKSIG_MIN", "function41": "CIRCLEDOT", "function42": "CJLVOL", "function43": "CLOSE", "function44": "CLOSEKLINE", "function45": "CLOSEMINUTE", "function46": "CLOSEMINUTE1", "function47": "CLOSEMINUTEEVERY", "function48": "CLOSEMINUTEEVERY1", "function49": "CLOSESEC", "function5": "ASIN", "function50": "CLOSESEC1", "function51": "CLOSESECEVERY", "function52": "CLOSESECEVERY1", "function53": "CODELIKE", "function54": "COEFFICIENTR", "function55": "COLORSTICK", "function56": "CONDBARS", "function57": "COS", "function58": "COST", "function59": "COUNT", "function6": "ATAN", "function60": "COUNTGROUPSIG", "function61": "COUNTSIG", "function62": "COVAR", "function63": "CROSS", "function64": "CROSS2", "function65": "CROSSDOT", "function66": "CROSSDOWN", "function67": "CROSSUP", "function68": "CUBE", "function69": "CURRENTDATE", "function7": "AUTOFILTER", "function70": "CURRENTTIME", "function71": "DASH", "function72": "DASHDOT", "function73": "DASHDOTDOT", "function74": "DATE", "function75": "DATE1", "function76": "DAY", "function77": "DAYBARPOS", "function78": "DAYSTOEXPIRED", "function79": "DAYTRADE", "function8": "AUTOFINANCING", "function80": "DAYTRADE1", "function81": "DEVSQ", "function82": "DIVERGENCE", "function83": "DIVIDEND", "function84": "DIVIDENDBARS", "function85": "DMA", "function86": "DOT", "function87": "DRAWBARLINE", "function88": "DRAWBKBMP", "function89": "DRAWBMP", "function9": "AVAILABLE_OPI", "function90": "DRAWCOLORKLINE", "function91": "DRAWCOLORLINE", "function92": "DRAWCOLUMNCHART", "function93": "DRAWGBK", "function94": "DRAWGBK1", "function95": "DRAWICON", "function96": "DRAWKLINE", "function97": "DRAWKLINE1", "function98": "DRAWKLINE2", "function99": "DRAWLASTBARICON"}, "FUNCTION_TYPE": {"NUM": 16, "type1": "K线数据引用", "type10": "信号记录函数", "type11": "信号执行函数", "type12": "头寸管理函数", "type13": "运行优化函数", "type14": "加密输出函数", "type15": "股票数据函数", "type16": "公式选股", "type2": "金融统计函数", "type3": "数理统计函数", "type4": "数学函数", "type5": "逻辑判断函数", "type6": "循环执行函数", "type7": "时间函数", "type8": "绘图函数", "type9": "计算控制函数"}, "GROUP": {"body": "GROUP()", "createtime": "20150318", "description": "GROUP('group') 判断分组的组别。\\r\\n\\r\\n注：\\r\\n1、参数group为A—I分别返回1-9。\\r\\n2、与LASTSIGGROUP函数配合使用，可以判断最后一个信号所在的组别。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK('A',1);//最新价上穿五周期均线，A组做多一手\\r\\nCROSS(MA(C,5),C),SP('A',BKVOL);//最新价下穿五周期均线，A组平仓\\r\\nCROSS(C,MA(C,10)),BK('B',2);//最新价上穿十周期均线，B组做多两手\\r\\nLASTSIG=200&&LASTSIGGROUP=GROUP('B'),SP('B',BKVOL);//上一个信号是B组的BK信号，则B组平仓", "explanation": "判断分组的组别", "markettype": 1, "modifytime": "20211230", "param": "", "tip": "GROUP判断分组的组别", "type": 10}, "GROUPBKPRICE": {"body": "GROUPBKPRICE", "createtime": "", "description": "GROUPBKPRICE('letter') 指令分组模型letter组最近一次买开信号价位。\\r\\n\\r\\n用法：\\r\\nGROUPBKPRICE('A') 返回A组最近一次买开信号价位。\\r\\n\\r\\n例：\\r\\nC>O,BK('A');\\r\\nBB:GROUPBKPRICE('A');//给BB赋值为A组指令中最近一次买开信号价位。", "explanation": "指令分组模型相应组别的最近一次买开信号价位", "markettype": 1, "modifytime": "", "param": "", "tip": "GROUPBKPRICE('X'),X为指令分组组别，A-I返回分组指令X组最近一次模型买开位置的买开信号价位", "type": 10}, "GROUPBKVOL": {"body": "BKVOL", "createtime": "20140319", "description": "GROUPBKVOL('letter') 取指令分组模型letter组的买开信号手数。\\r\\n\\r\\n用法：\\r\\nGROUPBKVOL('A')返回指令分组模型组A的多头模组持仓。\\r\\n参数可以取从A-I\\r\\n\\r\\n注：\\r\\n相应组的买开信号后，GROUPBKVOL('A')增加，即BK('A'),BPK('A'),BK('A',1)后GROUPBKVOL('A')增加，其他组的开仓信号，GROUPBKVOL('A')取值不变\\r\\n相应组的卖平信号后，GROUPBKVOL('A')取值相应的减少，即SP('A'),SPK('A'),SP('A',1)后，GROUPBKVOL('A')取值减少，其他组的平仓信号后，GROUPBKVOL('A')取值不变\\r\\n全清信号后，GROUPBKVOL('A')取值减为0\\r\\n\\r\\n例：\\r\\nMA1:MA(C,5);\\r\\nC>MA1,BK('A',1);\\r\\nC>O,BK('B',1);\\r\\nGROUPBKVOL('A')>0&&C>REF(H,1),BK('A',1);//A组多头持仓大于0并且最新价大于前一周前最高价，再买开一手\\r\\nC<MA1,SP('A',GROUPBKVOL('A'));//最新价小于5日均线，卖平所有的A组的多头持仓\\r\\nC<O,SP('B',GROUPBKVOL('B'));//K线收阴线，卖平所有的B组多头持仓", "explanation": "指令分组模型买开信号手数", "markettype": 1, "modifytime": "", "param": "", "tip": "GROUPBKVOL('X'),X为指令分组组别，A-I取指令分组模型X组的模组多头持仓.", "type": 10}, "GROUPSKPRICE": {"body": "GROUPSKPRICE", "createtime": "", "description": "GROUPSKPRICE('letter')  指令分组模型letter组最近一次卖开信号价位。\\r\\n\\r\\n用法：\\r\\nGROUPSKPRICE('A') 返回A组最近一次卖开信号价位。\\r\\n\\r\\n例：\\r\\nC<O,SK('B');\\r\\nSS:GROUPSKPRICE('B');//给SS赋值为B组指令中最近一次卖开信号价位。", "explanation": "指令分组模型相应组别的最近一次卖开信号价位", "markettype": 1, "modifytime": "", "param": "", "tip": "GROUPSKPRICE('X'),X为指令分组组别，A-I,返回分组指令X组最近一次模型卖开位置的卖开信号价位", "type": 10}, "GROUPSKVOL": {"body": "GROUPSKVOL", "createtime": "20140319", "description": "GROUPSKVOL('letter') 取指令分组模型letter组的卖开信号手数。\\r\\n\\r\\n用法：\\r\\nGROUPSKVOL('A')返回指令分组模型组A的空头模组持仓。\\r\\n参数可以取从A-I\\r\\n\\r\\n注：\\r\\n相应组的卖开信号后，GROUPSKVOL('A')增加，即SK('A’),SPK('A'),SK('A',1)后GROUPSKVOL('A')增加，其他组的开仓信号，GROUPSKVOL('A')取值不变\\r\\n相应组的买平信号后，GROUPSKVOL('A')取值减少，即BP('A'),BPK('A'),BP('A',1)后，GROUPSKVOL('A')取值减少，其他组的平仓信号后，GROUPSKVOL('A')取值不变\\r\\n全清信号后，GROUPBKVOL('A')取值减为0\\r\\n\\r\\n例：\\r\\nMA1:MA(C,5);\\r\\nC<MA1,SK('A',1);\\r\\nC<O,SK('B',1);\\r\\nGROUPSKVOL('A')>0&&C<REF(L,1),SK('A',1); //A组空头持仓大于0并且最新价大于前一周前最高价，再卖开一手\\r\\nC>MA1,BP('A',GROUPSKVOL('A')); //最新价大于5日均线，买平所有的A组的空头持仓\\r\\nC>O,BP('B',GROUPSKVOL('B')); //K线收阳线，买平所有的B组空头持仓", "explanation": "指令分组模型卖开信号手数", "markettype": 1, "modifytime": "", "param": "", "tip": "GROUPSKVOL('X'),X为指令分组组别，A-I取指令分组模型X组的模组空头持仓.", "type": 10}, "H": "H 最高价", "HARMEAN": {"body": "HARMEAN( , )", "createtime": "20150522", "description": "HARMEAN(X,N) 求X在N个周期内的调和平均值。\\r\\n\\r\\n算法举例：HARMEAN(X,5)=1/[(1/X1+1/X2+1/X3+1/X4+1/X5)/5]\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、调和平均值与倒数的简单平均值互为倒数。\\r\\n3、当N为有效值，但当前的k线数不足N根，函数返回空值。\\r\\n4、N为0或空值的情况下，函数返回空值。\\r\\n5、X为0或空值的情况下，函数返回空值。\\r\\n6、N可以为变量。\\r\\n\\r\\n例：\\r\\nHM5:=HARMEAN(C,5);//求5周期收盘价的调和平均值。", "explanation": "调和平均值", "markettype": 0, "modifytime": "", "param": "", "tip": "HARMEAN(X,N)求X在N个周期内的调和平均值", "type": 2}, "HASTRADEDATA": {"body": "HASTRADEDATA", "createtime": "20161026", "description": "HASTRADEDATA  判断数据合约当根K线交易合约是否有数据\\r\\n\\r\\n用法：\\r\\nHASTRADEDATA;  \\r\\n1、判断数据合约当根K线交易合约是否有数据，有数据返回1，无数据返回0。\\r\\n2、若数据合约和交易合约一致返回1", "explanation": "判断数据合约当根K线交易合约是否有数据", "markettype": 1, "modifytime": "", "param": "", "tip": "HASTRADEDATA判断数据合约当根K线交易合约是否有数据", "type": 5}, "HHV": {"body": "HHV( , )", "createtime": "", "description": "HHV(X,N)：求X在N个周期内的最高值。\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、若N为0则从第一个有效值开始算起;\\r\\n3、当N为有效值，但当前的k线数不足N根，按照实际的根数计算;\\r\\n4、N为空值时，返回空值。\\r\\n5、N可以是变量。\\r\\n\\r\\n例1：\\r\\nHH:HHV(H,4);//求4个周期最高价的最大值，即4周期高点（包含当前k线）。\\r\\n例2：\\r\\nN:=BARSLAST(DATE<>REF(DATE,1))+1;//分钟周期，日内k线根数\\r\\nHH1:=HHV(H,N);//在分钟周期上，日内高点", "explanation": "最高值", "markettype": 0, "modifytime": "", "param": "", "tip": "HHV(X,N),求X在N个周期内的最高值", "type": 2}, "HHVBARS": {"body": "HHVBARS( , )", "createtime": "", "description": "HHVBARS(X,N)： 求N周期内X最高值到当前周期数\\r\\n\\r\\n注：\\r\\n1、若N为0则从第一个有效值开始算起(不包含当前K线)；\\r\\n2、当N为有效值，但当前的k线数不足N根，按照实际的根数计算，第一根k线返回空值；\\r\\n3、N为空值时，返回空值。\\r\\n4、N可以是变量。\\r\\n\\r\\n例1：\\r\\nHHVBARS(VOL,0); 求历史成交量最大的周期到当前的周期数（最大值那根k线上HHVBARS(VOL,0);的返回值为0，最大值后的第一根k线返回值为1，依次类推）。\\r\\n例2：\\r\\nN:=BARSLAST(DATE<>REF(DATE,1))+1;//分钟周期，日内k线根数\\r\\nZHBARS:REF(HHVBARS(H,N),N)+N;//在分钟周期上，求昨天最高价所在的k线到当前k线之间的周期数。", "explanation": "前一最高点位置", "markettype": 0, "modifytime": "", "param": "", "tip": "HHVBARS(X,N),求N周期内X最高值到当前周期数", "type": 2}, "HIGH": {"body": "HIGH", "createtime": "", "description": "HIGH 取得K线图的最高价。\\r\\n\\r\\n注：\\r\\n1、可简写为H。\\r\\n\\r\\n例1：\\r\\nHH:H;//定义HH为最高价。\\r\\n例2：\\r\\nHH:HHV(H,5);//取的5个周期内最高价的最大值。\\r\\n例3：\\r\\nREF(H,1);//取的前一根K线的最高价", "explanation": "取得K线图的最高价", "markettype": 0, "modifytime": "", "param": "", "tip": "HIGH,取最高价", "type": 1}, "HISEXPDATE": {"body": "HISEXPDATE", "createtime": "20181120", "description": "HISEXPDATE 返回当前周期期货合约的最后交易日。\\r\\n\\r\\n用法：HISEXPDATE;取得当前周期期货合约的最后交易日。\\r\\n\\r\\n注：\\r\\n1、该函数返回期货合约的最后交易日，返回YYMMDD的形式。\\r\\n2、该函数只支持应用在日线及以下周期使用，在日周期以上的周期该函数返回值为0。\\r\\n3、应用到主连合约，返回对应的主力合约的最后交易日。\\r\\n   应用到月份合约，返回对应的月份合约的最后交易日。\\r\\n   应用到加权合约，返回值为0。\\r\\n4、该函数不支持在外盘主连合约上使用。\\r\\n\\r\\n例：\\r\\nA:HISEXPDATE;\\r\\nB:EXPIREDATE('');//A和B的返回值一样。加载到IF1406上返回值为140620。", "explanation": "返回当前周期期货合约的最后交易日", "markettype": 0, "modifytime": "20230707", "param": "", "tip": "HISEXPDATE返回当前周期期货合约的最后交易日", "type": 7}, "HISEXPDAYS": {"body": "HISEXPDAYS", "createtime": "20181120", "description": "HISEXPDAYS 返回当前周期期货合约距离最后交易日的天数。\\r\\n\\r\\n用法：HISEXPDAYS;取得当前周期期货合约距离最后交易日的天数。\\r\\n\\r\\n注：\\r\\n1、该函数只支持应用在日线及以下周期使用，在日周期以上的周期该函数返回值为0。\\r\\n2、应用到主连合约，返回对应的主力合约距最后交易日的天数。\\r\\n   应用到连续合约，返回对应的月份合约距最后交易日的天数。\\r\\n   应用到加权合约，返回值为0。\\r\\n3、该函数不支持在外盘主连合约上使用。\\r\\n\\r\\n例：\\r\\nA:=HISEXPDAYS=1&&CLOSEMINUTE=5;//定义变量A为最后交易日收盘前五分钟。", "explanation": "返回当前周期期货合约距离最后交易日的天数", "markettype": 0, "modifytime": "20220425", "param": "", "tip": "HISEXPDAYS返回当前周期期货合约距离最后交易日的天数", "type": 7}, "HOLLOW": {"body": "HOLLOW", "createtime": "********", "description": "HOLLOW 空心显示。\\r\\n\\r\\n用法：\\r\\n用在VOLSTICK、VOLUMESTICK函数后面，表示柱线空心显示。\\r\\n\\r\\n注：\\r\\n仅支持与VOLSTICK、VOLUMESTICK函数连用。\\r\\n\\r\\n例：\\r\\nVOL,VOLUMESTICK,HOLLOW;//画成交量柱状线，柱线空心显示。", "explanation": "空心显示", "markettype": 0, "modifytime": "", "param": "", "tip": "HOLLOW,画空心柱线", "type": 8}, "HOUR": {"body": "HOUR", "createtime": "", "description": "HOUR，返回某周期的小时数。\\r\\n\\r\\n注：\\r\\nHOUR的取值范围为0—23\\r\\n\\r\\n例1：\\r\\nNX:BARSLAST(CROSS(HOUR=9,0.5));\\r\\nDRAWLINE3(CROSSDOWN(HOUR=14,0.5),REF(H,NX),NX,CROSSDOWN(HOUR=14,0.5),REF(H,1),1,0),COLORGREEN;\\r\\n//连接9点到收盘前最后一根k线高点的连线。\\r\\n例2：\\r\\nHOUR=10;//在10:00的K线上返回值为1，其余K线上返回值为0。", "explanation": "小时", "markettype": 0, "modifytime": "", "param": "", "tip": "HOUR取某周期的小时（0-23）", "type": 7}, "HV": {"body": "HV( , )", "createtime": "", "description": "HV(X,N)： 求X在N个周期内（不包含当前k线）的最高值。\\r\\n\\r\\n注：\\r\\n1、若N为0则从第一个有效值开始算起(不包含当前K线)；\\r\\n2、当N为有效值，但当前的k线数不足N根，按照实际的根数计算，第一根k线返回空值；\\r\\n3、N为空值时，返回空值。\\r\\n4、N可以是变量。\\r\\n\\r\\n例1：\\r\\nHH:HV(H,10);//求前10根k线的最高点。\\r\\n例2：\\r\\nN:=BARSLAST(DATE<>REF(DATE,1))+1;\\r\\nNN:=REF(N,N);\\r\\nZH:VALUEWHEN(DATE<>REF(DATE,1),HV(H,NN));//在分钟周期上，求昨天最高价。\\r\\n例3：\\r\\nHV(H,5) 和 REF(HHV(H,5),1) 的结果是一样的，用HV编写更加方便。", "explanation": "除当前K线外最高值", "markettype": 0, "modifytime": "", "param": "", "tip": "HV(X,N)求X在N个周期内的最高值(不包含当前K线)", "type": 2}, "ICON": {"body": "ICON( , )", "createtime": "", "description": "ICON函数 在k线图上，显示小图标。\\r\\n\\r\\n用法：ICON(TYPE,ICON);\\r\\n当TYPE为1，则在K线最高价位置显示图标ICON，当TYPE为0，则在最低价位置显示图标ICON。\\r\\n\\r\\n注：\\r\\n1、该函数与判断条件连用，如：COND,ICON(TYPE,ICON);\\r\\n2、不支持将函数定义为变量，即不支持下面的写法：A:ICON(TYPE,ICON);\\r\\n3、该函数支持在函数后设置设置文字垂直对齐方式：VALIGN0（上对齐）、VALIGN1（中对齐）、VALIGN2（下对齐）\\r\\n即可以写为如下格式：\\r\\nCLOSE<OPEN,WORD(1,'阴'),VALIGN0\\r\\n\\r\\n例1：\\r\\nCLOSE>OPEN,ICON(1,'ICO1');//表示K线收盘大于开盘时，在最高价上显示图标1。\\r\\n写完“ICON(1,” 以后，点击插入图标按钮，再单击选中的图标插入到函数中，图标用'ICO1'~'ICO105'表示", "explanation": "显示图标", "markettype": 0, "modifytime": "", "param": "", "tip": "ICON(TYPE,ICON),在k线图上，显示小图标当TYPE为1，则在K线最高价位置显示图标ICON当TYPE为0，则在K线最低价位置显示图标ICON", "type": 8}, "IDLE": {"body": "IDLE()", "createtime": "20160126", "description": "IDLE(COND) 限制开仓信号发出委托\\r\\n\\r\\n用法：IDLE(COND)，当开仓信号发出时，如果COND条件成立，该信号不委托。IDLE函数对平仓信号不起作用，有持仓时即使满足COND也可以平仓。\\r\\n\\r\\n注：\\r\\n1、模组加载运行：\\r\\n（1）该函数不影响模型出现信号，满足开仓条件，图中仍然会出现信号\\r\\n（2）COND条件满足后，只对开仓信号起作用，开仓信号发出时，如果COND条件成立，该信号不委托；平仓信号发出时，如果模组单元持仓>0，根据平仓信号正常进行委托\\r\\n（3）COND条件满足后，理论持仓、理论资金的计算不受该函数的影响，仍然根据信号正常执行进行计算\\r\\n（4）COND条件满足后，可以进行头寸同步、手动调仓、补仓的操作\\r\\n2、回测：\\r\\n（1）该函数不影响模型出现信号，满足开仓条件，图中仍然会出现信号\\r\\n（2）模型中含有IDLE函数，信号仍然根据理论持仓、理论资金进行计算\\r\\n（3）模型中含有IDLE函数，回测报告根据模组理论持仓、资金进行计算\\r\\n3、该函数支持与反手指令同时使用。\\r\\n4、该函数与反手指令同时使用CODE条件中不支持含有平仓盈亏相关的函数（例如连续亏损、盈利次数以及可用资金）。\\r\\n5、该函数不支持加载到套利合约使用。\\r\\n6、该函数只允许在模组中使用，不支持加载到盒子。\\r\\n\\r\\n例:\\r\\nMA10:MA(C,20);\\r\\nMA30:MA(C,30);\\r\\nGG:=HHV(MONEYTOT,0);//第一个有效值开始权益的最大值\\r\\nC>MA30,BK;\\r\\nC<MA30,SK;\\r\\nC>MA10,BP;\\r\\nC<MA10,SP;\\r\\nIDLE(MONEYTOT<GG*0.95&&MONEYTOT>GG*0.92||MONEYTOT<GG*0.85);//权益回撤一定幅度停止交易\\r\\nAUTOFILTER;", "explanation": "限制开仓信号发出委托", "markettype": 1, "modifytime": "20240320", "param": "", "tip": "IDLE(CONE),限制开仓信号发出委托", "type": 13}, "IF": {"body": "IF( , , )", "createtime": "20140812", "description": "IF(COND,A,B) 若COND条件成立，则返回A，否则返回B\\r\\n\\r\\n注：\\r\\n1、COND是判断条件;A、B可以是条件，也可以是数值。\\r\\n2、该函数支持变量循环引用前一周期自身变量，即支持下面这样的写法Y: IF(CON,X,REF(Y,1));\\r\\n例1：\\r\\nIF(ISUP,H,L);//k线为阳线，取最高价，否则取最低价\\r\\n例2：\\r\\nA:=IF(MA5>MA10,CROSS(DIFF,DEA),IF(CROSS(D,K),2,0));//当MA5>MA10时，取是否满足DIFF上穿DEA，否则(MA5不大于MA10)，当K,D死叉时，令A赋值为2，若上述条件都不满足，A赋值为0\\r\\nA=1,BPK;//当MA5>MA10，以DIFF上穿DEA作为开多仓条件\\r\\nA=2,SPK;//当MA5不大于MA10，以K、D死叉作为开空仓条件", "explanation": "条件函数", "markettype": 0, "modifytime": "", "param": "", "tip": "IF(X,A,B),若满足条件X则取A，否则取B", "type": 5}, "IFELSE": {"body": "IFELSE( , , )", "createtime": "", "description": "IFELSE(COND,A,B) 若COND条件成立，则返回A，否则返回B\\r\\n\\r\\n注：\\r\\n1、COND是判断条件;A、B可以是条件，也可以是数值。\\r\\n2、该函数支持变量循环引用前一周期自身变量，即支持下面这样的写法Y: IFELSE(CON,X,REF(Y,1));\\r\\n例1：\\r\\nIFELSE(ISUP,H,L);//k线为阳线，取最高价，否则取最低价\\r\\n例2：\\r\\nA:=IFELSE(MA5>MA10,CROSS(DIFF,DEA),IFELSE(CROSS(D,K),2,0));//当MA5>MA10时，取是否满足DIFF上穿DEA，否则(MA5不大于MA10)，当K,D死叉时，令A赋值为2，若上述条件都不满足，A赋值为0\\r\\nA=1,BPK;//当MA5>MA10，以DIFF上穿DEA作为开多仓条件\\r\\nA=2,SPK;//当MA5不大于MA10，以K、D死叉作为开空仓条件", "explanation": "条件函数", "markettype": 0, "modifytime": "", "param": "", "tip": "IFELSE(X,A,B),若满足条件X则取A，否则取B", "type": 5}, "IMPLIEDVOLATILITY": {"body": "IMPLIEDVOLATILITY", "createtime": "20220127", "description": "IMPLIEDVOLATILITY  取期权隐含波动率\\r\\n\\r\\n原理：\\r\\n隐含波动率：根据期权市场当时价格倒推出的波动率，反映期权当前价格对未来波动率的预期。\\r\\n\\r\\n注：\\r\\n1、该函数仅支持加载在期权上，加载在其他合约返回空值；\\r\\n2、该函数不支持加载在量能周期；\\r\\n3、该函数不支持在跨周期、跨合约的引用模型中使用；\\r\\n4、该函数不支持与运行优化函数一起使用。\\r\\n\\r\\n例：\\r\\nAA:IMPLIEDVOLATILITY;//AA返回期权的隐含波动率。\\r\\n ", "explanation": "取期权隐含波动率", "markettype": 0, "modifytime": "20220215", "param": "", "tip": "IMPLIEDVOLATILITY，取期权隐含波动率", "type": 1}, "INITMONEY": {"body": "INITMONEY", "createtime": "20141219", "description": "INITMONEY 初次加载时的起始资金\\r\\n\\r\\n用法：INITMONEY 返回初次加载时的起始资金。\\r\\n\\r\\n注：\\r\\n1、回测时，该函数返回回测参数中设置的初始资金。\\r\\n2、主图右键装入模组时，该函数返回回测参数中设置的初始资金。\\r\\n3、新建模组时，该函数返回单元参数中设置的起始资金。\\r\\n4、组合测试中该函数返回添加组合成员时设置的初始资金。\\r\\n5、回测一篮子合约时，该函数返回主图回测参数中设置的初始资金。\\r\\n\\r\\n例：\\r\\nK:=INITMONEY*0.2/(C*MARGIN*UNIT+FEE); //初始资金的20%可以开仓的手数（此写法适用于按固定手数收取手续费的合约）", "explanation": "初次加载时的起始资金", "markettype": 1, "modifytime": "20230216", "param": "", "tip": "INITMONEY,返回初次加载时的起始资金", "type": 12}, "INTPART": {"body": "INTPART( )", "createtime": "", "description": "INTPART(X)：取X的整数部分。\\r\\n\\r\\n例1：\\r\\nINTPART(12.3);//返回值为12；\\r\\n例2：\\r\\nINTPART(-3.5);//返回值为-3；\\r\\n例3：\\r\\nINTPART(10);//返回值为10；\\r\\n例5：\\r\\nINTPART(C);//求收盘价的整数部分。", "explanation": "取整", "markettype": 0, "modifytime": "", "param": "", "tip": "INTPART(X),取X的整数部分", "type": 4}, "ISCONTRACT": {"body": "ISCONTRACT()", "createtime": "20150417", "description": "ISCONTRACT() 当前是否为指定的合约。\\r\\n\\r\\n用法：ISCONTRACT('CODE');是当前合约返回1，不是当前合约返回0。\\r\\n\\r\\n注：\\r\\n1、判断是否为指定合约时，CODE可以为合约的交易代码或者文华码\\r\\n2、判断是否为指定品种时，CODE必须为品种的中文名称（只支持内盘品种）\\r\\n3、对于国内期权合约判断是否为指定品种时，采用字符串从左开始匹配，CODE参数前N位与合约名称一致，认为是同一品种\\r\\n\\r\\n例1：\\r\\nISCONTRACT('CLZ5');//加载到美原油12上返回值为1，加载到非美原油12上返回值为0。\\r\\n例2：\\r\\nISCONTRACT('沪铜');//加载到沪铜合约上返回值为1，加载到非沪铜合约上返回值为0。", "explanation": "当前是否为指定的合约", "markettype": 0, "modifytime": "", "param": "", "tip": "ISCONTRACT('CODE')当前是否为指定的合约", "type": 5}, "ISDELIVERYDAY": {"body": "ISDELIVERYDAY", "createtime": "", "description": "ISDELIVERYDAY 判断该周期是否是最后交易日。当前k线是最后交易日则返回1(Yes)，否则返回0(No)\\r\\n\\r\\n注：\\r\\n1、只能使用在日线及小于日线的周期，在周线月线等大于日线的周期使用时返回值始终为0。\\r\\n2、该函数应加载在可交易合约上。\\r\\n3、该函数加载到主连合约上按照对应的主力合约的最后交易日期进行判断，因此不能在主连合约上使用。\\r\\n\\r\\n例1：\\r\\nISDELIVERYDAY=1&&TIME>=1000,CLOSEOUT;//当根k线是最后交易日并且时间是10:00,全平。", "explanation": "判断该周期是不是最后交易日", "markettype": 1, "modifytime": "20230707", "param": "", "tip": "ISDELIVERYDAY,判断当根k线是否是最后交易日", "type": 5}, "ISDOWN": {"body": "ISDOWN", "createtime": "", "description": "ISDOWN 判断该周期是否收阴\\r\\n\\r\\n注：\\r\\n1、ISDOWN等同于C<O\\r\\n\\r\\n例：\\r\\nISDOWN=1&&C<REF(C,1),SK;//当根k线收阴并且收盘价小于前一周期收盘价，则开空\\r\\n//ISDOWN=1&&C<REF(C,1),SK; 与 ISDOWN&&C<REF(C,1),SK; 表达同等意义", "explanation": "阴线", "markettype": 0, "modifytime": "", "param": "", "tip": "ISDOWN,判断该周期是否收阴。如果为阴线返回1，否则返回0", "type": 5}, "ISEQUAL": {"body": "ISEQUAL", "createtime": "", "description": "ISEQUAL 判断该周期是否平盘\\r\\n\\r\\n注：\\r\\n1、ISEQUAL等同于C=O\\r\\n\\r\\n例1：\\r\\nEVERY(ISEQUAL=1,2),CLOSEOUT;//持续2根k线都是平盘，则全平。", "explanation": "平盘", "markettype": 0, "modifytime": "", "param": "", "tip": "ISEQUAL,判断该周期是否平盘，如果K线为平盘返回1，否则返回0", "type": 5}, "ISLASTBAR": {"body": "ISLASTBAR", "createtime": "20160106", "description": "ISLASTBAR 判断该周期是否为最后一根k线。\\r\\n\\r\\n注：\\r\\n该函数仅支持在绘图函数中使用。\\r\\n \\r\\n例1：\\r\\nDRAWNUMBER(ISLASTBAR=1,HIGH,CLOSE,0,COLORRED);//当前k线是最后一根k线，则在最高价位置红色显示收盘价。", "explanation": "判断该周期是否为最后一根K线", "markettype": 0, "modifytime": "", "param": "", "tip": "ISLASTBAR,判断是否是最后一个K线，如果为最后一根K线返回1，否则返回0", "type": 8}, "ISLASTBK": {"body": "ISLASTBK", "createtime": "20140918", "description": "ISLASTBK 判断上一个交易信号是否是BK。\\r\\n\\r\\n用法：\\r\\nISLASTBK 上一个交易信号是BK则返回1（Yes），否则返回0（No）\\r\\na.信号执行方式选择K线走完确认信号下单及K线走完进行信号复核（例如：在模型中写入CHECKSIG(BK,'A',0,'D',0,3);）\\r\\nBK信号未确认时，ISLASTBK返回值0；\\r\\nBK信号确认后，ISLASTBK返回1\\r\\nb.信号执行方式选择不进行信号复核（例如：在模型中写入MULTSIG或MULTSIG_MIN;），BK信号当根ISLASTBK返回值为1\\r\\n\\r\\n注：模型中含有BPK条件，且上一个信号为平仓信号时，由BPK指令产生的BK信号，ISLASTBK返回0，ISLASTBPK返回1。\\r\\n\\r\\n例:\\r\\nC>O,BK;\\r\\nISLASTBK&&C>BKPRICE,SP;\\r\\nAUTOFILTER;//上一个信号是BK信号，且最新价大于开仓价格，卖平仓", "explanation": "判断上一个信号是否是BK", "markettype": 1, "modifytime": "", "param": "", "tip": "ISLASTBK，判断上一个指令是否是买开", "type": 10}, "ISLASTBP": {"body": "ISLASTBP", "createtime": "20140918", "description": "ISLASTBP 判断上一个交易信号是否是BP。\\r\\n\\r\\n用法：\\r\\nISLASTBP 上一个交易信号是BP则返回1（Yes），否则返回0（No）\\r\\na.信号执行方式选择K线走完确认信号下单及K线走完进行信号复核（例如：在模型中写入CHECKSIG(BP,'A',0,'D',0,3);）\\r\\nBP信号未确认时，ISLASTBP返回值0；\\r\\nBP信号确认后，ISLASTBP返回1\\r\\nb.信号执行方式选择不进行信号复核（例如：在模型中写入MULTSIG或MULTSIG_MIN;），BP信号当根ISLASTBP返回值为1\\r\\n\\r\\n例:\\r\\nC<O,SK(2);\\r\\nC>O,BP(1);\\r\\nISLASTBP,BP(1);//上一个信号是买平仓信号，则减仓一手", "explanation": "判断上一个信号是否是BP", "markettype": 1, "modifytime": "", "param": "", "tip": "ISLASTBP，判断上一个指令是否是买平", "type": 10}, "ISLASTBPK": {"body": "ISLASTBPK", "createtime": "20140918", "description": "ISLASTBPK判断上一个交易信号是否是BPK。\\r\\n\\r\\n用法：\\r\\nISLASTBPK 上一个交易信号是BPK则返回1（Yes），否则返回0（No）\\r\\na.信号执行方式选择K线走完确认信号下单及K线走完进行信号复核（例如：在模型中写入CHECKSIG(BPK,'A',0,'D',0,3);）\\r\\nBPK信号未确认时，ISLASTBPK返回值0；\\r\\nBPK信号确认后，ISLASTBPK返回1\\r\\nb.信号执行方式选择不进行信号复核（例如：在模型中写入MULTSIG或MULTSIG_MIN;），BPK信号当根ISLASTBPK返回值为1\\r\\n\\r\\n注：模型中含有BPK条件，且上一个信号为平仓信号时，由BPK指令产生的BK信号，ISLASTBK返回0，ISLASTBPK返回1。\\r\\n\\r\\n例:\\r\\nC>O,BPK;\\r\\nISLASTBPK&&C<O,SPK;\\r\\nAUTOFILTER;//上一个信号是BPK信号，则反手SPK", "explanation": "判断上一个信号是否是BPK", "markettype": 1, "modifytime": "", "param": "", "tip": "ISLASTBPK，判断上一个指令是否是买平开", "type": 10}, "ISLASTBUY": {"body": "ISLASTBUY", "createtime": "20221201", "description": "ISLASTBUY 判断上一个交易信号是否是BUY。\\r\\n\\r\\n用法：\\r\\nISLASTBUY 上一个交易信号是BUY则返回1（Yes），否则返回0（No）\\r\\n\\r\\n注：\\r\\n1、该函数支持在股票T+1策略运行池中使用。\\r\\n\\r\\n例:\\r\\nC>O,BUY;\\r\\nISLASTBUY&&C>BUYPRICE,SELL;\\r\\nAUTOFILTER;//上一个信号是BUY信号，且最新价大于买入价格，卖出持仓。", "explanation": "判断上一个交易信号是否是BUY", "markettype": 2, "modifytime": "20221205", "param": "", "tip": "ISLASTBUY 判断上一个交易信号是否是BUY。", "type": 10}, "ISLASTCLOSEOUT": {"body": "ISLASTCLOSEOUT", "createtime": "20140918", "description": "ISLASTCLOSEOUT判断上一个信号是否CLOSEOUT 。\\r\\n\\r\\n用法：\\r\\n1、ISLASTCLOSEOUT 上一个交易信号是CLOSEOUT返回1（Yes），否则返回0（No）\\r\\n2、CLOSEOUT默认为出信号立即下单不复核\\r\\na)收盘价模型中，CLOSEOUT信号下一根K线的ISLASTCLOSEOUT返回值为1\\r\\nb)指令价模型中，CLOSEOUT信号当根K线的ISLASTCLOSEOUT返回值为1\\r\\n\\r\\n例:\\r\\nISLASTCLOSEOUT&&C>O,BK(1);//上一个信号是清仓信号，并且当根K线是阳线，则买开一手", "explanation": "判断上一个信号是否是CLOSEOUT", "markettype": 1, "modifytime": "20220104", "param": "", "tip": "ISLASTCLOSEOUT，判断上一个指令是否是全平", "type": 10}, "ISLASTKLINE": {"body": "ISLASTKLINE", "createtime": "", "description": "ISLASTKLINE 判断该周期是否为每日收盘前最后一根k线，返回是1（Yes），否则返回0（No）。\\r\\n\\r\\n注：\\r\\n1、对于夜盘合约，夜盘收盘不是当日收盘，15点收盘才算作当日收盘。\\r\\n2、夜盘合约只显示白盘数据时，白盘收盘前最后一根K线ISLASTKLINE返回1；只显示夜盘数据时，夜盘收盘前最后一根K线ISLASTKLINE返回1。\\r\\n\\r\\n例1：\\r\\nISLASTKLINE=1,CLOSEOUT;//若该周期是当日收盘前最后一根k线，则全平。", "explanation": "判断该周期是否收盘前最后一根K线", "markettype": 1, "modifytime": "", "param": "", "tip": "ISLASTKLINE,判断该周期是否是当日收盘前最后一个K线，如果是返回1，否则返回0", "type": 5}, "ISLASTSELL": {"body": "ISLASTSELL", "createtime": "20221201", "description": "ISLASTSELL判断上一个交易信号是否是SELL。\\r\\n\\r\\n用法：\\r\\nISLASTSELL 上一个交易信号是SELL则返回1（Yes），否则返回0（No）\\r\\n\\r\\n注：\\r\\n1、该函数支持在股票T+1策略运行池中使用。\\r\\n\\r\\n例:ISLASTSELL,BUY;//上一个信号是卖出信号，则买入。", "explanation": "判断上一个交易信号是否是SELL", "markettype": 2, "modifytime": "20221205", "param": "", "tip": "ISLASTSELL判断上一个交易信号是否是SELL。", "type": 10}, "ISLASTSK": {"body": "ISLASTSK", "createtime": "20140918", "description": "ISLASTSK 判断上一个交易信号是否是SK。\\r\\n\\r\\n用法：\\r\\nISLASTSK 上一个交易信号是SK则返回1（Yes），否则返回0（No）\\r\\na.信号执行方式选择K线走完确认信号下单及K线走完进行信号复核（例如：在模型中写入CHECKSIG_(SK,'A',0,'D',0,3);）\\r\\nSK信号未确认时，ISLASTSK返回值0；\\r\\nSK信号确认后，ISLASTSK返回1\\r\\nb.信号执行方式选择不进行信号复核（例如：在模型中写入MULTSIG或MULTSIG_MIN;），SK信号当根ISLASTSK返回值为1\\r\\n\\r\\n注：模型中含有SPK条件，且上一个信号为平仓信号时，由SPK指令产生的SK信号，ISLASTSK返回0，ISLASTSPK返回1。\\r\\n\\r\\n例:\\r\\nC<O,SK;\\r\\nISLASTSK&&C<SKPRICE,BP;\\r\\nAUTOFILTER;//上一个信号是SK信号，且最新价小于开仓价格，买平仓", "explanation": "判断上一个信号是否是SK", "markettype": 1, "modifytime": "", "param": "", "tip": "ISLASTSK，判断上一个指令是否是卖开", "type": 10}, "ISLASTSP": {"body": "ISLASTSP", "createtime": "20140918", "description": "ISLASTSP判断上一个交易信号是否是SP。\\r\\n\\r\\n用法：\\r\\nISLASTSP 上一个交易信号是SP则返回1（Yes），否则返回0（No）\\r\\na.信号执行方式选择K线走完确认信号下单及K线走完进行信号复核（例如：在模型中写入CHECKSIG(SP,'A',0,'D',0,3);）\\r\\nSP信号未确认时，ISLASTSP返回值0；\\r\\nSP信号确认后，ISLASTSP返回1\\r\\nb.信号执行方式选择不进行信号复核（例如：在模型中写入MULTSIG或MULTSIG_MIN;），SP信号当根ISLASTSP返回值为1\\r\\n\\r\\n例:\\r\\nC>O,BK(2);\\r\\nC<O,SP(1);\\r\\nISLASTSP,SP(1);//上一个信号是卖平仓信号，则减仓一手", "explanation": "判断上一个信号是否是SP", "markettype": 1, "modifytime": "", "param": "", "tip": "ISLASTSP，判断上一个指令是否是卖平", "type": 10}, "ISLASTSPK": {"body": "ISLASTSPK", "createtime": "20140918", "description": "ISLASTSPK判断上一个交易信号是否是SPK。\\r\\n\\r\\n用法：\\r\\nISLASTSPK 上一个交易信号是SPK则返回1（Yes），否则返回0（No）\\r\\na.信号执行方式选择K线走完确认信号下单及K线走完进行信号复核（例如：在模型中写入CHECKSIG(SPK,'A',0,'D',0,3);）\\r\\nSPK信号未确认时，ISLASTSPK返回值0；\\r\\nSPK信号确认后，ISLASTSPK返回1\\r\\nb.信号执行方式选择不进行信号复核（例如：在模型中写入MULTSIG或MULTSIG_MIN;），SPK信号当根ISLASTSPK返回值为1\\r\\n\\r\\n注：模型中含有SPK条件，且上一个信号为平仓信号时，由SPK指令产生的SK信号，ISLASTSK返回0，ISLASTSPK返回1。\\r\\n\\r\\n例:\\r\\nC<O,SPK;\\r\\nISLASTSPK&&C>O,BPK;\\r\\nAUTOFILTER;//上一个信号是SPK信号，则反手BPK", "explanation": "判断上一个信号是否是SPK", "markettype": 1, "modifytime": "", "param": "", "tip": "ISLASTSPK，判断上一个指令是否是卖平开", "type": 10}, "ISLASTSTOP": {"body": "ISLASTSTOP", "createtime": "20150429", "description": "ISLASTSTOP 判断上一个交易信号是否是STOP。\\r\\n\\r\\n用法：\\r\\nISLASTSTOP 上一个交易信号是STOP则返回1（Yes），否则返回0（No）\\r\\n\\r\\n注：收盘价模型STOP信号下根K线ISLASTSTOP返回值为1；指令价模型STOP信号当根K线ISLASTSTOP返回值为1。\\r\\n\\r\\n例:\\r\\nCROSS(C,MA(C,5)),BK(2);\\r\\nSTOP(0,5);\\r\\nISLASTSTOP&&CROSS(C,MA(C,10)),BK(1);//上一个信号是STOP信号，且价格上穿10周期均线，开仓一手", "explanation": "判断上一个信号是否是STOP", "markettype": 1, "modifytime": "", "param": "", "tip": "ISLASTSTOP，判断上一个指令是否是STOP指令", "type": 10}, "ISMAINCONTRACT": {"body": "ISMAINCONTRACT", "createtime": "20160302", "description": "ISMAINCONTRACT 当前是否为主力合约。\\r\\n\\r\\n用法：\\r\\nISMAINCONTRACT;取当前交易合约判断是否是主力合约，是返回1，否返回0\\r\\n\\r\\n注：\\r\\n1、若交易合约不是普通合约则返回空值\\r\\n2、若没有指定交易合约，则取数据合约。\\r\\n\\r\\n例：\\r\\nISMAINCONTRACT;//当前的交易合约为主力合约，返回1，否则返回0", "explanation": "当前是否为主力合约", "markettype": 1, "modifytime": "", "param": "", "tip": "ISMAINCONTRACT当前是否为主力合约", "type": 5}, "ISMONTHEND": {"body": "ISMONTHEND", "createtime": "20180108", "description": "ISMONTHEND 是否为本月最后一个交易日\\r\\n此函数为系统封装函数。\\r\\n\\r\\n用法：\\r\\nISMONTHEND 是否为本月最后一个交易日\\r\\n\\r\\n例：\\r\\nCLOSE<MA(CLOSE,5) || ISMONTHEND,SP;//如果满足平仓条件或者当前为本月最后一个交易日，平仓", "explanation": "是否为本月最后一个交易日", "markettype": 1, "modifytime": "", "param": "", "tip": "ISMONTHEND是否为本月最后一个交易日", "type": 5}, "ISNEARHOLIDAY": {"body": "ISNEARHOLIDAY", "createtime": "********", "description": "ISNEARHOLIDAY 判断下一交易日是否是交易合约的节假日。交易合约下一交易日是节假日，当前k线返回1（Yes）,否则返回0（N0）\\r\\n\\r\\n注：\\r\\n1、该函数不支持在外盘上使用\\r\\n2、该函数依据交易合约判断。\\r\\n\\r\\n例：\\r\\nISNEARHOLIDAY=1&&TIME>=1000,CLOSEOUT;//下一个交易日是节假日并且时间是10:00,全平。", "explanation": "判断下一交易日是否是交易合约的节假日", "markettype": 0, "modifytime": "", "param": "", "tip": "ISNEARHOLIDAY，判断下一交易日是否是交易合约的节假日。", "type": 5}, "ISNULL": {"body": "ISNULL", "createtime": "20141216", "description": "ISNULL 判断空值\\r\\n\\r\\n用法：ISNULL(N);如果N为空值，函数返回1；如果N为非空值，函数返回0。\\r\\n\\r\\n例：MA5:IFELSE(ISNULL(MA(C,5))=1,C,MA(C,5));//定义五周期均线，K线数量不足五根时，返回当根K线的收盘价", "explanation": "判断空值", "markettype": 0, "modifytime": "", "param": "", "tip": "ISNULL(N)判断空值，如果N为空值返回1，否则返回0", "type": 5}, "ISRECORDDAY": {"body": "ISRECORDDAY", "createtime": "20161026", "description": "ISRECORDDAY  判断当根K线是否为股权登记日\\r\\n\\r\\n用法：ISRECORDDAY  判断当根K线是否为股权登记日，当根K线是股权登记日返回是1（Yes），否则返回0（No）。\\r\\n\\r\\n注：\\r\\n1、该函数只支持加载在国内股票日线及以下周期使用，加载在非国内股票合约或日以上周期时返回值为0；\\r\\n2、跨合约/跨周期被引用指标中返回值为0。", "explanation": "判断当根K线是否为股权登记日", "markettype": 0, "modifytime": "20221020", "param": "", "tip": "ISRECORDDAY判断当根K线是否为股权登记日", "type": 15}, "ISTIMETOKLINEEND": {"body": "ISTIMETOKLINEEND", "createtime": "20180628", "description": "ISTIMETOKLINEEND(N) 判断当前K线时间是否满足K线走完前N秒。\\r\\n\\r\\n用法：\\r\\nISTIMETOKLINEEND(N) 判断当前K线时间是否满足K线走完前N秒,满足返回1，不满足返回0。\\r\\n参数N为秒数。\\r\\n\\r\\n注：\\r\\n1、仅支持在日线及日线以下周期使用。\\r\\n2、该函数不支持使用在跨周期模型的被引用指标中。\\r\\n\\r\\n例：\\r\\nC>O&&ISTIMETOKLINEEND(5),BK;//当前K线为阳线并且满足K线走完前5秒，买开\\r\\nC<O&&ISTIMETOKLINEEND(5),SP;//当前K线为阴线并且满足K线走完前5秒，卖平\\r\\nAUTOFILTER;\\r\\nMULTSIG(0,0,3,0);", "explanation": "判断当前K线时间是否满足K线走完前N秒", "markettype": 1, "modifytime": "20220608", "param": "", "tip": "ISTIMETOKLINEEND(N)判断当前K线时间是否满足K线走完前N秒满足返回1，不满足返回0。参数N为秒数。", "type": 5}, "ISUP": {"body": "ISUP", "createtime": "", "description": "ISUP 判断该周期是否收阳\\r\\n\\r\\n注：\\r\\n1、ISUP等同于C>O\\r\\n\\r\\n例：\\r\\nISUP=1&&C>REF(C,1),BK;//若当根k线收阳并且收盘价大于前一周期收盘价，则开多\\r\\n//ISUP=1&&C>REF(C,1),BK; 与 ISUP&&C>REF(C,1),BK;//表达同等意义", "explanation": "阳线", "markettype": 0, "modifytime": "", "param": "", "tip": "ISUP,判断该周期是否收阳，如果K线为阳线返回1，否则返回0", "type": 5}, "ISWEEKEND": {"body": "ISWEEKEND", "createtime": "20180108", "description": "ISWEEKEND 判断是否为本周最后一个交易日\\r\\n此函数为系统封装函数。\\r\\n\\r\\n用法：\\r\\nISWEEKEND 判断是否为本周最后一个交易日\\r\\n\\r\\n例：\\r\\nC<MA(C,5) || ISWEEKEND,SP;//如果满足平仓条件或者当前为本周最后一个交易日，平仓", "explanation": "是否为本周最后一个交易日", "markettype": 1, "modifytime": "", "param": "", "tip": "ISWEEKEND判断是否为本周最后一个交易日", "type": 5}, "IS_WH9": 0, "KEYWORD": {"Num": 158, "keyword1": "O", "keyword10": "LIMIT_ORDER", "keyword100": "FONTSIZE36", "keyword101": "FONTSIZE37", "keyword102": "FONTSIZE38", "keyword103": "FONTSIZE39", "keyword104": "FONTSIZE40", "keyword105": "FONTSIZE41", "keyword106": "FONTSIZE42", "keyword107": "FONTSIZE43", "keyword108": "FONTSIZE44", "keyword109": "FONTSIZE45", "keyword11": "NEW_ORDER", "keyword110": "FONTSIZE46", "keyword111": "FONTSIZE47", "keyword112": "FONTSIZE48", "keyword113": "FONTSIZE49", "keyword114": "FONTSIZE50", "keyword115": "FONTSIZE51", "keyword116": "FONTSIZE52", "keyword117": "FONTSIZE53", "keyword118": "FONTSIZE54", "keyword119": "FONTSIZE55", "keyword12": "SIGPRICE_ORDER", "keyword120": "FONTSIZE56", "keyword121": "FONTSIZE57", "keyword122": "FONTSIZE58", "keyword123": "FONTSIZE59", "keyword124": "FONTSIZE60", "keyword125": "FONTSIZE61", "keyword126": "FONTSIZE62", "keyword127": "FONTSIZE63", "keyword128": "FONTSIZE64", "keyword129": "FONTSIZE65", "keyword13": "COLORRED", "keyword130": "FONTSIZE66", "keyword131": "FONTSIZE67", "keyword132": "FONTSIZE68", "keyword133": "FONTSIZE69", "keyword134": "FONTSIZE70", "keyword135": "FONTSIZE71", "keyword136": "FONTSIZE72", "keyword137": "CUSTOM_DAY", "keyword138": "CUSTOM_HOUR", "keyword139": "CUSTOM_MIN", "keyword14": "COLORGRAY", "keyword140": "CUSTOM_SEC", "keyword141": "STOP", "keyword142": "STICK1", "keyword143": "CANCEL_ORDER", "keyword144": "RATIO_CODE", "keyword145": "RATIO_ACCOUNT", "keyword146": "AND", "keyword147": "OR", "keyword148": "THEN", "keyword149": "BEGIN", "keyword15": "COLORGREEN", "keyword150": "END", "keyword151": "CLOSEOUT", "keyword152": "ELSE", "keyword153": "V", "keyword154": "BOLD", "keyword155": "AS", "keyword156": "VARIABLE", "keyword157": "BUY", "keyword158": "SELL", "keyword16": "COLORBLUE", "keyword17": "COLORMAGENTA", "keyword18": "COLORYELLOW", "keyword19": "COLORLIGHTGREY", "keyword2": "H", "keyword20": "COLORLIGHTRED", "keyword21": "COLORLIGHTGREEN", "keyword22": "COLORLIGHTBLUE", "keyword23": "COLORBLACK", "keyword24": "COLORWHITE", "keyword25": "COLORCYAN", "keyword26": "OPISTICK", "keyword27": "BK", "keyword28": "SK", "keyword29": "BP", "keyword3": "L", "keyword30": "SP", "keyword31": "BPK", "keyword32": "SPK", "keyword33": "LINETHICK1", "keyword34": "LINETHICK2", "keyword35": "LINETHICK3", "keyword36": "LINETHICK4", "keyword37": "LINETHICK5", "keyword38": "LINETHICK6", "keyword39": "LINETHICK7", "keyword4": "C", "keyword40": "MAX_VALUE", "keyword41": "MIN_VALUE", "keyword42": "MAX_POS", "keyword43": "MIN_POS", "keyword44": "MAX1_VALUE", "keyword45": "MIN1_VALUE", "keyword46": "MAX1_POS", "keyword47": "MIN1_POS", "keyword48": "SECONDMAX_VALUE", "keyword49": "SECONDMIN_VALUE", "keyword5": "SEC", "keyword50": "SECONDMAX_POS", "keyword51": "SECONDMIN_POS", "keyword52": "SECONDMAX1_VALUE", "keyword53": "SECONDMIN1_VALUE", "keyword54": "SECONDMAX1_POS", "keyword55": "SECONDMIN1_POS", "keyword56": "TIMES", "keyword57": "ADD", "keyword58": "AVERAGE", "keyword59": "ALIGN0", "keyword6": "WEEK", "keyword60": "ALIGN1", "keyword61": "ALIGN2", "keyword62": "VALIGN0", "keyword63": "VALIGN1", "keyword64": "VALIGN2", "keyword65": "PRECIS0", "keyword66": "PRECIS1", "keyword67": "PRECIS2", "keyword68": "PRECIS3", "keyword69": "PRECIS4", "keyword7": "PASSIVE_ORDER", "keyword70": "PRECIS5", "keyword71": "PRECIS6", "keyword72": "FONTSIZE8", "keyword73": "FONTSIZE9", "keyword74": "FONTSIZE10", "keyword75": "FONTSIZE11", "keyword76": "FONTSIZE12", "keyword77": "FONTSIZE13", "keyword78": "FONTSIZE14", "keyword79": "FONTSIZE15", "keyword8": "ACTIVE_ORDER", "keyword80": "FONTSIZE16", "keyword81": "FONTSIZE17", "keyword82": "FONTSIZE18", "keyword83": "FONTSIZE19", "keyword84": "FONTSIZE20", "keyword85": "FONTSIZE21", "keyword86": "FONTSIZE22", "keyword87": "FONTSIZE23", "keyword88": "FONTSIZE24", "keyword89": "FONTSIZE25", "keyword9": "CMPETITV_ORDER", "keyword90": "FONTSIZE26", "keyword91": "FONTSIZE27", "keyword92": "FONTSIZE28", "keyword93": "FONTSIZE29", "keyword94": "FONTSIZE30", "keyword95": "FONTSIZE31", "keyword96": "FONTSIZE32", "keyword97": "FONTSIZE33", "keyword98": "FONTSIZE34", "keyword99": "FONTSIZE35"}, "KLINESIG": {"body": "KLINESIG", "createtime": "20140904", "description": "KLINESIG 判断当根K线上最后一个固定的信号。\\r\\n\\r\\n用法：KLINESIG 判断当根K线上最后一个固定的信号。如果最后一根K线上没有信号，或者没有固定的信号，则该函数返回0。\\r\\n\\r\\n注：\\r\\n1、该函数只能用于指令价模型\\r\\n2、信号的返回值：\\r\\nBK:200;\\r\\nSK:201;\\r\\nBP:202;\\r\\nSP:203;\\r\\nBPK:204;\\r\\nSPK:205;\\r\\nCLOSEOUT:206;\\r\\nSTOP:207;\\r\\n3、由BPK指令产生的BK信号按BPK信号处理，SPK指令产生的SK信号同理。\\r\\n\\r\\n例：\\r\\nKLINESIG=200&&BKVOL>0,SP;//如果最后一个固定的信号是BK信号，并且多头持仓大于0，卖平仓", "explanation": "判断当根K线上最后一个固定的信号", "markettype": 1, "modifytime": "", "param": "", "tip": "KLINESIG判断当根K线上最后一个固定的信号", "type": 10}, "KLINESTART": {"body": "KLINESTART", "createtime": "2014-07-23", "description": "KLINESTART 判断K线当前状态是否是K线开始。\\r\\n\\r\\n用法：\\r\\n1、KLINESTART 当前K线状态为K线的开始，则返回1，否则返回0。\\r\\n2、加载运行过程中，当根K线接收到第一笔数据时，判断当根K线开始；历史信号计算中该函数返回值为0。\\r\\n\\r\\n例：\\r\\nC>REF(C,1)&&KLINESTART=0,BPK;//价格大于前一周期收盘价做多，K线开始的第一笔数据不交易\\r\\nC<REF(C,1)&&KLINESTART=0,SPK;//价格小于前一周期收盘价做空，K线开始的第一笔数据不交易\\r\\nMULTSIG(0,0,1,0);\\r\\nAUTOFILTER;", "explanation": "判断K线当前状态是否是K线开始", "markettype": 1, "modifytime": "", "param": "", "tip": "KLINESTART,判断K线当前状态是否是K线开始", "type": 5}, "KTEXT": {"body": "KTEXT( , , , , ,)", "createtime": "", "description": "KTEXT函数 在k线上标注文字。\\r\\n\\r\\n用法：\\r\\nKTEXT(COND,POSITION,PRICE,LCR,COLOR,TEXT); \\r\\n当COND条件满足时,移动POSITION根K线,在PRICE位置书写COLOR色文字TEXT。LCR是文字占K线左(0)中(1)右(2)位置。\\r\\n\\r\\n注：\\r\\n1、POSITION 参数负数代表向前移动 0代表满足条件当根K线 正数代表向后移动。LCR代表显示在字符位置的左右中位置，0为左，1为中，2为右\\r\\n2、显示的汉字用单引号标注。\\r\\n3、不支持将该函数直接定义为变量，即不支持下面的写法：\\r\\nA:KTEXT(COND,POSITION,PRICE,LCR,COLOR,TEXT);\\r\\n\\r\\n例1：\\r\\nKTEXT(O>C,2,H,1,COLORYELLOW,'注');//在阴线的后两根K线处，在最高价位置中心上写\"注\"字。\\r\\n例2：\\r\\nMA5:=MA(C,5);\\r\\nKTEXT(CROSS(C,MA5),-3,MA5,2,COLORRED,'买入');//在收盘价金叉5周期均线的前三根K线处，在MA5位置右侧上写\"买入\"字。", "explanation": "在K线附近标注文字", "markettype": 0, "modifytime": "", "param": "POSITION参数负数代表向前移动0代表满足条件当根K线正数代表向后移动LCR代表显示在字符位置的左右中位置，0为左，1为中，2为右", "tip": "KTEXT(COND,POSITION,PRICE,LCR,COLOR,TEXT)在k线上标注文字当COND条件满足时,移动POSITION根K线,在PRICE位置书写COLOR色文字TEXTLCR是文字占K线左(0)中(1)右(2)位置", "type": 8}, "KURTOSIS": {"body": "KURTOSIS( , )", "createtime": "20150525", "description": "KURTOSIS(X,N) 求X在N个周期内的峰度系数。\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、N为有效值，但当前的k线数不足N根，该函数返回空值。\\r\\n3、N为0时，该函数返回空值。\\r\\n4、N为空值，该函数返回空值。\\r\\n5、N可以为变量。\\r\\n6、N至少为4，少于4返回空值。\\r\\n\\r\\n算法举例：计算KURTOSIS(C,4);在最近一根K线上的值。\\r\\n用麦语言函数可以表示如下：\\r\\n((POW(C-MA(C,4),4)+POW(REF(C,1)-MA(C,4),4)+POW(REF(C,2)-MA(C,4),4)+POW(REF(C,3)-MA(C,4),4)) /POW(STD(C,4),4))*(4*(4+1)/((4-1)*(4-2)*(4-3)))-3*SQUARE(4-1)/((4-2)*(4-3));\\r\\n\\r\\n例：\\r\\nKURTOSIS(C,10);\\r\\n//表示收盘价的10周期峰值。峰值反映与正态分布相比某一分布的尖锐度或平坦度。正峰值表示相对尖锐的分布。负峰值表示相对平坦的分布。", "explanation": "峰度系数", "markettype": 0, "modifytime": "", "param": "", "tip": "KURTOSIS(X,N)求X在N个周期内的峰度系数", "type": 3}, "K_STATE": {"body": "K_STATE()", "createtime": "20160401", "description": "K_STATE 判断k线形态\\r\\n\\r\\n用法：\\r\\nK_STATE('STATE');STATE为代表k线形态的字符串。加载到k线图后，符合该k线形态，返回1，否则返回0。\\r\\n\\r\\n例：\\r\\nK_STATE('红三兵');//判断当前k线形态是否为红三兵\\r\\n\\r\\n该函数在k线形态源码中无参数的情况下进行判断是否满足\\r\\n'STATE'可以为\\r\\n'红三兵'\\r\\n'跳高上扬'\\r\\n'并排阳线'\\r\\n'十字线'\\r\\n'多方炮'\\r\\n'空方炮'\\r\\n'叠叠多方炮'\\r\\n'叠叠空方炮'\\r\\n'光头阳线'\\r\\n'光头阴线'\\r\\n'看跌提腰带线'\\r\\n'看跌吞没线'\\r\\n'看涨提腰带线'\\r\\n'看涨吞没线'\\r\\n'三只乌鸦'\\r\\n'双飞乌鸦'\\r\\n'向上跳空缺口'\\r\\n'上涨分离线'\\r\\n'下跌分离线'\\r\\n'上涨汇合线'\\r\\n'下跌汇合线'\\r\\n'跳空上涨卷轴线'\\r\\n'跳空下跌卷轴线'\\r\\n'跳空并排阳线'\\r\\n'跳空并排阴阳线'\\r\\n'乌云盖顶'\\r\\n'阳后双阴阳'\\r\\n'阴后双阳阴'\\r\\n'孕线'", "explanation": "判断k线形态", "markettype": 0, "modifytime": "", "param": "", "tip": "K_STATE()判断K线形态", "type": 5}, "K_STATE1": {"body": "K_STATE1()", "createtime": "20160401", "description": "K_STATE1 判断k线形态\\r\\n\\r\\n用法：\\r\\nK_STATE1(N1,'STATE');N1为k线形态源码中的参数；STATE为代表k线形态的字符串。加载到k线图后，当前k线符合该k线形态，返回1，否则返回0。\\r\\n\\r\\n例：\\r\\nK_STATE1(3,'上升三法');//上升三法为股价持续上涨中，某日出现一根大阳线，隔日后连续出现三根小阴线，被视为另一波上涨的信号。N1:=3为上升三法源码中的参数，可以根据需要自行调整。该函数为判断当前k线是否满足上升三法形态。\\r\\n\\r\\n该函数在k线形态源码中只有一个参数的情况下进行判断是否满足\\r\\n'STATE'可以为\\r\\n'上升三法'：参数N1表示实体大小比例，N1=3即实体比例为3%\\r\\n'下降三法'：参数N1表示实体大小比例，N1=3即实体比例为3%\\r\\n'曙光初现'：参数N1表示实体大小比例，N1=3即实体比例为3%\\r\\n'平底'：参数N1表示2根K线最低价的变化幅度，N1=1即变化幅度为1%\\r\\n'平顶'：参数N1表示两根K线最高价的误差比例，误差百分比由品种决定，N1=1即为1%\\r\\n'双飞燕'：参数N1表示两根K线成交量误差比例，误差百分比由品种决定，N1=1即为1%\\r\\n'上档盘旋'：参数N1表示整理期间最高收盘价的上涨最大限度，N1=3即上涨最大限度为3%\\r\\n'V形反转'：参数N1表示连续N根K线保持一个趋势\\r\\n'倒V形反转'：参数N1表示连续N根K线保持一个趋势\\r\\n'T形线（蜻蜓）'：参数N1表示下影线的百分比，N1=1即下影线占比1%\\r\\n'长上影线'：参数N1表示上影线占K线全长的比例，N1=667即上影线占k线全长的667/1000\\r\\n'长下影线'：参数N1表示下影线占K线全长的比例，N1=667即下影线占k线全长的667/1000\\r\\n'倒锤线'：参数N1表示(MAX(O,C)-L)的倍数\\r\\n'射击之星'：参数N1表示(MAX(O,C)-L)的倍数\\r\\n'吊颈线'：参数N1表示(H-MIN(O,C))的倍数", "explanation": "判断k线形态", "markettype": 0, "modifytime": "", "param": "", "tip": "K_STATE1()判断K线形态", "type": 5}, "K_STATE2": {"body": "K_STATE2()", "createtime": "20160401", "description": "K_STATE2 判断k线形态\\r\\n\\r\\n用法：\\r\\nK_STATE2(N1,N2,'STATE');N1,N2为k线形态源码中的参数；STATE为代表k线形态的字符串。加载到k线图后，符合该k线形态，返回1，否则返回0。\\r\\n\\r\\n例：\\r\\nK_STATE2(3,5,'早晨之星');//早晨之星为启示后市见底回升的阴阳烛组合形态，3和5分别为早晨之星源码中的两个默认参数，可以根据需要自行调整。该函数为判断当前k线是否满足早晨之星形态。\\r\\n\\r\\n该函数在k线形态源码中有两个参数的情况下进行判断是否满足\\r\\n'STATE'可以为\\r\\n'早晨之星'：参数N1为两侧K线K线实体比例，参数N2为星线涨跌幅。N1=3即收盘价相当于开盘价上涨或下跌3%，N2=5即星线涨跌幅为5%\\r\\n'黄昏之星'：参数N1为两侧K线K线实体比例，参数N2为星线涨跌幅。N1=5即收盘价相当于开盘价上涨或下跌5%，N2=3即星线涨跌幅为3%\\r\\n'大阳线'：参数N1为K线实体比例，参数N2为高低价比值与收开价比值的差值上线。N1=5即收盘价相当于开盘价上涨或下跌5%，N2=18即高低价比值 与收开价比值的差值上线18/1000\\r\\n'大阴线':参数N1为K线实体比例，参数N2为高低价比值与收开价比值的差值上线。N1=5即收盘价相当于开盘价上涨或下跌5%，N2=18即高低价比值与收开价比值的差值上线18/1000", "explanation": "判断k线形态", "markettype": 0, "modifytime": "", "param": "", "tip": "K_STATE2()判断K线形态", "type": 5}, "K_STATE3": {"body": "K_STATE3()", "createtime": "20160401", "description": "K_STATE3 判断k线形态\\r\\n\\r\\n用法：\\r\\nK_STATE3(N1,N2,N3,'STATE');N1,N2,N3为k线形态源码中的参数；STATE为代表k线形态的字符串。加载到k线图后，符合该k线形态，返回1，否则返回0。\\r\\n\\r\\n例：\\r\\nK_STATE3(5,10,20,'出水芙蓉');//一根大阳线上穿三条均线，均线为多头排列称为出水芙蓉。5,10,20分别为出水芙蓉源码中的三个默认参数，可以根据需要自行调整。该函数为判断当前k线是否满足出水芙蓉形态\\r\\n\\r\\n该函数在k线形态源码中有三个参数的情况下进行判断是否满足\\r\\n'STATE'可以为\\r\\n'出水芙蓉'：参数N1、N2、N3为三条均线周期\\r\\n'东方红大阳升'：参数N1、N2、N3为三条均线周期\\r\\n'断头铡刀'：参数N1、N2、N3为三条均线周期\\r\\n'金蜘蛛'：参数N1、N2、N3为三条均线周期\\r\\n'死蜘蛛'：参数N1、N2、N3为三条均线周期", "explanation": "判断k线形态", "markettype": 0, "modifytime": "20211223", "param": "", "tip": "K_STATE3()判断K线形态", "type": 5}, "K_STATE4": {"body": "K_STATE4()", "createtime": "20160401", "description": "K_STATE4 判断k线形态\\r\\n\\r\\n用法：\\r\\nK_STATE4(N1,N2,N3,N4,'STATE');N1,N2,N3,N4为k线形态源码中的参数；STATE为代表k线形态的字符串。加载到k线图后，符合该k线形态，返回1，否则返回0。\\r\\n\\r\\n例：\\r\\nK_STATE4(5,5,10,20,'九阴白骨爪')：参数N1为连续N根K线满足阴线，参数N2、N3、N4为三条均线的周期", "explanation": "判断k线形态", "markettype": 0, "modifytime": "20211223", "param": "", "tip": "K_STATE4()判断K线形态", "type": 5}, "L": "L 最低价", "LAST": {"body": "LAST( , , )", "createtime": "", "description": "LAST(COND,N1,N2) 判断过去N1到N2周期内，是否一直满足COND条件。\\r\\n\\r\\n注：\\r\\n1、若N1与N2只相差一个周期（如N1=3，N2=2），则函数判断距离当前K线最近的那个周期上是否满足条件（即判断过去N2个周期的那根K线上是否满足条件）\\r\\n2、当N1/N2为有效值，但当前的k线数不足N1/N2根，或者N1/N2空值的情况下，代表不成立，该函数返回0\\r\\n3、N1、N2不可以是变量。\\r\\n\\r\\n例1：\\r\\nLAST(CLOSE>OPEN,10,5);//表示从过去第10个周期到第5个周期内一直是阳线\\r\\n例2：\\r\\nMA5:=MA(C,5);\\r\\nLAST(C>MA5,4,3);//判断距离当前k线3个周期的那根k线上是否满足C大于MA5.", "explanation": "判断函数", "markettype": 0, "modifytime": "", "param": "", "tip": "LAST(X,N1,N2),判断过去N1到N2周期内，是否一直满足条件X一直满足返回1，否则返回0", "type": 5}, "LASTOFFSETPROFIT": {"body": "LASTOFFSETPROFIT", "createtime": "********", "description": "LASTOFFSETPROFIT 最近一次交易的平仓盈亏\\r\\n\\r\\n用法：LASTOFFSETPROFIT返回当前距当前K线最近一次交易的平仓盈亏，用于风险控制。\\r\\n\\r\\n注：\\r\\n1、从开仓到持仓为0被视为一次交易。\\r\\n2、信号执行方式为‘K线走完确认信号下单’：LASTOFFSETPROFIT根据信号当根K线的收盘价计算平仓盈亏，待下根K线时取值相应变化。\\r\\n3、信号执行方式为‘XX下单，K线走完复核’：LASTOFFSETPROFIT根据下单当时的行情最新价计算平仓盈亏，复核后待下根K线时取值相应变化。\\r\\n4、信号执行方式为‘出信号立即下单不复核’：LASTOFFSETPROFIT根据信号确认时的行情最新价计算平仓盈亏。\\r\\n5、LASTOFFSETPROFIT与日志中记录的平仓盈亏不同，后者根据成交价计算。\\r\\n6、LASTOFFSETPROFIT的计算不包含手续费。\\r\\n7、因信号消失产生的盈亏未纳入LASTOFFSETPROFIT的计算。\\r\\n8、股票T+1交易不支持统计平仓盈亏，该函数返回空值。\\r\\n\\r\\n例：\\r\\nLASTOFFSETPROFIT<=-40 && C<BKPRICE-60,CLOSEOUT;//最近一次交易的亏损额大于40并且当前亏损大于60，清仓", "explanation": "最近一次交易的平仓盈亏", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "LASTOFFSETPROFIT最近一次交易的平仓盈亏", "type": 12}, "LASTSIG": {"body": "LASTSIG", "createtime": "2015-07-01", "description": "LASTSIG判断最近一个信号\\r\\n\\r\\n注：由BPK指令产生的BK信号按BPK信号处理，SPK指令产生的SK信号同理。\\r\\n\\r\\n例：AA:LASTSIG=BK;//最近一个 稳定的信号为BK信号AA返回值为1，否则返回0.\\r\\nLASTSIG的不同返回值代表的信号：\\r\\nBK:200;\\r\\nSK:201;\\r\\nBP:202;\\r\\nSP:203;\\r\\nBPK:204;\\r\\nSPK:205;\\r\\nCLOSEOUT:206;\\r\\nSTOP:207;", "explanation": "判断最近一个信号", "markettype": 1, "modifytime": "", "param": "", "tip": "LASTSIG，取上一次交易指令方向", "type": 10}, "LASTSIGGROUP": {"body": "LASTSIGGROUP", "createtime": "20150303", "description": "LASTSIGGROUP 判断最近一个信号所在的分组。\\r\\n\\r\\n注：\\r\\n1、\"A\"组返回1，\"B\"组返回2，\"C\"组返回3，\"D\"组返回4，\"E\"组返回5，\"F\"组返回6，\"G\"组返回7，\"H\"组返回8，\"I\"组返回9。\\r\\n2、未设置过分组或者分组模型里面的无组别指令，返回0。\\r\\n3、出信号的当根K线，信号固定后，LASTSIGGROUP返回该信号的分组，信号尚未固定时，LASTSIGGROUP返回上一个固定的信号的分组。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK('A',1);//最新价上穿五周期均线，A组做多一手\\r\\nCROSS(MA(C,5),C),SP('A',BKVOL);//最新价下穿五周期均线，A组平仓\\r\\nCROSS(C,MA(C,10)),BK('B',2);//最新价上穿十周期均线，B组做多两手\\r\\nLASTSIG=200&&LASTSIGGROUP=2,SP('B',BKVOL);//上一个信号是B组的BK信号，则B组平仓", "explanation": "判断最近一个信号所在的分组", "markettype": 1, "modifytime": "20211230", "param": "", "tip": "LASTSIGGROUP判断最近一个信号所在的分组", "type": 10}, "LIMIT_ORDER": "LIMIT_ORDER 市价", "LINETHICK": {"body": "LINETHICK", "createtime": "20140408", "description": "添加线型粗细控制。\\r\\n用法：\\r\\nLINETHICK1  LINETHICK2————LINETHICK7 线型由细至粗。\\r\\n注：\\r\\n1、不支持将该函数直接定义为变量，即不支持下面的写法：A:LINETHICK1;\\r\\n2、该函数可以和颜色函数一起使用，即支持下面的写法：AA:C,COLORBLUE,LINETHICK5;\\r\\n例：MA5:MA(C,5),COLORRED,LINETHICK4; 给5日均线中度加粗,颜色为红色。", "explanation": "线型粗细控制", "markettype": 0, "modifytime": "", "param": "", "tip": "", "type": 8}, "LINETHICK1": "LINETHICK1 实线 粗细度为1", "LINETHICK2": "LINETHICK2 实线 粗细度为2", "LINETHICK3": "LINETHICK3 实线 粗细度为3", "LINETHICK4": "LINETHICK4 实线 粗细度为4", "LINETHICK5": "LINETHICK5 实线 粗细度为5", "LINETHICK6": "LINETHICK6 实线 粗细度为6", "LINETHICK7": "LINETHICK7 实线 粗细度为7", "LLV": {"body": "LLV( , )", "createtime": "", "description": "LLV(X,N)： 求X在N个周期内的最小值。\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、若N为0则从第一个有效值开始算起;\\r\\n3、当N为有效值，但当前的k线数不足N根，按照实际的根数计算;\\r\\n4、N为空值时，返回空值。\\r\\n5、N可以是变量。\\r\\n\\r\\n例1：\\r\\nLL:LLV(L,5);//求5根k线最低点（包含当前k线）。\\r\\n例2：\\r\\nN:=BARSLAST(DATE<>REF(DATE,1))+1;//分钟周期，日内k线根数\\r\\nLL1:=LLV(L,N);//在分钟周期上，求当天第一根k线到当前周期内所有k线最低价的最小值。", "explanation": "最低值", "markettype": 0, "modifytime": "", "param": "", "tip": "LLV(X,N),求X在N个周期内的最小值", "type": 2}, "LLVBARS": {"body": "LLVBARS( , )", "createtime": "", "description": "LLVBARS(X,N)： 求N周期内X最低值到当前周期数\\r\\n\\r\\n注：\\r\\n1、若N为0则从第一个有效值开始算起(不包含当前K线)；\\r\\n2、当N为有效值，但当前的k线数不足N根，按照实际的根数计算，第一根k线返回空值；\\r\\n3、N为空值时，返回空值。\\r\\n4、N可以是变量。\\r\\n\\r\\n例1：\\r\\nLLVBARS(VOL,0); 求历史成交量最小的周期到当前的周期数（最小值那根k线上LLVBARS(VOL,0);的返回值为0，最小值后的第一根k线返回值为1，依次类推）。\\r\\n例2：\\r\\nN:=BARSLAST(DATE<>REF(DATE,1))+1;//分钟周期，日内k线根数\\r\\nZLBARS:REF(LLVBARS(L,N),N)+N;//在分钟周期上，求昨天最低价所在的k线到当前k线之间的周期数。", "explanation": "前一个最低点位置", "markettype": 0, "modifytime": "", "param": "。", "tip": "LLVBARS(X,N),求N周期内X最低值到当前周期数", "type": 2}, "LN": {"body": "LN( )", "createtime": "", "description": "LN(X)：求X的自然对数。\\r\\n注：\\r\\n1、X取值范围为非0自然数，即1、2、3、4、5……\\r\\n2、若X取值为0或负数，返回值为空值。\\r\\n\\r\\n例：\\r\\nLN(OPEN);//求开盘价的对数。", "explanation": "自然对数", "markettype": 0, "modifytime": "", "param": "", "tip": "LN(X),求X的自然对数", "type": 4}, "LOG": {"body": "LOG( )", "createtime": "", "description": "LOG(X,Y) 求以Y为底X的对数值。\\r\\n\\r\\n注：\\r\\n1、该函数中X的取值范围为X>0\\r\\n2、0和负数没有对数，X为0或负数时返回值为空值。\\r\\n3、该函数中Y的取值范围为Y>0并且Y≠1。\\r\\n4、为保证返回值在实数范围，Y为1或负数时返回值为空值。\\r\\n\\r\\n例1：\\r\\nLOG(100,10);//返回2.\\r\\n例2：\\r\\nLOG(0,7);//返回空值。", "explanation": "求以Y为底X的对数值", "markettype": 0, "modifytime": "", "param": "", "tip": "LOG(X,Y)求以Y为底X的对数值。", "type": 4}, "LOG10": {"body": "LOG10( )", "createtime": "20181115", "description": "LOG10(X) 求X的常用对数值。\\r\\n\\r\\n注：\\r\\n1、该函数中X的取值范围为X>0\\r\\n2、0和负数没有对数，X为0或负数时返回值为空值。\\r\\n\\r\\n例1：\\r\\nLOG10(100);//返回2.\\r\\n例2：\\r\\nLOG10(0);//返回空值。", "explanation": "常用对数", "markettype": 0, "modifytime": "", "param": "", "tip": "LOG10(X)求X的常用对数。", "type": 4}, "LONGCROSS": {"body": "LONGCROSS( , , )", "createtime": "", "description": "LONGCROSS(A,B,N) 表示A在N个周期内都小于B，本周期A从下向上穿越B\\r\\n\\r\\n注：\\r\\n1、当N为有效值，但当前的k线数不足N根时，LONGCROSS函数返回空值\\r\\n2、N不支持变量。\\r\\n\\r\\n例1：\\r\\nLONGCROSS(CLOSE,MA(CLOSE,10),20);//表示收盘线在10日均线之下持续20周期后从下向上穿过10日均线", "explanation": "维持交叉函数", "markettype": 0, "modifytime": "", "param": "", "tip": "LONGCROSS(A,B,N),判断A在是否在N个周期内都小于B如果是则返回1，否则返回0", "type": 5}, "LOOP1": {"body": "LOOP1( , , )", "createtime": "", "description": "LOOP1(X,N,TYPE);循环统计函数 对X在N个周期进行TYPE相应的操作\\r\\n\\r\\n注：\\r\\nTYPE取值：\\r\\nMAX_VALUE 最大值；\\r\\nMIN_VALUE 最小值；\\r\\nMAX_POS 最大值位置；\\r\\nMIN_POS 最小值位置；\\r\\nMAX1_VALUE 最大值（不包括自身周期）；\\r\\nMIN1_VALUE 最小值（不包括自身周期）；\\r\\nMAX1_POS 最大值位置(不包括自身周期)；\\r\\nMIN1_POS 最小值位置（不包括自身周期）；\\r\\nSECONDMAX_VALUE 次大值；\\r\\nSECONDMIN_VALUE 次小值；\\r\\nSECONDMAX_POS 次大值位置； \\r\\nSECONDMIN_POS 次小值位置；\\r\\nSECONDMAX1_VALUE 次大值（不包括自身周期）；\\r\\nSECONDMIN1_VALUE 次小值（不包括自身周期）；\\r\\nSECONDMAX1_POS 次大值位置（不包括自身周期）；\\r\\nSECONDMIN1_POS 次小值位置（不包括自身周期）；\\r\\nTIMES 满足表达式的次数；\\r\\nADD 加和；\\r\\nAVERAGE 均值。\\r\\n\\r\\n例1：\\r\\nHH:VALUEWHEN(CROSS(C,MA(C,5)),H);//取收盘价上穿五周期均线时的最高价\\r\\nHH1:LOOP1(HH,50,SECONDMAX_VALUE);//50周期内收盘价上穿均线时的最高价的次高值\\r\\n含义说明：\\r\\n1、取包含当根K线内的50根K线内的收盘价上穿均线时的最高价\\r\\n2、对最高价从大到小进行排序\\r\\n3、当根K线的HH1返回值为排序中第二大的值\\r\\n\\r\\n注：如果50个周期最高值为唯一值，即50个周期的HH取值相同，则最高值与次高值相等，HH1返回对应的HH值\\r\\n\\r\\n例2：\\r\\nHH1:LOOP1(H,10,SECONDMAX1_POS);\\r\\n说明：不包含当根K线的前面10根K线的最高价中第二大的取值对应K线，距离当前K线的位置\\r\\n\\r\\n例3：\\r\\nPOS1:LOOP1(H,30,SECONDMAX1_POS);\\r\\nPOS2:LOOP1(H,30,MAX1_POS);\\r\\nPOS1<POS2&&REF(VOL,POS1)<REF(VOL,POS2)&&C<LV(L,30)&&VOL>REF(VOL,1),SK;\\r\\n说明：\\r\\n30周期内次高点的位置比最高点的位置靠近当前位置，并且次高点的成交量比最高点的成交量低，当前价格跌破了30周期内的最低点并且成交量增加，M头形成反转形态，做空入场。\\r\\n\\r\\n替代编写方法说明：\\r\\nLOOP1(X,N,MAX_VALUE)=HHV(X,N)\\r\\nLOOP1(X,N,MIN_VALUE)=LLV(X,N)\\r\\nLOOP1(X,N, MAX_POS)=HHVBARS(X,N)\\r\\nLOOP1(X,N, MIN_POS)=LLVBARS(X,N)\\r\\nLOOP1(X,N, MAX1_VALUE)=HV(X,N)\\r\\nLOOP1(X,N, MIN1_VALUE)=LV(X,N)\\r\\nLOOP1(X,N, TIMES)=COUNT(X,N)\\r\\nLOOP1(X,N, ADD)=SUM(X,N)\\r\\nLOOP1(X,N, AVERAGE)=MA(X,N)", "explanation": "循环统计函数", "markettype": 1, "modifytime": "", "param": "", "tip": "LOOP1(X,N,TYPE)循环统计函数；对变量X在N个周期进行TYPE相应的操作", "type": 6}, "LOOP2": {"body": "LOOP2( , , )", "createtime": "2014-07-07", "description": "LOOP2(COND,A,B);循环条件函数 若COND条件成立，则返回A，否则返回B\\r\\n\\r\\n注：\\r\\n1、COND是判断条件;A、B可以是条件，也可以是数值。\\r\\n2、该函数支持变量循环引用前一周期自身变量，即支持下面这样的写法Y: LOOP2(CON,X,REF(Y,1));\\r\\n\\r\\n例1：\\r\\nX:  LOOP2(ISUP,H,REF(X,1));//k线为阳线，取当根K线的最高价，否则取上一次是阳线的K线的最高价;若之前未出现过阳线时，X返回为空值\\r\\n\\r\\n例2：\\r\\nBB:LOOP2(BARSBK=1,LOOP2(L>LV(L,4),L,LV(L,4)),LOOP2(L>REF(BB,1),L,REF(BB,1)));//持有多单时，开多单那根的前面4个周期内的最低价为起始止损点BB，后续K线最低价比前一个最低价高，取当前最低价为止损点，否则取前一个低点为止损点，\\r\\nSS:LOOP2(BARSSK=1,LOOP2(H<HV(H,4),H,HV(H,4)),LOOP2(H<REF(SS,1),H,REF(SS,1)));//持有空单时，开空单那根的前面4个周期内的最高价为起始止损点SS，最高价比前一个最高价低，取当前最高价为止损点，否则取前一个高点为止损点\\r\\nH>HV(H,20),BK;\\r\\nL<LV(L,20),SK;\\r\\nC<BB,SP;\\r\\nC>SS,BP;\\r\\nAUTOFILTER;", "explanation": "循环条件函数", "markettype": 1, "modifytime": "", "param": "", "tip": "LOOP2(COND,A,B);循环条件函数；若COND条件成立，则返回A，否则返回B", "type": 6}, "LOW": {"body": "LOW", "createtime": "", "description": "LOW 取得K线图的最低价。\\r\\n\\r\\n注：\\r\\n1、可简写为L。\\r\\n\\r\\n例1：\\r\\nLL:L;//定义LL为最低价。\\r\\n例2：\\r\\nLL:LLV(L,5);//取得5个周期内最低价的最小值。\\r\\n例3：\\r\\nREF(L,1);//取得前一根K线的最低价", "explanation": "取得K线图的最低价", "markettype": 0, "modifytime": "", "param": "", "tip": "LOW取得当根K线的最低价", "type": 1}, "LV": {"body": "LV( , )", "createtime": "", "description": "LV(X,N) 求X在N个周期内的最小值（不包含当前k线）\\r\\n\\r\\n注：\\r\\n1、若N为0则从第一个有效值开始算起;\\r\\n2、当N为有效值，但当前的k线数不足N根，按照实际的根数计算;\\r\\n3、N为空值时，返回空值。\\r\\n4、N可以是变量。\\r\\n\\r\\n例1：\\r\\nLL:LV(L,10);//求前面10根k线的最低点。（不包含当前k线）\\r\\n例2：\\r\\nN:=BARSLAST(DATE<>REF(DATE,1))+1;//分钟周期，日内k线根数\\r\\nNN:=REF(N,N);\\r\\nZL:VALUEWHEN(DATE<>REF(DATE,1),LV(L,NN));//在分钟周期上，求昨天最低价。\\r\\n例3：\\r\\nLV(L,5) 和 REF(LLV(L,5),1) 的结果是一样的，用LV编写更加方便。", "explanation": "除当前K线外最低值", "markettype": 0, "modifytime": "", "param": "", "tip": "LV(X,N)求X在N个周期内的最小值(不包含当前K线)", "type": 2}, "MA": {"body": "MA( , )", "createtime": "", "description": "MA(X,N) 求X在N个周期内的简单移动平均\\r\\n\\r\\n算法：MA(X,5)=(X1+X2+X3+X4+X5)/5\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、简单移动平均线沿用最简单的统计学方式，将过去某特定时间内的价格取其平均值。\\r\\n3、当N为有效值，但当前的k线数不足N根，函数返回空值。\\r\\n4、N为0或空值的情况下，函数返回空值。\\r\\n5、N可以为变量\\r\\n\\r\\n例1：\\r\\nMA5:=MA(C,5);//求5周期收盘价的简单移动平均。\\r\\n例2：\\r\\nN:=BARSLAST(DATE<>REF(DATE,1))+1;//分钟周期，日内k线根数\\r\\nM:=IFELSE(N>10,10,N);//k线超过10根，M取10，否则M取实际根数\\r\\nMA10:MA(C,M);//在分钟周期上，当天k线不足10根，按照实际根数计算MA10，超过10根按照10周期计算MA10。", "explanation": "算数移动平均", "markettype": 0, "modifytime": "", "param": "", "tip": "MA(X,N),求X在N个周期内的简单移动平均", "type": 2}, "MARGIN": {"body": "MARGIN", "createtime": "2021-04-13", "description": "MARGIN 保证金\\r\\n\\r\\n用法：MARGIN在国内期货合约上返回当前合约的保证金比率、在外盘期货合约上返回每手保证金，用于模型中资金、手数相关计算。\\r\\n\\r\\n注：\\r\\n1、MARGIN返回值为小数\\r\\n2、主图加载、回测、模组运行中，MARGIN取值为设置保证金/手续费的量化交易参数中对保证金的设置\\r\\n\\r\\n例1：\\r\\nK:=MONEYTOT*0.2/(C*MARGIN*UNIT+FEE); //国内期货合约理论权益的20%可以开仓的手数（此写法适用于按固定手数收取手续费的合约）\\r\\n\\r\\n例2：\\r\\nK:=MONEYTOT*0.2/(MARGIN+FEE); //外盘期货合约理论权益的20%可以开仓的手数（此写法适用于按固定手数收取手续费的合约）", "explanation": "保证金", "markettype": 1, "modifytime": "20240201", "param": "", "tip": "MARGIN返回国内期货合约合约的保证金比率或者外盘期货合约的每手保证金", "type": 12}, "MARKET_TYPE": {"NUM": 4, "markettype0": "markettype=0的函数（基础函数）", "markettype1": "markettype=1的函数（T+0策略函数）", "markettype2": "markettype=2的函数（T+1策略函数）", "markettype3": "markettype=3的函数（选股函数）"}, "MAX": {"body": "MAX( , )", "createtime": "", "description": "MAX(A,B)：取最大值。取A，B中较大者。\\r\\n\\r\\n注：\\r\\n若A=B，返回值为A或者B的值。\\r\\n\\r\\n例1：\\r\\nMAX(CLOSE,OPEN);//表示取开盘价和收盘价中较大者。\\r\\n例2：\\r\\nMAX(CLOSE-OPEN,0);//表示若收盘价大于开盘价返回它们的差值，否则返回0。\\r\\n例3：\\r\\nMAX(A,MAX(B,MAX(C,D)));//求 A B C D四者中的最大值", "explanation": "最大值", "markettype": 0, "modifytime": "", "param": "", "tip": "MAX(A,B),取A，B中较大者", "type": 4}, "MAX1": {"body": "MAX1( )", "createtime": "20141112", "description": "MAX1(A1,...,A30) 在A1到A30中取最大值。\\r\\n\\r\\n注：\\r\\n1、支持2-30个数值进行比较。\\r\\n2、A1...A30可以为数字也可以为变量。\\r\\n\\r\\n例1：\\r\\nMAX1(CLOSE,OPEN);//表示取开盘价和收盘价中较大者。\\r\\n\\r\\n例2：\\r\\nMAX1(1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16);//表示取数字1-16中的最大值。", "explanation": "取最大值", "markettype": 0, "modifytime": "", "param": "", "tip": "MAX1(A1,...,A30),取A1...A30中的最大值（支持2-30个参数进行比较）", "type": 4}, "MAX1_POS": "MAX1_POS 最大值位置(不包括自身周期)", "MAX1_VALUE": "MAX1_VALUE 最大值（不包括自身周期）", "MAXBKVOL": {"body": "MAXBKVOL", "createtime": "20141216", "description": "MAXBKVOL 多头最大持仓手数\\r\\n\\r\\n用法：\\r\\nMAXBKVOL 返回一次多头交易过程中最大的持仓手数。一次多头交易指有多头持仓到持仓为0。\\r\\n\\r\\n注：\\r\\n1、开仓信号当根K线，MAXBKVOL返回值增加多头开仓数量。\\r\\n2、平全部多仓信号的当根K线，MAXBKVOL返回0。\\r\\n3、非一次完整交易中平仓信号当根K线持仓不为0，MAXBKVOL返回值不变。\\r\\n4、一根K线多信号的模型，平仓信号当根K线返回最新一次完整交易的持仓手数。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK(1);//价格上穿五周期均线，买开一手\\r\\nCROSS(C,MA(C,10)),BK(2);//价格上穿十周期均线，加仓两手\\r\\nMAXBKVOL=3,SP(BKVOL);//多头最大持仓手数为3时，卖平多头持仓", "explanation": "多头最大持仓手数", "markettype": 1, "modifytime": "", "param": "", "tip": "MAXBKVOL相邻两次多头持仓为0之间的多头最大持仓手数", "type": 10}, "MAXSKVOL": {"body": "MAXSKVOL", "createtime": "20141216", "description": "MAXSKVOL 空头最大持仓手数\\r\\n\\r\\n用法：\\r\\nMAXSKVOL 返回一次空头交易过程中最大的持仓手数。一次空头交易指有空头持仓到持仓为0\\r\\n\\r\\n注：\\r\\n1、开仓信号当根K线，MAXSKVOL返回值增加空头开仓数量。\\r\\n2、平全部空仓信号的当根K线，MAXSKVOL返回0。\\r\\n3、非一次完整交易中平仓信号当根K线持仓不为0，MAXSKVOL返回值不变。\\r\\n4、一根K线多信号的模型，平仓信号当根K线返回最新一次完整交易的持仓手数。\\r\\n\\r\\n例：\\r\\nCROSS(MA(C,5),C),SK(1);//价格下穿五周期均线，卖开一手\\r\\nCROSS(MA(C,10),C),SK(2);//价格下穿十周期均线，加仓两手\\r\\nMAXSKVOL=3,BP(SKVOL);//空头最大持仓手数为3时，买平空头持仓", "explanation": "空头最大持仓手数", "markettype": 1, "modifytime": "", "param": "", "tip": "MAXBKVOL相邻两次空头持仓为0之间的空头最大持仓手数", "type": 10}, "MAX_POS": "MAX_POS 最大值位置", "MAX_VALUE": "MAX_VALUE 最大值", "MEDIAN": {"body": "MEDIAN( , )", "createtime": "20150522", "description": "MEDIAN(X,N) 求X在N个周期内居于中间的数值。\\r\\n\\r\\n注：\\r\\n1、N个周期内所有X排序后，若N为奇数，则选择第（N+1）/2个为中位数，若N为偶数，则中位数是（N/2以及N/2+1）的平均数。\\r\\n2、N可以为变量。\\r\\n3、N周期内X只要存在空值，该函数返回值即为空值。 \\r\\n\\r\\n例1：\\r\\n豆粕2009最近3日的收盘价为2727、2754、2748，那么当前MEDIAN(C,3)的返回值是2748\\r\\n例2：\\r\\n豆粕2009最近4日的开盘价为2752、2743、2730、2728，那么当前MEDIAN(O,4)的返回值是2736.5", "explanation": "求中位数", "markettype": 0, "modifytime": "", "param": "", "tip": "MEDIAN(X,N)求X在N个周期内的中位数", "type": 4}, "MEDIAN1": {"body": "MEDIAN1()", "createtime": "20160122", "description": "MEDIAN1(A1,...,A30) 求A1到A30内居于中间的数值。\\r\\n\\r\\n注：\\r\\n1、支持2-30个数值进行计算。\\r\\n2、A1...A30可以为数值也可以为变量。\\r\\n3、若参数个数为N,对N个参数排序后，N为奇数，则选择第（N+1）/2个为中位数，若N为偶数，则中位数是（N/2以及N/2+1）的平均数。\\r\\n\\r\\n例1:\\r\\nAA:MEDIAN1(O,C,H);//开盘价、收盘价、最高价按数值排序，取居中的数值\\r\\n例2：\\r\\nBB:MEDIAN1(1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16);//表示取数字1-16的中位数,BB返回8.5", "explanation": "求中位数", "markettype": 0, "modifytime": "", "param": "", "tip": "MEDIAN1(A1,..,A30),求A1...A30的中位数（支持最多30个参数）", "type": 4}, "MIN": {"body": "MIN( , )", "createtime": "", "description": "MIN(A,B)：取最小值。取A，B中较小者\\r\\n\\r\\n注：\\r\\n若A=B，返回值为A或者B的值。\\r\\n\\r\\n例1：\\r\\nMIN(OPEN,CLOSE);//表示取开盘价和收盘价中的较小者。\\r\\n例2：\\r\\nMIN(C,MIN(O,REF(C,1)));//求当前周期的开盘价，收盘价，以及上周期的收盘价间最小的数值", "explanation": "最小值", "markettype": 0, "modifytime": "", "param": "", "tip": "MIN(A,B),取A，B中较小者", "type": 4}, "MIN1": {"body": "MIN1( )", "createtime": "20141112", "description": "MIN1(A1,...,A30) 在A1到A30中取最小值。\\r\\n\\r\\n注：\\r\\n1、支持2-30个数值进行比较。\\r\\n2、A1...A30可以为数字也可以为变量。\\r\\n\\r\\n例1：\\r\\nMIN1(CLOSE,OPEN);//表示取开盘价和收盘价中较小者。\\r\\n\\r\\n例2：\\r\\nMIN1(1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16);//表示取数字1-16中的最小值。", "explanation": "取最小值", "markettype": 0, "modifytime": "", "param": "", "tip": "MIN1(A1,...,A30),取A1...A30中的最小值（支持2-30个参数进行比较）", "type": 4}, "MIN1_POS": "MIN1_POS 最小值位置（不包括自身周期）", "MIN1_VALUE": "MIN1_VALUE 最小值（不包括自身周期）", "MINPRICE": {"body": "MINPRICE", "createtime": "", "description": "取数据合约的最小变动价位。\\r\\n用法：\\r\\nMINPRICE; 取加载数据合约的最小变动价位。", "explanation": "数据合约的最小变动价位", "markettype": 0, "modifytime": "", "param": "", "tip": "MINPRICE,取数据合约的最小变动价位", "type": 1}, "MINPRICE1": {"body": "MINPRICE1", "createtime": "", "description": "MINPRICE1  取交易合约的最小变动价位。\\r\\n用法：\\r\\nMINPRICE1; 取交易合约的最小变动价位。", "explanation": "交易合约的最小变动价位", "markettype": 1, "modifytime": "20221117", "param": "", "tip": "MINPRICE1,取交易合约的最小变动价位", "type": 1}, "MINPRICED": {"body": "MINPRICED", "createtime": "", "description": "返回某品种的最小变动价位。\\r\\n用法：\\r\\nMINPRICED(N); 返回N所对应合约的最小变动价位。\\r\\nN可以为参数，但不可以为变量。\\r\\n例1：\\r\\nMINPRICED(8603); 返回参数是文华码8603所对应IF1203合约的最小变动价位。\\r\\n例2：\\r\\nN:=8603;\\r\\nMINPRICED(N);返回8603所对应IF1203合约的最小变动价位。", "explanation": "最小变动价位", "markettype": 1, "modifytime": "", "param": "", "tip": "MINPRICED(N),返回N所对应合约的最小变动价位N只能为文华码", "type": 1}, "MINUTE": {"body": "MINUTE", "createtime": "", "description": "MINUTE,返回某个周期的分钟数。\\r\\n \\r\\n注：\\r\\n1：MINUTE的取值范围为0—59\\r\\n2：该函数返回当根K线开始的分钟数,日及日以上周期返回0。\\r\\n例1：\\r\\nMINUTE=0；//在分钟数为0的K线上返回值为1，其余K线返回值为0。\\r\\n例2：\\r\\nTIME>1400&&MINUTE=50,SP;//在14:50的时候卖平仓。", "explanation": "分钟", "markettype": 0, "modifytime": "", "param": "", "tip": "MINUTE,取某个周期的分钟数（0-59）", "type": 7}, "MIN_POS": "MIN_POS 最小值位置", "MIN_VALUE": "MIN_VALUE 最小值", "MOD": {"body": "MOD( , )", "createtime": "", "description": "MOD(A,B)：取模。返回A对B求模。\\r\\n\\r\\n注：\\r\\n第二个参数最多支持四位小数。\\r\\n\\r\\n例1：\\r\\nMOD(26,10);//返回6，26除以10所得余数为6，即26对10 的模为6。\\r\\n例2：\\r\\nDRAWICON(MOD(BARPOS,3)=0,H,'ICO1');//从数据开始第一根k线开始 分别在第3、6、9、\\r\\n12等k线依次往后每隔3根k线标注一个笑脸图案\\r\\n例3：\\r\\nMOD(A,2)=0;//判断A为偶数。", "explanation": "取模", "markettype": 0, "modifytime": "", "param": "", "tip": "MOD(A,B),A对B求模", "type": 4}, "MODE": {"body": "MODE( , )", "createtime": "20150522", "description": "MODE(X,N) 求X在N个周期内最常出现的值。\\r\\n\\r\\n注：\\r\\n1、如果N个周期内不含有重复的数值，则函数返回空值。\\r\\n2、N可以为变量。", "explanation": "求众数", "markettype": 0, "modifytime": "", "param": "", "tip": "MODE(X,N)求X在N个周期内最常出现的值", "type": 4}, "MONEY": {"body": "MONEY", "createtime": "20140718", "description": "MONEY 理论可用资金\\r\\n\\r\\n用法：MONEY返回当前理论可用资金，用于仓位、手数等计算。\\r\\n\\r\\n计算方法：\\r\\n1、模组中MONEY的初始值为单元参数中设置的起始资金。\\r\\n2、历史回测中MONEY的初始值为回测参数中设置的初始资金。\\r\\n3、开仓后k线的MONEY值：开仓前可用资金-持仓保证金-手续费+浮动盈亏，其中持仓保证金=开仓价格*保证金比例*交易单位*手数。\\r\\n4、平仓后k线的MONEY值：平仓前可用资金+平仓释放的保证金-手续费+平仓盈亏，其中平仓释放的保证金=开仓价格*保证金比例*交易单位*手数。\\r\\n\\r\\n注：\\r\\n1、模型选择收盘价模型：\\r\\n  a.开仓信号下一根K线，MONEY返回值为上根K线的可用资金-开仓保证金-手续费+浮动盈亏。\\r\\n  b.平仓信号下一根K线，MONEY返回值为平仓之前的可用资金+持仓释放的保证金-手续费+平仓盈亏。\\r\\n2、模型选择指令价模型：\\r\\n  a.开仓信号当根K线，MONEY返回值为开仓之前的可用资金-开仓保证金-手续费+浮动盈亏。\\r\\n  b.平仓信号当根K线，MONEY返回值为平仓之前的可用资金+平仓释放的保证金-手续费+平仓盈亏。\\r\\n3、信号执行方式为‘K线走完确认信号下单’时，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n4、信号执行方式为‘出信号立即下单，不复核’时，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n5、模组初始化后，MONEY返回值为初始化框中可用资金。\\r\\n6、模组监控K线图，十字光标所在K线左侧查价框中显示的资金数值等于MONEY函数返回值。\\r\\n\\r\\n例：\\r\\nK:=MONEY*0.2/(C*MARGIN*UNIT+FEE); //理论可用资金的20%可以开仓的手数（此写法适用于按固定手数收取手续费的合约）", "explanation": "理论可用资金", "markettype": 1, "modifytime": "20240408", "param": "", "tip": "MONEY，理论可用资金", "type": 12}, "MONEYRATIO": {"body": "MONEYRATIO", "createtime": "20140718", "description": "MONEYRATIO 理论资金使用率\\r\\n\\r\\n用法：MONEYRATIO返回当前理论资金使用率，进行资金管理控制仓位时使用。\\r\\n\\r\\n计算方法：资金使用率=动态的持仓保证金/权益。\\r\\n\\r\\n注：\\r\\n1、该函数返回值为小数。\\r\\n2、开仓信号当根k线持仓保证金：\\r\\n  a.信号执行方式为‘XX下单，K线走完复核’：持仓保证金=当根K线的收盘价*交易单位*持仓手数*保证金比例。\\r\\n  b.信号执行方式为‘出信号立即下单，不复核’：持仓保证金=最新价*交易单位*持仓手数*保证金比例。\\r\\n  c.信号执行方式为‘K线走完确认信号下单’：持仓保证金=当根K线的收盘价*交易单位*持仓手数*保证金比例。在出开仓信号之后的K线返回相应的值\\r\\n3、开仓后平仓前K线持仓保证金=当根K线的收盘价*交易单位*持仓手数*保证金比例。开仓后未平仓的k线的保证金和开仓信号当根k线的保证金一样，保持不变，当日如果存在未被平掉的持仓并持仓过夜，第二日未平仓k线的保证金不会按照前一日结算价计算。\\r\\n4、平仓信号，当平仓后无持仓时，MONEYRATIO在出平仓信号之后的k线上返回0\\r\\n\\r\\n例：\\r\\nA&&MONEYRATIO<0.3,BK;//A条件满足并资金使用率不超过30%时，买开仓", "explanation": "理论资金使用率", "markettype": 1, "modifytime": "20230216", "param": "", "tip": "MONEYRATIO返回理论资金使用率", "type": 12}, "MONEYTOT": {"body": "MONEYTOT", "createtime": "20140718", "description": "MONEYTOT 理论权益\\r\\n\\r\\n用法：MONEYTOT返回当前理论权益，模型进行仓位控制、下单手数等资金管理时使用\\r\\n\\r\\n计算方法：MONEYTOT=理论可用资金+持仓保证金\\r\\n\\r\\n注：\\r\\n1、模组中MONEYTOT的初始值为单元参数中设置的起始资金。\\r\\n2、历史回测中MONEYTOT的初始值为回测参数中设置的初始资金。\\r\\n3、模组初始化时：\\r\\n  a.当前信号为开仓信号，MONEYTOT返回值为初始化框中模组理论可用资金；\\r\\n  b.当前信号为平仓信号，则MONEYTOT返回初始化框中模组理论可用资金+持仓保证金。\\r\\n4、开仓信号当根k线：MONEYTOT=模组理论可用资金+持仓保证金\\r\\n5、开仓后平仓前：MONEYTOT返回模组理论可用资金+持仓保证金\\r\\n6、平仓信号当根k线：持仓为0时，MONEYTOT=模组理论可用资金；持仓不为0时，MONEYTOT=模组理论可用资金+持仓占用的保证金。\\r\\n注：\\r\\n模组持仓列表可用资金为包含了浮动盈亏的可用资金（= 当前权益 - 持仓占用的保证金）。\\r\\n\\r\\n例：\\r\\nK:=MONEYTOT*0.2/(C*MARGIN*UNIT+FEE); //理论权益的20%可以开仓的手数（此写法适用于按固定手数收取手续费的合约）", "explanation": "理论权益", "markettype": 1, "modifytime": "20230216", "param": "", "tip": "MONEYTOT理论权益", "type": 12}, "MONTH": {"body": "MONTH", "createtime": "", "description": "MONTH，返回某个周期的月份。\\r\\n \\r\\n注：\\r\\nMONTH的取值范围为1—12.\\r\\n \\r\\n例1：\\r\\nVALUEWHEN(MONTH=3&&DAY=1,C);//在K线日期为三月一日时取其收盘价。\\r\\n例2：\\r\\nC>=VALUEWHEN(MONTH<REF(MONTH,1),O),SP;", "explanation": "取月份", "markettype": 0, "modifytime": "", "param": "", "tip": "MONTH,取得某周期的月份（1-12）", "type": 7}, "MONTHTRADE": {"body": "MONTHTRADE", "createtime": "20141225", "description": "MONTHTRADE 月内交易函数。\\r\\n\\r\\n用法：\\r\\nMONTHTRADE 模型中写入该函数，信号和资金每月重新初始化进行计算，与历史割裂。\\r\\n\\r\\n注：\\r\\n1、该函数不支持自定义N日、月、季、年周期，其他周期均支持。\\r\\n2、回测报告中的出金/入金，为每月出金/入金的和。\\r\\n3、模型中不能同时使用DAYTRADE1\\DAYTRADE\\WEEKTRADE\\WEEKTRADE1\\MONTHTRADE\\QUARTERTRADE\\YEARTRADE函数。\\r\\n4、（1）历史回测中，当月K线走完持仓大于0，会对持仓进行全清处理。\\r\\n   （2）模组运行中，当月K线走完持仓大于0，信号和资金会重新初始化进行计算，但不会自动对持仓进行全清处理，需要在模型中编写实现全清。\\r\\n\\r\\n例：\\r\\nMA5^^MA(C,5);\\r\\nMA10^^MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;//5周期均线上穿10周期均线，买开仓\\r\\nCROSSDOWN(MA5,MA10),SK;//5周期均线下穿10周期均线，卖开仓\\r\\nC<BKPRICE-10*MINPRICE,SP;//亏损10点平多\\r\\nC>SKPRICE+10*MINPRICE,BP;//亏损10点平空\\r\\nCLOSEMINUTE<=1,CLOSEOUT;//收盘前一分钟，清仓。\\r\\nAUTOFILTER;//过滤模型\\r\\nMONTHTRADE;//使用每月数据计算", "explanation": "月内交易函数", "markettype": 1, "modifytime": "", "param": "", "tip": "MONTHTRADE,月内交易函数", "type": 9}, "MONTHTRADE1": {"body": "MONTHTRADE1", "createtime": "20180117", "description": "MONTHTRADE1 月内交易函数。\\r\\n\\r\\n用法：\\r\\nMONTHTRADE1 模型中写入该函数，信号和资金每月重新初始化进行计算，与历史割裂，并且每一个函数只使用当月K线数据进行计算，历史数据不参与计算。\\r\\n\\r\\n注：\\r\\n1、该函数不支持自定义N日、月、季、年周期，其他周期均支持。\\r\\n2、回测报告中的出金/入金，为每月出金/入金的和。\\r\\n3、模型中不能同时使用DAYTRADE1\\DAYTRADE\\WEEKTRADE\\WEEKTRADE1\\MONTHTRADE\\MONTHTRADE1\\QUARTERTRADE\\QUARTERTRADE1\\YEARTRADE\\YEARTRADE1函数。\\r\\n4、（1）历史回测中，当月K线走完持仓大于0，会对持仓进行全清处理。\\r\\n   （2）模组运行中，当月K线走完持仓大于0，信号和资金会重新初始化进行计算，但不会自动对持仓进行全清处理，需要在模型中编写实现全清。\\r\\n\\r\\n例：\\r\\nMA5^^MA(C,5);\\r\\nMA10^^MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;//5周期均线上穿10周期均线，买开仓\\r\\nCROSSDOWN(MA5,MA10),SK;//5周期均线下穿10周期均线，卖开仓\\r\\nC<BKPRICE-10*MINPRICE,SP;//亏损10点平多\\r\\nC>SKPRICE+10*MINPRICE,BP;//亏损10点平空\\r\\nCLOSEMINUTE<=1,CLOSEOUT;//收盘前一分钟，清仓。\\r\\nAUTOFILTER;//过滤模型\\r\\nMONTHTRADE1;//使用每月数据计算", "explanation": "月内交易函数", "markettype": 1, "modifytime": "", "param": "", "tip": "MONTHTRADE1月内交易函数，且历史数据不参与计算。", "type": 9}, "MULTSIG": {"body": "MULTSIG( , , , )", "createtime": "20150109", "description": "MULTSIG(Sec1,Sec2,N,INTERVAL) 设置一根k线多信号的指令价方式（TICK逐笔回测，可设置回测精度）\\r\\n\\r\\n用法：\\r\\nMULTSIG(Sec1,Sec2,N,INTERVAL) \\r\\n1、当INTERVAL不为0时，INTERVAL为数据时间间隔,每隔INTERVAL秒计算一次信号，开仓信号在出信号后的第Sec1个数据时间间隔时下单不复核,平仓信号在出信号后的第Sec2个数据时间间隔下单不复核，一根K线上最大的信号个数为N。\\r\\n（例：INTERVAL为10，豆粕合约开盘第一根K线21：00：09为第一次计算模型，21：00：19为第二次计算模型...）\\r\\n2、当INTERVAL为0时，每笔TICK计算一次信号，开仓信号Sec1秒后下单不复核,平仓信号Sec2秒后下单不复核，一根K线上最大的信号个数为N。\\r\\n（例：Sec1为0，则为开仓信号出信号立即下单，不复核；如果Sec1为1，则为开仓信号出信号1秒后下单，不复核）\\r\\n3、通过调整INTERVAL参数，模型可设置不同数据快照频率进行回测。\\r\\n\\r\\n注：\\r\\n1、该函数支持在期货月份合约和股票上运行。\\r\\n2、写了这个函数以后，模型会按照指令价方式运行。\\r\\n3、Sec1设置的信号为：BK/SK；Sec2设置的信号为：BP/SP/BPK/SPK\\r\\n4、含有该函数的模型，满足条件出信号下单后此信号固定，不随之后行情是否满足条件而变化\\r\\n5、INTERVAL代表数据时间间隔\\r\\n  1）支持0、3、5、10四个值，不支持变量。\\r\\n  2）参数为3、5、10分别代表用每隔3秒、5秒、10秒，计算一次模型\\r\\n  3）参数为3、5、10 ，回测速度可提升3-10倍，回测精度稍差\\r\\n  4）参数为0的时候 为每笔TICK计算一次模型\\r\\n  5）一个模型中只能写入一个INTERVAL值\\r\\n6、出信号后如果未到Sec个数据时间间隔K线已经走完，则提前确认信号下单。\\r\\n7、该函数不支持加载到页面盒子中使用。\\r\\n8、该函数支持一根K线上多个信号，最大的信号个数为N。N取值范围为1-60，超过这个范围，N值按照60计算\\r\\n9、CHECKSIG、MULTSIG、MULTSIG_MIN和CHECKSIG_MIN函数不能同时出现在一个模型中。\\r\\n10、模型中不含有该函数，信号执行方式默认为K线走完确认信号下单\\r\\n11、N支持写为变量。\\r\\n12、该函数不支持量能周期\\r\\n13、不支持与TRADE_OTHER、#CALL、#CALL_OTHER、#CALL_PLUS函数同时使用。\\r\\n\\r\\n例：\\r\\nC>REF(H,1),BK;//价格大于上一根k线最高价，开多仓\\r\\nC<BKPRICE-3*MINPRICE,SP;//亏损3点止损\\r\\nMULTSIG(2,0,4,10);//设置信号复核确认方式为开仓信号，出信号后第2个时间间隔下单不复核（例如09:00:09出现信号，09:00:29仍旧满足条件则确认信号并下单）。根据时间间隔计算出现平仓信号立即下单不复核（例如09:00:39出现平仓信号，则立即下单不复核）。一根K线上最大信号个数为4。每隔10秒计算一次信号。\\r\\nAUTOFILTER;", "explanation": "设置一根k线多信号的指令价方式（TICK逐笔回测，可设置回测精度）", "markettype": 1, "modifytime": "20230208", "param": "", "tip": "MULTSIG(Sec1,Sec2,N,INTERVAL),设置一根k线多信号的指令价方式（TICK逐笔回测，可设置回测精度），开仓信号出信号Sec1秒下单，不复核；平仓信号出信号Sec2秒下单，不复核，一根K线最大的信号个数为N,INTERVAL代表数据时间间隔", "type": 13}, "MULTSIG_MIN": {"body": "MULTSIG_MIN( , , )", "createtime": "20141125", "description": "设置一根k线多信号的指令价方式（逐分钟回测）\\r\\n\\r\\n用法：\\r\\nMULTSIG_MIN(min1,min2,N) 设置一根k线多信号的指令价方式（逐分钟回测），开仓信号出信号min1分钟下单不复核,平仓信号出信号min2分钟下单不复核，一根K线上最大的信号个数为N。\\r\\n\\r\\n注：\\r\\n1、写了这个函数以后，模型会按照指令价方式运行。\\r\\n2、使用该函数时，基础数据为1分钟数据。\\r\\n3、该函数不支持加载在15分钟以下周期使用\\r\\n4、min1设置的信号为：BK/SK；min2设置的信号为：BP/SP/BPK/SPK\\r\\n5、含有该函数的模型，满足条件后min分钟出信号立即下单，并且此信号固定，不随之后行情是否满足条件而变化。其中，min=0，出信号立即下单不复核；min>0 出信号min分钟下单不复核。\\r\\n6、出信号后如果未到min分钟K线已经走完，则提前确认信号下单。\\r\\n7、该函数不支持加载到页面盒子中使用。\\r\\n8、该函数支持一根K线上多个信号，最大的信号个数为N。N取值范围为1-60，超过这个范围，N值按照60计算\\r\\n9、CHECKSIG、MULTSIG、MULTSIG_MIN和CHECKSIG_MIN函数不能同时出现在一个模型中。\\r\\n10、模型中含有该函数，效果测试中模型信号价位为模型满足条件时候行情的最新价。\\r\\n11、模型中不含有该函数，信号执行方式默认为K线走完确认信号下单。\\r\\n12、N支持写为变量。\\r\\n\\r\\n例：\\r\\nC>REF(H,1),BK;//价格大于上一根k线最高价，开多仓\\r\\nC<BKPRICE-3*MINPRICE,SP;//亏损3点止损\\r\\nMULTSIG_MIN(3,0,3);//设置信号复核确认方式为开仓信号，出信号后3分钟下单，不复核；平仓信号出信号立即下单，不复核。一根K线上最大信号个数为3。\\r\\nAUTOFILTER;", "explanation": "设置一根k线多信号的指令价方式（逐分钟回测）", "markettype": 1, "modifytime": "20220106", "param": "", "tip": "MULTSIG_MIN(min1,min2,N),设置一根k线多信号的指令价方式（逐分钟回测）开仓信号出信号min1分钟下单，不复核；平仓信号出信号min2分钟下单，不复核，一根K线最大的信号个数为N", "type": 13}, "MV": {"body": "MV(,)", "createtime": "********", "description": "MV(A,...P) 取A到P的均值。\\r\\n\\r\\n注：\\r\\n1、支持取2-16个数值的均值\\r\\n2、A...P可以为数字也可以为变量\\r\\n\\r\\n例1：\\r\\nMV(CLOSE,OPEN);\\r\\n//取收盘价和开盘价的平均值", "explanation": "取均值", "markettype": 0, "modifytime": "", "param": "", "tip": "MV(A,...P),取A到P的均值", "type": 2}, "MYVOL": {"body": "MYVOL", "createtime": "", "description": "MYVOL 取下单手数\\r\\n\\r\\n用法：取下单手数，多用于在加减仓模型加载多个合约的时候的手数计算。\\r\\n\\r\\n注：\\r\\n1、模组加载：\\r\\n主图右键加载到模组：返回主图回测参数设置中设置的手数\\r\\n新建模组：返回交易参数中设置的手数\\r\\n2、主图回测：返回回测参数中设置的手数，回测时，如果加载的模型不含交易指令，该函数返回值为1。\\r\\n\\r\\n例：\\r\\n//加载参数中下单手数设置为3时，下面编写BK的下单手数为6；\\r\\nC>O,BK(2*MYVOL);\\r\\nC<O,SP(BKVOL);", "explanation": "取下单手数", "markettype": 1, "modifytime": "20230216", "param": "", "tip": "MYVOL,取下单手数", "type": 10}, "NAMELIKE": {"body": "NAMELIKE('')", "createtime": "20161216", "description": "NAMELIKE('') 模糊合约名称函数。\\r\\n\\r\\n用法：\\r\\nNAMELIKE('ST') 判断合约名称是否含有ST。是返回1（YES）,不是返回0（NO）。\\r\\n\\r\\n注：\\r\\n1、判断的内容用单引号标注。\\r\\n\\r\\n例1：\\r\\nNAMELIKE('沪铜');//判断期货名称是否含有沪铜。\\r\\nNAMELIKE('浦发');//判断股票名称是否含有浦发。\\r\\nNAMELIKE('cu');//判断期权名称是否含有cu。\\r\\n例2：\\r\\nC>O&&NAMELIKE('ST')=0;//最后一根K线为阳线并且名称不含有ST。", "explanation": "模糊合约名称函数", "markettype": 0, "modifytime": "20220628", "param": "", "tip": "NAMELIKE('')模糊股票名称函数。NAMELIKE('ST')判断股票名称是否含有ST。是返回1（YES）,不是返回0（NO）。", "type": 5}, "NEWHBARS": {"body": "NEWHBARS( , )", "createtime": "20150330", "description": "NEWHBARS(X,N) 计算高于当前X的第N个X到现在K线的距离。\\r\\n\\r\\n注：\\r\\n1、计算距离时不包括当根K线。\\r\\n2、参数N不支持写为变量。\\r\\n例：\\r\\nDISTH:=NEWHBARS(H,1);//上一次创新高距离当前的K线根数\\r\\nHI20:=REF(H,DISTH);//上一次创新高的高点\\r\\nCROSS(C,REF(HI20,1)),BK;//最新价突破上一次创新高的高点，多头开仓\\r\\nC<=BKPRICE-5,SP;\\r\\nAUTOFILTER;", "explanation": "创新高跨度", "markettype": 0, "modifytime": "", "param": "", "tip": "NEWHBARS(X,N)求高于当前X的第N个X的距离", "type": 2}, "NEWHBARS1": {"body": "NEWHBARS1( , , )", "createtime": "20201231", "description": "NEWHBARS1(X,Y,N) 计算高于当前X的第N个Y到现在K线的距离。\\r\\n\\r\\n注：\\r\\n1、计算距离时不包括当根K线。\\r\\n2、参数N不支持写为变量。\\r\\n例：\\r\\nMA5:MA(CLOSE,5);\\r\\nDISTH:=NEWHBARS1(MA5,C,1);//上一次高于当前MA5均线收盘价距离当前的K线根数\\r\\nHI20:=REF(H,DISTH);//上一次创新高的高点\\r\\nCROSS(C,HI20),BK;//最新价突破高点，多头开仓\\r\\nC<=BKPRICE-5,SP;\\r\\nAUTOFILTER;", "explanation": "创新高跨度", "markettype": 0, "modifytime": "", "param": "", "tip": "NEWHBARS1(X,Y,N)，计算高于当前X的第N个Y到现在K线的距离", "type": 2}, "NEWLBARS": {"body": "NEWLBARS( , )", "createtime": "20150522", "description": "NEWLBARS(X,N) 计算低于当前X的第N个X到现在K线的距离。\\r\\n\\r\\n注：\\r\\n1、计算距离时不包括当根K线。\\r\\n2、参数N不支持写为变量。\\r\\n例：\\r\\nDISTL:=NEWLBARS(L,1);//上一次创新低距离当前的K线根数\\r\\nLI20:=REF(L,DISTL);//上一次创新低的低点\\r\\nCROSS(REF(LI20,1),C),SK;//最新价跌破上一次创新低的低点，空头开仓\\r\\nC>=SKPRICE+5,BP;\\r\\nAUTOFILTER;", "explanation": "创新低跨度", "markettype": 0, "modifytime": "", "param": "", "tip": "NEWLBARS(X,N)求低于当前X的第N个X的距离", "type": 2}, "NEWLBARS1": {"body": "NEWLBARS1( , , )", "createtime": "20201231", "description": "NEWLBARS1(X,Y,N) 计算低于当前X的第N个Y到现在K线的距离。\\r\\n\\r\\n注：\\r\\n1、计算距离时不包括当根K线。\\r\\n2、参数N不支持写为变量。\\r\\n例：\\r\\nMA5:MA(CLOSE,5);\\r\\nDISTL:=NEWLBARS1(MA5,C,1);//上一次低于当前MA5均线收盘价距离当前的K线根数\\r\\nLI20:=REF(L,DISTL);//上一次创新低的低点\\r\\nCROSS(LI20,C),SK;//最新价跌破低点，空头开仓\\r\\nC>=SKPRICE+5,BP;\\r\\nAUTOFILTER;", "explanation": "创新低跨度", "markettype": 0, "modifytime": "", "param": "", "tip": "NEWLBARS1(X,Y,N)，计算低于当前X的第N个Y到现在K线的距离", "type": 2}, "NEW_ORDER": "NEW_ORDER 最新价", "NODRAW": {"body": "NODRAW", "createtime": "", "description": "NODRAW 只显示返回数值，不画线。\\r\\n注：\\r\\n不支持将该函数直接定义为变量，即不支持下面的写法：A:NODRAW;\\r\\n\\r\\n例：\\r\\n MA5:MA(C,5), NODRAW;显示5周期均线的返回值，K线图上不显示均线。", "explanation": "不画线", "markettype": 0, "modifytime": "", "param": "", "tip": "NODRAW,不画线", "type": 8}, "NORMPDF": {"body": "NORMPDF( , , )", "createtime": "20150122", "description": "NORMPDF(X,MU,SIGMA)：返回参数为MU和SIGMA的正态分布密度函数在X处的值\\r\\n\\r\\n注：\\r\\n1、MU或SIGMA为空值，该函数返回空值。\\r\\n2、MU和SIGMA支持变量。\\r\\n\\r\\n算法举例：随机变量X服从分布的算数平均值MU、分布的标准偏差SIGMA的概率分布，其概率密度为NORMPDF。\\r\\n\\r\\n用麦语言函数可以近似的表示如下：\\r\\n(1/(SQRT(2*3.14)*SIGMA))*EXP((-SQUARE(X-MU))/(2*SQUARE(SIGMA)));\\r\\n\\r\\n例：\\r\\nTR:=MAX(MAX((HIGH-LOW),ABS(REF(CLOSE,1)-HIGH)),ABS(REF(CLOSE,1)-LOW));\\r\\n\\r\\nATR:=MA(TR,26);//求26个周期内的TR的简单移动平均\\r\\n\\r\\nMu:=MA(ATR,10);//求10个周期内的ATR的简单移动平均\\r\\n\\r\\nSIGMA:=STD(ATR,10);//求10个周期内的ATR的标准差\\r\\n\\r\\nZZ..NORMPDF(ATR,MU,SIGMA);//定义变量ZZ，返回ATR服从正态分布的概率密度。", "explanation": "正态分布概率密度", "markettype": 0, "modifytime": "20230224", "param": "", "tip": "NORMPDF(X,MU,SIGMA),返回参数为MU和SIGMA的正态分布密度函数在X处的值", "type": 3}, "NOT": {"body": "NOT( )", "createtime": "", "description": "NOT(X)：取非。当X＝0时返回1，否则返回0。\\r\\n例1：\\r\\n NOT(ISLASTBK);如果上一个信号不是BK信号，则NOT(ISLASTBK)返回值为1；上一个信号是BK信号，则NOT(ISLASTBK)返回值为0。\\r\\n例2：\\r\\nNOT(BARSBK>=1)=1;//BK信号发出的当根K线上满足条件。\\r\\n//NOT(BARSBK>=1)=1 与 NOT(BARSBK>=1) 表达同等意义。", "explanation": "非", "markettype": 0, "modifytime": "", "param": "", "tip": "NOT(X),不满足条件X，不满足条件X返回1，否则返回0", "type": 5}, "NOTEXT": {"body": "NOTEXT", "createtime": "20160415", "description": "NOTEXT 只显示画线，不显示数值。\\r\\n\\r\\n注：\\r\\n不支持将该函数直接定义为变量，即不支持下面的写法：A:NOTEXT;\\r\\n\\r\\n例：\\r\\n MA5:MA(C,5), NOTEXT;K线图上显示5周期均线，不显示均线的数值。", "explanation": "不显示数值", "markettype": 0, "modifytime": "", "param": "", "tip": "NOTEXT不显示数值", "type": 8}, "NULL": {"body": "NULL", "createtime": "", "description": "返回空值\\r\\n用法：\\r\\nMA5:=MA(C,5);\\r\\nMA10:=MA(C,10);\\r\\nA:IFELSE(MA5>MA10,MA5,NULL),COLORRED;//当MA5>MA10时，画五日均线MA5，不满足MA5>MA10时，返回空值，不画线。", "explanation": "返回空值", "markettype": 0, "modifytime": "", "param": "", "tip": "NULL,返回空值", "type": 5}, "NUMPOW": {"body": "NUMPOW(,,)", "createtime": "", "description": "NUMPOW(X,N,M);自然数幂方和\\r\\n算法：\\r\\nNUMPOW(x,n,m)=n^m*x+(n-1)^m*ref(x,1)+(n-2)^m*ref(x,2)+...+2^m*ref(x,n-2)+1^m*ref(x,n-1)\\r\\n注意:\\r\\n1、N为自然数，M为实数；且N与M不能为变量\\r\\n2、X为基础变量\\r\\n\\r\\n例：\\r\\nJZ:=NUMPOW(C,5,2);", "explanation": "自然数幂方和", "markettype": 0, "modifytime": "", "param": "", "tip": "NUMPOW(X,N,M),自然数幂方和X为基础变量，N为自然数，M为实数", "type": 2}, "O": "O 开盘价", "OFFSETPROFIT": {"body": "OFFSETPROFIT", "createtime": "20140718", "description": "OFFSETPROFIT 理论平仓盈亏\\r\\n\\r\\n用法：OFFSETPROFIT返回当前理论平仓盈亏，用于风险控制。\\r\\n\\r\\n注：\\r\\n1、模组初始化后，OFFSETPROFIT清零重新计算，从模组加载时刻开始计算平仓盈亏。\\r\\n2、信号执行方式为‘K线走完确认信号下单’：OFFSETPROFIT根据信号当根K线的收盘价计算平仓盈亏，待下根K线时取值相应变化。\\r\\n3、信号执行方式为‘XX下单，K线走完复核’：OFFSETPROFIT根据下单时行情的最新价计算平仓盈亏，复核后待下根K线时取值相应变化。\\r\\n4、信号执行方式为‘出信号立即下单不复核’：OFFSETPROFIT根据信号确认时的行情最新价计算平仓盈亏。\\r\\n5、OFFSETPROFIT与日志中记录的平仓盈亏不同，后者根据成交价计算。\\r\\n6、OFFSETPROFIT的计算不包含手续费。\\r\\n7、因信号消失产生的盈亏未纳入OFFSETPROFIT的计算\\r\\n8、股票T+1交易不支持统计平仓盈亏，该函数返回空值。\\r\\n\\r\\n例：\\r\\nOFFSETPROFIT<-5000&&C>O,BK;//亏损大于5000，并且当前K线为阳线时，买开", "explanation": "理论平仓盈亏", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "OFFSETPROFIT,返回理论平仓盈亏", "type": 12}, "OFFSETPROFIT1": {"body": "OFFSETPROFIT1", "createtime": "********", "description": "OFFSETPROFIT1 累计平仓盈亏\\r\\n\\r\\n用法：OFFSETPROFIT1返回加载的全部K线的累计平仓盈亏。\\r\\n\\r\\n注：\\r\\n1、模组初始化后，OFFSETPROFIT1不会清零，延续之前的信号计算累计平仓盈亏。\\r\\n2、信号执行方式为‘K线走完确认信号下单’：OFFSETPROFIT1根据信号当根K线的收盘价计算平仓盈亏，待下根K线时取值相应变化。\\r\\n3、信号执行方式为‘XX下单，K线走完复核’：OFFSETPROFIT1根据下单时行情最新价计算平仓盈亏，复核后待下根K线时取值相应变化。\\r\\n4、信号执行方式为‘出信号立即下单不复核’：OFFSETPROFIT1根据信号确认时的行情最新价计算平仓盈亏。\\r\\n5、OFFSETPROFIT1与日志中记录的平仓盈亏不同，后者根据成交价计算。\\r\\n6、OFFSETPROFIT1的计算不包含手续费。\\r\\n7、因信号消失产生的盈亏未纳入OFFSETPROFIT1的计算。\\r\\n8、股票T+1交易不支持统计平仓盈亏，该函数返回空值。\\r\\n\\r\\n例：\\r\\nOFFSETPROFIT1<=-100,CLOSEOUT;//累计亏损大于100，清仓", "explanation": "累计平仓盈亏", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "OFFSETPROFIT1累计平仓盈亏", "type": 12}, "OPEN": {"body": "OPEN", "createtime": "", "description": "OPEN 取得K线图的开盘价。\\r\\n\\r\\n注：\\r\\n1、可简写为O。\\r\\n\\r\\n例1：\\r\\nOO:O;//定义OO为开盘价；\\r\\n例2：\\r\\nNN:=BARSLAST(DATE<>REF(DATE,1));\\r\\nOO:REF(O,NN);//取的当日的开盘价\\r\\n例3：\\r\\nMA5:MA(O,5);//定义开盘价的5周期均线（O为OPEN简写）。", "explanation": "取得K线图的开盘价", "markettype": 0, "modifytime": "", "param": "", "tip": "OPEN取得开盘价", "type": 1}, "OPENMINUTE": {"body": "OPENMINUTE", "createtime": "20150511", "description": "OPENMINUTE，返回开盘后经过的分钟数。\\r\\n\\r\\n注：\\r\\n1、该函数只能用于收盘价模型。\\r\\n2、该函数返回开盘时间距离当根K线开始时间的分钟数。\\r\\n3、该函数需要在秒周期，分钟，小时周期使用；不支持在量能周期，日线及以上周期使用。\\r\\n4、该函数的返回值包含小结和午休的时间。\\r\\n5、OPENMINUTE返回的是交易所的时间，不是本机的时间。\\r\\n6、对于夜盘合约，白盘开盘不是当日开盘，夜盘开盘才算作当日开盘。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5))&&OPENMINUTE>5,BPK;//当天开盘后五分钟内不开仓\\r\\nCROSS(MA(C,5),C)&&OPENMINUTE>5,SPK;\\r\\nAUTOFILTER;", "explanation": "开盘后经过的分钟数", "markettype": 1, "modifytime": "", "param": "", "tip": "OPENMINUTE,返回开盘后经过的分钟数", "type": 7}, "OPENMINUTE1": {"body": "OPENMINUTE1", "createtime": "20150511", "description": "OPENMINUTE1，返回开盘后经过的分钟数。\\r\\n\\r\\n注：\\r\\n1、该函数只能用于指令价模型。\\r\\n2、\\r\\n历史K线：该函数返回K线结束时间距离开盘的分钟数。\\r\\n盘中：该函数返回K线当前时间距离开盘的分钟数。\\r\\n3、该函数需要在秒周期，分钟，小时，日线周期使用；不支持在量能周期，周线及以上周期使用。\\r\\n4、该函数返回值包含小结和午休的时间。\\r\\n5、OPENMINUTE1返回的是交易所的时间，不是本机的时间。\\r\\n6、对于夜盘合约，白盘开盘不是当日开盘，夜盘开盘才算作当日开盘。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5))&&OPENMINUTE1>5,BPK;//当天开盘后五分钟内不开仓\\r\\nCROSS(MA(C,5),C)&&OPENMINUTE1>5,SPK;\\r\\nMULTSIG(0,0,1,0);//出信号立即下单，不复核\\r\\nAUTOFILTER;", "explanation": "开盘后经过的分钟数", "markettype": 1, "modifytime": "", "param": "", "tip": "OPENMINUTE1,返回开盘后经过的分钟数", "type": 7}, "OPENSEC": {"body": "OPENSEC", "createtime": "20201231", "description": "OPENSEC，返回开盘后经过的秒数。\\r\\n\\r\\n注：\\r\\n1、该函数只能用于收盘价模型。\\r\\n2、该函数返回开盘时间距离当根K线开始时间的秒数。\\r\\n3、该函数需要在秒周期，分钟，小时周期使用；不支持在量能周期，日线及以上周期使用。\\r\\n4、该函数的返回值包含小结和午休的时间。\\r\\n5、OPENSEC返回的是交易所的时间，不是本机的时间。\\r\\n6、对于夜盘合约，白盘开盘不是当日开盘，夜盘开盘才算作当日开盘。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5))&&OPENSEC>30,BPK;//当天开盘后30秒内不开仓\\r\\nCROSS(MA(C,5),C)&&OPENSEC>30,SPK;\\r\\nAUTOFILTER;", "explanation": "开盘后经过的秒数", "markettype": 1, "modifytime": "", "param": "", "tip": "OPENSEC,返回开盘后经过的秒数", "type": 7}, "OPENSEC1": {"body": "OPENSEC1", "createtime": "20201231", "description": "OPENSEC1，返回开盘后经过的秒数。\\r\\n\\r\\n注：\\r\\n1、该函数只能用于指令价模型。\\r\\n2、\\r\\n历史K线：该函数返回K线结束时间距离开盘的秒数。\\r\\n盘中：该函数返回K线当前时间距离开盘的秒数。\\r\\n3、该函数需要在秒周期，分钟，小时，日线周期使用；不支持在量能周期，周线及以上周期使用。\\r\\n4、该函数返回值包含小结和午休的时间。\\r\\n5、OPENSEC1返回的是交易所的时间，不是本机的时间。\\r\\n6、对于夜盘合约，白盘开盘不是当日开盘，夜盘开盘才算作当日开盘。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5))&&OPENSEC1>30,BPK;//当天开盘后30秒内不开仓\\r\\nCROSS(MA(C,5),C)&&OPENSEC1>30,SPK;\\r\\nMULTSIG(0,0,1,0);//出信号立即下单，不复核\\r\\nAUTOFILTER;", "explanation": "开盘后经过的秒数", "markettype": 1, "modifytime": "", "param": "", "tip": "OPENSEC1,返回开盘后经过的秒数", "type": 7}, "OPI": {"body": "OPI", "createtime": "", "description": "OPI 取得K线图的持仓量。\\r\\n\\r\\n注：\\r\\nOPI函数加载在股票合约上返回值为股票的成交额。\\r\\n\\r\\n例1：\\r\\nOPID:OPI;//定义OPID为持仓量。\\r\\n例2：\\r\\nOPI>=REF(OPI,1);//持仓量大于前一个周期的持仓量，表示持仓量增加。\\r\\n例3：\\r\\nNN:=BARSLAST(DATE<>REF(DATE,1))+1;\\r\\nOPID:REF(OPI,NN);//取的昨天收盘时的持仓量", "explanation": "取得K线图的持仓量", "markettype": 0, "modifytime": "", "param": "", "tip": "OPI取得持仓量", "type": 1}, "OPISTICK": "OPISTICK 画竖线，K线为阳线为红色，K线为阴线为青色", "OR": "", "PANZHENG": {"body": "PANZHENG", "createtime": "20141117", "description": "PANZHENG 判断当前行情是否为盘整\\r\\n\\r\\n用法：返回1:表示盘整，返回0:表示不是盘整。\\r\\n\\r\\n注：\\r\\n这个函数的目的是用于判断当根k线是否盘整状态，是否适合做开仓操作，从而优化趋势模型，避免在盘整阶段频繁交易。\\r\\n这个函数不适合用于判断一段行情，也就是说不适合用来开发突破模型。\\r\\n\\r\\n例：\\r\\nMA1:MA(CLOSE,5);\\r\\nMA2:MA(CLOSE,10);\\r\\nCROSS(MA1,MA2)&&PANZHENG=0,BK;//盘整行情不开新仓，减少模型在盘整阶段的资金回撤\\r\\nCROSS(MA2,MA1),SP;\\r\\nAUTOFILTER;", "explanation": "判断是否为盘整", "markettype": 1, "modifytime": "20211130", "param": "", "tip": "PANZHENG,判断行情是否盘整，返回1时表示盘整，返回0时表示非盘整", "type": 5}, "PARTLINE": {"body": "PARTLINE( , , )", "createtime": "20140603", "description": "PARTLINE 画线段。\\r\\n\\r\\n用法：\\r\\nPARTLINE(COND, DATA, COLOR); \\r\\n条件COND满足时，以COLOR颜色的直线连接DATA各点\\r\\n\\r\\n注：\\r\\n1、该函数是将满足条件的DATA以线段形式连接起来，连线并不连续。\\r\\n2、该函数支持在函数后设置线型（LINETHICK1 - LINETHICK7、POINTDOT、DOT），即支持下面的写法：\\r\\nPARTLINE(COND, DATA, COLOR),LINETHICK;\\r\\n3、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:PARTLINE(COND,DATA,COLOR);\\r\\n\\r\\n例1：\\r\\nPARTLINE(HIGH>REF(HIGH,1),HIGH,COLORRED);//表示当期最高价大于前期最高价时用红色绘制最高价线段。\\r\\n例2：\\r\\nPARTLINE(LOW<REF(LOW,1),LOW,COLORBLUE),LINETHICK5;//表示当期最低价小于前期最低价时用蓝色绘制最低价线段，线型粗细为5。", "explanation": "画线段", "markettype": 0, "modifytime": "", "param": "COND为条件,DATE为数值，COLOR为颜色", "tip": "PARTLINE(COND,DATA,COLOR)条件COND满足时，以COLOR颜色的直线连接DATA各点", "type": 8}, "PARTLINE1": {"body": "PARTLINE1( , )", "createtime": "20140603", "description": "PARTLINE1 画线段。\\r\\n\\r\\n用法：\\r\\nPARTLINE1(COND,DATA);\\r\\n条件COND满足时，用线段连接DATA各点。\\r\\n\\r\\n注：\\r\\n1、该函数是将满足条件的DATA以线段形式连接起来，连线并不连续。\\r\\n2、该函数支持在函数后设置颜色、线型（LINETHICK1 - LINETHICK7、POINTDOT、DOT），即支持下面的两种写法：\\r\\nPARTLINE1(COND,DATA),COLOR,LINETHICK;\\r\\nPARTLINE1(COND,DATA),LINETHICK,COLOR;\\r\\n3、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:PARTLINE1(COND,DATA);\\r\\n\\r\\n例：\\r\\nPARTLINE1(HIGH>REF(HIGH,1),HIGH),COLORRED,LINETHICK5;//表示当期最高价大于前期最高价时用红色绘制最高价线段，线型粗细为5。", "explanation": "画线段", "markettype": 0, "modifytime": "", "param": "COND为条件,DATA为数值", "tip": "PARTLINE1(COND,DATA)条件COND满足时，用直线段连接DATA各点", "type": 8}, "PASSIVE_ORDER": "PASSIVE_ORDER 排队价", "PCRATE": {"body": "PCRATE(,)", "createtime": "20220825", "description": "PCRATE(Y,N)  求N周期内Y值的趋势\\r\\n\\r\\n算法：根据N周期内Y值，通过最小二乘法线性拟合为一阶多项式，即y1=aN+b\\r\\n对函数求一阶导数（斜率），即PCRATE(Y,N)=dy1/dN\\r\\n通常和函数PCRATETREND(Y,N) 一起连用判断\\r\\n\\r\\n当PCRATE(Y,N)>0时，Y为上涨趋势；\\r\\n当PCRATE(Y,N)<0时，Y为下跌趋势；\\r\\n当PCRATE(Y,N)=0时，无趋势。\\r\\n\\r\\n注：\\r\\n1、N包含当前K线。\\r\\n2、N为有效值，但当前的K线数不足N根，该函数返回空值。\\r\\n3、N<2，或者N为空值，该函数返回空值。\\r\\n4、N支持为变量。\\r\\n5、Y存在空值时，跳过空值。\\r\\n6、该函数计算量较大，会导致计算时间变长。\\r\\n\\r\\n例：\\r\\nPCRATE(CLOSE,20);//20周期内收盘价的趋势变化\\r\\nPCRATETREND(CLOSE,20);//20周期内收盘价趋势变化的速度", "explanation": "求N周期内Y值的趋势", "markettype": 0, "modifytime": "20220829", "param": "", "tip": "求N周期内Y值的趋势", "type": 3}, "PCRATETREND": {"body": "PCRATETREND(,)", "createtime": "20220825", "description": "PCRATETREND(Y,N)  求N周期内Y值的趋势变化速度\\r\\n\\r\\n算法:\\r\\n根据N周期内Y值，通过最小二乘法线性拟合为二阶多项式，即y1=aN^2+bN+c\\r\\n对函数求二阶导数，即PCRATETREND(Y,N)=d^2(y1)/dN^2\\r\\n通常和函数PCRATE(Y,N)一起连用判断\\r\\n\\r\\n当PCRATETREND(Y,N)>0时，价格曲线为凹型，趋势加速；\\r\\n当PCRATETREND(Y,N)<0时，价格曲线为凸型，趋势减速。\\r\\n\\r\\n注：\\r\\n1、N包含当前K线。\\r\\n2、N为有效值，但当前的K线数不足N根，该函数返回空值。\\r\\n3、N<3，或者N为空值，该函数返回空值。\\r\\n4、N支持为变量。\\r\\n5、Y存在空值时，跳过空值。\\r\\n6、该函数计算量较大，会导致计算时间变长。\\r\\n\\r\\n例：\\r\\nPCRATE(CLOSE,20);//20周期内收盘价的趋势变化\\r\\nPCRATETREND(CLOSE,20);//20周期内收盘价趋势变化的速度", "explanation": "求N周期内Y值的趋势变化速度", "markettype": 0, "modifytime": "20220829", "param": "", "tip": "求N周期内Y值的趋势变化速度", "type": 3}, "PERCENTILE": {"body": "PERCENTILE", "createtime": "20180302", "description": "PERCENTILE 百分位函数。\\r\\n\\r\\n用法：\\r\\nPERCENTILE(Data,N,Per) 百分位函数\\r\\n取最近N个周期Data数据处于Per百分位的数值。\\r\\nData为需要排序的数据，N为需要排序的周期数，Per是百分位数值。\\r\\n\\r\\n注：\\r\\n1、参数Data可以为变量。\\r\\n2、参数N可以为变量。\\r\\n3、百分位数值的有效范围为[0,100],不能整除时向上取整。\\r\\n\\r\\n\\r\\n例：\\r\\nHH:PERCENTILE(HIGH,50,95);//取最近50根k线最高价处于95%位置的数值\\r\\nLL:PERCENTILE(LOW,50,5);//取最近50根k线最低价处于5%位置的数值", "explanation": "百分位函数", "markettype": 0, "modifytime": "", "param": "", "tip": "PERCENTILE(Data,N,Per)百分位函数取最近N个周期Data数据处于Per百分位的数值。Data为需要排序的数据，N为需要排序的周期数，Per是百分位数值。", "type": 2}, "PERIOD": {"body": "PERIOD", "createtime": "", "description": "PERIOD，返回当前技术分析图表的周期。\\r\\n\\r\\n注：\\r\\n1：该函数支持自定义周期。\\r\\n2：返回数字为1—11分别表示，1分钟，3分钟，5分钟，10分钟，15分钟，30分钟，1小时，1天，1周，1月，1年。\\r\\n3：返回数字为12—27分别表示,TICK、量能、5秒、10秒、15秒、30秒、2小时、3小时、4小时、1季、自定义秒、自定义分、自定义小时、自定义日、自定义月、自定义年\\r\\n例:\\r\\nN:=BARSLAST(DATE<>REF(DATE,1))+1;\\r\\nOO:VALUEWHEN(N=1,O);\\r\\nIFELSE(PERIOD=1,OO,NULL);//取当天一分钟周期的开盘价。", "explanation": "自动读取当前技术分析图表周期", "markettype": 0, "modifytime": "", "param": "", "tip": "PERIOD,自动读取当前技术图表周期", "type": 7}, "PLAYSOUND": {"body": "PLAYSOUND( , )", "createtime": "", "description": "PLAYSOUND 条件满足时，播放指定声音。\\r\\n\\r\\n用法：\\r\\nPLAYSOUND(COND, 'N') 当条件满足时，播放声音'N'\\r\\n\\r\\n注：\\r\\n1、点击设置声音按钮，在弹出来的界面中设置声音，声音用字符'A'~'J'表示。\\r\\n2、自定义声音可以在设置菜单的设置声音文件中设置\\r\\n3、条件一直满足，则只播放一次，不重复播放。\\r\\n4、不支持将该函数直接定义为变量，即不支持下面的写法：\\r\\nA:PLAYSOUND(COND, 'N');\\r\\n5、当前k线满足时才会播放声音，历史满足不会播放\\r\\n\\r\\n例：\\r\\nPLAYSOUND(CLOSE>OPEN,'A');表示CLOSE>OPEN时播放自定义声音'A'。", "explanation": "声音函数", "markettype": 0, "modifytime": "", "param": "", "tip": "PLAYSOUND(COND,N),条件COND满足时播放指定声音N为自定义声音代码(可在设置声音文件中设置)", "type": 8}, "POINTDOT": {"body": "POINTDOT", "createtime": "", "description": "画点线。\\r\\n用法：\\r\\nPOINTDOT 画点线。\\r\\n注：\\r\\n不支持将该函数直接定义为变量，即不支持下面的写法：A:POINTDOT;\\r\\n例：MA5:MA(C,5),POINTDOT;用点线画5日均线。", "explanation": "画点线", "markettype": 0, "modifytime": "", "param": "", "tip": "", "type": 8}, "POLYLINE": {"body": "POLYLINE( , , )", "createtime": "20140603", "description": "POLYLINE函数 画折线。\\r\\n\\r\\n用法：\\r\\nPOLYLINE(COND,DATA,COLOR); \\r\\n条件COND满足时，用颜色COLOR的折线连接DATA的值。\\r\\n\\r\\n注：\\r\\n1、该函数是将满足条件的DATA以折线形式连接起来，连线连续\\r\\n2、该函数支持在函数后设置线型（LINETHICK1 - LINETHICK7、POINTDOT、DOT），即支持下面的写法：POLYLINE(COND,DATA,COLOR),LINETHICK; \\r\\n3、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:POLYLINE(COND,DATA,COLOR);\\r\\n\\r\\n例1：\\r\\nPOLYLINE(CLOSE>=HHV(CLOSE,10),CLOSE,COLORRED);//表示在收盘价创10天新高点之间画折线。折线显示为红色。\\r\\n\\r\\n例2：\\r\\nPOLYLINE(CLOSE<=LLV(CLOSE,10),CLOSE,COLORBLUE),LINETHICK7;//表示在收盘价创10天新低点之间画折线。折线显示为蓝色,线型粗细为7。", "explanation": "画折线", "markettype": 0, "modifytime": "", "param": "COND为条件,DATE为数值，COLOR为颜色", "tip": "POLYLINE(COND,DATA,COLOR)，条件满足时，用颜色COLOR的直线连接DATA的值", "type": 8}, "POLYLINE1": {"body": "POLYLINE1( , )", "createtime": "20140603", "description": "POLYLINE1 画折线。\\r\\n\\r\\n用法：\\r\\nPOLYLINE1(COND,DATA); \\r\\n条件COND满足时，用折线连接DATA的值。\\r\\n\\r\\n注：\\r\\n1、该函数是将满足条件的DATA以折线形式连接起来，连线连续\\r\\n2、该函数支持在函数后设置颜色、线型（LINETHICK1 - LINETHICK7、POINTDOT、DOT），即支持下面的两种写法：\\r\\nPOLYLINE1(COND,DATA),LINETHICK,COLOR;\\r\\nPOLYLINE1(COND,DATA),COLOR,LINETHICK;\\r\\n\\r\\n3、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:POLYLINE1(COND,DATA);\\r\\n\\r\\n例1：\\r\\nPOLYLINE1(CLOSE>=HHV(CLOSE,10),CLOSE),COLORRED;//表示在收盘价创10天新高点之间画折线。折线显示为红色。\\r\\n\\r\\n例2：\\r\\nPOLYLINE1(CLOSE<=LLV(CLOSE,10),CLOSE),COLORBLUE,LINETHICK7;//表示在收盘价创10天新低点之间画折线。折线显示为蓝色,线型粗细为7。", "explanation": "画折线", "markettype": 0, "modifytime": "", "param": "COND为条件,DATE为数值", "tip": "POLYLINE1(COND,DATA)，条件满足时，用折线连接DATA的值", "type": 8}, "POW": {"body": "POW( , )", "createtime": "20140127", "description": "POW(X,Y)：求X的Y次幂。\\r\\n\\r\\n注：\\r\\n1、当X为负数时，Y必须为整数，因为底数为负时，不能进行开方运算，返回值为空值。\\r\\n2、X,Y可以为数值，也可以为变量。\\r\\n\\r\\n例1：\\r\\nPOW(CLOSE,2);//求得收盘价的2次方。\\r\\n例2：\\r\\nPOW(10,2);//返回值为100\\r\\n例3：\\r\\nPOW(1/2,-2);//返回值为4\\r\\n例4：\\r\\nPOW(100,O-C);//返回100的O-C次方", "explanation": "幂", "markettype": 0, "modifytime": "", "param": "", "tip": "POW(X,Y),求X的Y次幂", "type": 4}, "PRECIS": {"body": "PRECIS", "createtime": "20140418", "description": "指定数值的输出精度（小数位数）。\\r\\n用法：\\r\\nPRECISX，X为0至6，表示小数位数从0到6。\\r\\n注：\\r\\n不支持将该函数直接定义为变量，即不支持下面的写法：A:PRECIS0;\\r\\n\\r\\n例1： \\r\\nMA(C,5),PRECIS4;//计算五周期均线，数值精度为4位小数。", "explanation": "指定数值的输出精度（小数位数）", "markettype": 0, "modifytime": "", "param": "", "tip": "", "type": 8}, "PRECIS0": "PRECIS0 数值精度为0（不显示小数）", "PRECIS1": "PRECIS1 数值精度为1（显示1位小数）", "PRECIS2": "PRECIS2 数值精度为2（显示2位小数）", "PRECIS3": "PRECIS3 数值精度为3（显示3位小数）", "PRECIS4": "PRECIS4 数值精度为4（显示4位小数）", "PRECIS5": "PRECIS5 数值精度为5（显示5位小数）", "PRECIS6": "PRECIS6 数值精度为6（显示6位小数）", "PRECISION": {"body": "PRECISION", "createtime": "20160927", "description": "PRECISION(N) 设置小数位数，N为位数范\\r\\n\\r\\n注：\\r\\n1、当N设置为具体数值是，N范围可以为0-6\\r\\n\\r\\n例1：\\r\\nC,PRECISION(3); //设置小数点位数为3位，即返回收盘价显示三位小数\\r\\n\\r\\n例2：\\r\\nC,PRECISION(PRICEPRECISION); //返回收盘价，设置小数点位数为当前合约的小数位数", "explanation": "设置小数位数", "markettype": 1, "modifytime": "", "param": "", "tip": "PRECISION()，设置小数位数(范围0-6)", "type": 8}, "PRICEPRECISION": {"body": "PRICEPRECISION", "createtime": "20160927", "description": "PRICEPRECISION 取当前合约小数点位数\\r\\n\\r\\n用法：\\r\\n返回当前合约设置的小数点位数\\r\\n\\r\\n例：\\r\\nC,PRECISION(PRICEPRECISION); //返回收盘价，设置小数点位数为当前合约的小数位数", "explanation": "取当前合约小数点位数", "markettype": 1, "modifytime": "", "param": "", "tip": "PRICEPRECISION，取当前合约小数位数", "type": 8}, "PRICEPRECISION1": {"body": "PRICEPRECISION1", "createtime": "20160927", "description": "PRICEPRECISION1('CODE') 取指定合约设置的小数点位数\\r\\n\\r\\n注：\\r\\n1、CODE可以为文华码或合约名\\r\\n2、CODE可缺省，支持如下写法：AA:PRICEPRECISION1(''); 取当前合约设置的小数点位数\\r\\n\\r\\n例：\\r\\nPRICEPRECISION1('8608');//返回文华码8608合约设置的小数点位数\\r\\nC,PRECISION(PRICEPRECISION1('8608')); //返回收盘价，设置小数点位数为指定文华码8608的合约的小数位数", "explanation": "取指定合约设置的小数点位数", "markettype": 1, "modifytime": "", "param": "", "tip": "PRICEPRECISION1()，取某合约小数位数", "type": 8}, "PROFIT": {"body": "PROFIT", "createtime": "20140718", "description": "PROFIT 理论逐笔浮盈\\r\\n\\r\\n用法：PROFIT返回当前理论逐笔浮动盈亏，用于交易策略风险控制。\\r\\n\\r\\n计算方法：\\r\\n多头：PROFIT=（最新价-开仓价格）*手数*交易单位\\r\\n空头：PROFIT=（开仓价格-最新价）*手数*交易单位\\r\\n\\r\\n注：\\r\\n1、模组初始化多头持仓后，PROFIT返回值为（最新价-开仓价格）*手数*交易单位，空头返回值为（开仓价格-最新价）*手数*交易单位。\\r\\n2、开仓信号当根K线，信号确认后，多头持仓PROFIT返回（最新价-开仓价格）*手数*交易单位，空头持仓PROFIT返回（开仓价格-最新价）*手数*交易单位。\\r\\n3、开仓后平仓前多头持仓PROFIT返回值为（最新价-开仓价格）*手数*交易单位，空头持仓返回值为（开仓价格-最新价）*手数*交易单位。\\r\\n4、持仓为0，PROFIT返回值为0。\\r\\n5、模组开仓价格的算法：\\r\\n  a.信号执行方式为‘K线走完确认信号下单’开仓价格为开仓信号当根K线的收盘价\\r\\n  b.信号执行方式为‘XXX下单，K线走完复核’开仓价格为开仓信号指令价\\r\\n  c.信号执行方式为‘XXX下单，不进行信号复核’开仓价格为开仓信号指令价\\r\\n6、加减仓模型加仓后，开仓价格按照‘5’中不同信号执行方式取值后再进行平均。\\r\\n7、加减仓模型减仓按先开先平原则，平最先开的仓位，PROFIT计算公式中，用现有持仓的开仓价格计算，手数减少。\\r\\n\\r\\n例：\\r\\nPROFIT<-2000,SP;//亏损2000元止损", "explanation": "理论逐笔浮盈", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "PROFIT理论逐笔浮盈", "type": 12}, "QUARTER": {"body": "QUARTER", "createtime": "20150303", "description": "QUARTER,返回某周期的季度数。\\r\\n\\r\\n注：\\r\\n当前周期的月份为1~3月返回1，4~6月返回2，7~9月返回3，10~12月返回4。\\r\\n\\r\\n例1：\\r\\nCROSS(C,MA(C,5))&&QUARTER<3,BK;//在一二季度，最新价上穿5周期均线做多\\r\\nCROSS(MA(C,5),C)&&QUARTER<3,SP;//在一二季度，最新价下穿5周期均线平多\\r\\nTRADE_OTHER('AUTO');//交易合约为主力合约\\r\\nAUTOFILTER;", "explanation": "取得某周期的季度数", "markettype": 0, "modifytime": "20211230", "param": "", "tip": "QUARTER取得某周期的季度数", "type": 7}, "QUARTERTRADE": {"body": "QUARTERTRADE", "createtime": "20141225", "description": "QUARTERTRADE 季内交易函数。\\r\\n\\r\\n用法：\\r\\nQUARTERTRADE 模型中写入该函数，信号和资金每季度重新初始化进行计算，与历史割裂。\\r\\n\\r\\n注：\\r\\n1、该函数不支持自定义N日、季、年周期，其他周期均支持。\\r\\n2、回测报告中的出金/入金，为每季度出金/入金的和。\\r\\n3、模型中不能同时使用DAYTRADE1\\DAYTRADE\\WEEKTRADE\\WEEKTRADE1\\MONTHTRADE\\QUARTERTRADE\\YEARTRADE函数。\\r\\n4、（1）历史回测中，当季K线走完持仓大于0，会对持仓进行全清处理。\\r\\n   （2）模组运行中，当季K线走完持仓大于0，信号和资金会重新初始化进行计算，但不会自动对持仓进行全清处理，需要在模型中编写实现全清。\\r\\n\\r\\n例：\\r\\nMA5^^MA(C,5);\\r\\nMA10^^MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;//5周期均线上穿10周期均线，买开仓\\r\\nCROSSDOWN(MA5,MA10),SK;//5周期均线下穿10周期均线，卖开仓\\r\\nC<BKPRICE-10*MINPRICE,SP;//亏损10点平多\\r\\nC>SKPRICE+10*MINPRICE,BP;//亏损10点平空\\r\\nCLOSEMINUTE<=1,CLOSEOUT;//收盘前一分钟，清仓。\\r\\nAUTOFILTER;//过滤模型\\r\\nQUARTERTRADE;//使用每季度数据计算", "explanation": "季内交易函数", "markettype": 1, "modifytime": "", "param": "", "tip": "QUARTERTRADE,季内交易函数", "type": 9}, "QUARTERTRADE1": {"body": "QUARTERTRADE1", "createtime": "20180117", "description": "QUARTERTRADE1 季内交易函数。\\r\\n\\r\\n用法：\\r\\nQUARTERTRADE1 模型中写入该函数，信号和资金每季重新初始化进行计算，与历史割裂，并且每一个函数只使用当季K线数据进行计算，历史数据不参与计算。\\r\\n\\r\\n注：\\r\\n1、该函数不支持自定义N日、季、年周期，其他周期均支持。\\r\\n2、回测报告中的出金/入金，为每季度出金/入金的和。\\r\\n3、模型中不能同时使用DAYTRADE1\\DAYTRADE\\WEEKTRADE\\WEEKTRADE1\\MONTHTRADE\\MONTHTRADE1\\QUARTERTRADE\\QUARTERTRADE1\\YEARTRADE\\YEARTRADE1函数。\\r\\n4、（1）历史回测中，当季K线走完持仓大于0，会对持仓进行全清处理。\\r\\n   （2）模组运行中，当季K线走完持仓大于0，信号和资金会重新初始化进行计算，但不会自动对持仓进行全清处理，需要在模型中编写实现全清。\\r\\n\\r\\n例：\\r\\nMA5^^MA(C,5);\\r\\nMA10^^MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;//5周期均线上穿10周期均线，买开仓\\r\\nCROSSDOWN(MA5,MA10),SK;//5周期均线下穿10周期均线，卖开仓\\r\\nC<BKPRICE-10*MINPRICE,SP;//亏损10点平多\\r\\nC>SKPRICE+10*MINPRICE,BP;//亏损10点平空\\r\\nCLOSEMINUTE<=1,CLOSEOUT;//收盘前一分钟，清仓。\\r\\nAUTOFILTER;//过滤模型\\r\\nQUARTERTRADE1;//使用每季度数据计算", "explanation": "季内交易函数", "markettype": 1, "modifytime": "", "param": "", "tip": "QUARTERTRADE1季内交易函数，且历史数据不参与计算。", "type": 9}, "RAND": {"body": "RAND", "createtime": "20181030", "description": "RAND(X,Y) 产生随机数的随机函数,返回范围在X到Y之间的随机数。\\r\\n\\r\\n注：\\r\\n1、X、Y参数均支持设置为变量。\\r\\n2、该函数仅支持返回整数\\r\\n3、当X>Y时，函数返回空值。\\r\\n4、当X与Y的范围小于1时，函数返回无效值。\\r\\n\\r\\n例1：\\r\\nRAND(1,60);//返回1到60之间的随机数值\\r\\n例2：\\r\\nRAND(C,O);//返回收盘价到开盘价之间的随机数值", "explanation": "产生随机数的随机函数", "markettype": 0, "modifytime": "", "param": "", "tip": "RAND(X,Y)产生随机数的随机函数,返回范围在X到Y之间的随机数。", "type": 4}, "RANGE": {"body": "RANGE( , , )", "createtime": "", "description": "RANGE(X,Y,Z)：介于某个范围之内。表示X大于Y同时小于Z时返回1，否则返回0\\r\\n例1：\\r\\nRANGE(5,4,6);//返回值为1；\\r\\n例2：\\r\\nRANGE(8,3,6);//返回值为0；\\r\\n例3：\\r\\nMA5:MA(C,5);\\r\\nMA10:MA(C,10);\\r\\nMA20:MA(C,20);\\r\\nRANGE(MA10,MA20,MA5),BK;//10周期均线在5周期均线与20周期均线之间买开仓\\r\\n\\r\\n//RANGE(MA10,MA20,MA5)=1,BK; 与 RANGE(MA10,MA20,MA5),BK; 表达同等意义", "explanation": "范围", "markettype": 0, "modifytime": "", "param": "", "tip": "RANGE(A,B,C),判断是否A大于B同时小于C，如果是则返回1，否则返回0", "type": 4}, "RATIO_ACCOUNT": "RATIO_ACCOUNT 账户资金使用率", "RATIO_CODE": "RATIO_CODE 单品种资金使用率", "RAWDATA": {"body": "RAWDATA(  )", "createtime": "********", "description": "RAWDATA 取原始数据的高开低收\\r\\n注：\\r\\n1、该函数与STOCKDIVD或TRADE_SMOOTHING连用\\r\\n2、如未使用STOCKDIVD或TRADE_SMOOTHING，该函数返回无效值\\r\\n\\r\\n用法：\\r\\nRAWDATA('O');//取原始数据的开盘价\\r\\nSTOCKDIVD(1);//设置股票向后复权\\r\\n\\r\\n//其中'OPEN'可以替换为以下\\r\\n'HIGH':最高\\r\\n'LOW':最低\\r\\n'CLOSE'收盘价\\r\\n均可为简写", "explanation": "取原始数据的值", "markettype": 0, "modifytime": "********", "param": "", "tip": "RAWDATA()，取原始数据的高开低收", "type": 1}, "REF": {"body": "REF( , )", "createtime": "", "description": "引用X在N个周期前的值。\\r\\n\\r\\n注：\\r\\n1、当N为有效值，但当前的k线数不足N根，返回空值；\\r\\n2、N为0时返回当前X值；\\r\\n3、N为空值时返回空值。\\r\\n4、N可以为变量\\r\\n\\r\\n例1:\\r\\n REF(CLOSE,5);表示引用当前周期前第5个周期的收盘价\\r\\n例2：\\r\\nAA:IFELSE(BARSBK>=1,REF(C,BARSBK),C);//取最近一次买开仓信号K线的收盘价\\r\\n//1）发出BK信号的当根k线BARSBK返回空值,则发出BK信号的当根k线REF(C,BARSBK)返回\\r\\n空值；\\r\\n//2）发出BK信号的当根k线BARSBK返回空值,不满足BARSBK>=1,则当根k线的收盘价。\\r\\n//3）发出BK信号之后的k线BARSBK返回买开仓的K线距离当前K线的周期数，REF(C,BARSBK) \\r\\n返回开仓k线的收盘价。\\r\\n//4）例：1、2、3 三根k线，1 K线为开仓信号的当根k线，则返回当根k线的收盘价，2、3 \\r\\nK线返回 1 K线的收盘价。", "explanation": "向前引用", "markettype": 0, "modifytime": "", "param": "", "tip": "REF(X,N),取X在N个周期前的值", "type": 1}, "REFLINE": {"body": "REFLINE", "createtime": "", "description": "设定指标参考线。\\r\\n\\r\\n用法：\\r\\nREFLINE:A,B,C...;\\r\\n在A,B,C的位置画出指标参考线。\\r\\n\\r\\n注：\\r\\n1、A、B、C等均为常数。\\r\\n2、最多支持16个参数,如果超过16个参数，自动截取前16个参数。\\r\\n3、该函数连写，则只会显示最后一次设定的指标线。\\r\\n4、不支持将函数定义为变量，即不支持下面的写法：A:REFLINE;\\r\\n5、该指标参考线完全根据设置的坐标显示，不随其他指标值的最值调整坐标范围\\r\\n\\r\\n例1：\\r\\nREFLINE:-100,0,100;//在-100,0,100的位置画出指标参考线。", "explanation": "设定指标参考线", "markettype": 0, "modifytime": "", "param": "", "tip": "REFLINE设定指标参考线", "type": 8}, "REFLINE1": {"body": "REFLINE1", "createtime": "20160203", "description": "设定指标参考线。\\r\\n\\r\\n用法：\\r\\nREFLINE1:A,B,C...;\\r\\n在A,B,C的位置画出指标参考线。\\r\\n\\r\\n注：\\r\\n1、A、B、C等均为常数。\\r\\n2、最多支持16个参数,如果超过16个参数，自动截取前16个参数。\\r\\n3、该函数连写，则只会显示最后一次设定的指标线。\\r\\n4、不支持将函数定义为变量，即不支持下面的写法：A:REFLINE1;\\r\\n5、该函数编写指标参考线会与指标中最值取交集调整坐标范围显示\\r\\n\\r\\n例1：\\r\\nREFLINE1:-100,0,100;//在-100,0,100的位置画出指标参考线。", "explanation": "设定指标参考线", "markettype": 0, "modifytime": "", "param": "", "tip": "REFLINE1设定指标参考线", "type": 8}, "REFSIG_PLACE": {"body": "REFSIG_PLACE( , )", "createtime": "20140916", "description": "REFSIG_PLACE(Sig,N) 判断从当根K线开始倒数第N个固定的Sig信号所在K线的位置。\\r\\n\\r\\n用法：REFSIG_PLACE(Sig,N) 判断从当根K线开始倒数第N个固定的Sig信号所在K线的位置。如果没有Sig信号，或者没有固定的Sig信号，则该函数返回空值。\\r\\n\\r\\n注：\\r\\n1、Sig位置支持的信号有：BK,SK,BP,SP,BPK,SPK,CLOSEOUT,STOP\\r\\n2、K线位置是指当前K线到指定信号所在K线的根数。\\r\\n3、如果倒数第N个固定的Sig信号在当根K线上，那么该函数返回0。\\r\\n4、N为0或空值时，该函数返回空值。\\r\\n5、参数N支持变量。\\r\\n\\r\\n例：\\r\\nREFSIG_PLACE(BK,3)=5&&BKVOL>0,SP;//如果从当根K线开始倒数第3个固定的BK信号所在的距离当前K线有5根K线，并且多头持仓大于0，卖平仓", "explanation": "判断指定信号的K线位置", "markettype": 1, "modifytime": "", "param": "", "tip": "REFSIG_PLACE(Sig,N)判断从当根K线开始倒数第N个固定的Sig信号所在K线的位置", "type": 10}, "REFSIG_PRICE": {"body": "REFSIG_PRICE( , )", "createtime": "20140909", "description": "REFSIG_PRICE(Sig,N) 返回从当根K线开始倒数第N个固定的Sig信号的信号价位。\\r\\n\\r\\n用法：REFSIG_PRICE(Sig,N) 判断从当根K线开始倒数第N个固定的Sig信号的信号价位。如果没有Sig信号，或者没有固定的Sig信号，则该函数返回空值。\\r\\n\\r\\n注：\\r\\n1、Sig位置支持的信号有：BK,SK,BP,SP,BPK,SPK,CLOSEOUT,STOP\\r\\n2、如果当根K线上有固定的Sig信号，那么该函数计算信号时，包括当根K线的信号。\\r\\n3、N为0或空值时，该函数返回空值。\\r\\n4、参数N支持变量。\\r\\n\\r\\n例：\\r\\nREFSIG_PRICE(BK,3)=3000&&BKVOL>0,SP;//如果从当根K线开始倒数第3个固定的BK信号的开仓价位为3000，并且多头持仓大于0，卖平仓", "explanation": "判断指定信号的信号价位", "markettype": 1, "modifytime": "", "param": "", "tip": "REFSIG_PRICE(Sig,N)返回从当根K线开始倒数第N个固定的Sig信号的信号价位", "type": 10}, "REFSIG_PRICE1": {"body": "REFSIG_PRICE1( , )", "createtime": "20140924", "description": "REFSIG_PRICE1(Sig,N) 返回从当根K线开始倒数第N个固定的Sig信号的委托价格。\\r\\n\\r\\n用法：REFSIG_PRICE1(Sig,N) 判断从当根K线开始倒数第N个固定的Sig信号的委托价格。如果没有Sig信号，或者没有固定的Sig信号，则该函数返回空值。\\r\\n\\r\\n注：\\r\\n1、\\r\\n盘中运行：\\r\\nREFSIG_PRICE1(Sig,N)返回从当根K线开始倒数第N个固定的Sig信号的委托价格。\\r\\n回测：\\r\\n1)模型中含有信号控制函数：REFSIG_PRICE1(Sig,N)返回从当根K线开始倒数第N个固定的Sig信号发出时的行情最新价。\\r\\n2)模型中不含有信号控制函数：REFSIG_PRICE1(Sig,N)返回从当根K线开始倒数第N个固定的Sig信号当根K线的收盘价。\\r\\n3)设置指定价格下单时，委托价为指定的价格。\\r\\n2、Sig位置支持的信号有：BK,SK,BP,SP,BPK,SPK,CLOSEOUT,STOP\\r\\n3、如果当根K线上有固定的Sig信号，那么该函数计算信号时，包括当根K线的信号。\\r\\n4、N为0或空值时，该函数返回空值。\\r\\n5、参数N支持变量。\\r\\n\\r\\n例：\\r\\nREFSIG_PRICE1(BK,3)=3000&&BKVOL>0,SP;//如果从当根K线开始倒数第3个固定的BK信号的委托价格为3000，并且多头持仓大于0，卖平仓", "explanation": "判断指定信号的委托价格", "markettype": 1, "modifytime": "", "param": "", "tip": "REFSIG_PRICE1(<PERSON><PERSON>,<PERSON>)返回从当根K线开始倒数第N个固定的Sig信号的委托价格", "type": 10}, "REFSIG_PRICE2": {"body": "REFSIG_PRICE2( , )", "createtime": "20140924", "description": "REFSIG_PRICE2(Sig,N) 返回从当根K线开始倒数第N个固定的Sig信号的成交价格。\\r\\n\\r\\n用法：REFSIG_PRICE2(Sig,N) 判断从当根K线开始倒数第N个固定的Sig信号的成交价格。如果没有Sig信号，或者没有固定的Sig信号，则该函数返回空值。\\r\\n\\r\\n注：\\r\\n1、\\r\\n盘中运行：\\r\\nREFSIG_PRICE2(Sig,N)返回从当根K线开始倒数第N个固定的Sig信号的成交价格。\\r\\n回测：\\r\\n1)模型中含有信号控制函数：REFSIG_PRICE2(Sig,N)返回从当根K线开始倒数第N个固定的Sig信号发出时的行情最新价。\\r\\n2)模型中不含有信号控制函数：REFSIG_PRICE2(Sig,N)返回从当根K线开始倒数第N个固定的Sig信号当根K线的收盘价。\\r\\n3)设置指定价格下单时，成交价为指定的价格。\\r\\n2、Sig位置支持的信号有：BK,SK,BP,SP,BPK,SPK,CLOSEOUT,STOP\\r\\n3、如果当根K线上有固定的Sig信号，那么该函数计算信号时，包括当根K线的信号。\\r\\n4、如果信号发出了委托，但还未成交时，该函数返回空值。\\r\\n5、如果信号发出了委托，部分成交时，该函数返回部分成交价。\\r\\n6、如果反手信号SPK、BPK发出了委托，在开仓委托全部成交后，则该函数返回开仓成交均价。\\r\\n7、N为0或空值时，该函数返回空值。\\r\\n8、参数N支持变量。\\r\\n\\r\\n例：\\r\\nREFSIG_PRICE2(BK,3)=3000&&BKVOL>0,SP;//如果从当根K线开始倒数第3个固定的BK信号的成交价位为3000，并且多头持仓大于0，卖平仓", "explanation": "判断指定信号的成交价格", "markettype": 1, "modifytime": "", "param": "", "tip": "REFSIG_PRICE2(<PERSON><PERSON>,<PERSON>)返回从当根K线开始倒数第N个固定的Sig信号的成交价格", "type": 10}, "REFSIG_VOL": {"body": "REFSIG_VOL( , )", "createtime": "********", "description": "REFSIG_VOL(Sig,N) 返回从当根K线开始倒数第N个固定的Sig信号的信号手数(反手指令取开仓手数)。\\r\\n\\r\\n用法：REFSIG_VOL(Sig,N) 判断从当根K线开始倒数第N个固定的Sig信号的手数。如果没有Sig信号，或者没有固定的Sig信号，则该函数返回0。\\r\\n\\r\\n注：\\r\\n1、Sig位置支持的信号有：BK,SK,BP,SP,BPK,SPK,CLOSEOUT,STOP\\r\\n2、如果倒数第N个固定的Sig信号在当根K线上，那么该函数返回当前信号手数。\\r\\n4、N为0或空值时，该函数返回0。\\r\\n5、参数N支持变量。\\r\\n\\r\\n例：\\r\\nREFSIG_PLACE(BK,3)=5&&REFSIG_VOL(BK,3)>2,SP(BKVOL);//如果从当根K线开始倒数第3个固定的BK信号所在的距离当前K线有5根K线，并且信号手数大于2，平掉所有持仓", "explanation": "判断指定信号的手数", "markettype": 1, "modifytime": "", "param": "", "tip": "REFSIG_VOL(Sig,N)返回从当根K线开始倒数第N个固定的Sig信号的信号手数(反手指令取开仓手数)", "type": 10}, "REFWH": {"body": "REFWH( , )", "createtime": "", "description": "引用N周期前的数据。\\r\\n\\r\\n用法：\\r\\nREFWH(X,N)引用X在N个周期前的值。\\r\\n1、当N为有效值，但当前的k线数不足N根，按照实际的根数计算；\\r\\n2、N为0时返回当前X值；\\r\\n3、N为空值时返回空值。\\r\\n4、N可以为变量\\r\\n注：\\r\\n算法跟REF一样，区别在于：在不足N根的时候，按照实际的根数计算；", "explanation": "向前引用", "markettype": 0, "modifytime": "", "param": "", "tip": "REFWH(X,N),取X在N个周期前的值", "type": 1}, "REVERSE": {"body": "REVERSE( )", "createtime": "", "description": "REVERSE(X)：取相反值，返回－X。\\r\\n\\r\\n例1：\\r\\nREVERSE(LOW);//返回-LOW。\\r\\n例2：\\r\\nREVERSE(-55);//返回值为55\\r\\n例3：\\r\\nREVERSE(0);//返回值为0", "explanation": "取相反值", "markettype": 0, "modifytime": "", "param": "", "tip": "REVERSE(X)，取－X", "type": 4}, "ROUND": {"body": "ROUND( , )", "createtime": "20141226", "description": "ROUND(N,M) 对数字N进行位数为M的四舍五入。\\r\\n\\r\\n注：\\r\\n1、N支持写为变量和参数；M不支持写为变量，可以写为参数。\\r\\n2、M>0，则对数字N小数点后M位小数进行四舍五入。\\r\\n3、M=0，则将数字N四舍五入为整数。\\r\\n4、M<0，则在数字N小数点左侧前M位进行四舍五入。\\r\\n\\r\\n例1：\\r\\nROUND(125.345,2);//返回125.35。\\r\\n例2：\\r\\nROUND(125.345,0);//返回125。\\r\\n例3：\\r\\nROUND(125.345,-1);//返回130", "explanation": "指定位数四舍五入", "markettype": 0, "modifytime": "", "param": "", "tip": "ROUND(N,M),对N指定M位小数进行四舍五入", "type": 4}, "SAR": {"body": "SAR( , , )", "createtime": "", "description": "SAR(N,STEP,MAX) 返回抛物转向值。\\r\\n\\r\\n根据公式SAR(n)=SAR(n-1)+AF*(EP(n-1)-SAR(n-1))计算 \\r\\n\\r\\n其中： \\r\\nSAR(n-1)：上根K线SAR的绝对值 \\r\\nAF：加速因子，当AF小于MAX时，逐根的通过AF+STEP累加，涨跌发生转换时，AF重新计算 \\r\\nEP：一个涨跌内的极值，在上涨行情中为上根K线的最高价；下跌行情中为上根K线的最低价 \\r\\n\\r\\n注：\\r\\n1、参数N,Step,Max均不支持变量\\r\\n\\r\\n例1：\\r\\nSAR(17,0.03,0.3);//表示计算17个周期抛物转向，步长为3%，极限值为30%\\r\\n\\r\\n例2：\\r\\nSTEP1:=2/100;\\r\\nMVALUE1:=20/100;\\r\\nSARLINE:SAR(4,STEP1,MVALUE1),NODRAW;\\r\\nIF(SARLINE>0,SARLINE,NULL),CIRCLEDOT,COLORRED,NOTEXT;//SARLINE>0画红色小圆点线\\r\\nIF(SARLINE>0,NULL,ABS(SARLINE)),CIRCLEDOT,COLORCYAN,NOTEXT;//SARLINE不大于0画绿色小圆点线", "explanation": "抛物转向", "markettype": 0, "modifytime": "", "param": "", "tip": "SAR(N,Step,Max)，取抛物转向值N为周期数，Step为步长，Max为极值", "type": 2}, "SAR1": {"body": "SAR1( , , )", "createtime": "20160608", "description": "SAR1(N,STEP,MAX) 返回抛物转向值。\\r\\n\\r\\n根据公式SAR1(n)=SAR1(n-1)+AF*(EP(n-1)-SAR1(n-1))计算 \\r\\n\\r\\n其中： \\r\\nSAR1(n-1)：上根K线SAR1的绝对值 \\r\\n\\r\\nAF：加速因子，当AF小于MAX时，\\r\\n上涨行情，H>HV(H,N)   AF = AF+STEP; H<=HV(H,N) AF = AF;\\r\\n下跌行情，L<lV(L,N)   AF = AF+STEP; L>=LV(L,N) AF = AF;\\r\\n涨跌发生转换时，AF重新计算 \\r\\nEP：一个涨跌内的极值，在上涨行情中为前N根K线的最高价；下跌行情中为前N根K线的最低价 \\r\\n\\r\\n注：\\r\\n1、参数N,Step,Max均不支持变量\\r\\n\\r\\n例1：\\r\\nSAR1(17,0.03,0.3);//表示计算17个周期抛物转向，步长为3%，极限值为30%\\r\\n\\r\\n例2：\\r\\nSTEP1:=2/100;\\r\\nMVALUE1:=2/10;\\r\\nSARLINE:SAR1(4,STEP1,MVALUE1),NODRAW;\\r\\nIF(SARLINE>0,SARLINE,NULL),CIRCLEDOT,COLORRED,NOTEXT;//SARLINE>0画红色小圆点线\\r\\nIF(SARLINE>0,NULL,ABS(SARLINE)),CIRCLEDOT,COLORCYAN,NOTEXT;//SARLINE不大于0画绿色小圆点线", "explanation": "抛物转向", "markettype": 0, "modifytime": "", "param": "", "tip": "SAR1(N,Step,Max)，取抛物转向值N为周期数，Step为步长，Max为极值", "type": 2}, "SCALE": {"body": "SCALE", "createtime": "20140506", "description": "SCALE 取得K线图主动买占总成交量的比例。\\r\\n\\r\\n注：\\r\\n指数没有主动买和主动卖的概念，所以该函数在指数合约日线周期的比例是根据该指数的所有合约计算的；并且指数合约日线以下周期不支持该函数。\\r\\n例1：\\r\\nAA:=SCALE*VOL;//主动买\\r\\nBB:=(1-SCALE)*VOL;//主动卖", "explanation": "取得K线图主动买占总成交量的比例", "markettype": 0, "modifytime": "", "param": "", "tip": "SCALE返回主动买占总成交量的比例", "type": 1}, "SEC": "SEC 秒钟", "SECONDMAX1_POS": "SECONDMAX1_POS 次大值位置（不包括自身周期）", "SECONDMAX1_VALUE": "SECONDMAX1_VALUE 次大值（不包括自身周期）", "SECONDMAX_POS": "SECONDMAX_POS 次大值位置", "SECONDMAX_VALUE": "SECONDMAX_VALUE 次大值", "SECONDMIN1_POS": "SECONDMIN1_POS 次小值位置（不包括自身周期）", "SECONDMIN1_VALUE": "SECONDMIN1_VALUE 次小值（不包括自身周期）", "SECONDMIN_POS": "SECONDMIN_POS 次小值位置", "SECONDMIN_VALUE": "SECONDMIN_VALUE 次小值", "SEEK": {"body": "Seek", "createtime": "20180130", "description": "SEEK 标签统计函数\\r\\n此函数为系统封装函数。\\r\\n\\r\\n用法：\\r\\nSEEK(Cond) 标签统计函数\\r\\nCond为需要满足的条件\\r\\n\\r\\n注：\\r\\n1、图表中加载了含有SEEK函数的模型，可以图表右键选择SEEK统计查看结果\\r\\n2、一个模型中只能含有一个SEEK函数。\\r\\n3、一个图表中只能加载一个含有SEEK函数的模型。\\r\\n\\r\\n例：\\r\\nSEEK(C>O);", "explanation": "标签统计函数", "markettype": 1, "modifytime": "", "param": "", "tip": "SEEK(Cond)标签统计函数Cond为需要满足的条件", "type": 2}, "SELECT": {"body": "SELECT", "createtime": "20160927", "description": "SELECT 公式选股\\r\\n\\r\\n用法：\\r\\nCOND,SELECT;\\r\\n用SELECT进行公式选股，选出最后一根K线满足条件的合约。COND为选股条件\\r\\nSELECT函数支持公式选股、选股回测\\r\\n\\r\\n如有多个选股条件，例如：\\r\\nCOND1,SELECT;\\r\\nCOND2,SELECT;\\r\\n则选出最后一根K线满足COND1条件或COND2条件的合约\\r\\n\\r\\n注：\\r\\n1、SELECT函数不能与未来函数一起使用。\\r\\n2、SELECT函数不能与跨合约、跨周期函数一起使用。\\r\\n\\r\\n例：\\r\\nFINANCE_DATA('每股收益')>0,SELECT;//选出每股收益大于0的股票", "explanation": "公式选股", "markettype": 3, "modifytime": "20221206", "param": "", "tip": "SELECT公式选股", "type": 16}, "SELL": "SELL 卖出", "SETDEALPERCENT": {"body": "SETDEALPERCENT", "createtime": "20140718", "description": "SETDEALPERCENT  按理论资金比例下单\\r\\n\\r\\n用法：\\r\\nSETDEALPERCENT(fPercent,N)：每次按当前理论资金的fPercent比例下单，且最大为N手。\\r\\n\\r\\n注：\\r\\n1、fPercent比例可下单手数=（前期可用资金+平仓释放的保证金+平仓盈亏）*fPercent/（最新价*保证金比例*交易单位）\\r\\n2、fPercent取值范围1-100的整数，并且支持变量，如果fPercent为小数，向下取整，取整数部分参与计算\\r\\n3、当N<=fPercent比例可下单手数时，取N作为下单手数\\r\\n；参数N写为0或者缺省，表示不设置最大下单手数\\r\\n4、SETDEALPERCENT计算出的下单手数非整数时，遵循自动向下取整的规则；如果手数小于1，不进行开仓操作\\r\\n5、SETDEALPERCENT只作用于开仓指令，不作用于平仓指令。含有该函数的过滤模型，平仓信号默认全平\\r\\n6、加减仓模型中如果写了该函数，则开仓按照该函数设置的资金比例下单，默认下单手数及信号后写入的手数均无效；平仓信号按照信号后写入的手数进行下单\\r\\n7、模组中用单元参数中设置的初始资金、保证金比例计算下单手数\\r\\n8、历史回测用回测参数中设置的初始资金、保证金比例计算下单手数\\r\\n\\r\\n例：\\r\\nSETDEALPERCENT(20,10); //每次按理论资金比例的20%下单，并设置最大下单手数阈值为10手", "explanation": "按理论资金比例下单", "markettype": 1, "modifytime": "20230216", "param": "", "tip": "SETDEALPERCENT(fPercent,N)，每次按当前理论资金的fPercent比例下单，且最大为N手。\r", "type": 12}, "SETEXPIREDATE": {"body": "SETEXPIREDATE()", "createtime": "********", "description": "SETEXPIREDATE 设置加密模型的使用有效期的到期时间。\\r\\n\\r\\n用法：SETEXPIREDATE('yyyymmdd'); 设置加密模型的使用有效期的到期时间为yyyymmdd。\\r\\n\\r\\n注：\\r\\n1、写入该函数的模型加密输出后，只有在写入的日期前是有效的。\\r\\n2、参数位置写入的日期格式必须为八位数，即必须为yyyymmdd的形式。\\r\\n3、如果不写入该函数，默认没有到期日期，一直有效。\\r\\n4、该函数只对输出的可执行副本起作用。\\r\\n\\r\\n例：\\r\\nC>REF(H,1),BK;//价格大于上一根k线最高价，开多仓\\r\\nC<BKPRICE-3*MINPRICE,SP;//亏损3点止损\\r\\nMULTSIG(0,0,3,0);//设置信号复核确认方式为出信号立即下单，不复核。一根K线上最大信号个数为3\\r\\nSETEXPIREDATE('20141001');//该加密模型的使用有效期的到期时间为2014年10月1日\\r\\nAUTOFILTER;", "explanation": "设置加密模型的使用有效期的到期时间", "markettype": 1, "modifytime": "", "param": "", "tip": "SETEXPIREDATE('yyyymmdd')设置加密模型的使用有效期的到期时间", "type": 14}, "SETMOVEOPIPRICE": {"body": "SETMOVEOPIPRICE()", "createtime": "20140730", "description": "SETMOVEOPIPRICE(PRICE),设置模组换月移仓的委托方式。\\r\\n\\r\\n用法：SETMOVEOPIPRICE(PRICE)，模组中如果设置为自动换月移仓，则主力合约切换时以PRICE的委托方式进行移仓。\\r\\n\\r\\n注：\\r\\n1、PRICE位置可以填写以下五种委托方式：\\r\\n（1）NEW_ORDER 最新价\\r\\n（2）PASSIVE_ORDER 排队价\\r\\n（3）ACTIVE_ORDER 对价\\r\\n（4）CMPETITV_ORDER 超价\\r\\n超价参数在下单主界面-参数设置-超价参数中设置\\r\\n（5）LIMIT_ORDER 市价\\r\\n2、模型中未使用该函数设置换月移仓委托价格方式，换月移仓平旧的主力合约，开新主力合约均按照市价委托。\\r\\n3、模型中使用该函数设置换月移仓委托价格方式，换月移仓平旧的主力合约按市价委托，SETMOVEOPIPRICE(PRICE) 只对移仓开仓委托有效。\\r\\n4、该函数只在模组中生效。\\r\\n\\r\\n例：\\r\\nC>HV(H,20),BPK;//价格大于前20周期高点平空反手做多\\r\\nC<LV(L,20),SPK;//价格小于前20周期低点是平多反手做空\\r\\nTRADE_OTHER('AUTO');//加载到主连合约上，主力换月时自动进行移仓\\r\\nSETMOVEOPIPRICE(ACTIVE_ORDER);//主力合约切换时，以对价方式进行移仓\\r\\nAUTOFILTER;", "explanation": "设置模组换月移仓的委托方式", "markettype": 1, "modifytime": "********", "param": "", "tip": "SETMOVEOPIPRICE(PRICE),设置模组换月移仓的委托方式，PRICE为价格方式", "type": 11}, "SETQUOTACCOUNT": {"body": "SETQUOTACCOUNT()", "createtime": "********", "description": "SETQUOTACCOUNT 设置模型加密输出使用者的文华行情账号。\\r\\n\\r\\n用法：SETQUOTACCOUNT('ACCOUNT1'); 设置该模型加密输出给文华行情账号为ACCOUNT1的使用者。\\r\\n\\r\\n注：\\r\\n1、写入该函数的模型加密输出后，需要登录该函数指定的行情账号才可以使用。\\r\\n2、如果不写入该函数，则任何账号登录后都可以使用加密输出的模型。\\r\\n3、该函数支持加密输出给对多个行情账号使用，即支持SETQUOTACCOUNT('ACCOUNT1', 'ACCOUNT2');这样的写法，最大支持50个参数\\r\\n4、模型中支持同时写入SETTRADEACCOUNT和SETQUOTACCOUNT函数，即支持同时设置授权的行情账号和资金账号。\\r\\n5、该函数只对输出的可执行副本起作用。\\r\\n\\r\\n例：\\r\\nC>REF(H,1),BK;//价格大于上一根k线最高价，开多仓\\r\\nC<BKPRICE-3*MINPRICE,SP;//亏损3点止损\\r\\nMULTSIG(0,0,3,0);//设置信号复核确认方式为出信号立即下单，不复核。一根K线上最大信号个数为3\\r\\nSETQUOTACCOUNT('ACCOUNT1');//将该模型加密输出给文华行情账号为ACCOUNT1的使用者。\\r\\nAUTOFILTER;", "explanation": "设置模型加密输出使用者的文华行情账号", "markettype": 1, "modifytime": "", "param": "", "tip": "SETQUOTACCOUNT('ACCOUNT1')设置模型加密输出使用者的文华行情账号", "type": 14}, "SETSIGPRICE": {"body": "SETSIGPRICE( , )", "createtime": "********", "description": "SETSIGPRICE(SIG,PRICE),指定信号的下单价格\\r\\n\\r\\n用法：\\r\\nSETSIGPRICE(SIG,PRICE),设置SIG指令的下单委托价格，PRICE为委托价格。\\r\\n\\r\\n注：\\r\\n1、SIG位置为交易指令，包括BK\\SK\\BP\\SP\\BPK\\SPK 六种指令。\\r\\n2、PRICE位置为下单价格，包括以下五种：\\r\\n（1）CUR_CLOSE 当根收盘价\\r\\n（2）NEXT_OPEN 下根开盘价\\r\\n（3）MAX_CLOSE_NEXT_OPEN 收盘价和开盘价二者较大值\\r\\n（4）MIN_CLOSE_NEXT_OPEN 收盘价和开盘价二者较小值\\r\\n（5）指定价 可以为具体的数值，也可以为表达式，即支持如下的写法：\\r\\nA:HHV(H,3);//定义A为3个周期内的最高价\\r\\nSETSIGPRICE(BK,A);//BK信号按照3个周期的最高价委托\\r\\n3、该函数支持回测。\\r\\n4、该函数用于指令价模型，委托价格只能设置为指定价。\\r\\n5、模型中未设置指令委托价格和委托方式时，按照下单主界面-参数设置-程序化参数中的设置进行委托。\\r\\n\\r\\n例：\\r\\nC>HV(H,20),BK;//价格大于前20周期高点买开仓\\r\\nC<LV(L,20),SK;//价格小于前20周期低点卖开仓\\r\\nC>HV(H,10),BP;//价格大于前10周期高点平空仓\\r\\nC<LV(L,10),SP;//价格小于前10周期低点平多仓\\r\\nSETSIGPRICE(BK,CUR_CLOSE);//买开的委托以当根收盘价委托\\r\\nSETSIGPRICE(SK,NEXT_OPEN);//卖开的委托以下根开盘价委托\\r\\nSETSIGPRICE(BP,MAX_CLOSE_NEXT_OPEN);//买平的委托以收盘价和开盘价二者较大值委托\\r\\nSETSIGPRICE(SP,MIN_CLOSE_NEXT_OPEN);//卖平的委托以收盘价和开盘价二者较小值委托\\r\\nAUTOFILTER;\\r\\n\\r\\n", "explanation": "指定信号的下单价格", "markettype": 1, "modifytime": "20220621", "param": "", "tip": "SETSIGPRICE(SIG,PRICE),设置SIG指令的下单价格，PRICE为下单价格。\r\n", "type": 11}, "SETSIGPRICETYPE": {"body": "SETSIGPRICETYPE( , , )", "createtime": "20141022", "description": "SETSIGPRICETYPE(SIG,PRICE,IsCancel),设置信号的委托价格方式\\r\\n\\r\\n用法：SETSIGPRICETYPE(SIG,PRICE,IsCancel),设置SIG指令的委托价格方式，PRICE为委托价格方式，IsCancel为是否启用终止下单。\\r\\n\\r\\n注：\\r\\n1、SIG位置为交易指令，包括BK\\SK\\BP\\SP\\BPK\\SPK六种指令。\\r\\n2、PRICE位置为委托价格方式，包括以下六种：\\r\\n（1）NEW_ORDER 最新价\\r\\n（2）PASSIVE_ORDER 排队价\\r\\n（3）ACTIVE_ORDER 对价\\r\\n（4）CMPETITV_ORDER 超价\\r\\n超价参数在下单主界面-参数设置-超价参数中设置\\r\\n（5）LIMIT_ORDER 市价\\r\\n（6）SIGPRICE_ORDER 触发价\\r\\n3、IsCance位置写入参数CANCEL_ORDER表示启用终止下单，不写则表示不启用。\\r\\n启用终止下单时，根据PRICE设置价格委托后，N秒不成交对该笔委托自动撤单并终止下单\\r\\n启用终止下单时，PRICE位置委托价格必须编写设置，不能缺省\\r\\n例：SETSIGPRICETYPE(BK,NEW_ORDER,CANCEL_ORDER); //BK指令以最新价委托，设置N秒不成交终止下单\\r\\n注：\\r\\n（1）参数N秒，在下单主界面-参数设置-程序化参数中进行设置。\\r\\n（2）终止下单不考虑小节休息。\\r\\n（3）BP、SP、BPK、SPK信号不支持CANCEL_ORDER设置。\\r\\n4、在进行历史回测时\\r\\n A：收盘价模型回测，信号价格为信号所在K线的收盘价。\\r\\n B：指令价模型回测，信号价格为出现信号时的最新价。\\r\\n5、模型中未设置指令委托价格和委托方式时，按照下单主界面-参数设置-程序化参数中的设置进行委托。\\r\\n6、因信号消失产生的指令用市价委托。\\r\\n\\r\\n例：\\r\\nC>HV(H,20),BK;//价格大于前20周期高点买开仓\\r\\nC<LV(L,20),SK;//价格小于前20周期低点卖开仓\\r\\nC>HV(H,10),BP;//价格大于前10周期高点平空仓\\r\\nC<LV(L,10),SP;//价格小于前10周期低点平多仓\\r\\nSETSIGPRICETYPE(BK,SIGPRICE_ORDER,CANCEL_ORDER);//买开的委托以触发价委托，启用中止下单\\r\\nSETSIGPRICETYPE(SK,PASSIVE_ORDER);//卖开的委托以排队价委托\\r\\nSETSIGPRICETYPE(BP,ACTIVE_ORDER);//买平的委托以对价委托\\r\\nSETSIGPRICETYPE(SP,CMPETITV_ORDER);//卖平的委托以超价委托\\r\\nAUTOFILTER;", "explanation": "设置信号的委托价格方式", "markettype": 1, "modifytime": "20220621", "param": "", "tip": "SETSIGPRICETYPE(SIG,PRICE,IsCancel),设置SIG指令的委托价格方式，PRICE为委托价格方式，IsCancel为是否启用终止下单。", "type": 11}, "SETSTYLECOLOR": {"body": "SETSTYLECOLOR( , )", "createtime": "20140307", "description": "SETSTYLECOLOR函数 设置线型的粗细和颜色。\\r\\n\\r\\n用法：\\r\\nSETSTYLECOLOR(STYLE,COLOR);\\r\\n\\r\\n注：\\r\\n1、参数STYLE可以使用LINETHICK1——LINETHICK7,数值越大，线型越粗;\\r\\n2、参数STYLE可以使用DOT,设置线形为虚线。\\r\\n3、参数STYLE可以使用NODRAW，不画线只显示数值\\r\\n4、参数COLOR可以使用插入颜色中的所有颜色和颜色函数。(如:COLORRED或RGB(255,255,0));\\r\\n5、不支持将该函数直接定义为变量，即不支持下面的写法：A:SETSTYLECOLOR(LINETHICK5,COLORGREEN);\\r\\n\\r\\n例1：\\r\\n A:C,SETSTYLECOLOR(LINETHICK5,COLORGREEN);//以绿色LINETHICK5的粗细大小画收盘价连线。", "explanation": "线型的粗细和颜色控制", "markettype": 0, "modifytime": "", "param": "", "tip": "SETSTYLECOLOR(LINETHICK,COLOR);设置线型的粗细和颜色LINETHICK表示线形的粗细，可以使用LINETHICK1——LINETHICK7；COLOR为颜色", "type": 8}, "SETTLE": {"body": "SETTLE", "createtime": "", "description": "SETTLE 取得K线图的结算价或者取得当日成交均价\\r\\n\\r\\n注：\\r\\n1、日线周期，盘中返回的是全天成交均价；收盘后返回交易所公布的结算价。\\r\\n2、分钟周期，返回的是截止到当前k线的全天成交均价。\\r\\n\\r\\n例1：\\r\\nSS:SETTLE;//定义SS为结算价\\r\\n例2：\\r\\nCROSS(C,SETTLE);//收盘价上穿结算价", "explanation": "取得K线图的结算价或者取得当日成交均价", "markettype": 0, "modifytime": "", "param": "", "tip": "SETTLE求到某根k线的结算价", "type": 1}, "SETTRADEACCOUNT": {"body": "SETTRADEACCOUNT()", "createtime": "********", "description": "SETTRADEACCOUNT 设置模型加密输出使用者交易的资金账号。\\r\\n\\r\\n用法：SETTRADEACCOUNT('ACCOUNT1'); 设置该模型加密输出给交易资金账号为ACCOUNT1的使用者。\\r\\n\\r\\n注：\\r\\n1、写入该函数的模型加密输出后，需要登录该函数指定的资金账号才可以使用。\\r\\n2、如果不写入该函数，则任何账号登录后都可以使用加密输出的模型。\\r\\n3、该函数支持加密输出给对多个资金账号使用，即支持SETTRADEACCOUNT('ACCOUNT1', 'ACCOUNT2');这样的写法，最大支持50个参数\\r\\n4、模型中支持同时写入SETTRADEACCOUNT和SETQUOTACCOUNT函数，即支持同时设置授权的行情账号和资金账号。\\r\\n5、该函数只对输出的可执行副本起作用。\\r\\n\\r\\n例：\\r\\nC>REF(H,1),BK;//价格大于上一根k线最高价，开多仓\\r\\nC<BKPRICE-3*MINPRICE,SP;//亏损3点止损\\r\\nMULTSIG(0,0,3,0);//设置信号复核确认方式为出信号立即下单，不复核。一根K线上最大信号个数为3\\r\\nSETTRADEACCOUNT('ACCOUNT1');//将该模型加密输出给交易资金账号为ACCOUNT1的使用者。\\r\\nAUTOFILTER;", "explanation": "设置模型加密输出使用者交易的资金账号", "markettype": 1, "modifytime": "********", "param": "", "tip": "SETTRADEACCOUNT('ACCOUNT1')设置模型加密输出使用者交易的资金账号", "type": 14}, "SGN": {"body": "SGN( )", "createtime": "", "description": "SGN(X)：取符号。若X>0返回1,若X<0返回-1,否则返回0。\\r\\n\\r\\n例1：\\r\\nSGN(5);//返回值为1\\r\\n例2：\\r\\nSGN(-5);//返回值为-1\\r\\n例3：\\r\\nSGN(0);//返回值为0", "explanation": "取符号", "markettype": 0, "modifytime": "", "param": "", "tip": "SGN(X)，判断X正负数（若X>0返回1,若X<0返回-1,否则返回0）", "type": 4}, "SIGNUM": {"body": "SIGNUM", "createtime": "********", "description": "SIGNUM，返回值为当前信号为一次交易过程中的第几个信号\\r\\n\\r\\n注：\\r\\n1、一次交易过程指的是首次开仓到持仓为0的交易过程\\r\\n2、如果信号为反手信号，则该函数返回值为1\\r\\n\\r\\n例1：\\r\\nC>O,BK(1);\\r\\nSIGNUM>3&&C<O,SP(BKVOL);//如果本次交易前面已经出现大于3个信号，K线为阴线，全部平仓\\r\\nSIGNUM<=3&&C<O,SP(BKVOL/2);//如果本次交易前面已经出现小于等于3个信号，K线为阴线，平仓一半\\r\\nTRADE_AGAIN(4);", "explanation": "定位一次交易过程中的信号位置", "markettype": 1, "modifytime": "", "param": "", "tip": "SIGNUM，返回值为当前信号为一次交易过程中的第几个信号", "type": 10}, "SIGPRICE_ORDER": "SIGPRICE_ORDER 触发价", "SIGVOL": {"body": "SIGVOL()", "createtime": "********", "description": "SIGVOL(N),返回一次交易中第N个信号的下单手数。\\r\\n\\r\\n注：\\r\\n1、一次交易过程指的是首次开仓到持仓为0的交易过程\\r\\n2、在一次交易中，当前位置信号不足N个时，该函数返回无效值\\r\\n\\r\\n例1：\\r\\nTR:=MAX(MAX((HIGH-LOW),ABS(REF(CLOSE,1)-HIGH)),ABS(REF(CLOSE,1)-LOW));//真实波幅\\r\\nATR..MA(TR,26); //求26个周期内真实波幅的简单移动平均\\r\\nTC..INTPART((MONEYTOT*0.01/(UNIT*ATR)));//根据权益的1%计算下单手数\\r\\nHH:=HHV(H,20);\\r\\nLL:=LLV(L,20);\\r\\nMID:=(HH +LL)/2;\\r\\nCROSSUP(C,HH),BK(TC);\\r\\nSIGVOL(1)<12&&CROSSDOWN(C,MID),SP(BKVOL);//如果首次开仓手数小于12手，下穿中轨，全部平仓\\r\\nSIGVOL(1)>12&&CROSSDOWN(C,MID),SP(BKVOL/2);//如果首次开仓手数大于12手，下穿中轨，平仓一半\\r\\nCROSSDOWN(C,LL),SP(BKVOL);\\r\\nTRADE_AGAIN(10);", "explanation": "一次交易中指定信号的下单手数", "markettype": 1, "modifytime": "", "param": "", "tip": "SIGVOL(N),返回一次交易中第N个信号的下单手数", "type": 10}, "SIN": {"body": "SIN( )", "createtime": "", "description": "SIN(X)：求X的正弦值。\\r\\n\\r\\n注：\\r\\n1、X的取值为R（实数集）；\\r\\n2、值域为(-1，1)。\\r\\n\\r\\n例1：\\r\\nSIN(-1.57);//返回-1.57的正弦值\\r\\n例2：\\r\\nSIN(1.57);//返回1.57的正弦值", "explanation": "求正弦", "markettype": 0, "modifytime": "", "param": "", "tip": "SIN(X)，求X的正弦值", "type": 4}, "SK": "SK 卖开仓", "SKEWNESS": {"body": "SKEWNESS( , )", "createtime": "20150525", "description": "SKEWNESS(X,N) 求X在N个周期内的偏度系数。\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、N为有效值，但当前的k线数不足N根，该函数返回空值。\\r\\n3、N为0时，该函数返回空值。\\r\\n4、N为空值，该函数返回空值。\\r\\n5、N可以为变量。\\r\\n6、N至少为3，少于3返回空值。\\r\\n\\r\\n算法举例：计算SKEWNESS(C,3);在最近一根K线上的值。\\r\\n用麦语言函数可以表示如下：\\r\\n((POW(C-MA(C,3),3)+POW(REF(C,1)-MA(C,3),3)+POW(REF(C,2)-MA(C,3),3)) /POW(STD(C,3),3))*3/((3-1)*(3-2));\\r\\n\\r\\n例：\\r\\nSKEWNESS(C,10);\\r\\n//表示收盘价的10周期偏度。偏度反映分布的不对称度。不对称度反映以平均值为中心的分布的不对称程度。正不对称度表示不对称部分的分布更趋向正值。负不对称度表示不对称部分的分布更趋向负值。", "explanation": "偏度系数", "markettype": 0, "modifytime": "", "param": "", "tip": "SKEWNESS(X,N)求X在N个周期内的偏度系数", "type": 3}, "SKHIGH": {"body": "SKHIGH", "createtime": "20140718", "description": "返回数据合约卖开仓以来的最高价\\r\\n用法：\\r\\nSKHIGH返回数据合约最近一次模型卖开位置到当前的最高价。\\r\\n1、不同信号执行方式，其返回值分别为：\\r\\n（1）K线走完确认信号下单\\r\\n   a.历史信号计算中，SK(SPK)信号之后的K线返回委托以来的数据合约行情的最高价\\r\\n   b.加载运行过程中，SK(SPK)信号当根K线返回上个SK(SPK)信号发出以来的数据合约行情最高价，SK之后的K线返回委托以来的数据合约行情最高价\\r\\n（2）信号执行方式选择K线走完复核（例如：在模型中写入CHECKSIG(SK,'A',0,'D',0,0);）\\r\\n从SK(SPK)信号发出时开始统计数据合约行情的最高价；信号消失，返回上次卖开以来的数据合约行情的最高价，信号确认存在，返回当根K线记录的数据合约行情的最高价\\r\\n注：SK信号发出后，中间出了信号消失，从最后一次信号出现开始统计数据合约最高价\\r\\n（3）信号执行方式选择不进行信号复核（例如：在模型中写入MULTSIG或MULTSIG_MIN;）\\r\\nSK(SPK)信号的当根K线返回的从信号发出到K线走完时数据合约行情的最高价；SK(SPK)信号之后的K线返回信号发出以来数据合约行情的最高价。\\r\\n2、主连合约使用换月移仓函数，主力合约切换后，从新的主力合约第一根K线开盘价重新开始统计\\r\\n3、模组重新初始化后，数据合约和交易合约相同，则SKHIGH返回初始化后的最高价与初始化弹出框中填入的持仓价相比较后较大的数值；数据合约与交易合约不同时，则SKHIGH返回初始化后的最高价与初始化弹出框中填入的数据合约信号价相比较后较大的数值。\\r\\n\\r\\n例：\\r\\nC<O,SK;\\r\\nC<SKHIGH-5*MINPRICE,BP;\\r\\nAUTOFILTER;//最新价低于卖开仓以来数据合约的最高价5个点，平仓。", "explanation": "返回数据合约卖开仓以来的最高价", "markettype": 1, "modifytime": "20220525", "param": "", "tip": "SKHIGH,返回数据合约卖开仓以来的最高价", "type": 10}, "SKLOW": {"body": "SKLOW", "createtime": "20140718", "description": "返回数据合约卖开仓以来的最低价\\r\\n用法：\\r\\nSKLOW返回数据合约最近一次模型卖开位置到当前的最低价。\\r\\n1、不同信号执行方式，其返回值分别为：\\r\\n（1）K线走完确认信号下单\\r\\n   a.历史信号计算中，SK(SPK)信号之后的K线返回委托以来的数据合约行情的最低价\\r\\n   b.加载运行过程中，SK(SPK)信号当根K线返回上个SK(SPK)信号发出以来的数据合约行情最低价，SK之后的K线返回委托以来的数据合约行情最低价\\r\\n（2）信号执行方式选择K线走完复核（例如：在模型中写入CHECKSIG(SK,'A',0,'D',0,0);），从SK(SPK)信号发出时行情时开始统计数据合约行情的最低价；信号消失，返回上次卖开以来的数据合约行情的最低价，信号确认存在，返回当根K线记录的数据合约行情的最低价\\r\\n注：SK信号发出后，中间出了信号消失，从最后一次信号出现开始统计数据合约最低价\\r\\n（3）信号执行方式选择不进行信号复核（例如：在模型中写入MULTSIG或MULTSIG_MIN;）\\r\\nSK(SPK)信号的当根K线返回的从信号发出到K线走完时数据合约行情的最低价；SK(SPK)信号之后的K线返回信号发出以来数据合约行情的最低价\\r\\n2、主连合约使用换月移仓函数，主力合约切换后，从新的主力合约第一根K线开盘价重新开始统计\\r\\n3、模组重新初始化后，数据合约和交易合约相同，则SKLOW返回初始化后的最低价与初始化弹出框中填入的持仓价相比较后较小的数值；数据合约与交易合约不同时，则SKLOW返回初始化后的最低价与初始化弹出框中填入的数据合约信号价相比较后较小的数值。\\r\\n\\r\\n例：\\r\\nC<O,SK;\\r\\nC<SKPRICE&&C>SKLOW+5*MINPRICE,BP;\\r\\nAUTOFILTER;//最新价高于卖开仓以来数据合约的最低价5个点，止盈平仓。", "explanation": "返回数据合约卖开仓以来的最低价", "markettype": 1, "modifytime": "20220525", "param": "", "tip": "SKLOW,返回数据合约卖开仓以来的最低价", "type": 10}, "SKPRICE": {"body": "SKPRICE", "createtime": "20140718", "description": "SKPRICE 返回数据合约最近一次卖开信号价位。\\r\\n\\r\\n用法：\\r\\nSKPRICE 返回数据合约最近一次卖开信号发出时的行情的最新价。\\r\\n\\r\\n注：\\r\\n1、当数据合约和交易合约相同时SKPRICE值和SKPRICE1值相等。\\r\\n2、当模型存在连续多个开仓信号(加仓)的情况下，该函数返回的是最近一次开仓信号的价格,而不是开仓均价。\\r\\n3、不同信号执行方式，其返回值分别为：\\r\\n（1）信号执行方式为不进行信号复核\\r\\n    a.历史回测：SKPRICE返回信号发出时的数据合约行情最新价\\r\\n    b.模组运行：SKPRICE返回信号发出时的数据合约行情最新价\\r\\n（2）信号执行方式选择K线走完确认信号下单\\r\\n    a.历史回测：SKPRICE返回信号发出时数据合约当根K线的收盘价\\r\\n    b.模组运行：SKPRICE返回信号发出时数据合约当根K线的收盘价\\r\\n（3）信号执行方式设置为K线走完进行信号复核\\r\\n    a.历史回测：SKPRICE返回信号发出时数据合约当根K线的收盘价\\r\\n    b.模组运行：复核前，返回上一次SK信号当根K线数据合约的行情最新价；复核后，返回本次SK信号当根K线数据合约的行情最新价\\r\\n4、当模组自动初始化时，SKPRICE返回的为上一次卖开信号时数据合约行情的最新价。\\r\\n5、模组重新初始化后，数据合约和交易合约相同，则SKPRICE返回为初始化弹出框中填入的持仓价格；数据合约与交易合约不同时，则SKPRICE返回初始化弹出框中填入的数据合约信号价。\\r\\n6、加载在主连合约上，使用了换月移仓函数，主力换月后SKPRCIE取值为新的主力合约的第一根K线的开盘价\\r\\n\\r\\n例:\\r\\nCLOSE-SKPRICE>60 && SKPRICE>0 && SKVOL>0, BP;//如果卖开价位比当前价位低出60,且空头持仓存在，买平仓。", "explanation": "返回数据合约最近一次卖开信号价位", "markettype": 1, "modifytime": "", "param": "", "tip": "SKPRICE，返回数据合约最近一次卖开信号价位", "type": 10}, "SKPRICE1": {"body": "SKPRICE1", "createtime": "", "description": "SKPRICE1 返回交易合约最近一次卖开信号价位。\\r\\n\\r\\n用法：\\r\\nSKPRICE1：返回交易合约最近一次卖开信号发出时的行情的最新价。\\r\\n\\r\\n注：\\r\\n1、当数据合约和交易合约相同时SKPRICE值和SKPRICE1值相等。\\r\\n2、当数据合约和交易合约不同时，不同信号执行方式，其返回值分别为：\\r\\n（1）信号执行方式为不进行信号复核\\r\\n   a.历史回测：SKPRICE1返回信号发出时的交易合约行情最新价\\r\\n   b.模组运行：SKPRICE1返回信号发出时的交易合约行情最新价\\r\\n（2）信号执行方式选择K线走完确认信号下单\\r\\n    a.历史回测：SKPRICE1返回信号发出时交易合约当根K线的收盘价\\r\\n    b.模组运行：SKPRICE1返回信号发出时交易合约当根K线的收盘价\\r\\n（3）信号执行方式设置为K线走完进行信号复核\\r\\n    a.历史回测：SKPRICE1返回信号发出时交易合约当根K线的收盘价\\r\\n    b.模组运行：复核前，返回上一次SK信号当根K线交易合约的行情最新价；复核后，返回本次SK信号当根K线交易合约的行情最新价\\r\\n3、当模组自动初始化时，SKPRICE1取最近的SK信号发出时的交易合约行情的最新价；手动初始化时，SKPRICE1取初始化弹出框中填入的持仓价格。\\r\\n4、加载在加权/主连合约上，使用了换月移仓函数，主力换月后SKPRCIE1取值为新的主力合约的第一根K线的开盘价", "explanation": "返回交易合约最近一次卖开信号价位", "markettype": 1, "modifytime": "", "param": "", "tip": "SKPRICE1，返回交易合约最近一次卖开信号价位", "type": 10}, "SKPRICEAV": {"body": "SKPRICEAV", "createtime": "20151214", "description": "SKPRICEAV 返回数据合约空头开仓均价。\\r\\n\\r\\n用法：\\r\\nSKPRICEAV 返回返回数据合约空头开仓均价。\\r\\n\\r\\n注：\\r\\n1、过滤模型：\\r\\n（1）开仓信号后，未出平仓信号时：SKPRICEAV取值和SKPRICE取值相同。\\r\\n（2）平仓信号后：SKPRICEAV返回值为0。\\r\\n2、加减仓模型：\\r\\n（1）持仓不为0时：SKPRICEAV返回数据合约理论持仓的开仓均价。\\r\\n（2）加减仓模型持仓为0时：SKPRICEAV返回值为0。\\r\\n3、该函数在模组运行和回测中都读取的是模组理论持仓的开仓均价，非实际持仓开仓均价。\\r\\n4、模组重新初始化后，数据合约和交易合约相同，则SKPRICEAV计算取初始化弹出框中填入的持仓价格；数据合约与交易合约不同时，则SKPRICEAV计算取初始化弹出框中填入的数据合约信号价。\\r\\n\\r\\n注：\\r\\n该函数的计算考虑滑点。\\r\\n\\r\\n例：\\r\\nSKPRICEAV-CLOSE>60,BP(SKVOL);//当前价位比空头开仓均价低出60,平掉所有空头持仓", "explanation": "返回数据合约空头开仓均价", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "SKPRICEAV返回数据合约空头开仓均价", "type": 12}, "SKPRICEAV1": {"body": "SKPRICEAV1", "createtime": "20160419", "description": "SKPRICEAV1 返回交易合约空头开仓均价\\r\\n\\r\\n用法：\\r\\nSKPRICEAV1 返回返回交易合约空头开仓均价。\\r\\n\\r\\n注：\\r\\n1、当模型存在连续多个开仓信号(加仓)的情况下，该函数返回的是交易合约开仓均价。\\r\\n2、当数据合约和交易合约相同时SKPRICEAV值和SKPRICEAV1值相等。\\r\\n3、过滤模型：\\r\\n（1）开仓信号后，未出平仓信号时：SKPRICEAV1取值和SKPRICE1取值相同。\\r\\n（2）平仓信号后：SKPRICEAV1返回值为0。\\r\\n4、加减仓模型：\\r\\n（1）持仓不为0时：SKPRICEAV1返回交易合约理论持仓的开仓均价。\\r\\n（2）加减仓模型持仓为0时：SKPRICEAV1返回值为0。\\r\\n\\r\\n注：\\r\\n该函数的计算考虑滑点。\\r\\n\\r\\n例：\\r\\nSKPRICEAV1-CLOSE>60,BP(SKVOL);//当前价位比交易合约空头开仓均价低出60,平掉所有空头持仓", "explanation": "返回交易合约空头开仓均价", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "SKPRICEAV1交易合约空头开仓均价", "type": 12}, "SKVOL": {"body": "SKVOL", "createtime": "20140918", "description": "卖开信号手数\\r\\n用法：\\r\\nSKVOL返回模型当前的空头理论持仓。\\r\\n1、加载运行：\\r\\n（1）模组初始化后，SKVOL仍然返回根据信号下单手数计算的理论持仓，不受账户持仓的影响。\\r\\n（2）模组运行中手动调仓，头寸同步修改持仓，SKVOL返回值不变，仍然返回根据信号下单手数计算的理论持仓。\\r\\n（3）页面盒子运行中，SKVOL不受资金情况的限制，按照信号显示开仓手数。\\r\\n2、回测、模组运行中：\\r\\n（1）如果资金不够开仓，开仓手数为0，SKVOL返回值为0。\\r\\n（2）SK（SPK）信号出现并且确认固定后，SKVOL的取值增加开仓手数的数值；BP（BPK）信号出现并且确认固定后，SKVOL的取值减少平仓手数的数值。\\r\\n\\r\\n例：\\r\\nSKVOL=0&&C<O,SK(1);//空头理论持仓为0并且收盘价小于开盘价时，卖开一手\\r\\nSKVOL>=1&&L<LV(L,5),SK(2); //空头持仓大于等于1，并且当根K线的最低价小于前面5个周期中最低价中最小值时，加仓2手\\r\\nSKVOL>0&&H>REF(H,5),BP(SKVOL); //空头持仓大于0，并且当根K线的最高价大于5个周期前K线的最高价时，买平所有空头持仓", "explanation": "卖开信号手数", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "SKVOL返回模型当前的空头理论持仓", "type": 12}, "SKVOL2": {"body": "SKVOL2", "createtime": "20150513", "description": "卖开信号手数\\r\\n用法：\\r\\nSKVOL2返回模型当前的空头持仓。\\r\\n1、加载运行：\\r\\n（1）模组初始化后，SKVOL2返回的理论持仓仍然延续，返回模型信号手数，不受账户持仓的影响。\\r\\n（2）页面盒子和模组加载中，SKVOL2不受资金情况的限制，按照信号显示开仓手数。\\r\\n（3）模组运行过程中SK（SPK）信号出现并且确认固定后，SKVOL2的取值增加开仓手数的数值；BP（BPK）信号出现并且确认固定后，SKVOL2的取值减少平仓手数的数值。\\r\\n2、回测：\\r\\n（1）SKVOL2不受资金情况的限制，按照信号显示开仓手数。\\r\\n（2）SK（SPK）信号出现并且确认固定后，SKVOL2的取值增加开仓手数的数值；BP（BPK）信号出现并且确认固定后，SKVOL2的取值减少平仓手数的数值。\\r\\n\\r\\n例：\\r\\nSKVOL2=0&&C<O,SK(1);//空头持仓为0并且收盘价小于开盘价时，卖开一手\\r\\nSKVOL2>=1&&L>LV(L,5),SK(2); //空头持仓大于等于1，并且当根K线的最低价小于前面5个周期中最低价中最小值时，加仓2手\\r\\nSKVOL2>0&&H<REF(H,5),BP(SKVOL2); //空头持仓大于0，并且当根K线的最高价大于5个周期前K线的最高价时，买平所有空头持仓", "explanation": "卖开信号手数", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "SKVOL2模组空头持仓", "type": 12}, "SLOPE": {"body": "SLOPE( , )", "createtime": "20140326", "description": "SLOPE(X,N)：得到X的N周期的线型回归的斜率。\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、N为有效值，但当前的k线数不足N根，该函数返回空值；\\r\\n3、N为0时，该函数返回空值；\\r\\n4、N为空值,该函数返回空值；\\r\\n5、N可以为变量。\\r\\n\\r\\n举例：\\r\\n用最小平方法计算SLOPE(CLOSE,5)在最近一根K线上的的值：\\r\\n1、建立一元线性方程：close=a+slope*i+m\\r\\n2、close的估计值：close(i)^=a+slope*i\\r\\n3、求残差：m^=close(i)-close(i)^=close(i)-a-slope*i\\r\\n4、误差平方和：\\r\\nQ=m(1)*m(1)+...+m(5)*m(5)=[close(1)-a-slope*1]*[close(1)-a-slope*1]+...+[close(5)-a-slope*5]*[close(5)-a-slope*5]\\r\\n5、对线性方程中的参数a,slope求一阶偏导:\\r\\n2*{[close(1)-a-slope*1]+...+[close(5)-a-slope*5]}*(-1)=0\\r\\n2*{[close(1)-a-slope*1]+...+[close(5)-a-slope*5]}*(-5)=0\\r\\n6、联立以上两个公式，反解出slope的值：\\r\\nslope={[5*close(1))+...+1*close(5)]-[close(1)+...+close(5)]*(1+2+3+4+5)/5}/[(1*1+...+5*5)-(1+...+5)(1+...+5)/5]\\r\\n\\r\\n以上公式用麦语言函数可以表示如下：\\r\\n((5*C+4*REF(C,1)+3*REF(C,2)+2*REF(C,3)+1*REF(C,4))-SUM(C,5)*(1+2+3+4+5)/5)/((SQUARE(1)+SQUARE(2)+SQUARE(3)+SQUARE(4)+SQUARE(5))-SQUARE(1+2+3+4+5)/5);\\r\\n\\r\\n例：\\r\\nSLOPE(CLOSE,5);表示求收盘价5个周期线性回归线的斜率", "explanation": "线性回归的斜率", "markettype": 0, "modifytime": "", "param": "", "tip": "SLOPE(X,N)，求X的N周期的线型回归的斜率", "type": 3}, "SMA": {"body": "SMA( , , )", "createtime": "2014-05-16", "description": "SMA(X,N,M) 求X的N个周期内的扩展指数加权移动平均。M为权重。\\r\\n\\r\\n计算公式：SMA(X,N,M)=REF(SMA(X,N,M),1)*(N-M)/N+X(N)*M/N\\r\\n注：\\r\\n1、当N为有效值，但当前的k线数不足N根，按N根计算。\\r\\n2、 N为0或空值的情况下，函数返回空值。\\r\\n\\r\\n例1：\\r\\nSMA10:=SMA(C,10,3);//求的10周期收盘价的扩展指数加权移动平均。权重为3。", "explanation": "扩展指数加权移动平均", "markettype": 0, "modifytime": "", "param": "", "tip": "SMA(X,N,M)，求X的N个周期内的扩展指数加权移动平均。M为权重，N为周期数", "type": 2}, "SMMA": {"body": "SMMA(,)", "createtime": "", "description": "SMMA(X,N)，X为变量,N为周期，SMMA(X,N)表示当前K线上X在N个周期的通畅移动平均线\\r\\n算法：SMMA(X,N)=(SUM1-MMA+X)/N\\r\\n其中SUM1=X1+X2+.....+XN \\r\\nMMA=SUM1/N\\r\\n例1：\\r\\nSMMA(C,5);//收盘价的5周期通畅移动平均线", "explanation": "通畅移动平均", "markettype": 0, "modifytime": "", "param": "", "tip": "SMMA(X,N),表示当前K线上X在N个周期的通畅移动平均线", "type": 2}, "SOLID": {"body": "SOLID", "createtime": "20140617", "description": "SOLID 实心显示。\\r\\n\\r\\n用法：\\r\\n用在VOLSTICK、VOLUMESTICK函数后面，表示柱线实心显示。\\r\\n\\r\\n注：\\r\\n仅支持与VOLSTICK、VOLUMESTICK函数连用。\\r\\n\\r\\n例：\\r\\nVOL,VOLUMESTICK,SOLID;//画成交量柱状线，柱线实心显示。", "explanation": "实心显示", "markettype": 0, "modifytime": "", "param": "", "tip": "SOLID,画实心柱线", "type": 8}, "SORT": {"body": "SORT", "createtime": "20160225", "description": "SORT(Type,POS,N1,N2,...,N30); 按升(降)序排列，取第POS个参数对应的值\\r\\n\\r\\n注：\\r\\n1、当Type为0按升序排列，当Type为1按降序排列；\\r\\n2、TYPE,POS,不支持变量\\r\\n3、N1,...,N30为参数，支持常量、变量，最多支持30个参数\\r\\n\\r\\n例：\\r\\nSORT(0,3,2,1,5,3);//2、1、5、3按升序排列，取排列第三的数字3", "explanation": "取排序在相应位置的值", "markettype": 0, "modifytime": "", "param": "", "tip": "SORT(TYPE,POS,N1,N2,...,N30);按升(降)序排列，取第POS个参数对应的数值", "type": 2}, "SORTPOS": {"body": "SORTPOS", "createtime": "20160225", "description": "SORTPOS(Type,POS,N1,N2,...,N30); 按升(降)序排列，取第POS个数据的原始位置\\r\\n\\r\\n注：\\r\\n1、当Type为0按升序排列，当Type为1按降序排列；\\r\\n2、TYPE,POS,不支持变量；\\r\\n3、N1,...,N30为参数，支持常量、变量，最多支持30个参数\\r\\n4、如存在两个或两个以上相同的值，保持原有的顺序排列。如SORTPOS(1,3,2,1,2,5,3);返回值为1，取第一个2的位置；SORTPOS(1,4,2,1,2,5,3);返回值为3，取第二个2的位置\\r\\n5、返回的原始位置，从1开始\\r\\n\\r\\n例：\\r\\nSORTPOS(0,3,2,1,5,3);//2、1、5、3按升序排列，排列第三的数字3，所对应的原始位置。函数返回值为4", "explanation": "取排序后数值的位置", "markettype": 0, "modifytime": "", "param": "", "tip": "SORTPOS(Type,POS,N1,N2,...,N30);按升(降)序排列，取第POS个参数的原始位置", "type": 2}, "SOUND": {"body": "SOUND( )", "createtime": "", "description": "SOUND 播放声音。\\r\\n\\r\\n用法：SOUND(NAME)，播放NAME\\r\\n\\r\\n注：\\r\\n1、点击设置声音按钮，在弹出来的界面中设置声音，声音用字符'A'~'J'表示。\\r\\n2、自定义声音可以在设置菜单的设置声音文件中设置\\r\\n3、条件一直满足，则只播放一次，不重复播放。\\r\\n4、不支持将函数定义为变量，即不支持下面的写法：A:SOUND(NAME);\\r\\n5、当前k线满足时才会播放声音，历史满足不会播放\\r\\n\\r\\n例：\\r\\nCLOSE>OPEN,SOUND('A');表示K线收盘大于开盘时，播放声音\"A\"", "explanation": "播放声音", "markettype": 0, "modifytime": "", "param": "", "tip": "SOUND('N')，播放声音'N'", "type": 8}, "SP": "SP 卖平仓", "SPK": "SPK 卖平后卖开新仓", "SPLIT": {"body": "SPLIT()", "createtime": "20161027", "description": "SPLIT(N) 返回之前第N次除权(送股或配股)的除权比例，表示除权后股价的下跌比例。\\r\\n\\r\\n用法：\\r\\n1、该函数返回值为之前第N次除权(送股或配股)的除权比例（不包含最近一次除权）\\r\\n2、如果只有派发红利没有送股或者配股信息，则不计算除权比例\\r\\n\\r\\n计算方法：\\r\\n除权比例=除权参考价/股权登记日的收盘价；\\r\\n除权参考价=(股权登记日收盘价+配股价*配股率-派息率)/(1+送股率+配股率)；\\r\\n\\r\\n例：股权登记日收盘价为7.98 除权信息为：每10股配股1.5股 配股价5.6元\\r\\n除权参考价=(7.98+5.6*1.5/10)/(1+1.5/10)=7.6696\\r\\n除权比例=7.6696/7.98=0.9611\\r\\n\\r\\n注：\\r\\n1、当N为0时，返回最近一次的除权比例；\\r\\n2、当N为有效值，但本地数据范围内不足N次除权时，返回无效值；\\r\\n3、若N为无效值时，返回无效值；\\r\\n4、N可以为变量；\\r\\n5、该函数只支持加载在国内股票日线及日线以下周期使用。", "explanation": "返回之前第N次除权(送股或配股)的除权比例", "markettype": 0, "modifytime": "", "param": "", "tip": "SPLIT(N)返回之前第N次除权(送股或配股)的除权比例", "type": 15}, "SPLITBARS": {"body": "SPLITBARS()", "createtime": "20161027", "description": "SPLITBARS(N) 返回从之前第N个除权日到当前的周期数。\\r\\n\\r\\n用法：\\r\\n1、返回从之前第N个除权日（不包含最近一次除权日）到当前的周期数；\\r\\n2、若当前K线为之前的N个除权日，返回值为0；\\r\\n3、如果只有派发红利没有送股或者配股信息，则不计算到当前的周期数。\\r\\n\\r\\n注：\\r\\n1、当N为0时，返回从最近的一个除权日到当前的周期数；\\r\\n2、当N为有效值，但本地数据范围内不足N个除权日时，返回无效值；\\r\\n3、若N为无效值时，返回无效值；\\r\\n4、N可以为变量；\\r\\n5、该函数只支持加载在国内股票日线及日线以下周期使用。", "explanation": "返回从之前第N个除权日到当前的周期数", "markettype": 0, "modifytime": "", "param": "", "tip": "SPLITBARS(N)返回从之前第N次除权到当前的周期数", "type": 15}, "SQRT": {"body": "SQRT( )", "createtime": "", "description": "SQRT(X)：求X的平方根。\\r\\n\\r\\n注：\\r\\nX的取值为正数，X为负数时返回空值。\\r\\n\\r\\n例1：\\r\\nSQRT(CLOSE);//收盘价的平方根。", "explanation": "平方根", "markettype": 0, "modifytime": "", "param": "", "tip": "SQRT(X)，求X的平方根", "type": 4}, "SQUARE": {"body": "SQUARE( )", "createtime": "", "description": "SQUARE(X)求X的平方。\\r\\n\\r\\n例1：\\r\\nSQUARE(C);//收盘价的平方。\\r\\n例2：\\r\\nSQUARE(2);//2的平方。", "explanation": "平方", "markettype": 0, "modifytime": "", "param": "", "tip": "SQUARE(X)，求X的平方", "type": 4}, "STD": {"body": "STD( , )", "createtime": "2014-04-30", "description": "STD(X,N)：求X在N个周期内的样本标准差。\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、N为有效值，但当前的k线数不足N根，该函数返回空值；\\r\\n3、N为0时，该函数返回空值；\\r\\n4、N为空值，该函数返回空值。\\r\\n5、N可以为变量\\r\\n\\r\\n算法举例：计算STD(C,3);在最近一根K线上的值。\\r\\n\\r\\n用麦语言函数可以表示如下：\\r\\nSQRT((SQUARE(C-MA(C,3))+SQUARE(REF(C,1)-MA(C,3))+SQUARE(REF(C,2)-MA(C,3)))/2);\\r\\n\\r\\n例：\\r\\nSTD(C,10)求收盘价在10个周期内的样本标准差。\\r\\n//标准差表示总体各单位标准值与其平均数离差平方的算术平均数的平方根，它反映一个数据集的离散程度。STD(C,10)表示收盘价与收盘价的10周期均线之差的平方和的平均数的算术平方根。样本标准差是样本方差的平方根。", "explanation": "样本标准差", "markettype": 0, "modifytime": "", "param": "", "tip": "STD(X,N)，求X在N个周期内的样本标准差", "type": 3}, "STDP": {"body": "STDP( , )", "createtime": "2014-04-30", "description": "STDP(X,N)：为X的N周期总体标准差。\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、N为有效值，但当前的k线数不足N根，该函数返回空值；\\r\\n3、N为0时，该函数返回空值；\\r\\n4、N为空值，该函数返回空值。\\r\\n5、N可以为变量\\r\\n\\r\\n算法举例：计算STDP(C,3);在最近一根K线上的值。\\r\\n\\r\\n用麦语言函数可以表示如下：\\r\\nSQRT((SQUARE(C-MA(C,3))+SQUARE(REF(C,1)-MA(C,3))+SQUARE(REF(C,2)-MA(C,3)))/3);\\r\\n\\r\\n例：\\r\\nSTDP(C,10)为收盘价的10周期总体标准差。\\r\\n \\r\\n//总体标准差是反映研究总体内个体之间差异程度的一种统计指标，总体方差是一组资料中各数值与其算术平均数离差平方和的平均数，总体标准差则是总体方差的平方根。", "explanation": "总体标准差", "markettype": 0, "modifytime": "", "param": "", "tip": "STDP(X,N)，求X的N日总体标准差", "type": 3}, "STICK": {"body": "STICK(,,,,,)", "createtime": "", "description": "STICK(COND,P1,P2,N,COLOR,Empty);画不同粗细的柱线\\r\\n用法：当满足COND时，在P1与P2之间画一条粗细为N、颜色为COLOR的柱状图；若Empty不为0，则为空心柱；Empty为 0，则为实心柱。。用法和STICKLINE函数类似。\\r\\n注：\\r\\n1、参数N取值在0~9之间，为3时和主图K线宽度一致；\\r\\n2、不支持将该函数直接定义为变量，即不支持下面的写法：A:STICK(OPEN-CLOSE>0,OPEN,CLOSE,3,COLORCYAN,0);\\r\\n\\r\\n例1： \\r\\nSTICK(OPEN-CLOSE>0,OPEN,CLOSE,3,COLORCYAN,0);//表示当开盘价大于收盘价时，从开盘价到收盘价画宽度为3的青色的实心柱，即K线阴线的实体部分。", "explanation": "画指定粗细的柱线", "markettype": 0, "modifytime": "", "param": "", "tip": "STICK(COND,P1,P2,N,COLOR,Empty);画指定粗细的柱线当满足COND时，在P1与P2之间画一条粗细为N、颜色为COLOR的柱状图若Empty不为0，则为空心柱；Empty为0，则为实心柱；N取值0-9", "type": 8}, "STICK1": "STICK1，画柱线", "STICKLINE": {"body": "STICKLINE( , , , , )", "createtime": "20140603", "description": "STICKLINE 在图形上画柱线。\\r\\n\\r\\n用法：\\r\\nSTICKLINE(COND,P1,P2,Color,Empty); \\r\\n当COND条件满足时，从P1到P2画柱,颜色为Color。若Empty不为0，则为空心柱；Empty为0，则为实心柱，其中Empty不支持设置为变量。\\r\\n\\r\\n注：\\r\\n不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:STICKLINE(COND,P1,P2,Color,Empty);\\r\\n\\r\\n例1：\\r\\nSTICKLINE(OPEN-CLOSE>0,OPEN,CLOSE,COLORCYAN,0);//表示当开盘价大于收盘价时，从开盘价到收盘价画青色的实心柱，即K线阴线的实体部分。", "explanation": "画柱线", "markettype": 0, "modifytime": "", "param": "颜色为Color。若Empty不为0，则为空心柱", "tip": "STICKLINE(C,P1,P2,Color,Empty)当C条件满足时，从P1画到P2柱线", "type": 8}, "STICKLINE1": {"body": "STICKLINE1( , , , , )", "createtime": "20140603", "description": "STICKLINE1 在图形上画柱线。\\r\\n\\r\\n用法：\\r\\nSTICKLINE1(COND,P1,P2,Width,Empty); \\r\\n当COND条件满足时，从P1到P2画柱,宽度为Width。若Empty不为0，则为空心柱；Empty为0，则为实心柱。\\r\\n\\r\\n注：\\r\\n1、该函数支持在函数后设置颜色，即支持下面的写法：\\r\\nSTICKLINE1(COND,P1,P2,Width,Empty),COLOR;\\r\\n2、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:STICKLINE1(COND,P1,P2,Width,Empty);\\r\\n\\r\\n例：\\r\\nSTICKLINE1(OPEN-CLOSE>0,OPEN,CLOSE,4,0),COLORCYAN;//表示当开盘价大于收盘价时，从开盘价到收盘价画青色的实心柱，宽度为4，即K线阴线的实体部分。", "explanation": "画柱线", "markettype": 0, "modifytime": "", "param": "", "tip": "STICKLINE1(C,P1,P2,Width,Empty)当C条件满足时，从P1画到P2柱线，Width为宽度，若Empty不为0，则为空心柱", "type": 8}, "STKTYPE": {"body": "STKTYPE", "createtime": "20150522", "description": "STKTYPE 取市场类型。\\r\\n\\r\\n注：\\r\\n1、STKTYPE取当前交易合约的市场类型。\\r\\n2、返回值1为国内股票、2为美国股票、6为外汇、7为国内期货、8为国内期权、9为外盘、5为其它。\\r\\n\\r\\n例：\\r\\nA:STKTYPE;//加载到期货合约上，A返回值为7。", "explanation": "取市场类型", "markettype": 0, "modifytime": "", "param": "", "tip": "STKTYPE取市场类型，1为国内股票、2为美国股票、6为外汇、7为国内期货、8为国内期权、9为外盘、5为其它", "type": 5}, "STOCKDIVD": {"body": "STOCKDIVD", "createtime": "20160307", "description": "STOCKDIVD()  设置股票复权\\r\\n\\r\\n用法：\\r\\nSTOCKDIVD(0);表示设置向前复权；STOCKDIVD(1);表示设置向后复权\\r\\n\\r\\n注：\\r\\n1、该函数只支持加载在国内股票使用；\\r\\n2、该函数不支持TICK周期和量能周期；\\r\\n3、同一K线图不能同时加载不同参数的STOCKDIVD模型；\\r\\n4、该函数是只对K线价格（H、O、L、C）、成交量和成交额的复权处理。\\r\\n\\r\\n例：\\r\\nMA5:MA(C,5);\\r\\nMA10:MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;\\r\\nCROSSDOWN(MA5,MA10),SP;\\r\\nSTOCKDIVD(0);//设置股票向前复权\\r\\nAUTOFILTER;", "explanation": "设置股票复权", "markettype": 0, "modifytime": "20220211", "param": "", "tip": "STOCKDIVD()设置股票除权复权", "type": 15}, "STOP": "STOP(type,N) 限价止损指令，N为止损点数", "SUM": {"body": "SUM( , )", "createtime": "", "description": "SUM(X,N) 求X在N个周期内的总和。\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、若N为0则从第一个有效值开始算起。\\r\\n3、当N为有效值，但当前的k线数不足N根，按照实际的根数计算。\\r\\n4、N为空值时，返回空值。\\r\\n5、N可以为变量。\\r\\n\\r\\n例1：\\r\\nSUM(VOL,25);表示统计25周期内的成交量总和\\r\\n例2：\\r\\nN:=BARSLAST(DATE<>REF(DATE,1))+1;//分钟周期，日内k线根数\\r\\nSUM(VOL,N);//分钟周期上，取当天成交量总和。", "explanation": "求和", "markettype": 0, "modifytime": "", "param": "", "tip": "SUM(X,N)，求X在N个周期内的总和", "type": 2}, "SUMBARS": {"body": "SUMBARS( , )", "createtime": "", "description": "SUMBARS(X,A)：求累加到指定值的周期数\\r\\n\\r\\n注：\\r\\n参数A支持变量\\r\\n\\r\\n例1：\\r\\nSUMBARS(VOL,20000); 将成交量向前累加直到大于等于20000，返回这个区间的周期数。", "explanation": "累加到指定值的周期数", "markettype": 0, "modifytime": "", "param": "", "tip": "SUMBARS(X,A):求多少个周期的X向前累加能够大于等于A", "type": 2}, "T0TOTIME": {"body": "T0TOTIME()", "createtime": "20181120", "description": "T0TOTIME(X) 秒数转换为时间。\\r\\n\\r\\n用法：T0TOTIME(X);返回自该日0点以来的X秒处的时间。X可为变量或常数。\\r\\n\\r\\n注：该函数返回值为HHMMSS（时，分，秒）的形式。\\r\\n\\r\\n例：\\r\\nA:=T0TOTIME(60);//变量A返回值为100，表示1分钟", "explanation": "秒数转换为时间", "markettype": 0, "modifytime": "", "param": "", "tip": "T0TOTIME(X)返回自该日0点以来的X秒处的时间。X可为变量或常数", "type": 7}, "TAN": {"body": "TAN( )", "createtime": "", "description": "TAN(X)：返回X的正切值。\\r\\n例1：\\r\\nTAN(0);//返回0的正切值；\\r\\n例2：\\r\\nTAN(-3.14);//返回-3.14的正切值。", "explanation": "正切", "markettype": 0, "modifytime": "", "param": "", "tip": "TAN(X)，求X的正切值", "type": 4}, "TAVLOSS": {"body": "TAVLOSS", "createtime": "20160302", "description": "TAVLOSS 返回平均亏损额\\r\\n\\r\\n注：\\r\\n1、平均亏损额=总亏损/总亏损次数\\r\\n2、从开仓到持仓为0被视为一次交易。\\r\\n3、收盘价模型，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n指令价模型，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n4、TAVLOSS的计算不包含手续费。\\r\\n5、因信号消失产生的盈亏、交易次数未纳入TAVLOSS的计算。\\r\\n\\r\\n例：\\r\\nMA5:MA(C,5);\\r\\nMA10:MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;\\r\\nCROSSDOWN(MA5,MA10),SP;\\r\\nIDLE(TAVLOSS>550);//平均亏损额大于550限制开仓\\r\\nAUTOFILTER;", "explanation": "返回平均亏损额", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "TAVLOSS平均亏损额", "type": 12}, "TAVWIN": {"body": "TAVWIN", "createtime": "20160302", "description": "TAVWIN 返回平均盈利额\\r\\n\\r\\n注：\\r\\n1、平均盈利额=总盈利/总盈利次数\\r\\n2、从开仓到持仓为0被视为一次交易。\\r\\n3、收盘价模型，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n指令价模型，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n4、TAVWIN的计算不包含手续费。\\r\\n5、因信号消失产生的盈亏、交易次数未纳入TAVWIN的计算。\\r\\n\\r\\n例：\\r\\nMA5:MA(C,5);\\r\\nMA10:MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;\\r\\nCROSSDOWN(MA5,MA10),SP;\\r\\nIDLE(TAVWIN<700);//平均盈利额小于700限制开仓\\r\\nAUTOFILTER;", "explanation": "返回平均盈利额", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "TAVWIN平均盈利额", "type": 12}, "TAVWINLOSS": {"body": "TAVWINLOSS", "createtime": "20160302", "description": "TAVWINLOSS 返回平均盈亏额\\r\\n\\r\\n注：\\r\\n1、平均盈亏额=总盈亏/交易次数\\r\\n2、从开仓到持仓为0被视为一次交易。\\r\\n3、收盘价模型，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n指令价模型，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n4、TAVWINLOSS的计算不包含手续费。\\r\\n5、因信号消失产生的盈亏、交易次数未纳入TAVWINLOSS的计算。\\r\\n\\r\\n例：\\r\\nMA5:MA(C,5);\\r\\nMA10:MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;\\r\\nCROSSDOWN(MA5,MA10),SP;\\r\\nIDLE(TAVWINLOSS<100);//平均盈亏额小于100限制开仓\\r\\nAUTOFILTER;", "explanation": "返回平均盈亏额", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "TAVWINLOSS平均盈亏额", "type": 12}, "THEN": "", "TIME": {"body": "TIME", "createtime": "", "description": "TIME，取K线时间。\\r\\n\\r\\n注：\\r\\n1、该函数在盘中实时返回，在K线走完后返回K线的起始时间。\\r\\n2、该函数返回的是交易所数据接收时间，也就是交易所时间。\\r\\n3、TIME函数在秒周期使用时返回六位数的形式，即：HHMMSS，在其他周期上显示为四位数的形式，即：HHMM.\\r\\n4、该函数在指令价模型中使用，支持日线及日线以上周期，返回当时的分钟时间，收盘价模型使用该函数，只能加载在日周期以下的周期中，在日周期及日周期以上的周期中该函数返回值始终为1500。\\r\\n5、使用TIME函数进行尾盘平仓的操作需要注意\\r\\n（1）尾盘平仓设置的时间建议设置为K线返回值中实际可以取到的时间（如：螺纹加权 5分钟周期 最后一根K线返回时间为1455，尾盘平仓设置为TIME>=1458,SP;则效果测试中不能出现尾盘平仓的信号）\\r\\n（2）使用TIME函数作为尾盘平仓的条件的，建议开仓条件也要做相应的时间限制（如设置尾盘平仓条件为TIME>=1458,CLOSEOUT;则相应的开仓条件中需要添加条件TIME<1458；避免平仓后再次开仓的情况）\\r\\n\\r\\n例1:\\r\\nC>O&&TIME<1450,BK;\\r\\nC<O&&TIME<1450,SK;\\r\\nTIME>=1450,SP;\\r\\nTIME>=1450,BP;\\r\\nAUTOFILTER;\\r\\n//在14:50后平仓。\\r\\n例2：\\r\\nISLASTSK=0&&C>O&&TIME>=0915,SK;", "explanation": "取K线的时间", "markettype": 0, "modifytime": "20230817", "param": "", "tip": "TIME取周期的时数，分钟周期表示为0900，秒周期表示为090000", "type": 7}, "TIME0": {"body": "TIME0", "createtime": "20181120", "description": "TIME0 求当前周期自该日0点以来的秒数。\\r\\n\\r\\n用法：TIME0;求当前周期自该日0点以来的秒数。\\r\\n\\r\\n注：该函数返回值为0-86400\\r\\n\\r\\n例：\\r\\nAA:TIME0;//AA在商品合约当天最后一根K线上的返回值为54000，表示0点到15点之间的秒数为54000秒", "explanation": "求当前周期自该日0点以来的秒数", "markettype": 0, "modifytime": "", "param": "", "tip": "", "type": 7}, "TIMES": "TIMES 满足表达式的次数", "TIMETOT0": {"body": "TIMETOT0()", "createtime": "20181120", "description": "TIMETOT0(X) 时间转换为秒数。\\r\\n\\r\\n用法：TIMETOT0(X);返回时间X自该日0点以来的秒数。X可为变量或常数。\\r\\n\\r\\n注：该函数返回值为时间X距离0点的秒数，X为HHMMSS的形式。\\r\\n\\r\\n例：\\r\\nA:=TIMETOT0(100);//变量A返回值为60，表示60秒", "explanation": "时间转换为秒数", "markettype": 0, "modifytime": "", "param": "", "tip": "TIMETOT0(X)返回时间X自该日0点以来的秒数。X可为变量或常数", "type": 7}, "TMAXLOSS": {"body": "TMAXLOSS", "createtime": "20160302", "description": "TMAXLOSS 返回单次亏损最大额\\r\\n\\r\\n注：\\r\\n1、该函数返回交易以来，亏损最大的一次交易的亏损额\\r\\n2、从开仓到持仓为0被视为一次交易。\\r\\n3、收盘价模型，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n指令价模型，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n4、TMAXLOSS的计算不包含手续费。\\r\\n5、因信号消失产生的盈亏未纳入TMAXLOSS的计算。\\r\\n\\r\\n例：\\r\\nMA5:MA(C,5);\\r\\nMA10:MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;\\r\\nCROSSDOWN(MA5,MA10),SP;\\r\\nIDLE(TMAXLOSS>1000);//单次最大亏损额大于1000限制开仓\\r\\nAUTOFILTER;", "explanation": "返回单次亏损最大额", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "TMAXLOSS单次亏损最大额", "type": 12}, "TMAXSEQLOSS": {"body": "TMAXSEQLOSS", "createtime": "********", "description": "TMAXSEQLOSS 返回连续亏损交易的最大次数。\\r\\n\\r\\n注：\\r\\n1、该函数返回当前位置之前，连续亏损交易的最大次数。\\r\\n2、从开仓到持仓为0被视为一次交易。\\r\\n3、收盘价模型，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n指令价模型，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n4、TMAXSEQLOSS的计算不包含手续费。\\r\\n5、因信号消失产生的盈亏、交易次数未纳入TMAXSEQLOSS的计算。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK(2);//最新价上穿五周期均线，开仓两手\\r\\nCROSS(MA(C,5),C),SP(1);//最新价下穿五周期均线，卖平1手\\r\\nTSEQLOSS>60||TMAXSEQLOSS>3,SP(BKVOL);//最大连续亏损额大于60时或最大连续亏损次数大于3次时，平掉全部多头持仓", "explanation": "返回连续亏损交易的最大次数", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "TMAXSEQLOSS当前位置之前，连续亏损交易的最大次数", "type": 12}, "TMAXSEQWIN": {"body": "TMAXSEQWIN", "createtime": "********", "description": "TMAXSEQWIN 返回连续赢利交易的最大次数。\\r\\n\\r\\n注：\\r\\n1、该函数返回当前位置之前，连续赢利交易的最大次数。\\r\\n2、从开仓到持仓为0被视为一次交易。\\r\\n3、收盘价模型，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n指令价模型，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n4、TMAXSEQWIN的计算不包含手续费。\\r\\n5、因信号消失产生的盈亏、交易次数未纳入TMAXSEQWIN的计算。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK(2);//最新价上穿五周期均线，开仓两手\\r\\nCROSS(MA(C,5),C),SP(1);//最新价下穿五周期均线，卖平1手\\r\\nTSEQWIN>20||TMAXSEQWIN>3,BK(2);//最大连续赢利额大于20时或最大连续赢利次数大于3次时，加仓2手", "explanation": "返回连续赢利交易的最大次数", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "TMAXSEQWIN当前位置之前，连续盈利交易的最大次数", "type": 12}, "TMAXWIN": {"body": "TMAXWIN", "createtime": "20160302", "description": "TMAXWIN 返回单次盈利最大额\\r\\n\\r\\n注：\\r\\n1、该函数返回交易以来，盈利最大的一次交易的盈利额\\r\\n2、从开仓到持仓为0被视为一次交易。\\r\\n3、收盘价模型，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n指令价模型，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n4、TMAXWIN的计算不包含手续费。\\r\\n5、因信号消失产生的盈亏未纳入TMAXWIN的计算。\\r\\n\\r\\n例：\\r\\nMA5:MA(C,5);\\r\\nMA10:MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;\\r\\nCROSSDOWN(MA5,MA10),SP;\\r\\nIDLE(TMAXWIN<1000);//单次最大盈利额小于1000限制开仓\\r\\nAUTOFILTER;", "explanation": "返回单次盈利最大额", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "TMAXWIN单次盈利最大额", "type": 12}, "TNUMSEQLOSS": {"body": "TNUMSEQLOSS", "createtime": "20150303", "description": "TNUMSEQLOSS 返回持续亏损的次数。\\r\\n\\r\\n注：\\r\\n1、从开仓到持仓为0被视为一次交易。\\r\\n2、收盘价模型，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n指令价模型，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n3、TNUMSEQLOSS的计算不包含手续费。\\r\\n4、当根k线上是反手信号，反手信号开仓时，该函数返回上一次信号发出时的持续亏损次数（即此次反手信号的平仓部分的盈亏不计算在内）\\r\\n5、因信号消失产生的盈亏、交易次数未纳入TNUMSEQLOSS的计算。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK(2);//最新价上穿五周期均线，开仓两手\\r\\nCROSS(MA(C,5),C),SP(1);//最新价下穿五周期均线，卖平1手\\r\\nTNUMSEQLOSS>2,SP(BKVOL);//连续亏损的次数大于2时，平掉全部多头持仓", "explanation": "返回持续亏损的次数", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "TNUMSEQLOSS返回持续亏损的次数", "type": 12}, "TNUMSEQWIN": {"body": "TNUMSEQWIN", "createtime": "20150303", "description": "TNUMSEQWIN 返回持续赢利的次数。\\r\\n\\r\\n注：\\r\\n1、从开仓到持仓为0被视为一次交易。\\r\\n2、收盘价模型，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n指令价模型，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n3、TNUMSEQWIN的计算不包含手续费。\\r\\n4、当根k线上是反手信号，反手信号开仓时，该函数返回上一次信号发出时的持续赢利次数（即此次反手信号的平仓部分的盈亏不计算在内）\\r\\n5、因信号消失产生的盈亏、交易次数未纳入TNUMSEQWIN的计算。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK(2);//最新价上穿五周期均线，开仓两手\\r\\nCROSS(MA(C,5),C),SP(BKVOL);//最新价下穿五周期均线，卖平多头持仓\\r\\nTNUMSEQWIN>=2,BK(1);//连续赢利的次数大于等于2次时，加仓一手", "explanation": "返回持续赢利的次数", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "TNUMSEQWIN返回持续赢利的次数", "type": 12}, "TODAYDEUCETIMES": {"body": "TODAYDEUCETIMES", "createtime": "20150417", "description": "TODAYDEUCETIMES 返回当日平出次数。\\r\\n\\r\\n注：\\r\\n1、该函数返回当日平出的次数，平出指平仓盈亏为0。\\r\\n2、当日是以交易日计算的，不是自然日。\\r\\n3、从开仓到持仓为0被视为一次交易。\\r\\n4、收盘价模型，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n指令价模型，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n5、TODAYDEUCETIMES的计算不包含手续费。\\r\\n6、因信号消失产生的盈亏、交易次数未纳入TODAYDEUCETIMES的计算。\\r\\n\\r\\n例：\\r\\nTODAYDEUCETIMES<3&&CROSS(C,MA(C,5)),BK(1);//当日平出次数小于三次且最新价上穿五周期均线，开仓一手\\r\\nCROSS(MA(C,5),C),SP(BKVOL);//最新价下穿五周期均线，卖平多头持仓", "explanation": "返回当日平出次数", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "TODAYDEUCETIMES返回当日平出次数", "type": 12}, "TODAYLOSSTIMES": {"body": "TODAYLOSSTIMES", "createtime": "20150417", "description": "TODAYLOSSTIMES 返回当日亏损次数。\\r\\n\\r\\n注：\\r\\n1、该函数返回当日亏损的次数。\\r\\n2、当日是以交易日计算的，不是自然日。\\r\\n3、从开仓到持仓为0被视为一次交易。\\r\\n4、收盘价模型，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n指令价模型，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n5、TODAYLOSSTIMES的计算不包含手续费。\\r\\n6、因信号消失产生的盈亏、交易次数未纳入TODAYLOSSTIMES的计算。\\r\\n\\r\\n例：\\r\\nTODAYLOSSTIMES<3&&CROSS(C,MA(C,5)),BK(1);//当日亏损次数小于三次且最新价上穿五周期均线，开仓一手\\r\\nCROSS(MA(C,5),C),SP(BKVOL);//最新价下穿五周期均线，卖平多头持仓", "explanation": "返回当日亏损次数", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "TODAYLOSSTIMES返回当日亏损次数", "type": 12}, "TODAYWINTIMES": {"body": "TODAYWINTIMES", "createtime": "20150417", "description": "TODAYWINTIMES 返回当日赢利次数。\\r\\n\\r\\n注：\\r\\n1、该函数返回当日赢利的次数。\\r\\n2、当日是以交易日计算的，不是自然日。\\r\\n3、从开仓到持仓为0被视为一次交易。\\r\\n4、收盘价模型，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n指令价模型，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n5、TODAYWINTIMES的计算不包含手续费。\\r\\n6、因信号消失产生的盈亏、交易次数未纳入TODAYWINTIMES的计算。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK(1);//最新价上穿五周期均线，开仓一手\\r\\nTODAYWINTIMES=3,BK(2);//当日赢利3次时，加仓2手\\r\\nCROSS(MA(C,5),C),SP(BKVOL);//最新价下穿五周期均线，卖平多头持仓", "explanation": "返回当日赢利次数", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "TODAYWINTIMES返回当日赢利次数", "type": 12}, "TPROFIT_REF": {"body": "TPROFIT_REF( )", "createtime": "20150417", "description": "TPROFIT_REF(N) 取得前第N次交易的盈亏额。\\r\\n\\r\\n注：\\r\\n1、该函数返回当前位置之前第N次交易的盈亏额，如果赢利该函数返回正数，亏损返回负数。\\r\\n2、从开仓到持仓为0被视为一次交易。\\r\\n3、N支持写为变量或者参数。\\r\\n4、收盘价模型，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n指令价模型，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n5、TPROFIT_REF的计算不包含手续费\\r\\n6、按交易合约计算\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK(1);//最新价上穿五周期均线，开仓一手\\r\\nTPROFIT_REF(1)>0&&TPROFIT_REF(2)>0&&TPROFIT_REF(1)>TPROFIT_REF(2),BK(2);//最近连续两笔交易都是赢利的，且赢利额是增长的，加仓2手\\r\\nCROSS(MA(C,5),C),SP(BKVOL);//最新价下穿五周期均线，卖平多头持仓", "explanation": "取得前第N次交易的盈亏额", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "TPROFIT_REF(N)取得前第N次交易的盈亏额", "type": 12}, "TRACING_ORDER": {"body": "TRACING_ORDER( , , )", "createtime": "20220524", "description": "TRACING_ORDER(Sig,PriceType,Time);设置信号进行追价下单\\r\\n\\r\\n用法：\\r\\nTRACING_ORDER(Sig,PriceType,Time)，设置SIG指令按照追价方式委托\\r\\nPriceType为首次下单委托价格，Time 秒不成交市价追\\r\\n\\r\\n1、SIG位置为交易指令，包括BK\\SK\\BP\\SP\\BPK\\SPK六种指令\\r\\n2、PriceType位置为追价首次下单价格，包括以下五种：\\r\\n（1）NEW_ORDER 最新价\\r\\n（2）PASSIVE_ORDER 排队价\\r\\n（3）ACTIVE_ORDER 对价\\r\\n（4）CMPETITV_ORDER 超价\\r\\n超价参数在下单主界面-参数设置-超价参数中设置\\r\\n（5）SIGPRICE_ORDER 触发价\\r\\n3、time位置数字的有效范围为1-1000，超过这个时间即撤掉委托，改为市价追\\r\\n4、在进行历史回测时：\\r\\n A：收盘价模型回测，信号价格为信号所在K线的收盘价。\\r\\n B：指令价模型回测，信号价格为出现信号时的最新价。\\r\\n5、该函数不支持加载在股票合约上\\r\\n\\r\\n例：\\r\\nC>HV(H,20),BK;//价格大于前20周期高点买开仓\\r\\nC<LV(L,20),SK;//价格小于前20周期低点卖开仓\\r\\nC>HV(H,10),BP;//价格大于前10周期高点平空仓\\r\\nC<LV(L,10),SP;//价格小于前10周期低点平多仓\\r\\nTRACING_ORDER(BK,NEW_ORDER,2);//买开的委托以最新价委托，2秒未成交市价追\\r\\nTRACING_ORDER(SK,PASSIVE_ORDER,2);//卖开的委托以排队价委托，2秒未成交市价追\\r\\nTRACING_ORDER(BP,SIGPRICE_ORDER,2);//买平的委托以触发价委托，2秒未成交市价追\\r\\nTRACING_ORDER(SP,CMPETITV_ORDER,2);//卖平的委托以超价委托，2秒未成交市价追\\r\\nAUTOFILTER;", "explanation": "设置信号进行追价下单", "markettype": 1, "modifytime": "20220826", "param": "", "tip": "TRACING_ORDER(Sig,PriceType,Time);设置SIG指令按照追价方式委托，PriceType为首次下单委托价格，Time 秒不成交市价追", "type": 11}, "TRADE_AGAIN": {"body": "TRADE_AGAIN()", "createtime": "20140811", "description": "TRADE_AGAIN(N) 同一指令行可以连续出N个信号。\\r\\n\\r\\n用法：\\r\\nTRADE_AGAIN(N) 含有该函数的加减仓模型中,同一指令行可以连续出N个信号。\\r\\n\\r\\n注：\\r\\n1、该函数只适用于加减仓模型\\r\\n2、模型中写入该函数，一根K线只支持一个信号。不可以和MULTSIG、MULTSIG_MIN函数同时使用。\\r\\n3、N个信号必须连续执行，如果期间出现其他信号，则N从新计算。\\r\\n4、N不可以写为变量。\\r\\n\\r\\n例：\\r\\nC>O,BK(1);//K线为阳线，买开1手\\r\\nC<O,SP(BKVOL);//K线为阴线，卖平多头持仓\\r\\nTRADE_AGAIN(3);//同一指令行可以连续执行3次（如果连续三根阳线，则连续三次买开仓）", "explanation": "限制信号函数", "markettype": 1, "modifytime": "20221206", "param": "", "tip": "TRADE_AGAIN(N),含有该函数的加减仓模型中,同一指令行可以连续出N个信号", "type": 9}, "TRADE_OTHER": {"body": "TRADE_OTHER()", "createtime": "20141219", "description": "TRADE_OTHER('CODE') 指定CODE合约为交易合约\\r\\n模型出现信号后，下单时交易指定的CODE合约\\r\\n\\r\\n注：\\r\\n1、CODE位置可写为'AUTO'、'XX主连'、'SEMIAUTO'或具体合约的交易代码，不支持写入文华码\\r\\n（1）该函数写为TRADE_OTHER('AUTO')时，可以加载到加权合约上，自动交易主力合约，实现自动换月移仓。\\r\\n（2）该函数写为TRADE_OTHER('SEMIAUTO')时，可以加载到加权合约上，自动交易主力合约，主力切换时不自动移仓，需手动处理。\\r\\n（3）该函数写为TRADE_OTHER('XX主连')时，可以加载到商品加权、中金所加权、文华指数、全球股票指数上，自动交易主力合约，实现自动移仓换月。\\r\\n（4）该函数写为TRADE_OTHER('交易代码')时，可以加载到除主连的其他所有合约上，交易指定合约。\\r\\n2、\\r\\n（1）CODE位置写为'AUTO'、'SEMIAUTO'、'XX主连'时：\\r\\nA、模型加载在加权合约上时，从主连数据开始时间开始计算信号。\\r\\nB、不支持加载到量能周期、秒周期、日线以上周期使用。\\r\\n（2）CODE位置写为具体合约时：\\r\\nA、从该合约数据开始时间开始计算信号。\\r\\nB、不支持加载到量能周期、秒周期上使用。\\r\\n3、回测时：\\r\\n（1）信号价格取值为该函数定义的交易合约的信号价格。\\r\\n（2）移仓换月：在换月当天，旧主力以前一根K线的收盘价平仓，新主力以当前K线开盘价开新仓。\\r\\n4、模组运行时：\\r\\n（1）数据合约为加载模组时选择的数据合约，交易合约为该函数指定的合约。不写入该函数时，交易和数据合约一致。\\r\\n（2）移仓换月：\\r\\nA、CODE位置写为'AUTO'、'XX主连'：主力切换时，模组单元持仓自动切换为新主力合约；模组自动平旧主力合约，开新主力合约，委托价格均默认为市价。\\r\\nB、CODE位置写为'SEMIAUTO'：主力切换时，模组不自动移仓，模组单元持仓需点击模组界面“'SEMIAUTO移仓”按钮进行手动移仓。\\r\\n5、数据合约和交易合约的数据不对齐时，交易合约的信号价格取交易合约最后一根K线的收盘价。\\r\\n6、该函数必须在有信号的模型中使用。\\r\\n7、该函数不支持加载到副图中。\\r\\n8、该函数不支持加载到页面盒子中使用。\\r\\n9、该函数不支持与逐笔计算的运行优化函数连用，即不支持与CHECKSIG、MULTSIG连用。\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK;//最新价上穿5周期均线做多\\r\\nCROSS(MA(C,5),C),SP;//最新价下穿5周期均线平多\\r\\nTRADE_OTHER('AUTO');//自动移仓换月\\r\\nAUTOFILTER;", "explanation": "指定交易合约", "markettype": 1, "modifytime": "20231213", "param": "", "tip": "TRADE_OTHER('CODE')，指定CODE合约为交易合约", "type": 11}, "TRADE_REF": {"body": "TRADE_REF( )", "createtime": "20150417", "description": "TRADE_REF(N) 判断前N次交易是否赢利。\\r\\n\\r\\n注：\\r\\n1、该函数返回当前位置之前的第N次交易是否赢利，如果赢利返回1，亏损返回0。\\r\\n2、从开仓到持仓为0被视为一次交易。\\r\\n3、N支持写为变量或者参数。\\r\\n4、收盘价模型，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n指令价模型，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n5、TRADE_REF的计算不包含手续费\\r\\n6、按交易合约计算\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK(1);//最新价上穿五周期均线，开仓一手\\r\\nTRADE_REF(1)=1&&TRADE_REF(2)=1&&TRADE_REF(3)=1,BK(2);//最近连续三笔交易都是赢利的，加仓2手\\r\\nCROSS(MA(C,5),C),SP(BKVOL);//最新价下穿五周期均线，卖平多头持仓", "explanation": "判断前N次交易是否赢利", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "TRADE_REF(N)判断前N次交易是否赢利", "type": 12}, "TRADE_SMOOTHING": {"body": "TRADE_SMOOTHING", "createtime": "20151224", "description": "TRADE_SMOOTHING 消除隔日跳空函数\\r\\n\\r\\n用法：\\r\\n模型中含有TRADE_SMOOTHING函数，主图K线为消除跳空后计算得到的K线，模型根据消除跳空后K线取值计算\\r\\n\\r\\n注：\\r\\n1、该函数只支持加载在分钟或小时周期使用\\r\\n2、该函数只能加载在具体合约上使用，不支持加载在加权、主连等虚拟合约上使用\\r\\n3、该函数支持加载在国内期货品种上使用\\r\\n3、该函数不可以与TRADE_OTHER一起使用\\r\\n4、该函数不支持加载到盒子中使用\\r\\n5、模型中含有该函数，会叠加蓝色K线，蓝色K线为未消除跳空的真实K线数据\\r\\n6、消除跳空机制说明：\\r\\n（1）该函数只消除两个交易日之间的跳空\\r\\n（2）合约上市首日数据为该合约真实数据，之后数据为进行消除跳空处理后拟合数据；\\r\\n（3）模型含有该函数，K线图数据与盘口数据不一致\\r\\n\\r\\n例1\\r\\nMA5:MA(C,5);\\r\\nMA10:MA(C,10);\\r\\nCROSSUP(MA5,MA10),BPK;\\r\\nCROSSDOWN(MA5,MA10),SPK;\\r\\nTRADE_SMOOTHING;//消除跳空后的K线的均线满足上穿、下穿条件后执行信号\\r\\nAUTOFILTER;", "explanation": "消除隔日跳空函数", "markettype": 1, "modifytime": "20211130", "param": "", "tip": "TRADE_SMOOTHING;消除隔夜跳空函数", "type": 1}, "TREND": {"body": "TREND", "createtime": "2014-07-23", "description": "TREND 获取K线趋势。\\r\\n\\r\\n用法：\\r\\nTREND  K线的形成过程中最高价先出现，则返回值为3；最低价先出现，则返回值为2；若最高和最低一起出现，则返回值为1；默认为0。", "explanation": "获取K线趋势", "markettype": 1, "modifytime": "", "param": "", "tip": "TREND,获取K线趋势默认返回0，最高最低同时出现为1，最低先出现为2，最高先出现为3", "type": 5}, "TRMA": {"body": "TRMA( , )", "createtime": "", "description": "TRMA(X,N)： 求X在N个周期的三角移动平均值。\\r\\n\\r\\n算法：三角移动平均线公式，是采用算数移动平均，并且对第一个移动平均线再一次应用算数移动平均。\\r\\nTRMA(X,N) 算法如下\\r\\nma_half= MA(X,N/2)\\r\\ntrma=MA(ma_half,N/2)\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、当N为有效值，但当前的k线数不足N根，函数返回空值。\\r\\n3、N为0或空值的情况下，函数返回空值。\\r\\n4、N支持使用变量\\r\\n\\r\\n例1：\\r\\nTRMA5:TRMA(CLOSE,5);//计算5个周期内收盘价的三角移动平均。(N不能被2整除)\\r\\nTRMA(CLOSE,5)=MA(MA(CLOSE,(5+1)/2),(5+1)/2);\\r\\n例2:\\r\\nTRMA10:TRMA(CLOSE,10);// 计算10个周期内收盘价的三角移动平均。(N能被2整除)\\r\\nTRMA(CLOSE,10)=MA(MA(CLOSE,10/2),(10/2)+1);", "explanation": "三角移动平均", "markettype": 0, "modifytime": "20221013", "param": "", "tip": "TRMA(X,N)，求X在N周期内的三角移动平均", "type": 2}, "TSEQLOSS": {"body": "TSEQLOSS", "createtime": "********", "description": "TSEQLOSS 返回最大连续亏损额。\\r\\n\\r\\n注：\\r\\n1、该函数返回当前位置之前，连续亏损额的最大值。\\r\\n2、从开仓到持仓为0被视为一次交易。\\r\\n3、收盘价模型，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n指令价模型，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n4、TSEQLOSS的计算不包含手续费。\\r\\n5、因信号消失产生的盈亏未纳入TSEQLOSS的计算。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK(2);//最新价上穿五周期均线，开仓两手\\r\\nCROSS(MA(C,5),C),SP(BKVOL);//最新价下穿五周期均线，卖平全部持仓\\r\\nTSEQLOSS<-5000,SK(2);//最大连续亏损额达到5000时，反向开仓2手", "explanation": "返回最大连续亏损额", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "TSEQLOSS返回最大连续亏损额", "type": 12}, "TSEQWIN": {"body": "TSEQWIN", "createtime": "********", "description": "TSEQWIN 返回最大连续赢利额。\\r\\n\\r\\n注：\\r\\n1、该函数返回当前位置之前，连续赢利额的最大值。\\r\\n2、从开仓到持仓为0被视为一次交易。\\r\\n3、收盘价模型，平仓盈亏=（平仓信号当根K线的收盘价-开仓价格）*手数*交易单位。\\r\\n指令价模型，平仓盈亏=（平仓信号的指令价-开仓价格）*手数*交易单位。\\r\\n4、TSEQWIN的计算不包含手续费。\\r\\n5、因信号消失产生的盈亏未纳入TSEQWIN的计算。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK(2);//最新价上穿五周期均线，开仓两手\\r\\nCROSS(MA(C,5),C),SP(1);//最新价下穿五周期均线，卖平1手\\r\\nTSEQWIN>20,BK(2);//最大连续赢利额大于20时，加仓2手", "explanation": "返回最大连续赢利额", "markettype": 1, "modifytime": "20240220", "param": "", "tip": "TSEQWIN返回最大连续赢利额", "type": 12}, "TSMA": {"body": "TSMA( , )", "createtime": "", "description": "TSMA(X,N)：求X在N个周期内的时间序列三角移动平均\\r\\nTSMA(a,n) 算法如下：\\r\\nysum=a[i]+a[i-1]+...+a[i-n+1]\\r\\nxsum=i+i-1+..+i-n+1\\r\\nxxsum=i*i+(i-1)*(i-1)+...+(i-n+1)*(i-n+1)\\r\\nxysum=i*a[i]+(i-1)*a[i-1]+...+(i-n+1)*a[i-n+1]\\r\\nk=(xysum -(ysum/n)*xsum)/(xxsum- xsum/n * xsum) //斜率\\r\\nb= ysum/n - k*xsum/n\\r\\nforcast[i]=k*i+b //线性回归\\r\\ntsma[i] = forcast[i]+k  //线性回归+斜率\\r\\n\\r\\n注：\\r\\n1、当N为有效值，但当前的k线数不足N根，函数返回空值。\\r\\n2、N为0或空值的情况下，函数返回空值。\\r\\n3、N支持使用变量\\r\\n例1：\\r\\nTSMA5:TSMA(CLOSE,5);//计算5个周期内收盘价的序列三角移动平均", "explanation": "时间序列移动平均", "markettype": 0, "modifytime": "", "param": "", "tip": "TSMA(X,N)，求X在N周期内的时间序列三角移动平均", "type": 2}, "T_CLOSE": {"body": "T_CLOSE", "createtime": "20221201", "description": "T_CLOSE 取交易合约收盘价。\\r\\n\\r\\n注：\\r\\n1、当盘中k线没有走完的时候，取交易合约最新价。\\r\\n\\r\\n例1：\\r\\nA:T_CLOSE;//定义变量A为交易合约收盘价（盘中k线没有走完的时候A为交易合约最新价）。", "explanation": "取交易合约收盘价", "markettype": 1, "modifytime": "20221206", "param": "", "tip": "T_CLOSE 取交易合约收盘价。", "type": 1}, "T_MAX": {"body": "T_MAX", "createtime": "********", "description": "T_MAX(TYPE,N) 设置模组最大开仓手数\\r\\n\\r\\n用法：T_MAX(TYPE,N),根据设置的资金占用百分比计算模组最大可开仓手数，用于模组资金风控\\r\\n根据设置的资金占用百分比计算模组最大可开仓手数，下单时与理论开仓手数进行比较，模组实际下单手数不能超过该函数计算的手数\\r\\n\\r\\n注：\\r\\n1、TYPE为风控类型参数，只能写为RATIO_CODE或RATIO_ACCOUNT，不支持变量\\r\\nTYPE写为RATIO_CODE：单品种资金使用率=当前交易合约占用的保证金/账户权益\\r\\nTYPE写为RATIO_ACCOUNT：账户资金使用率=全部合约占用的保证金/账户权益\\r\\n另：保证金包含持仓占用保证金和挂单占用的保证金\\r\\n2、N为百分比，支持写为变量或参数；\\r\\n3、计算最大可开仓手数时，取模组单元参数中设置的保证金比率计算；\\r\\n4、该函数仅适用于模组运行，不支持回测；\\r\\n5、模组运行时，取交易账户的实际权益计算；\\r\\n6、该函数仅支持国内期货合约，不支持外盘；\\r\\n7、不支持将函数定义为变量，即不支持下面的写法：\\r\\nA:T_MAX(1,30);\\r\\n8、同一个模型中支持写入多行T_MAX函数：\\r\\n多行T_MAX函数的风控类型参数不同：取不同的风控类型计算的最大开仓手数的最小值，进行资金风控\\r\\n多行T_MAX函数的风控类型参数相同：取最后一行T_MAX计算的最大开仓手数，进行资金风控\\r\\n\\r\\n例：\\r\\nMA5:MA(C,5);\\r\\nMA10:MA(C,10);\\r\\nCROSS(MA5,MA10),BPK;\\r\\nCROSSDOWN(MA5,MA10),SPK;\\r\\nT_MAX(RATIO_CODE,5);//模组当前品种仓位占总资金的5%以下\\r\\nT_MAX(RATIO_ACCOUNT,25);//全部合约总仓位占总资金的25%以下\\r\\nAUTOFILTER;", "explanation": "设置模组最大开仓手数", "markettype": 1, "modifytime": "", "param": "", "tip": "T_MAX(TYPE,N),根据设置的资金占用百分比计算模组最大可开仓手数，用于模组资金风控", "type": 12}, "T_PLUS": {"body": "T_PLUS()", "createtime": "********", "description": "T_PLUS 设置开仓手数为默认手数的N倍\\r\\n\\r\\n用法：COND,T_PLUS(N) 当条件满足时，过滤模型的开仓手数为默认手数*N倍。\\r\\n\\r\\n注：\\r\\n1、该函数只能用于设置过滤模型的手数，不能用于设置加减仓模型的手数。\\r\\n2、N支持设置为参数，不支持设置为变量。\\r\\n3、\\r\\n该函数加载到模组中：满足条件时，下单手数为模组下单手数的N倍\\r\\n该函数加载到盒子中：满足条件时，下单手数为盒子右键中设置的默认下单手数的N倍。\\r\\n\\r\\n例：\\r\\nCROSS(C,MA(C,5)),BK;\\r\\nCROSS(MA(C,5),C),SP;\\r\\nCROSS(MA(C,5),MA(C,10)),T_PLUS(2);//开仓条件满足时，五周期均线上穿十周期均线，开仓手数增加为原来的2倍\\r\\nAUTOFILTER;", "explanation": "设置开仓手数为默认手数的N倍", "markettype": 1, "modifytime": "20240408", "param": "", "tip": "T_PLUS(N)当条件满足时，过滤模型的开仓手数为默认手数", "type": 12}, "UNIT": {"body": "UNIT", "createtime": "", "description": "取数据合约的交易单位。\\r\\n用法：\\r\\nUNIT 取加载数据合约的交易单位。", "explanation": "取数据合约的交易单位", "markettype": 0, "modifytime": "", "param": "", "tip": "UNIT,取加载数据合约的交易单位", "type": 1}, "UNIT1": {"body": "UNIT1", "createtime": "", "description": "UNIT1  取交易合约的交易单位。\\r\\n用法：\\r\\nUNIT1 取交易合约的交易单位。", "explanation": "取交易合约的交易单位", "markettype": 1, "modifytime": "20221117", "param": "", "tip": "UNIT1,取交易合约的交易单位", "type": 1}, "UNITLIMIT": {"body": "UNITLIMIT", "createtime": "", "description": "UNITLIMIT 取交易合约的限制拥有持仓数\\r\\n\\r\\n用法：UNITLIMIT自动取该合约交易所规定的限制拥有持仓数，避免违规。\\r\\n\\r\\n注：\\r\\n取不到时返回100000（如加权合约）\\r\\n\\r\\n例:\\r\\n(BKVOL+1)<=UNITLIMIT&&C>O,BK(1);//多头持仓再增加一手仍然小于交易合约的限制拥有的持仓数，并且满足收盘价大于开盘价的开仓条件时，买开一手。", "explanation": "取交易合约的限制拥有持仓数", "markettype": 1, "modifytime": "20230216", "param": "", "tip": "UNITLIMIT，取交易合约的限制拥有持仓数", "type": 1}, "V": "V,取成交量", "VALIGN": {"body": "VALIGN", "createtime": "20140528", "description": "设置文字对齐方式（上中下）。\\r\\n\\r\\n用法：DRAWTEXT(COND,PRICE,TEXT),VALIGNX;\\r\\n\\r\\nCOND条件满足时，在PRICE的位置，标注TEXT，文字按照VALIGNX写入的方式对齐。VALIGN0，VALIGN1，VALIGN2，分别表示上对齐，居中对齐，下对齐。\\r\\n\\r\\n例：\\r\\nDRAWTEXT(C>O,H,'涨'),ALIGN1,VALIGN1,FONTSIZE20,COLORGREEN;//在阳线的最高价标注文字“涨”，文字居中对齐，字体大小为20，颜色为绿色。", "explanation": "设置文字对齐方式（上中下）", "markettype": 0, "modifytime": "", "param": "", "tip": "VALIGN0,VALIGN1,VALIGN2,分别表示文字上对齐，居中对齐，下对齐", "type": 8}, "VALIGN0": "VALIGN0 上对齐", "VALIGN1": "VALIGN1 居中对齐", "VALIGN2": "VALIGN2 下对齐", "VALUEWHEN": {"body": "VALUEWHEN( , )", "createtime": "", "description": "VALUEWHEN(COND,X) 当COND条件成立时，取X的当前值。如COND条件不成立，则取上一次COND条件成立时X的值。\\r\\n\\r\\n注：\\r\\nX可以是数值也可以是条件。\\r\\n\\r\\n例1\\r\\nVALUEWHEN(HIGH>REF(HHV(HIGH,5),1),HIGH);表示当前最高价大于前五个周期最高价的最大值时返回当前最高价\\r\\n例2：\\r\\nVALUEWHEN(DATE<>REF(DATE,1),O);表示取当天第一根k线的开盘价（即当天开盘价）\\r\\n例3：\\r\\nVALUEWHEN(DATE<>REF(DATE,1),L>REF(H,1));//表示在当天第一根k线上判断当前最低价是否大于昨天最后一根K线的最高价。返回1，说明当天跳空高开。返回0，说明当天不满足跳空高开条件。", "explanation": "取值", "markettype": 0, "modifytime": "", "param": "", "tip": "VALUEWHEN(COND,X)，取满足条件COND时的X值", "type": 5}, "VAR": {"body": "VAR( , )", "createtime": "2014-04-30", "description": "VAR(X,N)求X在N周期内的样本方差。\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、N为有效值，但当前的k线数不足N根，该函数返回空值；\\r\\n3、N为0时，该函数返回空值；\\r\\n4、N为空值，该函数返回空值；\\r\\n5、N支持使用变量\\r\\n\\r\\n算法举例：计算VAR(C,3);在最近一根K线上的值。\\r\\n用麦语言函数可以表示如下：\\r\\n\\r\\n(SQUARE(C-MA(C,3))+SQUARE(REF(C,1)-MA(C,3))+SQUARE(REF(C,2)-MA(C,3)))/(3-1);\\r\\n\\r\\n例：\\r\\nVAR(C,5)求收盘价在5周期内的样本方差。\\r\\n//表示总体方差的N/(N-1)倍，VAR(C,5)表示收盘价的5周期总体样本方差的5/4倍。", "explanation": "样本方差", "markettype": 0, "modifytime": "", "param": "", "tip": "VAR(X,N)，求X在N周期内的样本方差", "type": 3}, "VARIABLE": "VARIABLE  定义全局变量并初始化", "VARP": {"body": "VARP( , )", "createtime": "2014-04-30", "description": "VARP(X,N)：为X的N周期总体方差\\r\\n\\r\\n注：\\r\\n1、N包含当前k线。\\r\\n2、N为有效值，但当前的k线数不足N根，该函数返回空值；\\r\\n3、N为0时，该函数返回空值；\\r\\n4、N为空值，该函数返回空值；\\r\\n5、N支持使用变量\\r\\n\\r\\n算法举例：计算VARP(C,3);在最近一根K线上的值。\\r\\n用麦语言函数可以表示如下：\\r\\n(SQUARE(C-MA(C,3))+SQUARE(REF(C,1)-MA(C,3))+SQUARE(REF(C,2)-MA(C,3)))/3;\\r\\n\\r\\n例：\\r\\nVARP(C,5)为收盘价的5周期总体方差\\r\\n//表示数据偏差平方和除以总周期数N，VARP(C,5)表示收盘价5个周期的数据偏差平方和除以5.", "explanation": "总体方差", "markettype": 0, "modifytime": "", "param": "", "tip": "VARP(X,N)，求X的N周期总体方差", "type": 3}, "VERTLINE": {"body": "VERTLINE( , )", "createtime": "20140603", "description": "VERTLINE 画垂直线。\\r\\n\\r\\n用法：\\r\\nVERTLINE(COND,COLOR); \\r\\n条件COND满足时，以颜色COLOR画垂直线。\\r\\n\\r\\n注：\\r\\n1、该函数支持在函数后设置线型（LINETHICK1 - LINETHICK7、POINTDOT、DOT），即支持下面的写法：\\r\\nVERTLINE(COND,COLOR),LINETHICK;\\r\\n2、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:VERTLINE(COND,COLOR);\\r\\n\\r\\n例1：\\r\\nVERTLINE(HIGH>=HHV(HIGH,30),COLORRED);//表示在价格创30天新高时画红色垂直线\\r\\n\\r\\n例2：\\r\\nVERTLINE(LOW<=LLV(LOW,30),COLORBLUE),LINETHICK3;//表示在价格创30天新低时画蓝色垂直线，线型粗细为3。", "explanation": "画垂直线", "markettype": 0, "modifytime": "", "param": "COND为条件,COLOR为颜色", "tip": "VERTLINE(COND,COLOR)，条件COND满足时，以颜色COLOR画垂直线", "type": 8}, "VERTLINE1": {"body": "VERTLINE1( )", "createtime": "20140603", "description": "VERTLINE1 画垂直线。\\r\\n\\r\\n用法：\\r\\nVERTLINE1(COND); \\r\\n条件COND满足时，画垂直线。\\r\\n\\r\\n注：\\r\\n1、该函数支持在函数后设置颜色、线型（LINETHICK1 - LINETHICK7、POINTDOT、DOT），即支持下面的两种写法：\\r\\nVERTLINE1(COND),LINETHICK,COLOR;\\r\\nVERTLINE1(COND),COLOR,LINETHICK;\\r\\n2、不支持将该函数定义为变量，即不支持下面的写法：\\r\\nA:VERTLINE1(COND);\\r\\n\\r\\n例1：\\r\\nVERTLINE1(HIGH>=HHV(HIGH,30)),COLORRED;//表示在价格创30天新高时画红色垂直线。\\r\\n例2：\\r\\nVERTLINE1(LOW<=LLV(LOW,30)),COLORBLUE,LINETHICK3;//表示在价格创30天新低时画蓝色垂直线,线型粗细为3。", "explanation": "画垂直线", "markettype": 0, "modifytime": "", "param": "COND为条件", "tip": "VERTLINE1(COND)条件COND满足时，画垂直线", "type": 8}, "VOL": {"body": "VOL", "createtime": "", "description": "VOL 取得K线图的成交量。\\r\\n\\r\\n注：\\r\\n可简写为V。\\r\\nVOL加载到非TICK图中，返回当根K线的成交量；VOL加载到TICK图中，在当根TICK上的返回值为当天所有TICK成交量的累计值。\\r\\n\\r\\n例1：\\r\\nVV:V;//定义VV为成交量\\r\\n例2：\\r\\nREF(V,1);//表示前一个周期的成交量\\r\\n例3：\\r\\nV>=REF(V,1);//成交量大于前一个周期的成交量，表示成交量增加(V为VOL的简写)。", "explanation": "取得K线图的成交量", "markettype": 0, "modifytime": "", "param": "", "tip": "VOL取成交量", "type": 1}, "VOLATILITY": {"body": "VOLATILITY()", "createtime": "20220127", "description": "VOLATILITY(N)，取期权历史波动率\\r\\n\\r\\n原理：\\r\\n历史波动率：标的合约的波动率，反映过去N个周期标的合约的波动率值。\\r\\n\\r\\n注：\\r\\n1、该函数仅支持加载在期权上，加载在其他合约返回空值；\\r\\n2、该函数不支持加载在量能周期；\\r\\n3、参数N取大于1的整数，不支持设置为变量；即不支持：\\r\\nA:VOLATILITY(DAY);\\r\\n4、该函数不支持在跨周期、跨合约的引用模型中使用；\\r\\n5、该函数不支持与运行优化函数一起使用。\\r\\n\\r\\n例1：\\r\\nAA:VOLATILITY(60);//AA返回过去60个周期的历史波动率。\\r\\n\\r\\n\\r\\n", "explanation": "取期权历史波动率", "markettype": 0, "modifytime": "20220215", "param": "", "tip": "VOLATILITY(N)，取期权历史波动率", "type": 1}, "VOLMARGIN": {"body": "VOLMARGIN", "createtime": "2021-04-13", "description": "VOLMARGIN 理论持仓保证金\\r\\n\\r\\n用法：VOLMARGIN返回当前理论持仓保证金，用于资金管理。\\r\\n\\r\\n计算公式：VOLMARGIN=价格*交易单位*手数*保证金比例\\r\\n\\r\\n注：\\r\\n1、该保证金为动态的保证金，随价格变化而变化\\r\\n2、模组中手动调仓会影响VOLMARGIN的返回值\\r\\n3、开仓信号当根k线VOLMARGIN=开仓价*交易单位*手数*保证金比例，根据不同信号执行方式，开仓价为：\\r\\n信号执行方式为‘k线走完确认信号下单’开仓为当根K线的收盘价\\r\\n信号执行方式为‘XXX下单，K线走完进行信号复核’，开仓价为信号发出时行情的最新价\\r\\n信号执行方式为‘出信号立即下单，不进行复核’，开仓价为信号发出时行情的最新价\\r\\n4、平仓信号当根k线和无持仓k线，VOLMARGIN返回值为0。", "explanation": "理论持仓保证金", "markettype": 1, "modifytime": "20230216", "param": "", "tip": "VOLMARGIN理论持仓保证金", "type": 12}, "VOLSTICK": {"body": "VOLSTICK", "createtime": "20140617", "description": "VOLSTICK 画柱线，K线为阳线画红色空心柱，K线为阴线画青色实心柱。\\r\\n\\r\\n注：\\r\\n1、柱高表示数值大小。\\r\\n2、K线为阳线，则对应的柱显示为红色空心，K线为阴线，则对应的柱显示为青色实心。\\r\\n3、默认红柱为空心柱，画实心柱需要加入SOLID函数。\\r\\n\\r\\n例1：\\r\\nVOL,VOLSTICK;//画成交量柱状线，柱高表示成交量大小，阳线对应红色空心柱，阴线对应青色实心柱。\\r\\n\\r\\n例2：\\r\\nVOL,VOLSTICK,SOLID;//画成交量柱状线，柱线实心显示。", "explanation": "画柱线", "markettype": 0, "modifytime": "", "param": "", "tip": "VOLSTICK画柱线，K线为阳线为红色，K线为阴线为青色", "type": 8}, "VOLTICK": {"body": "VOLTICK", "createtime": "2014-08-04", "description": "量能周期返回这根K线形成的TICK笔数，单位：笔。\\r\\n用法：\\r\\nVOLTICK 量能周期时，返回当前K线形成的TICK笔数。", "explanation": "返回K线是由多少笔TICK生成", "markettype": 1, "modifytime": "", "param": "", "tip": "VOLTICK返回K线是由多少笔TICK生成", "type": 7}, "VOLTIME": {"body": "VOLTIME", "createtime": "2014-08-04", "description": "量能周期返回这根K线形成的时间，单位：秒。\\r\\n用法：\\r\\nVOLTIME 量能周期时，返回当前K线形成的时间。", "explanation": "取K线形成的时间（秒）", "markettype": 1, "modifytime": "", "param": "", "tip": "VOLTIME取K线形成的时间（秒）", "type": 7}, "VOLUMESTICK": {"body": "VOLUMESTICK", "createtime": "20140617", "description": "VOLUMESTICK 画柱线，K线为阳线画红色空心柱，K线为阴线画青色实心柱。\\r\\n\\r\\n注：\\r\\n1、柱高表示数值大小。\\r\\n2、K线为阳线，则对应的柱显示为红色空心，K线为阴线，则对应的柱显示为青色实心。\\r\\n3、默认红柱为空心柱，画实心柱需要加入SOLID函数。\\r\\n\\r\\n例1：\\r\\nVOL,VOLUMESTICK;//画成交量柱状线，柱高表示成交量大小，阳线对应红色空心柱，阴线对应青色实心柱。\\r\\n\\r\\n例2：\\r\\nVOL,VOLUMESTICK,SOLID;//画成交量柱状线，柱线实心显示。", "explanation": "画柱线", "markettype": 0, "modifytime": "", "param": "", "tip": "VOLUMESTICK画柱线，K线为阳线为红色，K线为阴线为青色", "type": 8}, "WEEK": "WEEK 周", "WEEKDAY": {"body": "WEEKDAY", "createtime": "", "description": "WEEKDAY,取得星期数。\\r\\n \\r\\n注：\\r\\n1：WEEKDAY的取值范围是0—6。\\r\\n2：该函数在周周期上显示的值始终为5，在月周期上返回K线结束当天的星期数。\\r\\n \\r\\n例1：\\r\\nN:=BARSLAST(MONTH<>REF(MONTH,1))+1;\\r\\nCOUNT(WEEKDAY=5,N)=3&&TIME>=1450,BP;\\r\\nCOUNT(WEEKDAY=5,N)=3&&TIME>=1450,SP;\\r\\nAUTOFILTER;//每个月部分交易日尾盘自动平仓。\\r\\n例2：\\r\\nC>VALUEWHEN(WEEKDAY<REF(WEEKDAY,1),O)+10,BK; \\r\\nAUTOFILTER;", "explanation": "取得星期数", "markettype": 0, "modifytime": "20230707", "param": "", "tip": "WEEKDAY取得星期数（0-6）", "type": 7}, "WEEKTRADE": {"body": "WEEKTRADE", "createtime": "20141225", "description": "WEEKTRADE 周内交易函数。\\r\\n\\r\\n用法：\\r\\n模型中写入该函数，信号和资金每周重新初始化进行计算，与历史割裂。\\r\\n\\r\\n注：\\r\\n1、该函数不支持自定义N日、周、月、季、年周期，其他周期均支持。\\r\\n2、回测报告中的出金/入金，为每周出金/入金的和。\\r\\n3、模型中不能同时使用DAYTRADE1\\DAYTRADE\\WEEKTRADE\\WEEKTRADE1\\MONTHTRADE\\QUARTERTRADE\\YEARTRADE函数。\\r\\n4、（1）历史回测中，当周K线走完持仓大于0，会对持仓进行全清处理。\\r\\n   （2）模组运行中，当周K线走完持仓大于0，信号和资金会重新初始化进行计算，但不会自动对持仓进行全清处理，需要在模型中编写实现全清。\\r\\n\\r\\n例：\\r\\nMA5^^MA(C,5);\\r\\nMA10^^MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;//5周期均线上穿10周期均线，买开仓\\r\\nCROSSDOWN(MA5,MA10),SK;//5周期均线下穿10周期均线，卖开仓\\r\\nC<BKPRICE-10*MINPRICE,SP;//亏损10点平多\\r\\nC>SKPRICE+10*MINPRICE,BP;//亏损10点平空\\r\\nCLOSEMINUTE<=1,CLOSEOUT;//收盘前一分钟，清仓。\\r\\nAUTOFILTER;//过滤模型\\r\\nWEEKTRADE;//使用每周数据计算", "explanation": "周内交易函数", "markettype": 1, "modifytime": "", "param": "", "tip": "WEEKTRADE,周内交易函数", "type": 9}, "WEEKTRADE1": {"body": "WEEKTRADE1", "createtime": "20170510", "description": "WEEKTRADE1 周内交易函数。\\r\\n\\r\\n用法：\\r\\nWEEKTRADE1 模型中写入该函数，信号和资金每周重新初始化进行计算，与历史割裂，并且每一个函数只使用当周K线数据进行计算，历史数据不参与计算。\\r\\n\\r\\n注：\\r\\n1、该函数适用于周以下周期，不支持自定义日、周、月、季、年周期。\\r\\n2、回测报告的出金/入金为周内的出金/入金的和。\\r\\n3、不同函数对当周数据的引用不同，使用时需注意函数用法，如：\\r\\nMA(X,N)函数N的取值：当周如果k线小于N根，则返回空值。如果k线为大于等于N根，则取N。\\r\\nHHV(X,N)函数N的取值：当天如果k线小于N根，则返回实际根数，如果k线为大于等于N根，则取N。\\r\\n4、模型中不能同时使用DAYTRADE1\\DAYTRADE\\WEEKTRADE\\WEEKTRADE1\\MONTHTRADE\\QUARTERTRADE\\YEARTRADE函数。\\r\\n5、（1）历史回测中，当周K线走完持仓大于0，会对持仓进行全清处理。\\r\\n   （2）模组运行中，当周K线走完持仓大于0，信号和资金会重新初始化进行计算，但不会自动对持仓进行全清处理，需要在模型中编写实现全清。\\r\\n\\r\\n例：\\r\\nMA5^^MA(C,5);\\r\\nMA10^^MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;//5周期均线上穿10周期均线，买开仓\\r\\nCROSSDOWN(MA5,MA10),SK;//5周期均线下穿10周期均线，卖开仓\\r\\nC<BKPRICE-10*MINPRICE,SP;//亏损10点平多\\r\\nC>SKPRICE+10*MINPRICE,BP;//亏损10点平空\\r\\nCLOSEMINUTE<=1,CLOSEOUT;//收盘前一分钟，清仓。\\r\\nAUTOFILTER;//过滤模型\\r\\nWEEKTRADE1;//只用周内数据进行计算", "explanation": "周内交易函数", "markettype": 1, "modifytime": "", "param": "", "tip": "WEEKTRADE1周内交易函数，且历史数据不参与计算。", "type": 9}, "WINNER": {"body": "WINNER( )", "createtime": "********", "description": "WINNER 获利盘比例\\r\\n用法:\\r\\n WINNER(CLOSE),表示以当前收市价卖出的获利盘比例,例如返回0.1表示10%获利盘;WINNER(10.5)表示10.5元价格的获利盘比例\\r\\n\\r\\n注：\\r\\n 该函数仅对日线分析周期有效\\r\\n\\r\\n算法：\\r\\n 统计小于等于当前收盘价的K线成交量之和与所有K线成交量之和的比值；", "explanation": "获利盘比例", "markettype": 0, "modifytime": "", "param": "", "tip": "WINNER()，获利盘比例", "type": 2}, "WORD": {"body": "WORD( , )", "createtime": "", "description": "WORD,显示文字。\\r\\n\\r\\n用法：WORD(TYPE,TEXT) 当TYPE为1，则在K线最高价位置书写文字TEXT；不为1则在最低价位置书写文字TEXT。\\r\\n注：\\r\\n1：该函数与判断条件连用，如COND,WORD(TYPE,TEXT)。\\r\\n2、该函数可以用ALIGN，VALIGN设置文字的对齐方式。\\r\\n3、该函数可以用FONTSIZE设置文字显示的字体大小。\\r\\n4、该函数可以用COLOR来设置文字显示的颜色。\\r\\n\\r\\n例1：\\r\\nCLOSE>OPEN,WORD(1,'阳'),ALIGN0,VALIGN0,FONTSIZE54,COLORRED;//表示K线收盘大于开盘时，在最高价上写\"阳\"字，文字左上对齐，字体大小为54，颜色为红色。", "explanation": "显示文字", "markettype": 0, "modifytime": "", "param": "", "tip": "WORD,显示文字", "type": 8}, "YCLOSE": {"body": "YCLOSE", "createtime": "20170508", "description": "取得K线图的昨收盘价。\\r\\n用法：\\r\\nYCLOSE求某根K线的昨收盘价。\\r\\n注：\\r\\n1、用在周期小于'日'的K线上如5分钟K线，一小时k线，每根K线返回的值为前一天的收盘价\\r\\n2、该函数不支持跨周期或跨合约引用。\\r\\n3、主要用于股票合约取昨收盘价。", "explanation": "取得K线图的昨收盘价", "markettype": 1, "modifytime": "", "param": "", "tip": "YCLOSE求某根K线的昨收盘价。", "type": 1}, "YEAR": {"body": "YEAR", "createtime": "", "description": "YEAR，取得年份。\\r\\n \\r\\n注：\\r\\nYEAR的取值范围为1970—2033。\\r\\n \\r\\n例1：\\r\\nN:=BARSLAST(YEAR<>REF(YEAR,1))+1; \\r\\nHH:=REF(HHV(H,N),N);\\r\\nLL:=REF(LLV(L,N),N); \\r\\nOO:=REF(VALUEWHEN(N=1,O),N);\\r\\nCC:=REF(C,N);//取上一年的最高价，最低价，开盘价，收盘价。\\r\\n例2：\\r\\nNN:=IFELSE(YEAR>=2000 AND MONTH>=1,0,1);", "explanation": "年份", "markettype": 0, "modifytime": "", "param": "", "tip": "YEAR取得年份（1970-2033）", "type": 7}, "YEARTRADE": {"body": "YEARTRADE", "createtime": "20141225", "description": "YEARTRADE 年内交易函数。\\r\\n\\r\\n用法：\\r\\nYEARTRADE 模型中写入该函数，信号和资金每年重新初始化进行计算，与历史割裂。\\r\\n\\r\\n注：\\r\\n1、该函数不支持年周期，其他周期均支持。\\r\\n2、回测报告中的出金/入金，为每年出金/入金的和。\\r\\n3、模型中不能同时使用DAYTRADE1\\DAYTRADE\\WEEKTRADE\\WEEKTRADE1\\MONTHTRADE\\QUARTERTRADE\\YEARTRADE函数。\\r\\n4、（1）历史回测中，当年K线走完持仓大于0，会对持仓进行全清处理。\\r\\n   （2）模组运行中，当年K线走完持仓大于0，信号和资金会重新初始化进行计算，但不会自动对持仓进行全清处理，需要在模型中编写实现全清。\\r\\n\\r\\n例：\\r\\nMA5^^MA(C,5);\\r\\nMA10^^MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;//5周期均线上穿10周期均线，买开仓\\r\\nCROSSDOWN(MA5,MA10),SK;//5周期均线下穿10周期均线，卖开仓\\r\\nC<BKPRICE-10*MINPRICE,SP;//亏损10点平多\\r\\nC>SKPRICE+10*MINPRICE,BP;//亏损10点平空\\r\\nCLOSEMINUTE<=1,CLOSEOUT;//收盘前一分钟，清仓。\\r\\nAUTOFILTER;//过滤模型\\r\\nYEARTRADE;//使用每年数据计算", "explanation": "年内交易函数", "markettype": 1, "modifytime": "", "param": "", "tip": "YEARTRADE,年内交易函数", "type": 9}, "YEARTRADE1": {"body": "YEARTRADE1", "createtime": "20180117", "description": "YEARTRADE1 年内交易函数。\\r\\n\\r\\n用法：\\r\\nYEARTRADE1 模型中写入该函数，信号和资金每年重新初始化进行计算，与历史割裂，并且每一个函数只使用当年K线数据进行计算，历史数据不参与计算。\\r\\n\\r\\n注：\\r\\n1、该函数不支持年周期，其他周期均支持。\\r\\n2、回测报告中的出金/入金，为每年出金/入金的和。\\r\\n3、模型中不能同时使用DAYTRADE1\\DAYTRADE\\WEEKTRADE\\WEEKTRADE1\\MONTHTRADE\\MONTHTRADE1\\QUARTERTRADE\\QUARTERTRADE1\\YEARTRADE\\YEARTRADE1函数。\\r\\n4、（1）历史回测中，当年K线走完持仓大于0，会对持仓进行全清处理。\\r\\n   （2）模组运行中，当年K线走完持仓大于0，信号和资金会重新初始化进行计算，但不会自动对持仓进行全清处理，需要在模型中编写实现全清。\\r\\n\\r\\n例：\\r\\nMA5^^MA(C,5);\\r\\nMA10^^MA(C,10);\\r\\nCROSSUP(MA5,MA10),BK;//5周期均线上穿10周期均线，买开仓\\r\\nCROSSDOWN(MA5,MA10),SK;//5周期均线下穿10周期均线，卖开仓\\r\\nC<BKPRICE-10*MINPRICE,SP;//亏损10点平多\\r\\nC>SKPRICE+10*MINPRICE,BP;//亏损10点平空\\r\\nCLOSEMINUTE<=1,CLOSEOUT;//收盘前一分钟，清仓。\\r\\nAUTOFILTER;//过滤模型\\r\\nYEARTRADE1;//使用每年数据计算", "explanation": "年内交易函数", "markettype": 1, "modifytime": "", "param": "", "tip": "YEARTRADE1年内交易函数，且历史数据不参与计算。", "type": 9}, "YSETTLE": {"body": "YSETTLE", "createtime": "", "description": "取得K线图的昨结算价。\\r\\n用法：\\r\\nYSETTLE求某根k线的昨结算价\\r\\n说明：\\r\\n1、用在周期小于'日'的K线上如5分钟K线，1小时k线，每根k线返回的值为前一天的结算价\\r\\n2、该函数支持跨周期或跨合约引用", "explanation": "取得K线图的昨结算价", "markettype": 1, "modifytime": "", "param": "", "tip": "YSETTLE,求某根k线的昨结算价", "type": 1}}