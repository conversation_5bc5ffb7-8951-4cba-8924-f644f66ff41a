@echo off
echo 安装交易网关服务

rem 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 请以管理员身份运行此脚本！
    echo 右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b
)

rem 获取Python路径
for /f "tokens=*" %%i in ('where python') do set PYTHON_PATH=%%i

rem 检查pywin32是否安装
%PYTHON_PATH% -c "import win32service" >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装pywin32库...
    %PYTHON_PATH% -m pip install pywin32
    if %errorlevel% neq 0 (
        echo 安装pywin32失败，请手动运行: pip install pywin32
        pause
        exit /b
    )
)

rem 安装服务
echo 正在安装交易网关服务...
%PYTHON_PATH% "%~dp0SmartOrderManagerGateWay.py" install
if %errorlevel% neq 0 (
    echo 安装服务失败！
    pause
    exit /b
)

rem 启动服务
echo 正在启动服务...
%PYTHON_PATH% "%~dp0SmartOrderManagerGateWay.py" start
if %errorlevel% neq 0 (
    echo 启动服务失败！
    pause
    exit /b
)

echo 交易网关服务已成功安装并启动！
echo 服务将在Windows启动时自动运行。
pause
