import os
import time
import json
import pickle
import threading
import datetime
import random
import mmap
import struct
import logging
import psutil
import numpy as np
from threading import Lock
from logging.handlers import TimedRotatingFileHandler
from xtquant.xttype import StockAccount
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback

# 共享内存区域常量定义
MEM_SIZE = 1024 * 1024  # 1MB共享内存区
HEADER_SIZE = 64        # 头部信息大小
COMMAND_SIZE = 256 * 1024  # 命令区域大小
RESPONSE_SIZE = 256 * 1024  # 响应区域大小
DATA_SIZE = 512 * 1024  # 数据区域大小

# 共享内存区域名称
SHMEM_NAME = "QMT_TRADING_GATEWAY"
SHMEM_FILE = os.path.join(os.path.expanduser("~"), "QMT_TRADING_GATEWAY.dat")

# 状态和命令常量
CMD_NONE = 0
CMD_CONNECT = 1
CMD_PLACE_ORDER = 2
CMD_CANCEL_ORDER = 3
CMD_QUERY = 4
CMD_SHUTDOWN = 99

RESP_NONE = 0
RESP_SUCCESS = 1
RESP_FAILURE = 2
RESP_DATA = 3

# 数据区域索引
DATA_ACCOUNT = 0
DATA_POSITIONS = 1
DATA_ORDERS = 2
DATA_TRADES = 3
DATA_STATUS = 4

class SharedMemory:
    """共享内存管理类"""
    
    def __init__(self, create=False):
        self.shmem_file = SHMEM_FILE
        self.lock = Lock()
        self.is_initialized = False
        
        if create:
            # 创建或清空共享内存文件
            with open(self.shmem_file, 'wb') as f:
                f.write(b'\x00' * MEM_SIZE)
            
            # 初始化共享内存区域
            with open(self.shmem_file, 'r+b') as f:
                mm = mmap.mmap(f.fileno(), MEM_SIZE)
                # 初始化头部
                mm.seek(0)
                mm.write(struct.pack('I', 0))  # 命令标志
                mm.write(struct.pack('I', 0))  # 响应标志
                mm.write(struct.pack('Q', int(time.time())))  # 创建时间戳
                mm.write(struct.pack('I', 0))  # 心跳标志
                mm.close()
            
        # 检查共享内存文件是否存在
        if not os.path.exists(self.shmem_file):
            raise FileNotFoundError(f"共享内存文件 {self.shmem_file} 不存在")
        
        # 打开共享内存
        self.file = open(self.shmem_file, 'r+b')
        self.mm = mmap.mmap(self.file.fileno(), MEM_SIZE)
        self.is_initialized = True
    
    def __del__(self):
        """析构函数，关闭共享内存"""
        if hasattr(self, 'mm') and self.mm:
            self.mm.close()
        if hasattr(self, 'file') and self.file:
            self.file.close()
    
    def set_command(self, cmd, data=None):
        """设置命令"""
        with self.lock:
            # 写入命令区域
            self.mm.seek(0)
            self.mm.write(struct.pack('I', cmd))  # 命令标志
            
            # 写入命令数据
            if data:
                self.mm.seek(HEADER_SIZE)
                serialized = pickle.dumps(data)
                self.mm.write(serialized[:COMMAND_SIZE])
            
            # 不再在这里更新心跳，心跳更新由service_loop统一管理
    
    def get_command(self):
        """获取命令"""
        with self.lock:
            self.mm.seek(0)
            cmd = struct.unpack('I', self.mm.read(4))[0]
            
            if cmd != CMD_NONE:
                # 读取命令数据
                self.mm.seek(HEADER_SIZE)
                data = self.mm.read(COMMAND_SIZE)
                try:
                    # 尝试反序列化直到遇到EOF
                    data = pickle.loads(data)
                except:
                    data = None
                
                return cmd, data
            
            return CMD_NONE, None
    
    def clear_command(self):
        """清除命令"""
        with self.lock:
            self.mm.seek(0)
            self.mm.write(struct.pack('I', CMD_NONE))
    
    def set_response(self, resp, data=None):
        """设置响应"""
        with self.lock:
            # 写入响应区域
            self.mm.seek(4)
            self.mm.write(struct.pack('I', resp))  # 响应标志
            
            # 写入响应数据
            if data:
                self.mm.seek(HEADER_SIZE + COMMAND_SIZE)
                serialized = pickle.dumps(data)
                self.mm.write(serialized[:RESPONSE_SIZE])
    
    def get_response(self):
        """获取响应"""
        with self.lock:
            self.mm.seek(4)
            resp = struct.unpack('I', self.mm.read(4))[0]
            
            if resp != RESP_NONE:
                # 读取响应数据
                self.mm.seek(HEADER_SIZE + COMMAND_SIZE)
                data = self.mm.read(RESPONSE_SIZE)
                try:
                    # 尝试反序列化直到遇到EOF
                    data = pickle.loads(data)
                except:
                    data = None
                
                return resp, data
            
            return RESP_NONE, None
    
    def clear_response(self):
        """清除响应"""
        with self.lock:
            self.mm.seek(4)
            self.mm.write(struct.pack('I', RESP_NONE))
    
    def set_data(self, idx, data):
        """设置数据区域"""
        with self.lock:
            offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + idx * DATA_SIZE // 5
            self.mm.seek(offset)
            serialized = pickle.dumps(data)
            self.mm.write(serialized[:DATA_SIZE // 5])
    
    def get_data(self, idx):
        """获取数据区域"""
        with self.lock:
            offset = HEADER_SIZE + COMMAND_SIZE + RESPONSE_SIZE + idx * DATA_SIZE // 5
            self.mm.seek(offset)
            data = self.mm.read(DATA_SIZE // 5)
            try:
                return pickle.loads(data)
            except:
                return None
    
    def is_gateway_running(self):
        """检查网关是否运行中"""
        with self.lock:
            self.mm.seek(16)
            heartbeat = struct.unpack('I', self.mm.read(4))[0]
            current_time = int(time.time())
            # 心跳超过10秒视为网关不在运行
            return current_time - heartbeat < 10

# 交易回调对象
class GatewayCallback(XtQuantTraderCallback):
    def __init__(self, gateway):
        self.gateway = gateway
    
    def on_disconnected(self):
        self.logger.warning("[网关] 连接断开")
        self.gateway.connected = False
        self.gateway.status["connected"] = False
        self.gateway.status["last_disconnect"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    def on_order_stock_async_response(self, response):
        self.logger.info(f"[网关] 异步下单响应: {response.order_id}, 备注: {response.order_remark}")
    def on_stock_order(self, order):
        self.logger.info(f"[网关] 委托回报: {order.order_id}, 状态: {order.order_status}")
        with self.gateway.lock:
            self.gateway.orders[order.order_id] = {
                "order_id": order.order_id,
                "stock_code": order.stock_code,
                "order_status": order.order_status,
                "order_time": order.order_time,
                "traded_volume": order.traded_volume,
                "traded_price": order.traded_price,
                "order_volume": order.order_volume,
                "price": order.price,
                "order_remark": order.order_remark
            }
    
    def on_stock_trade(self, trade):
        self.logger.info(f"[网关] 成交回报: {trade.order_id}, 数量: {trade.traded_volume}")
        with self.gateway.lock:
            # 保存成交记录
            trade_key = f"{trade.order_id}_{trade.traded_id}"
            self.gateway.trades[trade_key] = {
                "order_id": trade.order_id,
                "stock_code": trade.stock_code,
                "traded_id": trade.traded_id,
                "traded_time": trade.traded_time,
                "traded_volume": trade.traded_volume,
                "traded_price": trade.traded_price
            }
            
            # 更新订单信息
            if trade.order_id in self.gateway.orders:
                self.gateway.orders[trade.order_id]["traded_volume"] = trade.traded_volume
                self.gateway.orders[trade.order_id]["traded_price"] = trade.traded_price
    
    def on_order_error(self, error):
        self.logger.error(f"[网关] 委托错误: {error.error_msg}")
        self.gateway.status["last_error"] = {
            "time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "message": error.error_msg,
            "order_id": error.order_id
        }

class TradingGatewayService:
    """交易网关服务类"""
    def __init__(self):
        # 加载配置文件
        self.config = self._load_config()
        # 初始化日志系统
        self.logger = self._init_logger()
        # 创建共享内存
        self.shmem = SharedMemory(create=True)
        self.callback = GatewayCallback(self)
        # 交易API相关
        self.client_path = None
        self.account_id = None
        self.trader = None
        self.account = None
        self.connected = False
        self.session_id = None
        
        # 数据缓存
        self.account_data = {}
        self.positions = {}
        self.orders = {}
        self.trades = {}
        self.status = {"connected": False, "start_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
        
        # 同步锁
        self.lock = Lock()
        
        # 服务线程
        self.running = False
        self.service_thread = None

    def _load_config(self):
        """从同目录加载配置文件"""
        config_path = os.path.join(os.path.dirname(__file__), "gateway_config.json")
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {str(e)}")
            raise SystemExit("无法启动服务：缺少配置文件")

    def _init_logger(self):
        """初始化日志记录器"""
        logger = logging.getLogger("TradingGateway")
        logger.setLevel(logging.INFO)
        
        # 创建每天轮转的日志文件
        handler = TimedRotatingFileHandler(
            filename="gateway_service.log",
            when="midnight",
            backupCount=7,
            encoding='utf-8'
        )
        formatter = logging.Formatter(
            '[%(asctime)s] [%(levelname)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        return logger
    
    def start(self):
        """启动交易网关服务"""
        if self.service_thread and self.service_thread.is_alive():
            self.logger.info("服务已在运行")
            return
        
        self.running = True
        self.service_thread = threading.Thread(target=self._service_loop, daemon=True)
        self.service_thread.start()
        
        time.sleep(0.5)
        if self.service_thread.is_alive():
            self.logger.info("服务线程已成功启动")
        else:
            self.logger.error("服务线程未能启动")
        
        # 更新状态
        self.status["running"] = True
        self.shmem.set_data(DATA_STATUS, self.status)
    
    def _service_loop(self):
        """增强的服务循环"""
        self.logger.info("服务循环已启动")
        last_health_check = time.time()
        last_heartbeat = time.time()
        reconnect_attempts = 0
        client_path = self.config["client_path"]
        account_id = self.config["account_id"]
        
        # 初始化状态记录启动信息
        init_ts = time.time()
        init_time_str = datetime.datetime.fromtimestamp(init_ts).strftime("%Y-%m-%d %H:%M:%S")
        self.status = {
            "running": True,
            "connected": self.connected,
            "last_heartbeat": init_time_str,
            "last_heartbeat_timestamp": init_ts,
            "is_healthy": False,  # 初始状态设为未健康
            "start_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        self.shmem.set_data(DATA_STATUS, self.status)
        
        while self.running:
            current_time = time.time()
            
            # 健康检查（每分钟）
            if current_time - last_health_check > 60:
                healthy = self._health_check()
                # 健康检查会更新self.status，包括is_healthy字段
                last_health_check = current_time
            
            # 自动重连逻辑
            if not self.connected and reconnect_attempts < self.config.get("max_reconnect", 3):
                self.logger.warning("检测到连接断开，尝试自动重连...")
                if self._auto_reconnect(client_path, account_id):
                    reconnect_attempts = 0
                else:
                    reconnect_attempts += 1
                    time.sleep(5)

            # 处理命令
            try:
                cmd, data = self.shmem.get_command()
                if cmd > 0:
                    self._handle_command(cmd, data)
            except Exception as e:
                self.logger.error(f"[网关] 处理命令异常: {e}")
            
            # 添加直接更新心跳的代码，每10秒更新一次
            if current_time - last_heartbeat >= 10.0:
                heartbeat_value = int(current_time)
                
                # 1. 更新心跳标志
                with self.shmem.lock:
                    self.shmem.mm.seek(16)
                    self.shmem.mm.write(struct.pack('I', heartbeat_value))
                
                # 2. 获取当前状态信息（保留is_healthy字段）
                current_status = self.shmem.get_data(DATA_STATUS) or {}
                is_healthy = current_status.get("is_healthy", False)
                
                # 3. 更新状态信息
                heartbeat_time_str = datetime.datetime.fromtimestamp(current_time).strftime("%Y-%m-%d %H:%M:%S")
                self.status = {
                    "running": True,
                    "connected": self.connected,
                    "last_heartbeat": heartbeat_time_str,
                    "last_heartbeat_timestamp": current_time,
                    "is_healthy": is_healthy,  # 保留当前的健康状态
                    "start_time": self.status.get("start_time")
                }
                
                # 4. 写入更新后的状态
                self.shmem.set_data(DATA_STATUS, self.status)
                self.logger.debug(f"[网关] 已更新心跳: {heartbeat_value}, 健康状态: {is_healthy}")
                
                last_heartbeat = current_time
            
            time.sleep(0.1)  # 避免CPU占用过高
        
        self.logger.info("[网关] 服务循环已退出")
    
    def _handle_command(self, cmd, data):
        """处理命令"""
        if cmd == CMD_CONNECT:
            # 连接命令
            result = self._connect(data.get("client_path"), data.get("account_id"))
            self.shmem.set_response(RESP_SUCCESS if result else RESP_FAILURE, 
                                   {"success": result, "message": "连接成功" if result else "连接失败"})
        
        elif cmd == CMD_PLACE_ORDER:
            # 下单命令
            order_id = self._place_order(
                data.get("symbol"),
                data.get("direction"),
                data.get("quantity"),
                data.get("price_type"),
                data.get("price"),
                data.get("strategy_name", "auto_trading"),
                data.get("remark", "")
            )
            self.shmem.set_response(RESP_SUCCESS if order_id else RESP_FAILURE, 
                                   {"success": bool(order_id), "order_id": order_id})
        
        elif cmd == CMD_CANCEL_ORDER:
            # 撤单命令
            result = self._cancel_order(data.get("order_id"))
            self.shmem.set_response(RESP_SUCCESS if result else RESP_FAILURE, 
                                   {"success": result})
        
        elif cmd == CMD_QUERY:
            # 查询命令
            query_type = data.get("query_type")
            result = None
            
            if query_type == "account":
                result = self._query_account()
            elif query_type == "positions":
                result = self._query_positions()
            elif query_type == "orders":
                result = self._query_orders()
            elif query_type == "trades":
                result = self._query_trades()
            elif query_type == "order":
                result = self._query_order(data.get("order_id"))
            
            self.shmem.set_response(RESP_DATA, {"data": result})
        
        elif cmd == CMD_SHUTDOWN:
            # 关闭命令
            self._shutdown()
            self.shmem.set_response(RESP_SUCCESS, {"message": "服务已关闭"})
    
    def _connect(self, client_path, account_id):
        """连接交易接口"""
        try:            
            if not client_path or not account_id:
                self.logger.error("[网关] 客户端路径或账户ID为空")
                return False
            
            self.client_path = client_path
            self.account_id = account_id
            self.session_id = int(time.time())
                        
            # 创建交易对象
            self.trader = XtQuantTrader(self.client_path, self.session_id)
            self.account = StockAccount(self.account_id)
            
            # 注册回调
            self.trader.register_callback(self.callback)
            
            # 启动交易线程
            self.trader.start()
            
            # 建立连接
            connect_result = self.trader.connect()
            if connect_result == 0:
                self.connected = True
                self.status["connected"] = True
                self.status["last_connect"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.logger.info("[网关] 交易服务器连接成功")
                
                # 订阅交易推送
                subscribe_result = self.trader.subscribe(self.account)
                if subscribe_result == 0:
                    self.logger.info("[网关] 交易主推订阅成功")
                else:
                    self.logger.error(f"[网关] 交易主推订阅失败，错误码: {subscribe_result}")
                
                # 初始查询数据
                self._update_data()
                
                return True
            else:
                self.logger.error(f"[网关] 交易服务器连接失败，错误码: {connect_result}")
                return False
                
        except Exception as e:
            self.logger.error(f"[网关] 连接异常: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False
    
    def _place_order(self, symbol, direction, quantity, price_type, price, strategy_name="auto_trading", remark=""):
        """下单"""
        if not self.trader or not self.account or not self.connected:
            self.logger.error("[网关] 交易对象或账号未初始化，或连接未建立")
            return None
        
        try:
            order_id = self.trader.order_stock(
                self.account, 
                symbol, 
                direction, 
                quantity, 
                price_type, 
                price, 
                strategy_name, 
                remark
            )
            
            if order_id:
                self.logger.info(f"[网关] 下单成功，订单ID: {order_id}")
                return order_id
            else:
                self.logger.info("[网关] 下单失败")
                return None
                
        except Exception as e:
            self.logger.error(f"[网关] 下单异常: {str(e)}")
            return None
    
    def _cancel_order(self, order_id):
        """撤单"""
        if not self.trader or not self.account or not self.connected:
            self.logger.error("[网关] 交易对象或账号未初始化，或连接未建立")
            return False
        
        try:
            result = self.trader.cancel_order_stock(self.account, order_id)
            if result == 0:
                self.logger.info(f"[网关] 撤单成功，订单ID: {order_id}")
                return True
            else:
                self.logger.error(f"[网关] 撤单失败，订单ID: {order_id}, 错误码: {result}")
                return False
                
        except Exception as e:
            self.logger.error(f"[网关] 撤单异常: {str(e)}")
            return False
    
    def _query_account(self):
        """查询账户资产"""
        if not self.trader or not self.account or not self.connected:
            return None
        
        try:
            asset = self.trader.query_stock_asset(self.account)
            if asset:
                self.account_data = {
                    "cash": asset.cash,
                    "frozen_cash": asset.frozen_cash,
                    "market_value": asset.market_value,
                    "total_asset": asset.total_asset,
                    "fetch_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                return self.account_data
            return None
        except Exception as e:
            self.logger.error(f"[网关] 查询账户异常: {str(e)}")
            return None
    
    def _query_positions(self):
        """查询持仓"""
        if not self.trader or not self.account or not self.connected:
            return []
        
        try:
            positions = self.trader.query_stock_positions(self.account)
            self.positions = {}
            
            for pos in positions:
                self.positions[pos.stock_code] = {
                    "stock_code": pos.stock_code,
                    "volume": pos.volume,
                    "can_use_volume": pos.can_use_volume,
                    "avg_price": pos.avg_price,
                    "market_value": pos.market_value,
                    "cost_price": pos.cost_price if hasattr(pos, 'cost_price') else 0
                }
            
            return self.positions
        except Exception as e:
            self.logger.error(f"[网关] 查询持仓异常: {str(e)}")
            return {}
    
    def _query_orders(self):
        """查询当日委托"""
        if not self.trader or not self.account or not self.connected:
            return []
        
        try:
            orders = self.trader.query_stock_orders(self.account)
            
            # 更新订单字典
            with self.lock:
                for order in orders:
                    self.orders[order.order_id] = {
                        "order_id": order.order_id,
                        "stock_code": order.stock_code,
                        "order_status": order.order_status,
                        "order_time": order.order_time,
                        "traded_volume": order.traded_volume,
                        "traded_price": order.traded_price,
                        "order_volume": order.order_volume,
                        "price": order.price,
                        "order_remark": order.order_remark
                    }
            
            return self.orders
        except Exception as e:
            self.logger.error(f"[网关] 查询委托异常: {str(e)}")
            return {}
    
    def _query_trades(self):
        """查询当日成交"""
        if not self.trader or not self.account or not self.connected:
            return []
        
        try:
            trades = self.trader.query_stock_trades(self.account)
            
            # 更新成交字典
            with self.lock:
                for trade in trades:
                    trade_key = f"{trade.order_id}_{trade.traded_id}"
                    self.trades[trade_key] = {
                        "order_id": trade.order_id,
                        "stock_code": trade.stock_code,
                        "traded_id": trade.traded_id,
                        "traded_time": trade.traded_time,
                        "traded_volume": trade.traded_volume,
                        "traded_price": trade.traded_price
                    }
            
            return self.trades
        except Exception as e:
            self.logger.error(f"[网关] 查询成交异常: {str(e)}")
            return {}
    
    def _query_order(self, order_id):
        """查询单个订单"""
        if not self.trader or not self.account or not self.connected:
            return None
        
        try:
            order = self.trader.query_stock_order(self.account, order_id)
            if order:
                return {
                    "order_id": order.order_id,
                    "stock_code": order.stock_code,
                    "order_status": order.order_status,
                    "order_time": order.order_time,
                    "traded_volume": order.traded_volume,
                    "traded_price": order.traded_price,
                    "order_volume": order.order_volume,
                    "price": order.price,
                    "order_remark": order.order_remark
                }
            return None
        except Exception as e:
            self.logger.error(f"[网关] 查询订单异常: {str(e)}")
            return None
    
    def _update_data(self):
        """更新所有数据"""
        try:
            # 查询并更新账户数据
            self._query_account()
            self.shmem.set_data(DATA_ACCOUNT, self.account_data)
            
            # 查询并更新持仓数据
            self._query_positions()
            self.shmem.set_data(DATA_POSITIONS, self.positions)
            
            # 查询并更新订单数据
            self._query_orders()
            self.shmem.set_data(DATA_ORDERS, self.orders)
            
            # 查询并更新成交数据
            self._query_trades()
            self.shmem.set_data(DATA_TRADES, self.trades)
            
            # 更新状态
            self.shmem.set_data(DATA_STATUS, self.status)
            
        except Exception as e:
            self.logger.error(f"[网关] 更新数据异常: {str(e)}")
    
    def _shutdown(self):
        """关闭服务"""
        self.running = False  # 先停止服务循环
        self.connected = False  # 停止监控线程
        
        if self.trader:
            try:
                self.trader.stop()
            except Exception as e:
                self.logger.error(f"停止交易接口异常: {str(e)}")
            finally:
                self.trader = None
        
        self.logger.info("[网关] 服务已关闭")
    
    def run_forever(self):
        """阻塞运行"""
        self.start()
        
        try:
            while self.running and self.service_thread.is_alive():
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("[网关] 收到中断信号，关闭服务...")
            self._shutdown()

    def _auto_reconnect(self,client_path, account_id):
        """自动重连核心逻辑"""
        try:
            # 先停止监控线程
            self.connected = False  # 新增：先停止监控线程循环
            
            # 停止旧连接
            if self.trader:
                try:
                    self.trader.stop()
                except Exception as e:
                    self.logger.error(f"停止交易接口异常: {str(e)}")
                finally:
                    self.trader = None  # 确保置空
            
            # 重建交易对象前等待1秒
            time.sleep(1)
            
            # 创建新会话ID
            self.session_id = int(time.time()) + random.randint(0, 1000)
            
            # 重建交易对象
            self.trader = XtQuantTrader(client_path, self.session_id)  # 使用参数传入的client_path
            self.account = StockAccount(account_id)  # 使用参数传入的account_id
            self.trader.register_callback(self.callback)
            
            # 启动交易线程
            self.trader.start()
            
            # 执行连接
            result = self.trader.connect()
            if result == 0:
                self.connected = True  # 先设置连接状态
                self._after_successful_connection(self.account)
                return True
            return False
        except Exception as e:
            self.logger.error(f"自动重连失败: {str(e)}")
            return False

    def _after_successful_connection(self,account):
        """自动重连的初始化"""
        # 订阅账户
        if account:
            subscribe_result = self.trader.subscribe(account)
            if subscribe_result != 0:
                self.logger.error(f"订阅失败，错误码: {subscribe_result}")
        
        # 初始化数据
        self._update_data()
        
        # 启动监控线程
        self._start_monitor_thread()

    def _start_monitor_thread(self):
        """启动监控线程"""
        def monitor_task():
            # 增加运行状态检查
            while self.running and self.connected:  # 增加self.running判断
                try:
                    # 检查客户端进程
                    if not self._check_qmt_process():
                        self.logger.error("检测到QMT客户端进程异常退出")
                        self.connected = False
                        break
                    
                    # 检查网络连接（增加返回值处理）
                    network_ok = self._check_network()
                    if not network_ok:
                        self.logger.warning("网络连接异常，尝试重新连接...")
                        self.connected = False
                        break
                    
                    time.sleep(30)
                except Exception as e:
                    self.logger.error(f"监控线程异常: {str(e)}")
                    break
        
        threading.Thread(target=monitor_task, daemon=True).start()

    def _check_qmt_process(self):
        """检查QMT客户端进程"""
        try:
            for proc in psutil.process_iter(['name']):
                if proc.info['name'] in ('miniquote.exe', 'XtMiniQmt.exe'):
                    return True
            return False
        except Exception as e:
            self.logger.error(f"进程检查失败: {str(e)}")
            return False

    def _check_network(self):
        """网络连接检查"""
        import socket
        try:
            # 测试连接QMT服务器（示例地址，需替换实际地址）
            sock = socket.create_connection(("127.0.0.1", 80), timeout=3)
            sock.close()
            return True
        except Exception as e:
            self.logger.warning(f"网络连接检查失败: {str(e)}")
            return False

    def _health_check(self):
        """系统健康检查"""
        check_items = {
            "共享内存状态": self.shmem.is_initialized,
            "交易线程状态": bool(self.trader),  # 修改为检查trader是否存在，而不是调用is_alive()
            "客户端路径存在": os.path.exists(self.config["client_path"]),
            "账户余额": self.account_data.get("cash", 0) > 0 if self.account_data else False
        }
        
        is_healthy = all(check_items.values())
        
        # 更新健康状态
        self.status["is_healthy"] = is_healthy
        self.status["last_check"] = int(time.time())
        self.shmem.set_data(DATA_STATUS, self.status)
        
        if not is_healthy:
            self.logger.warning(f"健康检查异常: {check_items}")
        else:
            self.logger.info("健康检查通过")
        
        return is_healthy

# 启动网关服务的脚本示例
def start_gateway_service():
    """启动交易网关服务"""
    gateway = TradingGatewayService()
    gateway.start()
    # 检查是否已连接，若未连接则尝试连接
    if not gateway.connected:
        client_path = gateway.config.get("client_path")
        account_id = gateway.config.get("account_id")
        if client_path and account_id:
            gateway.logger.info("尝试自动连接交易账户...")
            if gateway._connect(client_path, account_id):
                gateway.logger.info("交易账户连接成功")
            else:
                gateway.logger.error("交易账户连接失败")
        else:
            gateway.logger.error("配置文件中缺少客户端路径或账户ID")
    
    gateway.logger.info("启动交易网关服务")
    gateway.run_forever()

if __name__ == "__main__":
    start_gateway_service()