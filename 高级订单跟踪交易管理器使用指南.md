# 高级订单跟踪交易管理器使用指南

## 概述

本文档详细说明了优化后的交易封装类`AdvancedTradingManager`的使用方法。该类解决了机智量化平台中简易交易函数的局限性，提供了完整的订单全寿命周期跟踪机制。

## 主要改进

### 1. 类属性替代全局变量

**原始方式（全局变量）：**
```python
BKS = 0  # 多头开仓状态
SKS = 0  # 空头开仓状态
BPS = 0  # 多头平仓状态
SPS = 0  # 空头平仓状态
```

**优化方式（类属性）：**
```python
class AdvancedTradingManager:
    def __init__(self):
        self.BKS = 0  # 多头开仓状态
        self.SKS = 0  # 空头开仓状态
        self.BPS = 0  # 多头平仓状态
        self.SPS = 0  # 空头平仓状态
```

**优势：**
- 更好的封装性和模块化
- 避免全局变量污染
- 支持多实例管理
- 便于状态管理和调试

### 2. 订单全寿命周期跟踪

**核心跟踪字典：**
```python
self.pending_entry_orders = {}  # 待成交开仓订单
self.pending_exit_orders = {}   # 待成交平仓订单
```

**订单信息结构：**
```python
order_info = {
    'order_id': order_id,           # 订单号
    'direction': 'BUY/SELL',        # 方向
    'action': 'ENTRY/EXIT',         # 开仓/平仓
    'symbol': tcode,                # 合约代码
    'quantity': qty,                # 数量
    'price': price,                 # 价格
    'timestamp': time.time(),       # 时间戳
    'status': 'PENDING',            # 状态
    'retry_count': 0,               # 重试次数
    'original_signal': 'BK/SK/BP/SP' # 原始信号
}
```

## 核心功能

### 1. 智能价格计算

```python
def get_safe_price(self, base_price, tick_offset, direction, tcode):
    """
    获取安全的交易价格，自动处理涨跌停限制
    
    参数:
        base_price: 基础价格
        tick_offset: 跳数偏移
        direction: 方向 ('BUY' 或 'SELL')
        tcode: 合约代码
    """
    tick_size = PriceTick(tcode)
    
    if direction == 'BUY':
        target_price = base_price + tick_offset * tick_size
        upper_limit = Q_UpperLimit(tcode)
        safe_price = min(target_price, upper_limit) if upper_limit > 0 else target_price
    else:  # SELL
        target_price = base_price - tick_offset * tick_size
        lower_limit = Q_LowLimit(tcode)
        safe_price = max(target_price, lower_limit) if lower_limit > 0 else target_price
    
    return safe_price
```

### 2. 订单状态检查

```python
def check_and_update_orders(self):
    """
    检查和更新所有待处理订单状态
    每次策略触发时都必须调用此函数
    """
    current_time = time.time()
    
    # 检查开仓订单
    self._check_entry_orders(current_time)
    
    # 检查平仓订单
    self._check_exit_orders(current_time)
    
    # 清理超时订单
    self._cleanup_timeout_orders(current_time)
```

### 3. 订单状态处理

**订单状态常量：**
```python
ORDER_STATUS = {
    'PENDING': '0',      # 待报
    'SUBMITTED': '1',    # 已报
    'PARTIAL': '2',      # 部分成交
    'FILLED': '6',       # 完全成交
    'CANCELLED': '7',    # 已撤单
    'REJECTED': '9',     # 拒绝
    'ERROR': 'F'         # 错误
}
```

**状态处理逻辑：**
- **已成交 ('6')**: 更新统计信息，移除跟踪
- **失败状态 ('7', '9', 'F')**: 重置状态，允许重新交易
- **超时**: 自动撤单，重置状态
- **其他状态**: 继续等待

### 4. 自动重试机制

```python
def _retry_entry_order(self, order_info):
    """重试开仓订单"""
    if order_info['retry_count'] >= self.max_retry_count:
        return
    
    LogInfo(f"重试开仓订单 - 方向: {order_info['direction']}, "
           f"重试次数: {order_info['retry_count'] + 1}")
    
    # 重新发送订单
    if order_info['original_signal'] == 'BK':
        self.BKS = 0  # 重置状态
        self.tim_trigger_entry(True, False, order_info['quantity'], order_info['symbol'])
    
    self.stats['retry_orders'] += 1
```

## 使用方法

### 1. 创建交易管理器

```python
# 在策略初始化时创建
def initialize(context):
    global trading_manager
    
    trading_manager = AdvancedTradingManager(
        contract_code='SHFE|F|RB|MAIN',  # 基准合约
        entry_tick_offset=2,              # 开仓超价跳数
        exit_tick_offset=1,               # 平仓超价跳数
        order_timeout=300,                # 订单超时时间(秒)
        max_retry_count=2                 # 最大重试次数
    )
```

### 2. 策略主循环

```python
def handle_data(context):
    global trading_manager
    
    # 1. 必须首先调用订单检查（核心功能）
    trading_manager.check_and_update_orders()
    
    # 2. 生成交易信号
    long_signal = False   # 你的多头信号逻辑
    short_signal = False  # 你的空头信号逻辑
    
    # 3. 执行交易
    if long_signal or short_signal:
        results = trading_manager.trigger_entry(long_signal, short_signal, position_size)
        if results:
            LogInfo(f"开仓结果: {results}")
    
    # 4. 平仓管理
    exit_long, exit_short = generate_exit_signals()
    if exit_long or exit_short:
        results = trading_manager.trigger_exit(exit_short, exit_long, position_size)
        if results:
            LogInfo(f"平仓结果: {results}")
```

### 3. 统一接口使用

```python
# 自动判断实时/历史阶段的统一接口
def execute_signals(long_signal, short_signal):
    # 开仓
    results = trading_manager.trigger_entry(long_signal, short_signal, qty)
    
    # 平仓
    results = trading_manager.trigger_exit(exit_short, exit_long, qty)
```

### 4. 状态监控

```python
# 获取交易状态
status = trading_manager.get_trading_status()
print(f"BKS: {status['BKS']}, SKS: {status['SKS']}")
print(f"待处理订单: {status['pending_entry']} + {status['pending_exit']}")

# 获取统计信息
stats = trading_manager.get_statistics()
print(f"成功率: {stats['success_rate']}")
```

## 关键特性

### 1. 防重复开仓

通过状态管理确保同一信号不会重复开仓：
```python
if BK and self.BKS == 0:  # 只有在未开仓状态才能开仓
    # 发送开仓订单
    self.BKS = 1  # 立即标记为已开仓
```

### 2. 超时处理

自动处理超时订单，避免资金占用：
```python
if current_time - order_info['timestamp'] > self.order_timeout:
    A_DeleteOrder(order_id)  # 撤销超时订单
    self.BKS = 0  # 重置状态，允许重新开仓
```

### 3. 异常恢复

订单失败时自动重置状态：
```python
if order_status in ['7', '9', 'F']:  # 失败状态
    self.BKS = 0  # 重置状态
    # 可选：实现重试逻辑
```

### 4. 上期所规则处理

自动处理上期所今昨仓规则：
```python
if ExchangeName(tcode) in ['SHFE', 'INE']:
    # 优先平今仓逻辑
    today_lots = A_TodaySellPosition(tcode)
    if today_lots >= lots:
        A_SendOrder(Enum_Buy(), Enum_ExitToday(), lots, price, tcode)
    else:
        # 部分平今，部分平昨
```

## 配置参数

### 基础配置
```python
contract_code = 'SHFE|F|RB|MAIN'  # 基准合约
entry_tick_offset = 2              # 开仓超价跳数
exit_tick_offset = 1               # 平仓超价跳数
order_timeout = 300                # 订单超时时间(秒)
max_retry_count = 2                # 最大重试次数
```

### 调优建议

1. **超价跳数**：
   - 流动性好的品种：1-2跳
   - 流动性差的品种：2-3跳
   - 实时阶段可以比历史阶段设置更大

2. **超时时间**：
   - 高频策略：60-300秒
   - 中频策略：300-600秒
   - 根据品种流动性调整

3. **重试次数**：
   - 建议1-3次
   - 避免过度重试造成滑点

## 注意事项

### 1. 必须调用订单检查

```python
# 每次handle_data都必须调用
trading_manager.check_and_update_orders()
```

### 2. 状态重置

```python
# 每日开盘前重置
trading_manager.reset_trading_status()

# 强制平仓后重置
trading_manager.cancel_all_pending_orders()
```

### 3. 异常处理

所有交易函数都包含异常处理，但建议在调用前检查：
```python
# 检查持仓状态
if trading_manager.is_real_time():
    position = A_BuyPosition(contract_code)
else:
    position = BuyPosition(contract_code)
```

### 4. 日志监控

关注以下日志信息：
- 订单发送成功/失败
- 订单成交确认
- 超时订单处理
- 状态重置信息

## 性能优势

1. **减少滑点**：智能价格计算，避免过度超价
2. **提高成交率**：自动重试机制，处理临时失败
3. **避免重复开仓**：完善的状态管理
4. **降低风险**：超时保护，异常恢复
5. **提升稳定性**：全面的异常处理

## 总结

优化后的交易封装类提供了：

- ✅ **类属性状态管理**：替代全局变量，更好的封装性
- ✅ **订单全寿命周期跟踪**：从发送到成交的完整监控
- ✅ **智能价格控制**：自动处理涨跌停限制
- ✅ **自动重试机制**：处理临时失败，提高成交率
- ✅ **超时保护**：避免资金长期占用
- ✅ **异常恢复**：自动重置状态，保证策略连续性
- ✅ **统计监控**：完整的交易统计信息

这套系统已经在Alpha-Tick Pro策略中得到验证，可以显著提升实盘交易的稳定性和成功率。
