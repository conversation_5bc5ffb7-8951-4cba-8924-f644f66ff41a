import time
import talib
import datetime
import numpy as np 
from collections import deque

#参数区
g_params['交易合约'] = 'SHFE|Z|AG|MAIN'    # 交易下单的合约品种,一般用主连
g_params['检查登录状态'] = 1     #1检查登录和资金状态，非1则不检查
g_params['开盘后等待分钟数'] = 3 # 忽略开盘后等待时间内的信号
g_params['回测开关'] = 0        #0启动回测不等于0关闭回测
g_params['策略运行次数'] = 10   #策略运行次数达到后停止策略
g_params['最大持仓'] = 6        #最大单向持仓

g_params['首单下单量'] = 1   #首单下单量
# g_params['固定止盈跳数'] = 5     #固定止盈跳数
# g_params['固定止损跳数'] = 5     #固定止损跳数

g_params['交易方向开关'] = 0   #大于0开多，小于0开空，等于0双向开仓

g_params['开多确认条件1'] = 1  #开多确认条件1
g_params['开多确认条件2'] = 3  #开多确认条件2
g_params['开多确认条件3'] = 1  #开多确认条件3
g_params['开多确认条件4'] = 60 #开多确认条件4
g_params['开多确认条件5'] = 1  #开多确认条件5

g_params['开空确认条件1'] = 1  #开空确认条件1
g_params['开空确认条件2'] = 3  #开空确认条件2
g_params['开空确认条件3'] = 1  #开空确认条件3
g_params['开空确认条件4'] = 60 #开空确认条件4
g_params['开空确认条件5'] = 1  #开空确认条件5

g_params['加仓条件']   = 5  #
g_params['加仓下单量']  = 2  #
g_params['动态跟踪止盈点数']  = 10  #从最高/最低点回撤点数达到后出场

g_params['K线基础时间']     = 'M'        # k线基础时间,tick和秒填"T",分钟填"M",日线填"D"
g_params['K线基础周期']     =  1         # k线周期,K线基础时间的倍数,15分钟填写"15"

g_params['下单超价跳数'] = 1             #下单超价跳数
g_params['总盈利停止交易'] =  5000       #总盈利停止交易
g_params['总亏损停止交易'] = -3000       #总亏损停止交易
g_params['收盘倒计时分钟数设置'] = 1     #距离收盘时间范围不开仓，并清仓  
g_params['订阅数据起点日期'] = 20250601  #订阅数据起点日期
g_params['订阅数据终点日期'] = 20250620  #订阅数据终点日期,大于0时按确定终止日期，小于等于0时不限终止日期
# 日内成交量加权平均价类
class DailyVWAP:
    def __init__(self, contractId=''):
        # 初始化状态变量
        self.contractId = contractId        # 合约ID
        self.last_trading_day_mark = 0      # 上一个交易日标识
        self.last_date = 0                  # 上一个自然日期，用于夜盘第二时段判断
        self.vol = 0.0                      # 日内累计成交量
        self.money = 0.0                    # 日内累计成交额
        self.vwap = 0.0                     # 日内成交量加权平均价
        self.is_first_update = True         # 是否为首次更新
    
    def _get_trading_day_mark(self, current_date, current_time):
        """生成交易日标识
        
        Args:
            current_date: 当前自然日期(Date()函数返回值，YYYYMMDD格式)
            current_time: 当前时间(Time()函数返回值，0.HHMMSS格式)
            
        Returns:
            int: 交易日标识，如果获取失败则返回0
        """
        try:
            # 获取交易时段开盘时间
            session_open_times = SessionOpenTime(self.contractId)
            if not session_open_times:
                return 0
                
            # 通过交易时段数量判断是否有夜盘
            has_night_session = len(session_open_times) == 3
            
            if not has_night_session:
                # 无夜盘合约：基于第一交易时段开盘时间判断
                first_session_open = session_open_times[0]
                if current_time >= first_session_open:
                    # 当前时间在开盘后，属于当日交易日
                    trading_day_mark = current_date * 1000000 + int(first_session_open * 1000000)
                else:
                    # 当前时间在开盘前，属于前一日交易日
                    prev_date = current_date - 1
                    trading_day_mark = prev_date * 1000000 + int(first_session_open * 1000000)
            else:
                # 有夜盘合约：分两个时段判断
                first_session_open = session_open_times[0]   # 夜盘开盘时间
                second_session_open = session_open_times[1]  # 日盘开盘时间
                
                if current_time >= first_session_open:
                    # 当前时间在夜盘时段，以夜盘开盘时间为准
                    trading_day_mark = current_date * 1000000 + int(first_session_open * 1000000)
                elif current_time >= second_session_open:
                    # 当前时间在日盘时段，需要判断是否切换交易日
                    if self.last_date != 0:
                        date_diff = current_date - self.last_date
                        if date_diff <= 3:
                            # 差值<=3，正常的连续交易日或周末跳跃，交易日不切换
                            trading_day_mark = self.last_trading_day_mark
                        else:
                            # 差值>3，节假日，需要切换交易日
                            trading_day_mark = current_date * 1000000 + int(first_session_open * 1000000)
                    else:
                        # 首次运行，直接使用当前日期
                        trading_day_mark = current_date * 1000000 + int(first_session_open * 1000000)
                else:
                    # 当前时间在所有交易时段之前，属于前一日
                    prev_date = current_date - 1
                    trading_day_mark = prev_date * 1000000 + int(first_session_open * 1000000)
            
            return trading_day_mark
            
        except Exception as e:
            # 如果出现异常，返回0表示无法生成标识
            return 0
    
    def update(self, current_date, current_time, volume, price):
        """更新日内VWAP
        
        Args:
            current_date: 当前日期（Date()函数返回值）
            current_time: 当前时间（Time()函数返回值）
            volume: 当前K线成交量
            price: 当前K线均价
        
        Returns:
            bool: 是否为新的交易日
        """
        is_new_day = False
        
        # 生成当前交易日标识
        current_trading_day_mark = self._get_trading_day_mark(current_date, current_time)
        
        if current_trading_day_mark == 0:
            # 无法生成交易日标识，使用备用方法
            return self._update_fallback(volume, price)
        
        # 判断是否为新交易日
        if self.is_first_update:
            # 首次更新，初始化
            self.last_trading_day_mark = current_trading_day_mark
            self.last_date = current_date
            self.vol = volume
            self.money = volume * price
            self.is_first_update = False
            is_new_day = True
        elif current_trading_day_mark != self.last_trading_day_mark:
            # 检测到新交易日
            self.last_trading_day_mark = current_trading_day_mark
            self.last_date = current_date
            self.vol = volume
            self.money = volume * price
            is_new_day = True
        else:
            # 同一交易日内，累加数据
            self.vol += volume
            self.money += volume * price
            
        # 更新last_date用于下次判断
        self.last_date = current_date
            
        # 计算VWAP
        self.vwap = self.money / self.vol if self.vol > 0 else price
        
        return is_new_day
    
    def _update_fallback(self, volume, price):
        """备用更新方法，当无法获取交易时段时使用"""
        if self.is_first_update:
            self.vol = volume
            self.money = volume * price
            self.is_first_update = False
            return True
        else:
            self.vol += volume
            self.money += volume * price
            
        self.vwap = self.money / self.vol if self.vol > 0 else price
        return False
    
    def get_vwap(self):
        """获取当前VWAP均价线
        
        Returns:
            float: 当前VWAP均价
        """
        return self.vwap
    
    def reset(self):
        """手动重置VWAP数据"""
        self.vol = 0.0                     
        self.money = 0.0
        self.vwap = 0.0
        self.is_first_update = True
        self.last_trading_day_mark = 0
        self.last_date = 0


# 日内MACD指标计算类
class DailyMACD:
    def __init__(self, contractId='', short=12, long=26, signal=9):
        # 初始化状态变量
        self.contractId = contractId        # 合约ID
        self.short = short                  # 短周期EMA参数
        self.long = long                    # 长周期EMA参数
        self.signal = signal                # 信号线EMA参数
        self.last_trading_day_mark = 0      # 上一个交易日标识
        self.last_date = 0                  # 上一个自然日期
        self.daily_closes = []              # 当日收盘价序列
        self.is_first_update = True         # 是否为首次更新
        
        # MACD指标值
        self.diff = 0.0                     # DIFF值(快线-慢线)
        self.dea = 0.0                      # DEA值(信号线)
        self.macd = 0.0                     # MACD柱状图值
        
    def _get_trading_day_mark(self, current_date, current_time):
        """生成交易日标识（与DailyVWAP相同的逻辑）"""
        try:
            session_open_times = SessionOpenTime(self.contractId)
            if not session_open_times:
                return 0
                
            has_night_session = len(session_open_times) == 3
            
            if not has_night_session:
                first_session_open = session_open_times[0]
                if current_time >= first_session_open:
                    trading_day_mark = current_date * 1000000 + int(first_session_open * 1000000)
                else:
                    prev_date = current_date - 1
                    trading_day_mark = prev_date * 1000000 + int(first_session_open * 1000000)
            else:
                first_session_open = session_open_times[0]
                second_session_open = session_open_times[1]
                
                if current_time >= first_session_open:
                    trading_day_mark = current_date * 1000000 + int(first_session_open * 1000000)
                elif current_time >= second_session_open:
                    if self.last_date != 0:
                        date_diff = current_date - self.last_date
                        if date_diff <= 3:
                            trading_day_mark = self.last_trading_day_mark
                        else:
                            trading_day_mark = current_date * 1000000 + int(first_session_open * 1000000)
                    else:
                        trading_day_mark = current_date * 1000000 + int(first_session_open * 1000000)
                else:
                    prev_date = current_date - 1
                    trading_day_mark = prev_date * 1000000 + int(first_session_open * 1000000)
            
            return trading_day_mark
            
        except Exception as e:
            return 0
    
    def update(self, current_date, current_time, close_price):
        """更新日内MACD指标
        
        Args:
            current_date: 当前日期（Date()函数返回值）
            current_time: 当前时间（Time()函数返回值）
            close_price: 当前收盘价
        
        Returns:
            tuple: (is_new_day, diff, dea, macd) - 是否新交易日，DIFF值，DEA值，MACD值
        """
        is_new_day = False
        
        # 生成当前交易日标识
        current_trading_day_mark = self._get_trading_day_mark(current_date, current_time)
        
        if current_trading_day_mark == 0:
            # 无法生成交易日标识，使用备用方法
            return self._update_fallback(close_price)
        
        # 判断是否为新交易日
        if self.is_first_update:
            # 首次更新，初始化
            self.last_trading_day_mark = current_trading_day_mark
            self.last_date = current_date
            self.daily_closes = [close_price]
            self.is_first_update = False
            is_new_day = True
        elif current_trading_day_mark != self.last_trading_day_mark:
            # 检测到新交易日，重置当日数据
            self.last_trading_day_mark = current_trading_day_mark
            self.last_date = current_date
            self.daily_closes = [close_price]
            is_new_day = True
        else:
            # 同一交易日内，累加数据
            self.daily_closes.append(close_price)
            
        # 更新last_date用于下次判断
        self.last_date = current_date
        
        # 计算MACD指标
        self._calculate_macd()
        
        return is_new_day, self.diff, self.dea, self.macd
    
    def _calculate_macd(self):
        """计算MACD指标值"""
        if len(self.daily_closes) == 0:
            self.diff = 0.0
            self.dea = 0.0
            self.macd = 0.0
            return
            
        # 根据当日K线数量决定计算参数
        daily_count = len(self.daily_closes)
        
        # 动态调整计算周期
        if daily_count < self.long:
            # 当日K线数量少于长周期，使用实际数量
            actual_short = min(self.short, daily_count)
            actual_long = daily_count
        else:
            # 当日K线数量足够，使用标准周期
            actual_short = self.short
            actual_long = self.long
            
        # 计算EMA
        import numpy as np
        closes_array = np.array(self.daily_closes)
        
        try:
            # 计算短周期和长周期EMA
            if daily_count >= actual_short:
                ema_short = talib.EMA(closes_array, actual_short)[-1]
            else:
                ema_short = np.mean(closes_array)  # 不足时用简单平均
                
            if daily_count >= actual_long:
                ema_long = talib.EMA(closes_array, actual_long)[-1]
            else:
                ema_long = np.mean(closes_array)   # 不足时用简单平均
            
            # 计算DIFF
            self.diff = ema_short - ema_long
            
            # 计算DEA (DIFF的EMA)
            if not hasattr(self, 'diff_history'):
                self.diff_history = []
            self.diff_history.append(self.diff)
            
            # 限制DIFF历史长度，避免内存过度使用
            if len(self.diff_history) > 100:
                self.diff_history = self.diff_history[-50:]
            
            actual_signal = min(self.signal, len(self.diff_history))
            if len(self.diff_history) >= actual_signal:
                diff_array = np.array(self.diff_history)
                self.dea = talib.EMA(diff_array, actual_signal)[-1]
            else:
                self.dea = np.mean(self.diff_history)
            
            # 计算MACD柱状图
            self.macd = (self.diff - self.dea) * 2
            
        except Exception as e:
            # 计算出错时使用简单方法
            self.diff = 0.0
            self.dea = 0.0
            self.macd = 0.0
    
    def _update_fallback(self, close_price):
        """备用更新方法"""
        if self.is_first_update:
            self.daily_closes = [close_price]
            self.is_first_update = False
            self._calculate_macd()
            return True, self.diff, self.dea, self.macd
        else:
            self.daily_closes.append(close_price)
            self._calculate_macd()
            return False, self.diff, self.dea, self.macd
    
    def get_values(self):
        """获取当前MACD指标值
        
        Returns:
            tuple: (diff, dea, macd)
        """
        return self.diff, self.dea, self.macd
    
    def get_daily_count(self):
        """获取当日K线数量"""
        return len(self.daily_closes)
    
    def reset(self):
        """手动重置MACD数据"""
        self.daily_closes = []
        self.diff = 0.0
        self.dea = 0.0
        self.macd = 0.0
        self.is_first_update = True
        self.last_trading_day_mark = 0
        self.last_date = 0
        if hasattr(self, 'diff_history'):
            self.diff_history = []


CheckLoginStatus,Open_after_minutes,Backtest_sw,StrategyRunCount,MaximumPosition=0,0,0,0,0

lots_o1,fixed_stop_win_tick,fixed_stop_loss_tick,trade_sw=0,0,0,0

long_confirm_condition1,long_confirm_condition2,long_confirm_condition3,long_confirm_condition4,long_confirm_condition5=0,0,0,0,0

short_confirm_condition1,short_confirm_condition2,short_confirm_condition3,short_confirm_condition4,short_confirm_condition5=0,0,0,0,0

add_position_condition,add_position_lots,dynamic_trailing_stop_ticks=0,0,0

scode,k_btime,k_cycle='','',0

ovprice_tick,TotalProfitStopTrading,TotalLossStopTrading,CloseTime,订阅起点日期=0,0,0,0,0

SHORT,LONG,M=12,26,9

# 添加VWAP实例
daily_vwap = None
daily_macd = None

def initialize(context): 
    global daily_vwap, daily_macd
    global g_params,scode,k_btime,k_cycle
    global CheckLoginStatus,Open_after_minutes,Backtest_sw,StrategyRunCount,MaximumPosition
    global lots_o1,fixed_stop_win_tick,fixed_stop_loss_tick,trade_sw
    global long_confirm_condition1,long_confirm_condition2,long_confirm_condition3,long_confirm_condition4,long_confirm_condition5
    global short_confirm_condition1,short_confirm_condition2,short_confirm_condition3,short_confirm_condition4,short_confirm_condition5
    global add_position_condition,add_position_lots,dynamic_trailing_stop_ticks
    global ovprice_tick,TotalProfitStopTrading,TotalLossStopTrading,CloseTime,订阅起点日期

    # 初始化VWAP实例
    daily_vwap = DailyVWAP(scode)
    
    # 初始化MACD实例
    daily_macd = DailyMACD(scode, SHORT, LONG, M)

    CheckLoginStatus = g_params['检查登录状态']
    Open_after_minutes = g_params['开盘后等待分钟数']
    Backtest_sw = g_params['回测开关']
    StrategyRunCount = g_params['策略运行次数']
    MaximumPosition = g_params['最大持仓']

    lots_o1 = g_params['首单下单量']
    # fixed_stop_win_tick = g_params['固定止盈跳数']
    # fixed_stop_loss_tick = g_params['固定止损跳数']
    trade_sw = g_params['交易方向开关']

    long_confirm_condition1 = g_params['开多确认条件1']
    long_confirm_condition2 = g_params['开多确认条件2']
    long_confirm_condition3 = g_params['开多确认条件3']
    long_confirm_condition4 = g_params['开多确认条件4']
    long_confirm_condition5 = g_params['开多确认条件5']

    short_confirm_condition1 = g_params['开空确认条件1']
    short_confirm_condition2 = g_params['开空确认条件2']
    short_confirm_condition3 = g_params['开空确认条件3']
    short_confirm_condition4 = g_params['开空确认条件4']
    short_confirm_condition5 = g_params['开空确认条件5']

    add_position_condition = g_params['加仓条件']
    add_position_lots = g_params['加仓下单量']
    dynamic_trailing_stop_ticks = g_params['动态跟踪止盈点数']

    scode = g_params['交易合约']
    k_btime = g_params['K线基础时间']
    k_cycle = g_params['K线基础周期']

    ovprice_tick = g_params['下单超价跳数']
    TotalProfitStopTrading = g_params['总盈利停止交易']
    TotalLossStopTrading = g_params['总亏损停止交易']
    CloseTime = g_params['收盘倒计时分钟数设置']
    订阅起点日期 = g_params['订阅数据起点日期']
    订阅终点日期 = g_params['订阅数据终点日期']

    订阅起终点=[订阅起点日期*1000000,订阅终点日期*1000000] if 订阅终点日期>0 else str(订阅起点日期)
    LogInfo(订阅起终点,scode)
    SetBarInterval(scode, k_btime, k_cycle,订阅起终点,LONG+M) #订阅交易合约
    cycle_set = 1 if Backtest_sw==0 else 1
    SetBarInterval(scode,'T', cycle_set,订阅起终点,100,isTrigger=True) #订阅交易合约
    SetActual()           #设置实盘运行
    SetOrderWay(1)        #设置K线实时发单
    SetTriggerType(1)
    SetTriggerType(2)
    SetTriggerType(5)     #设置K线触发

ModeStatus=0
BARCNT,BARCNT2=0,0
CrossUpTrigger,CrossDownTrigger=0,0
MACDCrossUPTrigger,MACDCrossDownTrigger=0,0
HCS,sBARS,buys,sells,sassets=deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3),deque([0,0],maxlen=3)
BKVOL,SKVOL,BKPRICE,SKPRICE,BKS,SKS,BPS,SPS,K_status,K_time,HH,LL,_2_Flag_K_LL,_2_Flag_K_HH,BKStatus2,SKStatus2=0,0,0,0,0,0,0,0,0,0,0,0,1e+12,0,0,0
BKStatus,SKStatus,BPStatus,SPStatus,BKProfitability,SKProfitability,BK_time,SK_time,_assets,trade_enable,TradeCount,_TotalProfitLoss=0,0,0,0,0,0,0,0,0,1,0,0   
# 策略触发事件每次触发时都会执行该函数
def handle_data(context):
    global ModeStatus
    global BARCNT,BARCNT2
    global CrossUpTrigger,CrossDownTrigger
    global MACDCrossUPTrigger,MACDCrossDownTrigger
    global BKVOL,SKVOL,BKPRICE,SKPRICE,BKS,SKS,BPS,SPS,K_status,K_time,HH,LL,_2_Flag_K_LL,_2_Flag_K_HH,BKStatus2,SKStatus2
    global BKStatus,SKStatus,BPStatus,SPStatus,BKProfitability,SKProfitability,BK_time,SK_time,_assets,trade_enable,TradeCount,_TotalProfitLoss
    global daily_vwap, daily_macd
    
    O=Open( scode, k_btime, k_cycle)     # 交易合约开盘价格
    H=High( scode, k_btime, k_cycle)     # 交易合约最高价格
    L=Low(  scode, k_btime, k_cycle)     # 交易合约最低价格
    C=Close(scode, k_btime, k_cycle)     # 交易合约收盘价格
    V=Vol(scode, k_btime, k_cycle)    # 交易合约成交量

    tcode = scode
    absolute_seconds,_BuyPosition,_SellPosition = 0,0,0
    if context.strategyStatus()=="C":
        absolute_seconds = int(time.time())
        _BuyPosition  = BKVOL# A_BuyPosition(tcode) 
        _SellPosition = SKVOL# A_SellPosition(tcode)
    else:
        absolute_seconds = int_to_seconds(context.dateTimeStamp())
        _BuyPosition = BuyPosition(tcode)
        _SellPosition = SellPosition(tcode)
    absolute_minutes = int(absolute_seconds / 60)

    MINPRICE=PriceTick(tcode)            # 交易合约最小变动价
        
    #指数计算指标
    if  len(C)<LONG:
       return

    current_date = Date()  # 获取当前日期
    current_volume = V[-1]  # 当前K线成交量
    current_price = (H[-1] + L[-1] + C[-1]) / 3  # 典型价格(HLC均价)
    # 更新VWAP数据
    is_new_day = daily_vwap.update(current_date, Time(), current_volume, current_price)
    # 获取当前VWAP值
    current_vwap = daily_vwap.get_vwap()
    if  CrossUpTrigger==0 and C[-1]>current_vwap:
        CrossUpTrigger=absolute_seconds
        CrossDownTrigger=0
    if  CrossDownTrigger==0 and C[-1]<current_vwap:
        CrossUpTrigger=0
        CrossDownTrigger=absolute_seconds

    # 绘制VWAP线
    PlotNumeric('Close', C[-1], 0x00cccc, False, False,0,"均价线副图")
    PlotNumeric('VWAP', current_vwap, 0xffcccc, False, False,0,"均价线副图")

    # 获取K线结束触发状态
    _VTS=VTS(Time())
    TradeEnbTime =TimeTo_Minutes(_VTS[3])-TimeTo_Minutes(_VTS[2])>CloseTime
    TradeEnbTime2=TimeTo_Minutes(_VTS[2])-TimeTo_Minutes(_VTS[1])>Open_after_minutes
    # LogInfo(Time(),TradeEnbTime,TradeEnbTime2)
    TradeOffTime=CloseTime>=TimeTo_Minutes(_VTS[3])-TimeTo_Minutes(_VTS[2])>0
    # HTS=1 if context.strategyStatus()=="C" else 0   
    s_CurrentBar=CurrentBar(scode, k_btime, k_cycle)
    sBARS.append(s_CurrentBar)
    K_EndTrigger = sBARS[0]>0 and sBARS[1]<sBARS[2]

    # 只在K线结束时更新MACD指标，确保时间粒度与基础K线一致
    if K_EndTrigger or daily_macd.is_first_update:
        # 更新日内MACD指标
        macd_is_new_day, DIFF_current, DEA_current, PW_current = daily_macd.update(current_date, Time(), C[-1])
    else:
        # 非K线结束时，使用上一次的MACD值
        DIFF_current, DEA_current, PW_current = daily_macd.get_values()
    
    # 获取前一个MACD值用于信号判断
    if hasattr(daily_macd, 'prev_diff'):
        DIFF_prev = daily_macd.prev_diff
    else:
        DIFF_prev = DIFF_current
        
    if hasattr(daily_macd, 'prev_macd'):
        PW_prev = daily_macd.prev_macd
    else:
        PW_prev = PW_current
    
    # 只在K线结束时更新前值，避免频繁更新
    if K_EndTrigger:
        daily_macd.prev_diff = DIFF_current
        daily_macd.prev_macd = PW_current
    
    # MACD信号判断
    if PW_current < 0 and PW_prev >= 0:
        MACDCrossUPTrigger = absolute_seconds
    if PW_current > 0 and PW_prev <= 0:
        MACDCrossDownTrigger = absolute_seconds
    
    # 绘制MACD指标到副图
    PlotBar('macd', PW_current, 0, RGB_Red() if PW_current > 0 else RGB_Green(), False, True)  
    PlotNumeric('DIFF', DIFF_current, 0xffffff, False, False)
    PlotNumeric('DEA', DEA_current, 0xaaaa00, False, False)
    
    if K_EndTrigger:
        if BKS>=1 and _BuyPosition==0: BKS=0
        if SKS>=1 and _SellPosition==0: SKS=0 
        BPS=0
        SPS=0     
        BKStatus2=0
        SKStatus2=0
        
        SessionEndFlag=SessionOpenTime(tcode)[-1]<=Time()<SessionCloseTime(tcode)[-1]
        if not SessionEndFlag and TradeEnbTime:
            BARCNT+=1
        else:
            BARCNT=0
        if SessionEndFlag:
            BARCNT2+=1
        else:
            BARCNT2=0
        # LogInfo("sBARS=>",sBARS,"BKS=>",BKS,"SKS=>",SKS,"BARCNT=>",BARCNT,"BARCNT2=>",BARCNT2)       

    BKSingal1=C[-1]>current_vwap+long_confirm_condition1*MINPRICE
    BKSingal2=CrossUpTrigger>0 and absolute_seconds-CrossUpTrigger<long_confirm_condition2*60
    BKSingal3=PW_current> long_confirm_condition3*MINPRICE and PW_current>PW_prev
    BKSingal4=PW_current>0 and absolute_seconds-MACDCrossUPTrigger<long_confirm_condition4*60
    BKSingal5=DIFF_current>long_confirm_condition5*MINPRICE and DIFF_current>DIFF_prev

    SKSingal1=C[-1]<current_vwap-short_confirm_condition1*MINPRICE
    SKSingal2=CrossDownTrigger>0 and absolute_seconds-CrossDownTrigger<short_confirm_condition2*60
    SKSingal3=PW_current<-short_confirm_condition3*MINPRICE and PW_current<PW_prev
    SKSingal4=PW_current<0 and absolute_seconds-MACDCrossDownTrigger<short_confirm_condition4*60
    SKSingal5=DIFF_current<-short_confirm_condition5*MINPRICE and DIFF_current<DIFF_prev

    #主连下单，计算止损止盈价格线
    # MP=MarketPosition(tcode)   #持仓方向
    # MC=CurrentContracts(tcode) #持仓手数
    # AVP=AvgEntryPrice(tcode)   #EntryPrice(tcode) #持仓均价/第一笔建仓价
    HCS.append(context.strategyStatus())


    BK=TradeEnbTime and TradeEnbTime2 and BKSingal1 and BKSingal2 and BKSingal3 and BKSingal4 and BKSingal5
    SK=TradeEnbTime and TradeEnbTime2 and SKSingal1 and SKSingal2 and SKSingal3 and SKSingal4 and SKSingal5
    Checking_add_parameters=not 0 in (add_position_condition,add_position_lots)
    buys.append(_BuyPosition)   
    sells.append(_SellPosition) 
    sassets.append(A_CoverProfit())
    if (buys[2]==0 and buys[1]>0) or (sells[2]==0 and sells[1]>0):
            TradeCount+=1
            _TotalProfitLoss+=(sassets[2]-sassets[1])

    if HCS[2]=='C' and HCS[2]!=HCS[1]:
        SKPRICE=0
        BKPRICE=0
        SKVOL=0
        BKVOL=0
        SPStatus =0
        BPStatus =0
        BKStatus2=0
        SKStatus2=0
        _assets =0
        TradeCount=0
        _TotalProfitLoss=0
        LogInfo("进入实时行情，清理历史回测阶段信号和虚拟持仓")

        # R_BuyPosition =min(BuyPosition(tcode),A_BuyPosition(tcode))
        # R_SellPosition=min(SellPosition(tcode),A_SellPosition(tcode))
        his_trigger_Exit(False,True,20,BuyPosition(tcode))
        his_trigger_Exit(True,False,20,SellPosition(tcode))
        BKS=0
        SKS=0   
        BPS=0
        SPS=0        



    tradestatus = context.strategyStatus()=="C"  #and  ExchangeStatus(ExchangeName(tcode)) in ('1','2','3') #QuoteSvrState() == 1 and TradeSvrState()==1
    if tradestatus:

        if (BPStatus==2 or SPStatus==2 or BPStatus==3 or SPStatus==3 or TradeCount>StrategyRunCount) and _BuyPosition==0 and _SellPosition==0:
            LogInfo(触发交易次数上限强制退出策略) 
    else:
        if is_new_day:
            trade_enable  = Date()
            SPStatus=0
            BPStatus=0
            _assets =0
            TradeCount=0
            LogInfo(Date(),'/',Time(),'新交易日重启策略')        
                
        if (BPStatus==2 or SPStatus==2 or BPStatus==3 or SPStatus==3) and _BuyPosition==0 and _SellPosition==0:
            LogInfo('回测阶段触发账户总亏损清仓停止交易')
            return        

    Assets =  A_Assets() if tradestatus else CurrentEquity()
    if _assets==0 or (HCS[2]=='C' and HCS[2]!=HCS[1]):
        _assets = Assets
    R_Available = _TotalProfitLoss
    if  BKS==0 and BPS==0 and SPS==0 and trade_sw>=0 and _BuyPosition==0 and BK:
        if tradestatus:
            iprc = min(Q_AskPrice(tcode) +ovprice_tick*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价 
            if checklots(R_Available,iprc,lots_o1) or CheckLoginStatus!=1:
                BKPRICE=iprc
                _lots=min(lots_o1,MaximumPosition-BKVOL)
                BKVOL= _lots
                BKStatus2=1
                LogInfo(tcode, "开多仓首单", _lots, "手","下单价",iprc)
                tim_trigger(True,False,_lots,ovprice_tick,tcode)
        elif Backtest_sw==0:
            BKStatus2=1
            LogInfo(tcode, "回测开多仓首单", lots_o1, "手")
            his_trigger(True,False,lots_o1,ovprice_tick)    
        BK_time=absolute_minutes
        BKStatus=0
        SPStatus=0
        _2_Flag_K_HH=0
        BKProfitability=0

    if SKS==0 and BPS==0 and SPS==0 and trade_sw<=0 and _SellPosition==0 and SK:
        if tradestatus:
            iprc = max(Q_BidPrice(tcode) - ovprice_tick*PriceTick(tcode), Q_LowLimit(tcode))  # 对盘超价  
            if checklots(R_Available,iprc,lots_o1) or CheckLoginStatus!=1:
                SKPRICE=iprc
                _lots=min(lots_o1,MaximumPosition-SKVOL)
                SKVOL= _lots
                SKStatus2=1
                LogInfo(tcode, "开空仓首单", _lots, "手","下单价",iprc)
                tim_trigger(False,True,_lots,ovprice_tick,tcode)
        elif Backtest_sw==0:
            SKStatus2=1
            LogInfo(tcode, "回测开空仓首单", lots_o1, "手")
            his_trigger(False,True,lots_o1,ovprice_tick)    
        SK_time=absolute_minutes
        SKStatus=0
        BPStatus=0
        _2_Flag_K_LL=1e+12
        SKProfitability=0

    if _BuyPosition>0: 
        _AVP=BKPRICE if tradestatus else  LastEntryPrice(tcode)
        profit_diff=(C[-1]-_AVP)/MINPRICE
        BKProfitability=max(BKProfitability,profit_diff)
        true_lots= _BuyPosition
        if _AVP>0:
            PlotPartLine("多单开仓线",CurrentBar(),_AVP,2,_AVP,0x00ffff)
            _2_Flag_K_HH = max(_2_Flag_K_HH,H[-1])         
        if BKStatus==0:
            # ll_stop = _AVP - fixed_stop_loss_tick*MINPRICE
            hh_stop = _2_Flag_K_HH-dynamic_trailing_stop_ticks*MINPRICE

            PlotNumeric("多单动态高点",_2_Flag_K_HH,0xaaaa00,True,False)
            PlotNumeric("多单动态止盈线",hh_stop,0xffff00,True,False)

            # if C[-1]<ll_stop:
            #     BKS=1
            #     SPStatus=1
            #     LogInfo(tcode,"多单止损",true_lots,"手")
            #     if tradestatus:
            #         tim_trigger_Exit(False,True,ovprice_tick,tcode,true_lots)    
            #         BKVOL=0
            #         BKPRICE=0 
            #     elif Backtest_sw==0:
            #         his_trigger_Exit(False,True,ovprice_tick,true_lots)
            if _2_Flag_K_HH>0 and C[-1]<hh_stop:
                BKS=1
                SPStatus=1
                # LogInfo('运行==>',TradeCount,'次已超过==>',ModeTurnsCount,'次转为动态止盈模式')
                LogInfo(tcode,"多单动态止盈",true_lots,"手")
                if tradestatus:
                    tim_trigger_Exit(False,True,ovprice_tick,tcode,true_lots)    
                    BKVOL=0
                    BKPRICE=0 
                elif Backtest_sw==0:
                    his_trigger_Exit(False,True,ovprice_tick,true_lots)        
            # if C[-1]>_AVP+fixed_stop_win_tick*MINPRICE:        
            #     BKS=1
            #     SPStatus=1
            #     LogInfo(tcode,"多单止赢",true_lots,"手")
            #     if tradestatus:
            #         tim_trigger_Exit(False,True,ovprice_tick,tcode,true_lots)  
            #         BKVOL=0
            #         BKPRICE=0
            #     elif Backtest_sw==0:
            #         his_trigger_Exit(False,True,ovprice_tick,true_lots)

        elif BKStatus==0 and Checking_add_parameters and absolute_minutes-BK_time>=add_position_condition and C[-1]>_AVP:
            BKStatus=1
            BKS=0
            BK_time=absolute_minutes
            tick_diff=(C[-1]-_AVP)/MINPRICE
            BK_lot=int(add_position_lots)
            LogInfo(tcode, "多单加仓1", BK_lot, "手")
            if tradestatus:
                iprc = min(Q_AskPrice(tcode) +ovprice_tick*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
                if checklots(R_Available,iprc,BK_lot) or CheckLoginStatus!=1:
                    _lots=min(BK_lot,MaximumPosition-BKVOL)
                    BKPRICE=iprc
                    BKVOL+=_lots
                    BKStatus2=1
                    tim_trigger(True,False,_lots,ovprice_tick,tcode)
            elif Backtest_sw==0:
                BKStatus2=1
                his_trigger(True,False,BK_lot,ovprice_tick) 
        elif  TradeOffTime:
            BKS=1
            LogInfo(tcode,"收盘前平多仓",true_lots,"手")
            if tradestatus:
                tim_trigger_Exit(False,True,ovprice_tick,tcode,true_lots)   
                BKVOL=0
                BKPRICE=0 
            elif Backtest_sw==0:
                his_trigger_Exit(False,True,ovprice_tick,true_lots)
        elif SPStatus==0 and _TotalProfitLoss >=TotalProfitStopTrading:
            if tradestatus:
                BKS=1
                SPStatus=2
                LogInfo(tcode,"总盈利止盈",true_lots,"手")
                tim_trigger_Exit(False,True,ovprice_tick,tcode,true_lots)   
                BKVOL=0
                BKPRICE=0 
            # elif Backtest_sw!=0:
            #     BKS=1
            #     his_trigger_Exit(False,True,ovprice_tick,true_lots)
        elif SPStatus==0 and _TotalProfitLoss <=TotalLossStopTrading:
            if tradestatus:
                BKS=1
                SPStatus=3
                LogInfo(tcode,"总亏损止损",true_lots,"手")
                tim_trigger_Exit(False,True,ovprice_tick,tcode,true_lots)     
                BKVOL=0
                BKPRICE=0 
            # elif Backtest_sw!=0:
            #     BKS=1
            #     his_trigger_Exit(False,True,ovprice_tick,true_lots)
   
    if _SellPosition>0: 
        _AVP=SKPRICE if tradestatus else  LastEntryPrice(tcode)
        profit_diff=(_AVP-C[-1])/MINPRICE
        SKProfitability=max(SKProfitability,profit_diff)
        true_lots= _SellPosition
        if _AVP>0:
            PlotPartLine("空单开仓线",CurrentBar(),_AVP,2,_AVP,0x00ffff)
            _2_Flag_K_LL=min(_2_Flag_K_LL,L[-1])
        if SKStatus==0:
            # hh_stop = _AVP + fixed_stop_win_tick*MINPRICE            
            ll_stop = _2_Flag_K_LL+dynamic_trailing_stop_ticks*MINPRICE

            PlotNumeric("空单动态低点",_2_Flag_K_LL,0xaa00aa,True,False)
            PlotNumeric("空单动态止盈线",ll_stop,0xff00ff,True,False)

            # if C[-1]>hh_stop:
            #     SKS=1
            #     BPStatus=1
            #     LogInfo(tcode,"空单止损",true_lots,"手")
            #     if tradestatus:
            #         tim_trigger_Exit(True,False,ovprice_tick,tcode,true_lots)
            #         SKVOL=0
            #         SKPRICE=0
            #     elif Backtest_sw==0:
            #         his_trigger_Exit(True,False,ovprice_tick,true_lots)
            if _2_Flag_K_LL<1e+12 and C[-1]>ll_stop:
                SKS=1
                BPStatus=1
                # LogInfo('运行==>',TradeCount,'次已超过==>',ModeTurnsCount,'次转为动态止盈模式')
                LogInfo(tcode,"空单动态止盈",true_lots,"手")
                if tradestatus:
                    tim_trigger_Exit(True,False,ovprice_tick,tcode,true_lots)
                    SKVOL=0
                    SKPRICE=0
                elif Backtest_sw==0:
                    his_trigger_Exit(True,False,ovprice_tick,true_lots)        
            # if C[-1]<_AVP-fixed_stop_win_tick*MINPRICE:
            #     SKS=1
            #     BPStatus=1
            #     LogInfo(tcode,"空单止赢",true_lots,"手")
            #     if tradestatus:
            #         tim_trigger_Exit(True,False,ovprice_tick,tcode,true_lots)
            #         SKVOL=0
            #         SKPRICE=0
            #     elif Backtest_sw==0:
            #         his_trigger_Exit(True,False,ovprice_tick,true_lots)

        elif SKStatus==0 and Checking_add_parameters and absolute_minutes-SK_time>=add_position_condition and C[-1]<_AVP:
            SKStatus=1
            SKS=0
            SK_time=absolute_minutes
            tick_diff=(_AVP-C[-1])/MINPRICE
            SK_lot=int(add_position_lots)
            LogInfo(tcode, "空单加仓1", SK_lot, "手")
            if tradestatus:
                iprc = max(Q_BidPrice(tcode) - ovprice_tick*PriceTick(tcode), Q_LowLimit(tcode))  # 对盘超价  
                if checklots(R_Available,iprc,SK_lot) or CheckLoginStatus!=1:
                    _lots=min(SK_lot,MaximumPosition-SKVOL)
                    SKPRICE=iprc
                    SKVOL+=_lots
                    SKStatus2=1
                    tim_trigger(False, True, _lots, ovprice_tick, tcode)
            elif Backtest_sw==0:
                SKStatus2=1
                his_trigger(False, True, SK_lot, ovprice_tick)
        elif  TradeOffTime:
            SKS=1
            LogInfo(tcode,"收盘前平空仓",true_lots,"手")
            if tradestatus:
                tim_trigger_Exit(True,False,ovprice_tick,tcode,true_lots)
                SKVOL=0
                SKPRICE=0
            elif Backtest_sw==0:
                his_trigger_Exit(True,False,ovprice_tick,true_lots)
        elif BPStatus==0 and _TotalProfitLoss >=TotalProfitStopTrading:
            if tradestatus:
                SKS=1
                BPStatus=2
                LogInfo(tcode,"总盈利止盈",true_lots,"手")
                tim_trigger_Exit(True,False,ovprice_tick,tcode,true_lots)
                SKVOL=0
                SKPRICE=0
            # elif Backtest_sw!=0:
            #     SKS=1
            #     his_trigger_Exit(True,False,ovprice_tick,true_lots)
        elif BPStatus==0 and _TotalProfitLoss <=TotalLossStopTrading:
            if tradestatus:
                SKS=1
                BPStatus=3
                LogInfo(tcode,"总亏损止损",true_lots,"手")
                tim_trigger_Exit(True,False,ovprice_tick,tcode,true_lots)
                SKVOL=0
                SKPRICE=0
            # elif Backtest_sw!=0:
            #     SKS=1
            #     his_trigger_Exit(True,False,ovprice_tick,true_lots)

def tim_trigger(BK,SK,qty,itk,tcode):#盘中实时开仓
    global BKS,SKS
    if BK and BKS==0 :
        iprc = min(Q_AskPrice(tcode) +itk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        A_SendOrder(Enum_Buy(), Enum_Entry(), qty, iprc,tcode) 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单买入开仓价==>",iprc,"买入数量==>",qty)
        BKS=1    
    elif SK and SKS==0 :    
        iprc = max(Q_BidPrice(tcode) - itk*PriceTick(tcode), Q_LowLimit(tcode))  # 对盘超价                         
        A_SendOrder(Enum_Sell(), Enum_Entry(), qty, iprc,tcode)   
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单卖出开仓价==>",iprc,"卖出数量==>",qty)
        SKS=1    

def tim_trigger_Exit(BP,SP,otk,tcode,clots):#盘中实时平仓
    global BKS,SKS,BPS,SPS
    if BP and BPS==0 and A_SellPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_SellPosition(tcode))
        prc = min(Q_AskPrice(tcode) +otk*PriceTick(tcode), Q_UpperLimit(tcode)) # 对盘超价
        if ExchangeName(tcode) not in ['SHFE','INE']:    
            retExit, ExitOrderId=A_SendOrder(Enum_Buy(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodaySellPosition(tcode)
            dlots=lots-tlots            
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),lots, prc,tcode) #今仓足够平仓,上期所能交所优先超价全部平今仓    
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Buy(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Buy(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Buy(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"空单买入平仓价==>",prc,"买入平仓数量==>",_lots)
        BPS=1  
        if SKS==1:SKS=2      
    elif SP and SPS==0 and A_BuyPosition(tcode) > 0 and clots>0 :
        _lots=min(clots,A_BuyPosition(tcode))
        prc = max(Q_BidPrice(tcode) - otk*PriceTick(tcode), Q_LowLimit(tcode))
        if ExchangeName(tcode) not in ['SHFE','INE']:
            retExit, ExitOrderId=A_SendOrder(Enum_Sell(), Enum_Exit(), _lots,prc,tcode) 
        else:
            lots=_lots
            tlots=A_TodayBuyPosition(tcode)
            dlots=lots-tlots
            if tlots>=lots:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),lots, prc,tcode)   #今仓足够平仓,上期所能交所优先超价全部平今仓  
            elif tlots>0:       
                TretExit,TExitOrderId =A_SendOrder(Enum_Sell(), Enum_ExitToday(),tlots, prc,tcode)  #今仓不够，上期所能交所优先超价部分平今仓  
                TretExit2,TExitOrderId2 =A_SendOrder(Enum_Sell(), Enum_Exit(),int(dlots), prc,tcode)  #今仓不够，上期所能交所优先超价剩余部分平昨仓  
            elif tlots==0:  
                retExit,ExitOrderId   =A_SendOrder(Enum_Sell(), Enum_Exit(), lots, prc,tcode) #上期所能交所超价平昨仓 
        LogInfo(Q_UpdateTime(tcode),"->合约==>",tcode,"多单卖出平仓价==>",prc,"卖出平仓数量==>",_lots)
        SPS=1    
        if BKS==1:BKS=2 
        
def his_trigger(BK,SK,qty,itk):#历史开仓
    global BKS,SKS
    if BK and BKS==0:
        iprc = Close()[-1]+itk*PriceTick()
        Buy(qty, iprc) 
        LogInfo(Time(),"->合约==>",Symbol(),"多单买入开仓价==>",iprc,"买入数量==>",qty)
        BKS=1    
    elif SK and SKS==0:
        iprc = Close()[-1]-itk*PriceTick()
        SellShort(qty, iprc) 
        LogInfo(Time(),"->合约==>",Symbol(),"空单卖出开仓价==>",iprc,"卖出数量==>",qty)
        SKS=1    
def his_trigger_Exit(BP,SP,otk,clots):#历史平仓
    global BKS,SKS,BPS,SPS
    if BP and BPS==0 and SellPosition() > 0 and clots>0 :
        _lots=min(clots,SellPosition())
        prc =Close()[-1]+otk*PriceTick()
        BuyToCover(_lots,prc)
        LogInfo(Time(),"->合约==>",Symbol(),"空单买入平仓价==>",prc,"买入平仓数量==>",_lots)
        BPS=1  
        if SKS==1:SKS=2   
    elif SP and SPS==0 and BuyPosition() > 0 and clots>0 :
        _lots=min(clots,BuyPosition())
        prc =Close()[-1]-otk*PriceTick()
        Sell(_lots,prc)
        LogInfo(Time(),"->合约==>",Symbol(),"多单卖出平仓价==>",prc,"卖出平仓数量==>",_lots)
        SPS=1  
        if BKS==1:BKS=2   

def checklots(_Available:float,price:float,lot:int) -> bool:
    # if not A_CalcParam():
    #     LogInfo('未通过开仓量检查，因为账户未登录或者策略未关联账户')
    #     return False
    results=_Available>=(MarginRatio()*price*ContractUnit())*lot
    if results:
        LogInfo('通过开仓量检查,可以下单')
    else:
        LogInfo('未通过开仓量检查,不能下单')
    return results   

def floattime_sum(floatin1, floatin2, len_set=12):  # 高精度浮点时间求和（精确到毫秒）
    # 设置浮点数格式，保留len_set位小数
    lensave = f"%0.{len_set}f"
    
    # 格式化浮点数并提取各时间部分
    def extract_time_parts(floatin):
        strfloat = lensave % floatin
        return int(strfloat[2:4]), int(strfloat[4:6]), int(strfloat[6:8]), int(strfloat[8:11])
    
    h1, m1, s1, ms1 = extract_time_parts(floatin1)
    h2, m2, s2, ms2 = extract_time_parts(floatin2)
    
    # 计算总和并处理进位
    total_ms = ms1 + ms2
    ms_carry = total_ms // 1000
    new_ms = total_ms % 1000
    
    total_s = s1 + s2 + ms_carry
    s_carry = total_s // 60
    new_s = total_s % 60
    
    total_m = m1 + m2 + s_carry
    m_carry = total_m // 60
    new_m = total_m % 60
    
    new_h = h1 + h2 + m_carry
    new_h = min(new_h, 99)  # 限制小时数不超过99
    
    # 组合新的浮点时间字符串并转换回浮点数
    new_str_time = f"0.{new_h:02}{new_m:02}{new_s:02}{new_ms:03}"
    return float(new_str_time)

def TimeTo_Minutes(time_in):
    timestr='%0.6f'%time_in
    hsave=int(timestr[2:4])
    msave=int(timestr[4:6])
    tcout=hsave*60+msave
    return tcout
def SessionOpenTime(contractId=''):  # 获取交易时段开盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler:continue
        tlout.append(GetSessionStartTime(contractId, i))  # 获取每个交易时段的开盘时间并加入列表
    return tlout

def SessionCloseTime(contractId=''):  # 获取交易时段收盘时间的浮点数元组
    tlout = []    
    SessionCount = GetSessionCount(contractId)  # 获取交易时段的数量
    fitler=1 if SessionCount==3 else 2
    for i in range(SessionCount):
        if i==fitler-1:continue
        tlout.append(GetSessionEndTime(contractId, i))  # 获取每个交易时段的收盘时间并加入列表
    return tlout

def VTS(time_in, contractId=''):  # 根据输入时间和合约ID计算交易时段
    RTS, CTS, TSession = [], [], []  # 初始化三个列表，用于存储修正后的时间、收盘时间和交易时段
    opentimet = SessionOpenTime(contractId)  # 获取所有交易时段的开盘时间
    Closetimet = SessionCloseTime(contractId)  # 获取所有交易时段的收盘时间
    
    for open_time, close_time in zip(opentimet, Closetimet):
        if time_in >= open_time:  # 判断输入时间是否在开盘时间之后
            RTS.append(time_in)  # 如果是，加入RTS列表
        else:
            RTS.append(floattime_sum(time_in, 0.24))  # 如果不是，修正时间后加入RTS列表
        
        if close_time >= open_time:  # 判断收盘时间是否在开盘时间之后
            CTS.append(close_time)  # 如果是，加入CTS列表
        else:
            CTS.append(floattime_sum(close_time, 0.24))  # 如果不是，修正时间后加入CTS列表
        
        if open_time <= RTS[-1] <= CTS[-1]:  # 判断修正后的时间是否在交易时段内
            TSession.append(len(RTS) - 1)  # 如果是，加入TSession列表

    if len(TSession) == 1:  # 如果只有一个交易时段
        idx = TSession[0]
        return idx, opentimet[idx], RTS[idx], CTS[idx]  # 返回交易时段和相关时间
    else:
        return -1, time_in, time_in, time_in  # 否则返回默认值



def int_to_seconds(datetime_int: int) -> int:
    """
    将整数格式的 YYYYMMDDHHMMSSfff 转换为绝对秒数，使用整数求余解析时间部分。
    
    :param datetime_int: 整数，格式为 YYYYMMDDHHMMSSfff
    :return: 从1970年1月1日开始的绝对秒数
    """
    # 提取时间部分（从右往左逐步解析）
    millisecond = datetime_int % 1000
    datetime_int //= 1000
    second = datetime_int % 100
    datetime_int //= 100
    minute = datetime_int % 100
    datetime_int //= 100
    hour = datetime_int % 100
    datetime_int //= 100
    day = datetime_int % 100
    datetime_int //= 100
    month = datetime_int % 100
    datetime_int //= 100
    year = datetime_int  # 剩余部分为年份

    # 构造 datetime 对象
    dt = datetime.datetime(year, month, day, hour, minute, second, millisecond * 1000)

    # 转为绝对秒数
    return int(dt.timestamp())

def datetime_to_seconds(yyyymmdd: int, fractional_time: float) -> int:
    """
    将 YYYYMMDD 的整数日期和 0.HHMMSS 的浮点时间转换为绝对秒数。
    
    :param yyyymmdd: 整数日期（格式：YYYYMMDD）
    :param fractional_time: 浮点时间（格式：0.HHMMSS）
    :return: 从1970年1月1日开始的绝对秒数
    """
    # 提取日期部分
    year = yyyymmdd // 10000
    month = (yyyymmdd // 100) % 100
    day = yyyymmdd % 100
    
    # 将浮点时间转为小时、分钟、秒
    hhmmss = int(fractional_time * 1e6)  # 转为整数以方便提取
    hour = hhmmss // 10000
    minute = (hhmmss // 100) % 100
    second = hhmmss % 100
    
    # 构造 datetime 对象
    dt = datetime.datetime(year, month, day, hour, minute, second)
    
    # 转为绝对秒数
    return int(dt.timestamp())
def datetime_to_minutes(yyyymmdd: int, fractional_time: float) -> int:
    """
    将 YYYYMMDD 的整数日期和 0.HHMMSS 的浮点时间转换为绝对分钟数。
    
    :param yyyymmdd: 整数日期（格式：YYYYMMDD）
    :param fractional_time: 浮点时间（格式：0.HHMMSS）
    :return: 从1970年1月1日开始的绝对分钟数
    """
    # 提取日期部分
    year = yyyymmdd // 10000
    month = (yyyymmdd // 100) % 100
    day = yyyymmdd % 100
    
    # 将浮点时间转为小时、分钟、秒
    hhmmss = int(fractional_time * 1e6)  # 转为整数以方便提取
    hour = hhmmss // 10000
    minute = (hhmmss // 100) % 100
    second = hhmmss % 100
    
    # 构造 datetime 对象
    dt = datetime.datetime(year, month, day, hour, minute, second)
    
    # 转为绝对分钟数
    return int(dt.timestamp() // 60)


class ZigZag:
    def __init__(self, deviation=5):
        """
        初始化ZigZag类
        deviation: 偏差百分比,用于确定转折点,默认为5%
        """
        self.deviation = deviation / 100
        self.high_price = 0
        self.low_price = float('inf')
        self.last_high = 0  
        self.last_low = float('inf')
        self.trend = 0  # 1表示上升趋势,-1表示下降趋势
        self.zigzag_points = []  # 存储转折点
        self.high_pos = 0  # 最高点位置
        self.low_pos = 0   # 最低点位置
        
    def update(self, high, low, pos):
        """
        更新ZigZag状态
        high: 当前最高价
        low: 当前最低价  
        pos: 当前位置(可以是K线索引)
        返回: 当前是否形成新的转折点
        """
        is_new_point = False
        
        if self.trend >= 0:  # 上升或初始趋势
            if high > self.high_price:  # 创新高
                self.high_price = high
                self.high_pos = pos
            
            # 下跌超过偏差,确认顶部
            if low < self.high_price * (1 - self.deviation):
                if self.trend == 1:  # 之前是上升趋势
                    self.zigzag_points.append((self.high_pos, self.high_price))
                    is_new_point = True
                self.trend = -1
                self.low_price = low
                self.low_pos = pos
                self.last_high = self.high_price
                
        if self.trend <= 0:  # 下降或初始趋势
            if low < self.low_price:  # 创新低
                self.low_price = low  
                self.low_pos = pos
                
            # 上涨超过偏差,确认底部    
            if high > self.low_price * (1 + self.deviation):
                if self.trend == -1:  # 之前是下降趋势
                    self.zigzag_points.append((self.low_pos, self.low_price))
                    is_new_point = True
                self.trend = 1
                self.high_price = high
                self.high_pos = pos
                self.last_low = self.low_price
                
        return is_new_point
    
    def get_last_point(self):
        """获取最后一个转折点"""
        if len(self.zigzag_points) > 0:
            return self.zigzag_points[-1]
        return None
        
    def get_points(self):
        """获取所有转折点"""
        return self.zigzag_points
    
    def get_current_trend(self):
        """获取当前趋势"""
        return self.trend

