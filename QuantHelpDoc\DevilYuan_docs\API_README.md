# DevilYuan API文档

## 概述

DevilYuan量化交易系统的API文档提供了系统中各模块、类和函数的详细说明，帮助开发者理解系统架构和使用各种功能。本文档遵循 `.cursorrules` 文件中定义的规范：

- 所有函数注释包含参数类型和返回值说明
- 类注释描述继承关系和主要方法
- 使用 reStructuredText 语法标记代码示例

## 文档结构

API文档按照系统的模块结构组织：

1. **主界面** (DyMainWindow)
   - 应用程序入口点和UI界面

2. **股票模块** (Stock)
   - **交易模块** (Stock/Trade)
     - 策略基类
     - 持仓管理
     - 账户管理
   - **选股模块** (Stock/Select)
     - 选股策略模板
     - 选股引擎
   - **回测模块** (Stock/BackTesting)
   - **数据模块** (Stock/Data)
   - **通用模块** (Stock/Common)

3. **事件引擎** (EventEngine)
   - 事件驱动模型

4. **通用模块** (DyCommon)
   - 项目通用功能

## 关键类和接口

### 核心策略类

- **DyStockStrategyBase** - 股票策略状态管理基类
- **DyStockSelectStrategyTemplate** - 选股策略模板，所有选股策略的父类
- **DyStockPos** - 股票持仓类，管理单只股票的持仓信息

### 数据引擎类

- **DyStockDataTicksEngine** - 股票Tick数据引擎
- **DyStockDataStrategyDataPrepareEngine** - 股票实盘准备数据引擎
- **DyStockDataMin5Engine** - 股票5分钟线数据引擎
- **DyStockDataMin1Engine** - 股票1分钟线数据引擎
- **DyStockDataMainEngine** - 股票数据主引擎
- **DyStockDataFinancialTableEngine** - 股票历史财务数据引擎
- **DyStockDataEngine** - 股票数据基础引擎
- **DyStockDataDaysEngine** - 股票历史日线数据引擎
- **DyStockDataCommonEngine** - 代码表和交易日数据引擎

### 主要界面类

- **DyMainWindow** - 主窗口类，整个应用程序的入口
- **DyStockTradeMainWindow** - 交易主窗口
- **DyStockSelectMainWindow** - 选股主窗口
- **DyStockDataMainWindow** - 数据管理主窗口
- **DyStockBackTestingMainWindow** - 策略回测主窗口

## 如何贡献文档

1. 使用项目根目录下的 `generate_api_docs.py` 为新开发的类或函数生成符合规范的API文档注释骨架
2. 手动完善文档注释内容，确保描述清晰、准确
3. 添加代码示例以便更好地理解接口的使用方法
4. 确保文档注释能够通过代码审查

## 数据引擎类详细说明

### DyStockDataDaysEngine
```python
class DyStockDataDaysEngine(object):
    """
    股票（指数）历史日线数据引擎，包含股票代码表和交易日数据
    
    继承关系：无继承
    
    主要方法：
        - getDays: 获取指定股票的日线数据
        - getTradeDays: 获取交易日列表
        - getStockCodes: 获取股票代码表
    
    ::
    
        # 使用示例
        engine = DyStockDataDaysEngine()
        days_data = engine.getDays('000001', '2020-01-01', '2020-12-31')
        trade_days = engine.getTradeDays('2020-01-01', '2020-12-31')
    """
```

### DyStockDataMin1Engine
```python
class DyStockDataMin1Engine(object):
    """
    股票1分钟线数据引擎
    
    继承关系：无继承
    
    主要方法：
        - getMin1: 获取指定股票的1分钟线数据
        - getTradeDays: 获取交易日列表
    
    ::
    
        # 使用示例
        engine = DyStockDataMin1Engine()
        min1_data = engine.getMin1('000001', '2020-01-01', '2020-12-31')
    """
```

### DyStockDataTicksEngine
```python
class DyStockDataTicksEngine(object):
    """
    股票Tick数据引擎
    
    继承关系：无继承
    
    主要方法：
        - getTicks: 获取指定股票的Tick数据
        - getTradeDays: 获取交易日列表
    
    ::
    
        # 使用示例
        engine = DyStockDataTicksEngine()
        ticks_data = engine.getTicks('000001', '2020-01-01')
    """
```

## 文档规范示例

### 类文档示例

```python
class DyStockPos:
    """
    股票持仓类，表示一只股票的持仓信息。
    
    继承关系：无继承
    
    主要方法：
        - addPos: 增加持仓
        - removePos: 减少持仓
        - onOpen: 开盘前处理
        - onTick: 处理Tick数据
        - onClose: 收盘后处理
    
    ::
    
        # 创建持仓示例
        pos = DyStockPos('2021-01-01', Strategy, '000001', '平安银行', 12.5, 100)
        
        # 增加持仓
        pos.addPos('2021-01-02', Strategy, 12.6, 100)
    """
```

### 函数文档示例

```python
def removePos(self, price, volume, tradeCost=0, removeAvailVolume=True):
    """
    减少持仓，注意：成本不会重新计算，这与炒股软件不同
    
    :param price: 减仓价格
    :type price: float
    :param volume: 减仓数量
    :type volume: int
    :param tradeCost: 交易成本
    :type tradeCost: float
    :param removeAvailVolume: 是否从可用数量中减少
    :type removeAvailVolume: bool
    :return: 盈亏和盈亏比例，如果减仓失败则返回None, None
    :rtype: tuple(float, float)或tuple(None, None)
    """
```

## 相关资源

- [API文档生成工具使用指南](api_docs_guide.md)
- [项目整体架构](brief_introduction.pdf)
- [股票交易模块架构](trade/trade_xmind.png)

## 常见问题

**Q: 我想查看某个类或方法的详细说明，应该如何操作？**

A: 您可以直接查看源代码中的文档注释，或者使用IDE的代码提示功能查看。

**Q: 我发现文档有错误或不清晰的地方，应该怎么办？**

A: 欢迎提交问题反馈或Pull Request来改进文档。

**Q: 如何为我开发的新功能添加文档？**

A: 请参照[API文档生成工具使用指南](api_docs_guide.md)，使用文档生成工具并手动完善文档内容。
