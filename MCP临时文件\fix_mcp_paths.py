#!/usr/bin/env python3
"""
修复MCP配置文件中的Python路径问题
"""

import json
import sys
from pathlib import Path

def fix_augment_config():
    """修复Augment配置文件中的Python路径"""
    config_path = Path("C:/Users/<USER>/.codegeex/agent/configs/user_mcp_config.json")
    
    if not config_path.exists():
        print(f"配置文件不存在: {config_path}")
        return False
    
    # 备份原文件
    backup_path = config_path.with_suffix('.json.backup2')
    try:
        import shutil
        shutil.copy2(config_path, backup_path)
        print(f"备份创建: {backup_path}")
    except Exception as e:
        print(f"备份失败: {e}")
    
    # 读取配置
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"读取配置失败: {e}")
        return False
    
    # 获取正确的Python路径
    python_exe = sys.executable
    print(f"使用Python路径: {python_exe}")
    
    # 更新配置
    if "mcpServers" in config:
        for server_name, server_config in config["mcpServers"].items():
            if server_name in ["graphiti", "opik", "ragie"]:
                # 更新Python命令路径
                server_config["command"] = python_exe
                print(f"更新 {server_name} 的Python路径")
    
    # 保存配置
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print("配置文件更新成功")
        return True
    except Exception as e:
        print(f"保存配置失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 修复MCP配置文件路径 ===")
    
    success = fix_augment_config()
    
    if success:
        print("\n✅ 修复完成！")
        print("请重启Augment插件以使配置生效。")
    else:
        print("\n❌ 修复失败，请检查错误信息。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
