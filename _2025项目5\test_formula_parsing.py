import re

# 合约正则表达式
CONTRACT_PATTERN = re.compile(r'(?:(?:ZCE)\|[A-Z]\|[A-Z]+\|\d{3}|(?:DCE|SHFE|INE|CFFEX|GFEX)\|[A-Z]\|[A-Za-z]+\|\d{4})')

def parse_arbitrage_directions_and_coefficients(formula):
    """
    解析套利组合公式中各合约的交易方向和倍数系数
    
    参数:
        formula: 套利组合公式，如 "DCE|F|P|2509*3-DCE|F|P|2507-DCE|F|Y|2509*2"
    
    返回:
        contract_info: 字典 {contract: (direction, coefficient)}, 
                      direction为'BUY'或'SELL', coefficient为倍数
    """
    contract_info = {}
    
    # 替换括号，便于处理
    simplified = re.sub(r'[()]', ' ', formula)
    
    # 分割公式，保留操作符
    # 先在操作符两边加空格，但要小心不要拆分合约代码中的内容
    simplified = re.sub(r'([+\-])', r' \1 ', simplified)
    tokens = simplified.split()
    
    current_sign = '+'  # 默认第一个合约为正
    
    for token in tokens:
        token = token.strip()
        if not token:
            continue
            
        if token in ['+', '-']:
            current_sign = token
        else:
            # 检查是否包含倍数（*号）
            if '*' in token:
                # 分离合约代码和倍数
                parts = token.split('*')
                if len(parts) == 2:
                    contract_part = parts[0]
                    try:
                        coefficient = int(parts[1])
                    except ValueError:
                        coefficient = 1
                        print(f"警告：无法解析倍数 {parts[1]}，使用默认倍数1")
                else:
                    contract_part = token
                    coefficient = 1
            else:
                contract_part = token
                coefficient = 1
            
            # 检查是否是合约代码
            if CONTRACT_PATTERN.fullmatch(contract_part):
                direction = 'BUY' if current_sign == '+' else 'SELL'
                contract_info[contract_part] = (direction, coefficient)
                # 重置为正号，为下一个合约做准备
                current_sign = '+'
    
    print(f"套利公式解析结果: {formula} -> {contract_info}")
    return contract_info

def test_formula_parsing():
    """测试公式解析功能"""
    print("=== 套利公式解析测试 ===\n")
    
    # 测试用例
    test_formulas = [
        "SHFE|F|rb|2505-SHFE|F|rb|2501",  # 基础格式
        "DCE|F|P|2509*3-DCE|F|P|2507-DCE|F|Y|2509*2",  # 带倍数格式
        "SHFE|F|rb|2505*2+SHFE|F|rb|2501*3-SHFE|F|hc|2505",  # 复杂格式
        "DCE|F|P|2509*5",  # 单合约带倍数
        "DCE|F|P|2509-DCE|F|P|2507*4+DCE|F|Y|2509*2-DCE|F|Y|2507",  # 多合约混合
    ]
    
    for i, formula in enumerate(test_formulas, 1):
        print(f"测试 {i}: {formula}")
        result = parse_arbitrage_directions_and_coefficients(formula)
        
        print("解析结果:")
        for contract, (direction, coef) in result.items():
            print(f"  {contract}: {direction}, 倍数={coef}")
        
        # 模拟下单计算
        base_qty = 1
        print(f"如果基础下单量={base_qty}手，实际下单:")
        for contract, (direction, coef) in result.items():
            actual_qty = base_qty * coef
            action = "买入" if direction == "BUY" else "卖出"
            print(f"  {contract}: {action} {actual_qty}手")
        print("-" * 50)

if __name__ == "__main__":
    test_formula_parsing() 